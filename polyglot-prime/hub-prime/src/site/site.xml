<?xml version="1.0" encoding="ISO-8859-1"?>

<project name="Maven Site">
    
   <!-- Define the skin for the site -->
    <skin>
        <groupId>org.apache.maven.skins</groupId>
        <artifactId>maven-fluido-skin</artifactId>
        <version>2.0.0-M9</version>
    </skin>
    <custom>
	    <fluidoSkin>
		<topBarEnabled>false</topBarEnabled>   <!-- Hide the top bar -->
		<sideBarEnabled>true</sideBarEnabled>  <!-- Show the side bar (left menu) -->
	    </fluidoSkin>
    </custom>

    <!-- Main body of the site configuration -->
    <body>
        <!-- Define the main menu -->
        <menu name="Project Documentation">
            <item name="Home" href="index.html" />
            <item name="Project Information">
                <item name="Dependencies" href="dependencies.html" />
                <item name="Maven Coordinates" href="dependency-info.html" />
                <item name="Dependency Management" href="dependency-management.html" />
                <item name="Distribution Management" href="distribution-management.html" />
                <!-- Add more menu items as needed -->
                <item name="About" href="about.html" />
                <item name="Licenses" href="licenses.html" />
                <item name="Plugin Management" href="plugin-management.html" />
                <item name="Plugins" href="plugins.html" />
                <item name="Source Code Management" href="scm.html" />
                <item name="Summary" href="summary.html" />
                <item name="Team" href="team.html" />
            </item>
        </menu>

        <!-- Define the reports menu -->
        <menu name="report">
            <item name="Project Reports" href="project-reports.html" />
        </menu>
    </body>
</project>

