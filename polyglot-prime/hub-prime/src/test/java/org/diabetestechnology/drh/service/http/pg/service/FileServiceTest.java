package org.diabetestechnology.drh.service.http.pg.service;

import static org.assertj.core.api.Assertions.assertThat;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class FileServiceTest {

    @TempDir
    Path tempDir;

    @Test
    void testExtractCsvHeaders() throws IOException {
        Path csvFile = tempDir.resolve("test.csv");
        try (BufferedWriter writer = Files.newBufferedWriter(csvFile)) {
            writer.write("header1,header2,header3");
        }

        FileService fileService = new FileService();
        List<String> headers = fileService.extractHeaders(csvFile.toString());

        assertEquals(List.of("header1", "header2", "header3"), headers);
    }

    @Test
    void testExtractExcelHeaders() throws IOException {
        Path excelFile = tempDir.resolve("test.xlsx");
        try (FileOutputStream fos = new FileOutputStream(excelFile.toFile());
                Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet();
            Row row = sheet.createRow(0);
            row.createCell(0).setCellValue("header1");
            row.createCell(1).setCellValue("header2");
            row.createCell(2).setCellValue("header3");
            workbook.write(fos);
        }

        FileService fileService = new FileService();
        List<String> headers = fileService.extractHeaders(excelFile.toString());

        assertEquals(List.of("header1", "header2", "header3"), headers);
    }

    @Test
    void testExtractExcelHeaders_NoHeaderRow() throws IOException {
        Path excelFile = tempDir.resolve("no_header.xlsx");
        try (FileOutputStream fos = new FileOutputStream(excelFile.toFile());
                Workbook workbook = new XSSFWorkbook()) {
            workbook.createSheet(); // Create an empty sheet without any rows
            workbook.write(fos);
        }

        FileService fileService = new FileService();

        assertThrows(IllegalArgumentException.class,
                () -> fileService.extractHeaders(excelFile.toString()),
                "Excel sheet has no header row.");
    }

    @Test
    void testExtractTextHeaders() throws IOException {
        Path textFile = tempDir.resolve("test.txt");
        try (BufferedWriter writer = Files.newBufferedWriter(textFile)) {
            writer.write("header1 header2 header3");
        }

        FileService fileService = new FileService();
        List<String> headers = fileService.extractHeaders(textFile.toString());

        assertEquals(List.of("header1", "header2", "header3"), headers);
    }

    @Test
    void testExtractXmlHeaders() throws IOException {
        Path xmlFile = tempDir.resolve("test.xml");
        try (BufferedWriter writer = Files.newBufferedWriter(xmlFile)) {
            writer.write("""
                        <Records>
                            <Record>
                                <header1>value1</header1>
                                <header2>value2</header2>
                                <header3>value3</header3>
                            </Record>
                        </Records>
                    """);
        }

        FileService fileService = new FileService();
        List<String> headers = fileService.extractHeaders(xmlFile.toString());

        assertThat(headers).containsExactlyInAnyOrder("header1", "header2", "header3");
    }

    @Test
    void testExtractXmlHeaders_NoHeaderRow() throws IOException {
        Path xmlFile = tempDir.resolve("test.xml");
        try (BufferedWriter writer = Files.newBufferedWriter(xmlFile)) {
            writer.write("""
                        <Records>
                            <Record>
                            </Record>
                        </Records>
                    """);
        }

        FileService fileService = new FileService();
        assertThrows(
                IOException.class,
                () -> fileService.extractHeaders(xmlFile.toString()),
                "XML file has no headers.");
    }

    @Test
    void testExtractJsonHeaders() throws IOException {
        // Create a temporary JSON file
        Path jsonFile = tempDir.resolve("test.json");
        try (BufferedWriter writer = Files.newBufferedWriter(jsonFile)) {
            writer.write("""
                    [
                        {"name": "John", "age": 30, "city": "New York"},
                        {"name": "Alice", "gender": "Female", "city": "London"}
                    ]
                    """);
        }
        FileService fileService = new FileService();
        List<String> headers = fileService.extractHeaders(jsonFile.toString());
        assertTrue(Set.of("name", "age", "city", "gender").equals(Set.copyOf(headers)));
    }

    @Test
    void testUnsupportedFileType() {
        FileService fileService = new FileService();
        assertThrows(IllegalArgumentException.class, () -> fileService.extractHeaders("unsupported.filetype"));
    }
}
