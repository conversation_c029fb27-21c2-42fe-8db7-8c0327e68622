package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;

import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectLimitPercentStep;
import org.jooq.SelectSelectStep;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@ExtendWith(MockitoExtension.class)
class PartyServiceTest {
    @Mock
    private DSLContext dsl;

    @InjectMocks
    private PartyService partyService;

    @Mock
    private SelectSelectStep<Record1<String>> selectMock;

    @Mock
    private SelectJoinStep<Record1<String>> joinStepMock;

    @Mock
    private SelectConditionStep<Record1<String>> conditionStepMock;

    @SuppressWarnings("unchecked")
    @Test
    void testGetPartyIdByUserId_ReturnsValidPartyId() {
        String userId = "user123";
        String expectedPartyId = "party456";

        org.springframework.security.core.Authentication authentication = mock(
                org.springframework.security.core.Authentication.class);
        when(authentication.getPrincipal()).thenReturn(userId);
        org.springframework.security.core.context.SecurityContext securityContext = mock(
                org.springframework.security.core.context.SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        org.springframework.security.core.context.SecurityContextHolder.setContext(securityContext);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(eq(String.class))).thenReturn(expectedPartyId);

        String actualPartyId = partyService.getPartyIdByUserId(userId);

        assertEquals(expectedPartyId, actualPartyId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetPartyIdByUserId_AsUser() {
        String userId = "<EMAIL>";
        String expectedPartyId = "party-123";

        org.springframework.security.core.Authentication authentication = mock(
                org.springframework.security.core.Authentication.class);
        org.springframework.security.core.context.SecurityContext securityContext = mock(
                org.springframework.security.core.context.SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        org.springframework.security.core.context.SecurityContextHolder.setContext(securityContext);

        org.springframework.security.core.userdetails.UserDetails userDetails = mock(
                org.springframework.security.core.userdetails.UserDetails.class);
        when(authentication.getPrincipal()).thenReturn(userDetails);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(eq("drh_stateless_authentication.super_admin_view"))).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(expectedPartyId);

        String result = partyService.getPartyIdByUserId(userId);

        assertEquals(expectedPartyId, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetPartyIdByUserId_ReturnsNull_WhenNoResult() {
        String userId = "user123";

        org.springframework.security.core.Authentication authentication = mock(
                org.springframework.security.core.Authentication.class);
        when(authentication.getPrincipal()).thenReturn(userId);
        org.springframework.security.core.context.SecurityContext securityContext = mock(
                org.springframework.security.core.context.SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        org.springframework.security.core.context.SecurityContextHolder.setContext(securityContext);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(null);

        String actualPartyId = partyService.getPartyIdByUserId(userId);

        assertNull(actualPartyId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetPartyIdByOrganization_ReturnsValidPartyId() {
        String organizationId = "org123";
        String expectedPartyId = "party789";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(expectedPartyId);

        String actualPartyId = partyService.getPartyIdByOrganization(organizationId);

        assertEquals(expectedPartyId, actualPartyId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetPartyIdByOrganization_ReturnsNull_WhenNoResult() {
        String organizationId = "org123";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn(null);

        String actualPartyId = partyService.getPartyIdByOrganization(organizationId);

        assertNull(actualPartyId);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(String.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetActiveUserPartyId_ReturnsValidJson() throws Exception {
        String userId = "user123";
        String jsonResult = "[{\"practitioner_party_id\":\"party456\",\"organization_party_id\":\"org789\"}]";
        JSONB expectedJsonb = JSONB.valueOf(jsonResult);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(expectedJsonb);

        String actualJson = partyService.getActiveUserPartyId(userId);

        String expectedJsonPretty = """
                [
                  {
                    "practitioner_party_id" : "party456",
                    "organization_party_id" : "org789"
                  }
                ]""";

        ObjectMapper mapper = new ObjectMapper();
        JsonNode expectedJsonNode = mapper.readTree(expectedJsonPretty);
        JsonNode actualJsonNode = mapper.readTree(actualJson);

        assertEquals(expectedJsonNode, actualJsonNode);

        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetActiveUserPartyId_ReturnsEmptyJson_WhenNoResult() {
        String userId = "user123";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(null);

        String actualJson = partyService.getActiveUserPartyId(userId);

        assertEquals("{}", actualJson);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(JSONB.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetActiveUserPartyId_ReturnsEmptyJson_WhenJsonProcessingFails() {
        String userId = "user123";
        JSONB invalidJsonb = JSONB.valueOf("{invalid_json}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(invalidJsonb);

        String actualJson = partyService.getActiveUserPartyId(userId);

        assertEquals("{}", actualJson);
        verify(dsl, times(1)).select(any(Field.class));
        verify(selectMock, times(1)).from(anyString());
        verify(joinStepMock, times(1)).where(any(Condition.class));
        verify(conditionStepMock, times(1)).fetchOneInto(JSONB.class);
    }

    @Test
    void testGetOrganizationPartyIdByUser_ReturnsValidId() {

        String userId = "user123";
        String expectedPartyId = "org789";

        PartyService spyPartyService = spy(partyService);
        doReturn(expectedPartyId).when(spyPartyService).getUserOrganizationPartyId(userId);

        String actualPartyId = spyPartyService.getOrganizationPartyIdByUser(userId);

        assertEquals(expectedPartyId, actualPartyId);
        verify(spyPartyService, times(1)).getUserOrganizationPartyId(userId);
    }

    @Test
    void testGetOrganizationPartyIdByUser_ReturnsNull() {
        String userId = "user123";

        PartyService spyPartyService = spy(partyService);
        doReturn(null).when(spyPartyService).getUserOrganizationPartyId(userId);

        String actualPartyId = spyPartyService.getOrganizationPartyIdByUser(userId);

        assertNull(actualPartyId);
        verify(spyPartyService).getUserOrganizationPartyId(userId);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetTenantIdByUserId_AsUser() {
        String userId = "user1";

        org.springframework.security.core.Authentication authentication = mock(
                org.springframework.security.core.Authentication.class);
        when(authentication.getPrincipal()).thenReturn("notUserDetails");
        org.springframework.security.core.context.SecurityContext securityContext = mock(
                org.springframework.security.core.context.SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        org.springframework.security.core.context.SecurityContextHolder.setContext(securityContext);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(String.class)).thenReturn("tenant-456");

        String result = partyService.getTenantIdByUserId(userId);

        assertEquals("tenant-456", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetRolesByUserId_ReturnsRoles() {
        String userId = "user1";
        List<String> expectedRoles = Arrays.asList("ROLE_USER", "ROLE_ADMIN");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where((Condition) any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchInto(String.class)).thenReturn(expectedRoles);

        List<String> roles = partyService.getRolesByUserId(userId);

        assertEquals(expectedRoles, roles);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetRolesByUserId_ReturnsEmptyList() {
        String userId = "user2";
        List<String> expectedRoles = Collections.emptyList();

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchInto(String.class)).thenReturn(expectedRoles);

        List<String> roles = partyService.getRolesByUserId(userId);

        assertTrue(roles.isEmpty());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserOrganizationPartyId_AsUser() {
        String userId = "user1";
        String expectedPartyId = "org-party-456";

        org.springframework.security.core.Authentication authentication = mock(
                org.springframework.security.core.Authentication.class);
        org.springframework.security.core.context.SecurityContext securityContext = mock(
                org.springframework.security.core.context.SecurityContext.class);

        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn("notUserDetails");
        org.springframework.security.core.context.SecurityContextHolder.setContext(securityContext);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<String>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(eq("drh_stateless_authentication.user_profile_view"))).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOneInto(String.class)).thenReturn(expectedPartyId);

        String result = partyService.getUserOrganizationPartyId(userId);

        assertEquals(expectedPartyId, result);
    }

    @Test
    void testGetUserOrganizationPartyId_Exception() {
        String userId = "user2";

        org.springframework.security.core.Authentication authentication = mock(
                org.springframework.security.core.Authentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        try (var mockedSecurityContextHolder = mockStatic(SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            lenient().when(securityContext.getAuthentication()).thenReturn(authentication);
            lenient().when(authentication.getPrincipal()).thenReturn("notUserDetails");
        }

        lenient().when(dsl.select((Field<?>) any())).thenThrow(new RuntimeException("DB error"));

        String result = partyService.getUserOrganizationPartyId(userId);

        assertEquals("", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetSuperUserOrganizationTenantId_Success() {
        String userId = "<EMAIL>";
        String organizationPartyId = "party-123";
        String expectedTenantId = "tenant-456";

        PartyService spyService = spy(partyService);
        doReturn(organizationPartyId).when(spyService).getUserOrganizationPartyId(userId);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> joinStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> conditionStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<String>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(eq("drh_stateless_research_study.organization_party_view"))).thenReturn(joinStepMock);
        when(joinStepMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOneInto(String.class)).thenReturn(expectedTenantId);

        String result = spyService.getSuperUserOrganizationTenantId(userId);

        assertEquals(expectedTenantId, result);
    }

    @Test
    void testGetSuperUserOrganizationTenantId_Exception() {
        String userId = "<EMAIL>";

        PartyService spyService = spy(partyService);
        doThrow(new RuntimeException("DB error")).when(spyService).getUserOrganizationPartyId(userId);

        String result = spyService.getSuperUserOrganizationTenantId(userId);

        assertEquals("", result);
    }

}
