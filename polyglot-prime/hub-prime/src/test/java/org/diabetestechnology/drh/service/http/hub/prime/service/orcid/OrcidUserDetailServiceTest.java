package org.diabetestechnology.drh.service.http.hub.prime.service.orcid;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import jakarta.servlet.http.HttpServletRequest;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;

@ExtendWith(MockitoExtension.class)
class OrcidUserDetailServiceTest {

    @InjectMocks
    private OrcidUserDetailService orcidUserDetailService;

    @Mock
    private UserNameService userNameService;

    @Mock
    private AuditService auditService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private RestTemplate restTemplate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        orcidUserDetailService = new OrcidUserDetailService(restTemplate, auditService, userNameService);
        ReflectionTestUtils.setField(orcidUserDetailService, "orcidApiBaseUrl",
                "https://api.orcid.org/v3.0");

    }

    @Test
    void testGetUserDetails_WithOrcidProvider() {
        // Mock the user details for ORCID provider
        when(userNameService.getUserProvider()).thenReturn("orcid");
        when(auditService.getCurrentRequest()).thenReturn(request);
        when(userNameService.getUserId()).thenReturn("userId123");
        when(userNameService.getUserName()).thenReturn("John Doe");
        when(userNameService.getUserEmail()).thenReturn("<EMAIL>");
        when(userNameService.getUserInstitution()).thenReturn("Test Institution");

        // Call the method
        Map<String, Object> userDetails = orcidUserDetailService.getUserDetails();

        // Verify the result
        assertEquals("userId123", userDetails.get("userId"));
        assertEquals("John Doe", userDetails.get("userName"));
        assertEquals("<EMAIL>", userDetails.get("userEmail"));
        assertEquals("Test Institution", userDetails.get("userInstitution"));
        assertEquals(4, userDetails.size()); // Verify the map contains the expected number of keys
    }

    @Test
    void testGetUserDetails_WithoutOrcidProvider() {
        // Mock the user details for a provider that is not ORCID
        when(userNameService.getUserProvider()).thenReturn("google");

        // Call the method
        Map<String, Object> userDetails = orcidUserDetailService.getUserDetails();

        // Verify the result
        assertEquals(1, userDetails.size()); // Only the provider should be returned
        assertEquals("google", userDetails.get("provider"));
    }

    @Test
    void testGetUserDetails_AnonymousProvider() {
        // Mock the user details for an anonymous provider
        when(userNameService.getUserProvider()).thenReturn("anonymous");

        // Call the method
        Map<String, Object> userDetails = orcidUserDetailService.getUserDetails();

        // Verify the result
        assertEquals(1, userDetails.size()); // Only the provider should be returned
        assertEquals("anonymous", userDetails.get("provider"));
    }

    @Test
    void testGetUserId_WithOrcidProvider() {
        when(userNameService.getUserProvider()).thenReturn("orcid");
        when(auditService.getCurrentRequest()).thenReturn(request);
        when(userNameService.getUserId()).thenReturn("userId123");

        String userId = orcidUserDetailService.getUserId();
        assertEquals("userId123", userId);
    }

    @Test
    void testGetUserId_WithNonOrcidProvider() {

        when(userNameService.getUserProvider()).thenReturn("github");
        String userId = orcidUserDetailService.getUserId();
        assertNull(userId);
    }

    @Test
    void testExtractFullName_WithGivenAndFamilyName() {
        String jsonString = """
                {
                    "person": {
                        "name": {
                            "given-names": { "value": "John" },
                            "family-name": { "value": "Doe" }
                        }
                    }
                }
                """;

        String fullName = orcidUserDetailService.extractFullName(jsonString);
        assertEquals("John Doe", fullName);
    }

    @Test
    void testExtractFullName_WithOnlyGivenName() {
        String jsonString = """
                {
                    "person": {
                        "name": {
                            "given-names": { "value": "Alice" }
                        }
                    }
                }
                """;
        String fullName = orcidUserDetailService.extractFullName(jsonString);
        assertEquals("Alice", fullName);
    }

    @Test
    void testExtractFullName_WithNullFamilyName() {
        String jsonString = """
                {
                    "person": {
                        "name": {
                            "given-names": { "value": "Emma" },
                            "family-name": null
                        }
                    }
                }
                """;
        String fullName = orcidUserDetailService.extractFullName(jsonString);
        assertEquals("Emma", fullName);
    }

    @Test
    void testExtractEmail_WithValidEmail() {
        String jsonString = """
                {
                    "person": {
                        "emails": {
                            "email": [
                                { "email": "<EMAIL>" }
                            ]
                        }
                    }
                }
                """;
        String email = orcidUserDetailService.extractEmail(jsonString);
        assertEquals("<EMAIL>", email);
    }

    @Test
    void testExtractEmail_WithEmptyEmailArray() {
        String jsonString = """
                {
                    "person": {
                        "emails": {
                            "email": []
                        }
                    }
                }
                """;
        String email = orcidUserDetailService.extractEmail(jsonString);
        assertNull(email);
    }

    @Test
    void testExtractInstitution_WithValidInstitution() {
        String jsonString = """
                {
                    "activities-summary": {
                        "employments": {
                            "affiliation-group": [
                                {
                                    "summaries": [
                                        {
                                            "employment-summary": {
                                                "organization": {
                                                    "name": "Harvard University"
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
                """;
        String institution = orcidUserDetailService.extractInstitution(jsonString);
        assertEquals("Harvard University", institution);
    }

    @Test
    void testExtractInstitution_WithMalformedJson_ShouldCatchException() {

        String malformedJson = "{ \"activities-summary\": { \"employments\": { ";
        String institution = orcidUserDetailService.extractInstitution(malformedJson);
        assertNull(institution);
    }

    @Test
    void testGetOrcidUserInfo_ValidOrcidId_ShouldReturnApiResponse() {
        String validOrcidId = "0000-0002-1825-0097";
        String expectedResponseBody = "{\"person\": {\"name\": {\"given-names\": {\"value\": \"John\"}}}}";

        String orcidApiBaseUrlFromService = (String) ReflectionTestUtils.getField(
                orcidUserDetailService, "orcidApiBaseUrl");
        String url = String.format("%s/%s", orcidApiBaseUrlFromService, validOrcidId);
        ResponseEntity<String> mockResponse = new ResponseEntity<>(expectedResponseBody, HttpStatus.OK);
        when(restTemplate.getForEntity(eq(url), eq(String.class))).thenReturn(mockResponse);

        ResponseEntity<String> response = orcidUserDetailService.getOrcidUserInfo(validOrcidId);
        assertNotNull(response, "Response should not be null");
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedResponseBody, response.getBody());
    }

    @Test
    void testGetOrcidUserInfo_InvalidOrcidId_ShouldReturnBadRequest() {

        String invalidOrcidId = "1234-5678-9ABC";

        ResponseEntity<String> response = orcidUserDetailService.getOrcidUserInfo(invalidOrcidId);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Invalid ORCID ID format", response.getBody());
    }

    @Test
    void testGetOrcidUserInfo_WhenRestTemplateThrowsException_ShouldReturnErrorResponse() {
        String validOrcidId = "0000-0002-1825-0097";
        String url = "https://api.orcid.org/v3.0/" + validOrcidId;

        when(restTemplate.getForEntity(eq(url), eq(String.class)))
                .thenThrow(new RuntimeException("Failed to fetch data from ORCID"));
        ResponseEntity<String> response = orcidUserDetailService.getOrcidUserInfo(validOrcidId);

        assertNotNull(response, "Response should not be null");
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertTrue(response.getBody().contains("Error fetching ORCID details"));
        assertTrue(response.getBody().contains("Failed to fetch data from ORCID"));
    }

}
