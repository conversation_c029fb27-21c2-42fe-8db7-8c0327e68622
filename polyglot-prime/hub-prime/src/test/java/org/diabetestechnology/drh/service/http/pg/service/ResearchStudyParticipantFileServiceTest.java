package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantFileDataRequest;
import org.jooq.JSONB;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

@ExtendWith(MockitoExtension.class)
public class ResearchStudyParticipantFileServiceTest {

    @Mock
    private S3FileUploadService s3FileUploadService;

    @Mock
    private ParticipantService participantService;

    @Mock
    private MasterService masterService;

    @Mock
    private UserNameService userNameService;

    @Mock
    private InteractionService interactionService;

    @InjectMocks
    private ResearchStudyParticipantFileService researchStudyParticipantFileService;

    @Mock
    private MultipartFile multipartFile;

    @Mock
    private ParticipantFileDataRequest request;

    @Test
    void testUploadAndSaveParticipantFile_Success() throws Exception {

        String csvContent = "participant_id,gender,age,race,ethnicity\nP001,Male,25,Asian,Hispanic";
        byte[] fileContent = csvContent.getBytes(StandardCharsets.UTF_8);

        MultipartFile multipartFile = mock(MultipartFile.class);
        when(multipartFile.getOriginalFilename()).thenReturn("participants.csv");
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getInputStream()).thenAnswer(invocation -> new ByteArrayInputStream(fileContent));
        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        when(request.orgPartyId()).thenReturn("org-123");
        when(request.studyId()).thenReturn("study-456");

        // Mock S3 Upload
        String fakeS3Url = "https://s3-bucket.com/participants.csv";
        when(s3FileUploadService.uploadParticipantFileToS3Bucket(multipartFile, "org-123", "study-456"))
                .thenReturn(fakeS3Url);

        // Mock File Type Validation
        Path tempFile = Files.createTempFile("participants", ".csv");
        Files.write(tempFile, fileContent);
        String tempFilePath = tempFile.toString(); // Get actual file path

        when(s3FileUploadService.saveFileToTempLocation(multipartFile)).thenReturn(tempFilePath);
        // Mock JSON Validation
        ArrayNode mockJsonArray = new ObjectMapper().createArrayNode();
        ObjectNode record = new ObjectMapper().createObjectNode();
        record.put("participant_id", "P001");
        record.put("gender", "Male");
        record.put("age", "25");
        record.put("ethnicity", "Hispanic");
        record.put("race", "Asian");
        mockJsonArray.add(record);

        when(s3FileUploadService.validateParticipantFileJson(any())).thenReturn("All records are valid.");

        JSONB mockEthnicityData = JSONB
                .valueOf("[{\"display\": \"Hispanic\"}, {\"display\": \"Non-Hispanic\"}, {\"display\": \"Unknown\"}]");
        JSONB mockRaceData = JSONB.valueOf(
                "[{\"display\": \"Asian\"}, {\"display\": \"Black\"}, {\"display\": \"White\"}, {\"display\": \"Unknown\"}]");
        when(masterService.readEthnicityType()).thenReturn(mockEthnicityData);
        when(masterService.readRaceType()).thenReturn(mockRaceData);
        JSONB mockResponse = JSONB.valueOf("{\"status\":\"success\"}");
        when(participantService.saveParticipantsDataFromFile(request, fakeS3Url, mockJsonArray, null))
                .thenReturn(mockResponse);
        when(interactionService.getHubIntercationIdOfStudy("study-456")).thenReturn("hub-123");
        when(interactionService.saveFileInteraction(any(), any(), any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(), any(), anyInt(), any(), any(), any()))
                .thenReturn(JSONB.valueOf("{}"));
        when(interactionService.getInteractionHierarchy(any(), any())).thenReturn(new ArrayList<>());
        JSONB result = researchStudyParticipantFileService.uploadAndSaveParticipantFile(multipartFile, request);

        assertNotNull(result);
        assertEquals(mockResponse, result);
        verify(s3FileUploadService).deleteTempFile(anyString()); // Ensure temp file is deleted
    }

    @Test
    void testUploadAndSaveParticipantFile_Failure_S3Upload() throws Exception {

        String csvContent = "participant_id,gender,age,race,ethnicity\nP001,Male,25,Asian,Hispanic";
        byte[] fileContent = csvContent.getBytes(StandardCharsets.UTF_8);
        MultipartFile multipartFile = mock(MultipartFile.class);
        when(multipartFile.getOriginalFilename()).thenReturn("participants.csv");
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getInputStream()).thenAnswer(invocation -> new ByteArrayInputStream(fileContent));

        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        when(request.orgPartyId()).thenReturn("org-123");
        when(request.studyId()).thenReturn("study-456");

        when(s3FileUploadService.uploadParticipantFileToS3Bucket(multipartFile, "org-123", "study-456"))
                .thenReturn(null);

        UnsupportedOperationException exception = assertThrows(
                UnsupportedOperationException.class,
                () -> researchStudyParticipantFileService.uploadAndSaveParticipantFile(multipartFile, request));

        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Error uploading file to S3", exception.getCause().getMessage());
    }

    @Test
    void testUploadAndSaveParticipantFile_validate_Header() throws Exception {

        String csvContent = "participant_id,gender,race,ethnicity\nP001,Male,Asian,Hispanic";
        byte[] fileContent = csvContent.getBytes(StandardCharsets.UTF_8);
        MultipartFile multipartFile = mock(MultipartFile.class);
        when(multipartFile.getOriginalFilename()).thenReturn("participants.csv");
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getInputStream()).thenAnswer(invocation -> new ByteArrayInputStream(fileContent));

        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        when(request.orgPartyId()).thenReturn("org-123");
        when(request.studyId()).thenReturn("study-456");
        String fakeS3Url = "https://s3-bucket.com/participants.csv";

        when(s3FileUploadService.uploadParticipantFileToS3Bucket(multipartFile, "org-123", "study-456"))
                .thenReturn(fakeS3Url);

        UnsupportedOperationException exception = assertThrows(
                UnsupportedOperationException.class,
                () -> researchStudyParticipantFileService.uploadAndSaveParticipantFile(multipartFile, request));

        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Participant ID , Age and Gender are required fields.", exception.getCause().getMessage());
    }

    @Test
    void testUploadAndSaveParticipantFile_Empty_Json_Array() throws Exception {

        String csvContent = "participant_id,gender,age,race,ethnicity\nP001,Male,25,Asian,Hispanic";
        byte[] fileContent = csvContent.getBytes(StandardCharsets.UTF_8);
        MultipartFile multipartFile = mock(MultipartFile.class);
        when(multipartFile.getOriginalFilename()).thenReturn("participants.csv");
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getInputStream()).thenAnswer(invocation -> new ByteArrayInputStream(fileContent));

        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        when(request.orgPartyId()).thenReturn("org-123");
        when(request.studyId()).thenReturn("study-456");
        String fakeS3Url = "https://s3-bucket.com/participants.csv";

        when(s3FileUploadService.uploadParticipantFileToS3Bucket(multipartFile, "org-123", "study-456"))
                .thenReturn(fakeS3Url);

        String csvContent2 = "participant_id,gender,age,race,ethnicity\\n";
        byte[] fileContent2 = csvContent2.getBytes(StandardCharsets.UTF_8);
        Path tempFile = Files.createTempFile("participants", ".csv");
        Files.write(tempFile, fileContent2);
        String tempFilePath = tempFile.toString(); // Get actual file path

        when(s3FileUploadService.saveFileToTempLocation(multipartFile)).thenReturn(tempFilePath);
        // Mock JSON Validation
        ArrayNode mockJsonArray = new ObjectMapper().createArrayNode();
        ObjectNode record = new ObjectMapper().createObjectNode();
        mockJsonArray.add(record);
        UnsupportedOperationException exception = assertThrows(
                UnsupportedOperationException.class,
                () -> researchStudyParticipantFileService.uploadAndSaveParticipantFile(multipartFile, request));

        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("No records found in the file.", exception.getCause().getMessage());
    }

    @Test
    void testUploadAndSaveParticipantFile_jsonValidateResponse_error() throws Exception {

        String csvContent = "participant_id,gender,age,race,ethnicity\nP001,Male,25,Asian,Hispanic";
        byte[] fileContent = csvContent.getBytes(StandardCharsets.UTF_8);

        MultipartFile multipartFile = mock(MultipartFile.class);
        when(multipartFile.getOriginalFilename()).thenReturn("participants.csv");
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getInputStream()).thenAnswer(invocation -> new ByteArrayInputStream(fileContent));
        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        when(request.orgPartyId()).thenReturn("org-123");
        when(request.studyId()).thenReturn("study-456");

        // Mock S3 Upload
        String fakeS3Url = "https://s3-bucket.com/participants.csv";
        when(s3FileUploadService.uploadParticipantFileToS3Bucket(multipartFile, "org-123", "study-456"))
                .thenReturn(fakeS3Url);

        // Mock File Type Validation
        Path tempFile = Files.createTempFile("participants", ".csv");
        Files.write(tempFile, fileContent);
        String tempFilePath = tempFile.toString(); // Get actual file path

        when(s3FileUploadService.saveFileToTempLocation(multipartFile)).thenReturn(tempFilePath);
        // Mock JSON Validation
        ArrayNode mockJsonArray = new ObjectMapper().createArrayNode();
        ObjectNode record = new ObjectMapper().createObjectNode();
        record.put("participant_id", "P001");
        record.put("gender", "Male");
        record.put("age", "25");
        record.put("ethnicity", "Hispanic");
        record.put("race", "Asian");
        mockJsonArray.add(record);

        when(s3FileUploadService.validateParticipantFileJson(any())).thenReturn("");
        UnsupportedOperationException exception = assertThrows(
                UnsupportedOperationException.class,
                () -> researchStudyParticipantFileService.uploadAndSaveParticipantFile(multipartFile, request));

        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Invalid records found in the file.", exception.getCause().getMessage());
    }

    @Test
    void testUploadAndSaveParticipantFile_ValidAndUnknownRace() throws Exception {
        String csvContent = "participant_id,gender,age,race,ethnicity\nP001,Male,25,Asian,Hispanic\nP002,Female,30,Martian,dummy-Hispanic";
        byte[] fileContent = csvContent.getBytes(StandardCharsets.UTF_8);

        MultipartFile multipartFile = mock(MultipartFile.class);
        when(multipartFile.getOriginalFilename()).thenReturn("participants.csv");
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getInputStream()).thenAnswer(invocation -> new ByteArrayInputStream(fileContent));

        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        when(request.orgPartyId()).thenReturn("org-123");
        when(request.studyId()).thenReturn("study-456");

        String fakeS3Url = "https://s3-bucket.com/participants.csv";
        when(s3FileUploadService.uploadParticipantFileToS3Bucket(multipartFile,
                "org-123", "study-456"))
                .thenReturn(fakeS3Url);

        Path tempFile = Files.createTempFile("participants", ".csv");
        Files.write(tempFile, fileContent);
        String tempFilePath = tempFile.toString();

        when(s3FileUploadService.saveFileToTempLocation(multipartFile)).thenReturn(tempFilePath);

        ArrayNode mockJsonArray = new ObjectMapper().createArrayNode();

        ObjectNode record = new ObjectMapper().createObjectNode();
        record.put("participant_id", "P002");
        record.put("gender", "Female");
        record.put("age", "30");
        record.put("ethnicity", "dummy-Hispanic");
        record.put("race", "Martian");
        mockJsonArray.add(record);

        when(s3FileUploadService.validateParticipantFileJson(any())).thenReturn("All records are valid.");

        JSONB mockEthnicityData = JSONB
                .valueOf("[{\"display\": \"Hispanic\"}, {\"display\": \"Non-Hispanic\"}, {\"display\": \"Unknown\"}]");
        JSONB mockRaceData = JSONB.valueOf(
                "[{\"display\": \"Asian\"}, {\"display\": \"Black\"}, {\"display\": \"White\"}, {\"display\": \"Unknown\"}]");

        when(masterService.readEthnicityType()).thenReturn(mockEthnicityData);
        when(masterService.readRaceType()).thenReturn(mockRaceData);

        UnsupportedOperationException exception = assertThrows(
                UnsupportedOperationException.class,
                () -> researchStudyParticipantFileService.uploadAndSaveParticipantFile(multipartFile, request));

        assertTrue(exception.getCause() instanceof IllegalArgumentException);
        assertEquals("Participant data upload failed.", exception.getCause().getMessage());

    }

    @Test
    void testUploadAndSaveParticipantFile_ValidAndUnknownRace_exception() throws Exception {
        String csvContent = "participant_id,gender,age,race,ethnicity\nP001,Male,25,Asian,Hispanic\nP002,Female,30,Martian,dummy-Hispanic";
        byte[] fileContent = csvContent.getBytes(StandardCharsets.UTF_8);

        MultipartFile multipartFile = mock(MultipartFile.class);
        when(multipartFile.getOriginalFilename()).thenReturn("participants.csv");
        when(multipartFile.isEmpty()).thenReturn(false);
        when(multipartFile.getInputStream()).thenAnswer(invocation -> new ByteArrayInputStream(fileContent));

        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        when(request.orgPartyId()).thenReturn("org-123");
        when(request.studyId()).thenReturn("study-456");

        String fakeS3Url = "https://s3-bucket.com/participants.csv";
        when(s3FileUploadService.uploadParticipantFileToS3Bucket(multipartFile,
                "org-123", "study-456"))
                .thenReturn(fakeS3Url);

        Path tempFile = Files.createTempFile("participants", ".csv");
        Files.write(tempFile, fileContent);
        String tempFilePath = tempFile.toString();

        when(s3FileUploadService.saveFileToTempLocation(multipartFile)).thenReturn(tempFilePath);

        when(s3FileUploadService.validateParticipantFileJson(any())).thenReturn("All records are valid.");

        JSONB mockEthnicityData = JSONB
                .valueOf("[{\"pisplay\": \"Hispanic\"}, {\"display\": \"Non-Hispanic\"}, {\"display\": \"Unknown\"}]");
        when(masterService.readEthnicityType()).thenReturn(mockEthnicityData);
        UnsupportedOperationException exception = assertThrows(
                UnsupportedOperationException.class,
                () -> researchStudyParticipantFileService.uploadAndSaveParticipantFile(multipartFile, request));

        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Failed to parse ethnicity data from JSONB", exception.getCause().getMessage());

    }
}
