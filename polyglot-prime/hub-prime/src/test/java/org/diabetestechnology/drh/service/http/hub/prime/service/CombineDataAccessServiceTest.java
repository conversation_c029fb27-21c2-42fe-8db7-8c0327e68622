package org.diabetestechnology.drh.service.http.hub.prime.service;

import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.jdbc.JdbcResponse;
import org.diabetestechnology.drh.service.http.hub.prime.service.request.CohortRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

@Disabled
public class CombineDataAccessServiceTest {

    @InjectMocks
    private CombineDataAccessService combineDataAccessService;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private DataAccessService dataAccessService;

    @Mock
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetAllStudyTotalDataPoints() throws SQLException {
        // Arrange
        Map<String, Object> mockResult = Map.of("total_data_points", 100);
        when(jdbcTemplate.queryForMap("SELECT total_data_points FROM agg_data_points_cached;")).thenReturn(mockResult);

        // Act
        Map<String, Object> result = combineDataAccessService.getAllStudyTotalDataPoints();

        // Assert
        verify(jdbcTemplate).queryForMap("SELECT total_data_points FROM agg_data_points_cached;");
        assert result.equals(mockResult);
    }

    @Test
    public void testGetAllStudyTotalCgmWear() throws SQLException {
        // Arrange
        Map<String, Object> mockResult = Map.of("total_cgm_wear", 200);
        when(jdbcTemplate.queryForMap("SELECT total_cgm_wear FROM agg_cgm_wear_cached;")).thenReturn(mockResult);

        // Act
        Map<String, Object> result = combineDataAccessService.getAllStudyTotalCgmWear();

        // Assert
        verify(jdbcTemplate).queryForMap("SELECT total_cgm_wear FROM agg_cgm_wear_cached;");
        assert result.equals(mockResult);
    }

    @Test
    public void testGetAllStudyVanityMetrics() throws SQLException {
        // Arrange
        Map<String, Object> mockResult = Map.of(
                "total_number_of_participants", 50,
                "percent_female", 30,
                "average_age", 45);
        when(jdbcTemplate.queryForMap(
                "SELECT total_number_of_participants, percent_female, average_age FROM all_participants_cached"))
                .thenReturn(mockResult);

        // Act
        Map<String, Object> result = combineDataAccessService.getAllStudyVanityMetrics();

        // Assert
        verify(jdbcTemplate).queryForMap(
                "SELECT total_number_of_participants, percent_female, average_age FROM all_participants_cached");
        assert result.equals(mockResult);
    }

    @Test
    public void testGetAllStudyTotalCgmFiles() throws SQLException {
        // Arrange
        Map<String, Object> mockResult = Map.of("total_cgm_file_count", 150);
        when(jdbcTemplate.queryForMap("SELECT total_cgm_file_count FROM total_cgm_file_count_cached;"))
                .thenReturn(mockResult);

        // Act
        Map<String, Object> result = combineDataAccessService.getAllStudyTotalCgmFiles();

        // Assert
        verify(jdbcTemplate).queryForMap("SELECT total_cgm_file_count FROM total_cgm_file_count_cached;");
        assert result.equals(mockResult);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testGetCohortReport() {
        List<Object> mockList = new ArrayList<>();
        // Define mock data
        Map<String, Object> mockData = Collections.singletonMap("key", mockList);

        JdbcResponse mockJdbcResponse = JdbcResponse.builder()
                .data(mockData)
                .status("success")
                .message("cohortReport")
                .errors(null)
                .build();

        // Mocking the JdbcTemplate queryForList method
        when(namedParameterJdbcTemplate.queryForList(any(String.class), any(Map.class)))
                .thenReturn(mockList);

        // Mocking the responseBuilder method
        when(dataAccessService.responseBuilder(any(Map.class), anyString(), anyString(), any()))
                .thenReturn(mockJdbcResponse);

        // Create a sample request
        CohortRequest request = new CohortRequest(Collections.singletonList("studyId1"),
                Collections.singletonList("filter1 = 'value'"));
        // Call the method under test
        Object response = combineDataAccessService.getCohortReport(request);

        // Verify the result
        assertEquals(mockJdbcResponse, response);
    }

    @Test
    public void testGetAllStudyAvgGlucose() throws SQLException {
        // Arrange
        Map<String, Object> mockResult = Map.of("avg_glucose", "200mg/dL");
        when(jdbcTemplate.queryForMap(
                "SELECT CONCAT(ROUND(avg_glucose, 2), 'mg/dL') AS avg_glucose FROM agg_avg_glucose_cached;"))
                .thenReturn(mockResult);

        // Act
        Map<String, Object> result = combineDataAccessService.getAllStudyAvgGlucose();

        // Assert
        verify(jdbcTemplate).queryForMap(
                "SELECT CONCAT(ROUND(avg_glucose, 2), 'mg/dL') AS avg_glucose FROM agg_avg_glucose_cached;");
        assert result.equals(mockResult);
    }
}
