package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantDataRequest;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantFileDataRequest;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectLimitPercentStep;
import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.jooq.Field;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

public class ParticipantServiceTest {
    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private AuditService auditService;
    @Mock
    private InteractionService interactionService;
    @Mock
    private MasterService masterService;

    @InjectMocks
    private ParticipantService participantService;

    private SelectSelectStep<Record1<JSONB>> selectMock;
    private SelectJoinStep<Record1<JSONB>> joinStepMock;
    private SelectConditionStep<Record1<JSONB>> conditionStepMock;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this); // Initialize mocks

        // Initialize common mocks
        selectMock = mock(SelectSelectStep.class);
        joinStepMock = mock(SelectJoinStep.class);
        conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveParticipantData_Success() throws Exception {
        ParticipantDataRequest request = mock(ParticipantDataRequest.class);
        JSONB mockJsonb = JSONB.valueOf("{\"status\":\"success\"}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);
        when(userNameService.getUserId()).thenReturn("user-123");
        when(partyService.getPartyIdByUserId("user-123")).thenReturn("party-456");
        when(request.diagnosisIcd()).thenReturn("icd-10-code");
        when(request.medRxnorm()).thenReturn("rxnorm-med");
        when(request.treatmentModality()).thenReturn("modality-type");
        when(request.raceId()).thenReturn("race-001");
        when(request.ethnicityId()).thenReturn("ethnicity-002");
        when(request.diabetesType()).thenReturn("Type-1");
        when(request.studyArm()).thenReturn("Control");
        when(masterService.getRaceIdByName("Unknown")).thenReturn("race-unknown");
        when(masterService.getEthnicityIdByName("Unknown")).thenReturn("ethnicity-unknown");

        Object result = participantService.saveParticipantData(request);

        assertNotNull(result);
        assertEquals(mockJsonb, result);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveParticipantData_Failure() throws Exception {
        ParticipantDataRequest request = mock(ParticipantDataRequest.class);
        JSONB mockJsonb = JSONB.valueOf("{\"status\":\"failure\", \"message\":\"Error saving data\"}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);
        when(userNameService.getUserId()).thenReturn("user-123");
        when(partyService.getPartyIdByUserId("user-123")).thenReturn("party-456");

        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            participantService.saveParticipantData(request);
        });

        assertTrue(exception.getMessage().contains("Failed to save participant data"));
        assertTrue(exception.getMessage().contains("Error saving data"));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUpdateParticipantDataInline_Success() {
        String participantId = "participant-123";
        JSONB jsonInput = JSONB.valueOf("{\"race_type_id\":\"\", \"ethnicity_type_id\":\"\"}");
        JSONB mockJsonb = JSONB.valueOf("{\"status\":\"success\"}");

        when(userNameService.getUserId()).thenReturn("user123");
        when(partyService.getPartyIdByUserId("user123")).thenReturn("party456");
        when(interactionService.getHubIntercationIdOfStudyParticipant("study789")).thenReturn("hub123");

        when(masterService.getRaceIdByName("Unknown")).thenReturn("race123");
        when(masterService.getEthnicityIdByName("Unknown")).thenReturn("ethnicity123");

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<String>> limitMock = mock(SelectLimitPercentStep.class);
        SelectSelectStep<Record1<JSONB>> finalSelectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(finalSelectMock);
        when(finalSelectMock.fetchOneInto(JSONB.class)).thenReturn(JSONB.valueOf("{\"status\":\"success\"}"));

        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.limit(1)).thenReturn(limitMock);
        when(limitMock.fetchOneInto(String.class)).thenReturn("study789");

        Object result = participantService.updateParticipantDataInline(participantId, jsonInput);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
        verify(masterService, times(1)).getRaceIdByName("Unknown");
        verify(masterService, times(1)).getEthnicityIdByName("Unknown");
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetParticipantDetails_Success() {
        String participantId = "participant-123";
        JSONB mockJsonb = JSONB.valueOf("{\"participant_id\":\"participant-123\", \"age\":30}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Map<String, Object> result = participantService.getParticipantDetails(participantId);

        assertNotNull(result);
        assertTrue(result.containsKey("participant_id")); // Ensure key exists
        assertEquals("participant-123", result.get("participant_id")); // Ensure correct value
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetParticipantDetails_NoData() {
        String participantId = "participant-123";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Map<String, Object> result = participantService.getParticipantDetails(participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetParticipantDetails_ExceptionHandling() {
        String participantId = "participant-123";
        JSONB invalidJsonb = JSONB.valueOf("invalid json data");
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(invalidJsonb);
        Map<String, Object> result = participantService.getParticipantDetails(participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveParticipantsDataFromFile_Success() {
        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        String fileUrl = "http://example.com/sample.csv";
        ArrayNode fileContent = new ObjectMapper().createArrayNode();
        fileContent.add(new ObjectMapper().createObjectNode().put("participant_id", "P001"));
        JSONB mockJsonb = JSONB.valueOf("{\"status\":\"success\"}");
        String lastInteractionId = "123";

        when(userNameService.getUserId()).thenReturn("user-123");
        when(partyService.getPartyIdByUserId("user-123")).thenReturn("party-456");
        when(request.studyId()).thenReturn("study-001");
        when(request.orgPartyId()).thenReturn("org-789");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        JSONB result = participantService.saveParticipantsDataFromFile(request, fileUrl, fileContent,
                lastInteractionId);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveParticipantsDataFromFile_Failure() {
        ParticipantFileDataRequest request = mock(ParticipantFileDataRequest.class);
        String fileUrl = "http://example.com/file.csv";
        ArrayNode fileContent = mock(ArrayNode.class);
        String lastInteractionId = "123";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);
        when(userNameService.getUserId()).thenReturn("user-123");
        when(partyService.getPartyIdByUserId("user-123")).thenReturn("party-456");

        JSONB result = participantService.saveParticipantsDataFromFile(request, fileUrl, fileContent,
                lastInteractionId);

        assertNull(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdateParticipantData_Success() throws JsonProcessingException {
        String participantId = "participant-123";
        ParticipantDataRequest request = mock(ParticipantDataRequest.class);
        JSONB mockJsonb = JSONB.valueOf("{\"status\":\"success\"}");

        when(request.diagnosisIcd()).thenReturn("E11");
        when(request.medRxnorm()).thenReturn("12345");
        when(request.treatmentModality()).thenReturn("Insulin");
        when(request.genderId()).thenReturn("M");
        when(request.age()).thenReturn(45);
        when(request.bmi()).thenReturn((double) 25.3);
        when(request.baselineHba1c()).thenReturn((double) 7.2);
        when(request.diabetesType()).thenReturn("Type 2");
        when(request.studyArm()).thenReturn("Control");
        when(request.raceId()).thenReturn("1");
        when(request.ethnicityId()).thenReturn("2");

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<String>> limitMock = mock(SelectLimitPercentStep.class);

        when(dsl.selectDistinct(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.limit(1)).thenReturn(limitMock);
        when(limitMock.fetchOneInto(String.class)).thenReturn("study789");

        SelectSelectStep<Record1<JSONB>> jsonbSelectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(jsonbSelectMock);
        when(jsonbSelectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = participantService.updateParticipantData(participantId, request);

        assertNotNull(result);
        assertEquals(mockJsonb, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdateParticipantData_Failure() {
        String participantId = "participant-123";
        ParticipantDataRequest request = mock(ParticipantDataRequest.class);

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            participantService.updateParticipantData(participantId, request);
        });

        // assertTrue(exception.getMessage().contains("Failed to save participant
        // data"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_CheckIfParticipantDisplayIdExists() {
        String studyId = "study-123";
        String participantDisplayId = "participant-456";
        SelectSelectStep<Record1<Integer>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Integer>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Integer>> whereStepMock = mock(SelectConditionStep.class);
        when(dsl.selectCount()).thenReturn(selectMock);
        when(selectMock.from("drh_stateful_research_study.research_subject")).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetchOne(0, int.class)).thenReturn(1);

        boolean result = participantService.checkIfParticipantDisplayIdExists(studyId, participantDisplayId);
        assertTrue(result, "Expected participant ID to exist");
    }

}
