package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record2;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.TableLike;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class StudyDashboardServiceTest {
    @Mock
    private DSLContext dsl;

    @Mock
    private AuditService auditService;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private Result<Record1<Integer>> mockResult;
    @Mock
    private SelectConditionStep<Record2<Double, Integer>> avgAgeWhereStepMock;

    @InjectMocks
    private StudyDashboardService service;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(userNameService.getUserId()).thenReturn("user-123");
        when(partyService.getPartyIdByUserId("user-123")).thenReturn("party-456");
        when(partyService.getOrganizationPartyIdByUser("user-123")).thenReturn("mockOrgId");

        // Mock jOOQ query chain
        SelectSelectStep<Record1<Integer>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Integer>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Integer>> whereStepMock = mock(SelectConditionStep.class);

        // Mock the query execution flow
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(any(TableLike.class))).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetchOneInto(Integer.class)).thenReturn(10);
        when(whereStepMock.fetchOneInto(Double.class)).thenReturn(45.67);

        SelectSelectStep<Record2<Double, Integer>> avgAgeSelectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record2<Double, Integer>> avgAgeFromStepMock = mock(SelectJoinStep.class);
        avgAgeWhereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class), any(Field.class))).thenReturn(avgAgeSelectMock);
        when(avgAgeSelectMock.from(any(TableLike.class))).thenReturn(avgAgeFromStepMock);
        when(avgAgeFromStepMock.where(any(Condition.class))).thenReturn(avgAgeWhereStepMock);

    }

    @Test
    void testGetTotalParticipants() {
        Integer result = service.getTotalParticipants();
        assertNotNull(result);
        assertEquals(10, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetTotalParticipants_ExceptionHandling() {
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));
        Integer result = service.getTotalParticipants();
        assertNotNull(result);
        assertEquals(0, result);
    }

    @Test
    void testGetFemalePercentage() {
        String result = service.getFemalePercentage();
        assertNotNull(result);
        assertEquals("45.67%", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetFemalePercentage_ExceptionHandling() {
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));
        String result = service.getFemalePercentage();
        assertNotNull(result);
        assertEquals("0.00%", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAverageAgeForDashboard_NormalCase() {

        Record2<Double, Integer> mockRecord = mock(Record2.class);
        when(mockRecord.get(0, Double.class)).thenReturn(250.0);
        when(mockRecord.get(1, Integer.class)).thenReturn(5);
        when(avgAgeWhereStepMock.fetchOne()).thenReturn(mockRecord);

        String result = service.getAverageAgeForDashboard();
        assertNotNull(result);
        assertEquals("50 Years", result);
    }

    @Test
    void testGetAverageAgeForDashboard_NoData() {

        when(avgAgeWhereStepMock.fetchOne()).thenReturn(null);
        String result = service.getAverageAgeForDashboard();
        assertNotNull(result);
        assertEquals("0 Years", result);
    }

    @Test
    void testGetAverageAgeForDashboard_ExceptionHandling() {

        when(avgAgeWhereStepMock.fetchOne()).thenThrow(new RuntimeException("Database error"));
        String result = service.getAverageAgeForDashboard();
        assertNotNull(result);
        assertEquals("0.0", result);
    }

}
