# api test of console/project 
# without Authentication
GET http://localhost:8080/console/project
Accept: text/html
?? status == 200
?? response.body  != null

# without Authentication
GET http://localhost:8080/console/project
Accept: text/html
# add the authenticated active session Id(mvn site)
Cookie: JSESSIONID= 97088F6511D2F55B97C70BB399F5AE40 
?? status == 200
?? response.body  != null



# api test of console/Health information 
# without Authentication
GET http://localhost:8080/console/health-info
Accept: text/html
?? status == 200
?? response.body  != null

# without Authentication
GET http://localhost:8080/console/health-info
Accept: text/html
# add the authenticated active session Id(mvn site)
Cookie: JSESSIONID= 97088F6511D2F55B97C70BB399F5AE40 
?? status == 200
?? response.body  != null




# api test of console/Schema  
# only work when the schemaSpy is generated
GET http://localhost:8080/console/schema
Accept: text/html
?? status == 200
?? response.body  != null

