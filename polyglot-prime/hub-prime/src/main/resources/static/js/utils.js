const reloadSvg = `<img src="/images/reload.svg" alt="Reload Icon">`;
const nodataSvg = `<div class="@apply inline-flex flex-wrap flex-col">
    <img src="/images/nodata.svg" alt="No data Icon">
    <span class="@apply font-normal text-[10px] text-[#444444]">No Data</span>
</div> `;
const tirSkeleton = `<img src="/images/tir_skeleton.svg" alt="No data Icon">`;


async function fetchRawData(url, callback) {
    try {
        const response = await fetch(url, {
            method: "GET",
        });
        if (!response.ok) {
            throw new Error(
                "Network response was not ok " + response.statusText
            );
        }
        const data = await response.json();
        if (data.errors && data.errors !== "") {
            callback([], data.errors);
        } else {
            callback(data);
        }
    } catch (error) {
        callback([], error);
        console.error("Error fetching raw data:", error);
    }
}

async function fetchData(url, callback, retryCount = 3) {
    try {
        const response = await fetch(url, {
            method: "GET",
        });

        if (!response.ok) {
            throw new Error(
                `Network response was not ok: ${response.status} ${response.statusText}`
            );
        }

        const data = await response.json();
        if (data.status && data.status == "success") {
            callback(data); // Successful response
        }
        else if (data.status && data.status == "failed") {
            callback([], data.message); // API returned an error
        } else {
            callback([], data.errors); // API returned an error
        }
    } catch (error) {
        console.log(`Error fetching data from ${url}:`, error);

        // Retry logic
        if (retryCount > 0 && error.message.includes("500")) {
            console.log(`Retrying... attempts left: ${retryCount - 1}`);
            setTimeout(() => {
                fetchData(url, callback, retryCount - 1); // Retry after 2 seconds
            }, 2000);
        } else {
            callback([], error); // No more retries or different error
        }
    }
}
function assignRetry(metric, value, clickHandler) {
    let eleClass = metric.class;

    document.querySelectorAll("." + eleClass).forEach((element) => {
        element.innerHTML = value;


        // Get the element by its ID (generated dynamically)
        const spanElement = document.getElementById(eleClass);

        // Add the click event handler if the element is found
        if (spanElement) {
            spanElement.addEventListener("click",
                () => {
                    console.log("Span with ID " + eleClass + " clicked!");
                    // Additional logic for the click event
                    clickHandler([metric]);
                    spanElement.classList.add("hidden")
                });
        }
    });
}

async function postData(url, requestBody, callback) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        const data = await response.json();  // Parse response as JSON
        callback(data);
    } catch (error) {
        callback([], error);
        console.error('Error:', error);
    }
}

async function updateData(url, requestBody, callback) {
    try {
        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        const data = await response.json();  // Parse response as JSON
        callback(data);
    } catch (error) {
        callback([], error);
        console.error('Error:', error);
    }
}
function validateField(fieldId) {
    let errorClass = "border-[#e23636]";
    const field = document.getElementById(fieldId);
    if (!field || field.value.trim() === "") {
        field.classList.add(errorClass);
        return false;
    } else {
        field.classList.remove(errorClass);
        return true;
    }
}

function resetForm(formFields) {
    formFields.map((fieldId) => {
        document.getElementById(fieldId).value = "";
    }, {});
}

function isValidOrcid(orcid) {
    // Remove any leading/trailing whitespace
    orcid = orcid.trim();

    // ORCID format regex
    const orcidRegex = /^(\d{4}-\d{4}-\d{4}-\d{3}[0-9X])$/;

    // Check format
    if (!orcidRegex.test(orcid)) {
        return false;
    }

    // Remove hyphens for checksum validation
    const orcidDigits = orcid.replace(/-/g, '');

    // Calculate checksum
    let total = 0;
    for (let i = 0; i < orcidDigits.length - 1; i++) {
        total = (total + parseInt(orcidDigits[i], 10)) * 2;
    }
    const remainder = total % 11;
    const result = (12 - remainder) % 11;
    const checksum = result === 10 ? 'X' : result.toString();

    // Validate checksum
    return checksum === orcidDigits[15];
}
function showToast(message, type = 'info', duration = 3000) {
    // Create a toast element
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.style = `
    margin-bottom: 10px;
    padding: 10px 20px;
    border-radius: 5px;
    color: #fff;
    font-family: Arial, sans-serif;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: opacity 0.5s ease;
    opacity: 1;
    background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
  `;
    toast.innerHTML = message;

    // Append the toast to the container
    const container = document.getElementById('toasterContainer');
    container.appendChild(toast);

    // Remove the toast after the specified duration
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            container.removeChild(toast);
        }, 1000); // Wait for the fade-out effect
    }, duration);
}

function getStudyVisibility() {
    return new Promise((resolve, reject) => {
        fetchRawData(`/study-visibility`, (res) => {
            try {
                // const data = JSON.parse(res.data.studyVisibility);
                const data = res.data.studyVisibility;
                console.log("Study Visibility:", data);

                // Define the custom sort order: visibility_id 2 should come first, then 3, then 1.
                const sortOrder = {
                    2: 0,  // Private
                    3: 1,  // Internal
                    1: 2   // Public
                };

                // Sort the data array using the custom order mapping
                data.sort((a, b) => sortOrder[a.visibility_id] - sortOrder[b.visibility_id]);

                console.log(data);
                resolve(data); // Resolve the promise with the fetched data
            } catch (error) {
                reject(error); // Reject the promise in case of an error
            }
        });
    });
}


