import {
  AGGridA<PERSON>,
  AGGridAideBuilder,
} from "@presentation/shell/aggrid-aide.js";
import ModalAide from "@presentation/shell/modal-aide.js";

// let savedCohortFilters = localStorage.getItem("savedCohortFilters");
// let savedCohortFilterArray = savedCohortFilters ? JSON.parse(savedCohortFilters) : []; //total saved filters

let savedCohortFilterArray = []; //total saved filters
let selectedCohortFilterInfo = {};

//window.selectedCohortFilterInfo = selectedCohortFilterInfo;
//let filterInfo = window.filterInfo = Object.keys(selectedCohortFilterInfo).length > 0 ? selectedCohortFilterInfo.filteroptions  : {};

let filterInfo = {};

let loggedInUser = "anonymousUser";
let clearFilter = false;

const filterCategories = [
  {
    "value": "age",
    "text": "Age"
  },
  {
    "value": "tir",
    "text": "Time in Range (TIR)"
  },
  {
    "value": "tbr",
    "text": "Time Below Range (TBR)"
  },
  {
    "value": "tar",
    "text": "Time Above Range (TAR)"
  },
  {
    "value": "wear_time_percentage",
    "text": "% Wear Time"
  },
  {
    "value": "gri",
    "text": "Glycemic Risk Index (GRI)"
  }
];

export const appliedFilters = document.getElementById("applied-filters");
export const filterCategory = document.getElementById("filter-category");

export const optionsData = [
  { value: "equals", text: "Equals (=)" },
  { value: "greaterThan", text: "Greater than (>)" },
  { value: "greatersOrEqual", text: "Greater than or equal to (>=)" },
  { value: "lessThan", text: "Less than (<)" },
  { value: "lessOrEqual", text: "Less than or equal to (<=)" },
  { value: "between", text: "Between (Range)" }
];


let agGridFilterParams = {};
let defaultStudiesList = ["DCLP1", "DCLP3", "DSS1", "NTLT", "CTR3", "DFA", "IEOGC", "RTCCGM", "WAD1"];
let selectedStudies = defaultStudiesList;

let selectedAdvancedFilters = [];
let lastResponseLength = 100;
let hasMoreData = true;


let filterParamDefault = { "studyIds": selectedStudies, "filters": selectedAdvancedFilters };

const cohortMetrics = [
  {
    "eleId": "totalParticipants",
    "url": "/svm/cohort/totalParticipant.html"
  },
  {
    "eleId": "totalCgmFiles",
    "url": "/svm/cohort/cgm-count.html"
  },
  {
    "eleId": "totalFemalePercent",
    "url": "/svm/cohort/femalePercentage.html"
  },
  {
    "eleId": "totalAverageAge",
    "url": "/svm/cohort/average-age.html"
  }
];


const dynamicFilters = document.getElementById("dynamic-filters");

const removeButtonHtml = `<div class="box-border w-[41px] h-9 border rounded-[3px] border-solid border-[#E3C3C5] bg-[#E8D0D1] justify-center flex pt-[8px]">
                              <svg width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.6393 2.35223H10.1469V1.65374C10.1469 0.88231 9.52144 0.256775 8.74997 0.256775H5.25758C4.48611 0.256775 3.86061 0.88231 3.86061 1.65374V2.35223H0.368225V3.05071H1.08787L1.78703 16.3218C1.78703 17.0933 2.41253 17.7188 3.18399 17.7188H10.8673C11.6387 17.7188 12.2642 17.0933 12.2642 16.3218L12.9511 3.05071H13.6394L13.6393 2.35223ZM4.5591 1.65374C4.5591 1.26836 4.8729 0.955259 5.25758 0.955259H8.74997C9.13535 0.955259 9.44845 1.26836 9.44845 1.65374V2.35223H4.5591V1.65374ZM11.5671 16.2856L11.5657 16.3034V16.3218C11.5657 16.7065 11.2526 17.0203 10.8672 17.0203H3.18396C2.79928 17.0203 2.48548 16.7065 2.48548 16.3218V16.3034L2.48481 16.285L1.78703 3.05071H12.252L11.5671 16.2856Z" fill="#CE131B"/>
                                <path d="M7.353 4.44765H6.65451V15.6233H7.353V4.44765Z" fill="#CE131B"/>
                                <path d="M5.27326 15.6008L4.55843 4.44701L3.86132 4.49131L4.57618 15.6451L5.27326 15.6008Z" fill="#CE131B"/>
                                <path d="M10.1517 4.46948L9.4546 4.42585L8.74997 15.6015L9.44708 15.6451L10.1517 4.46948Z" fill="#CE131B"/>
                              </svg>
                            </div>`;
const applyButtonHtml = `<div class="box-border w-[41px] h-9 border rounded-[3px] border-solid border-[#BAD2AF] bg-[#d3e9c9] justify-center flex pt-3">
                            <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M16.3334 1.5L6.25002 11.5833L1.66669 7" stroke="#628652" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </div>`;
const editButtonHtml = `<div class="box-border w-[41px] h-[25px] ml-6 border rounded-[6px] border-solid border-[#A7BFDE] bg-[#c0d6f2] justify-center flex pt-[10px]">
                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M14.5744 2.70054L12.2997 0.42581C12.0251 0.151208 11.66 0 11.2717 0C10.8834 0 10.5183 0.151208 10.2438 0.425801L0.721676 9.94786C0.631295 10.0382 0.573034 10.1558 0.55586 10.2824L0.00549407 14.3371C-0.0190378 14.5179 0.0422918 14.6998 0.171336 14.8289C0.281641 14.9392 0.430565 15 0.584451 15C0.610557 15 0.636831 14.9983 0.663079 14.9947L4.71772 14.4443C4.84439 14.4271 4.96193 14.3689 5.05231 14.2785L14.5744 4.75647C14.849 4.4819 15.0002 4.11682 15.0002 3.72852C15.0002 3.34019 14.849 2.97512 14.5744 2.70054ZM4.36531 13.3128L1.26672 13.7334L1.68731 10.6348L8.43493 3.88726L11.1129 6.56527L4.36531 13.3128ZM13.7481 3.93013L11.9392 5.73894L9.26125 3.06094L11.0701 1.25214C11.1427 1.17946 11.2276 1.1686 11.2717 1.1686C11.3158 1.1686 11.4007 1.17946 11.4734 1.25214L13.7481 3.52687C13.8208 3.59954 13.8316 3.68439 13.8316 3.72849C13.8316 3.7726 13.8207 3.85745 13.7481 3.93013Z" fill="#073266"/>
                            </svg>
                          </div>`;

let initialLoad = true;
document.addEventListener("DOMContentLoaded", function () {
  loggedInUser = getLoggedInUser();
  initializeFilters();

  //loadCohortMetrics();
  //initGrid();
  initTomSelect();
  //loadFilterCategories(filterInfo);
  const modalAide = new ModalAide();

  document.getElementById("gender").addEventListener("change", function (event) {
    handleGenderSelection(event.target.value)
  });
  // Select a parent element that is present in the DOM during page load
  document.body.addEventListener('click', function (event) {
    if (event.target.classList.contains('applied-filter-div')) {
      let this_ele = event.target;
      let dataId = event.target.querySelector('.applied-filter-span').getAttribute('data-id');
      let catTextArr = filterCategories.find(opt => opt.value === dataId);
      let filterDiv = createFilterDiv(dataId, catTextArr.text);
      filterDiv.style.display = "flex";
      filterCategory.querySelector(
        `option[value="${dataId}"]`
      ).disabled = false;
      this_ele.remove();
    }
    if (event.target.classList.contains('applied-filter-span')) {
      // Handle the click event for the dynamically created element here
      let this_ele = event.target;
      let dataId = event.target.getAttribute('data-id');
      let catTextArr = filterCategories.find(opt => opt.value == dataId);
      let filterDiv = createFilterDiv(dataId, catTextArr.text, filterInfo.filters[dataId]);
      filterDiv.style.display = "flex";
      filterCategory.querySelector(
        `option[value="${dataId}"]`
      ).disabled = false;
      this_ele.parentNode.remove();
    }
    if (event.target.classList.contains('filter-item')) {
      filterInfo = {};
      // Handle the click event for the dynamically created element here
      let this_ele = event.target;
      let eleId = event.target.id;
      let filterItems = document.getElementsByClassName("filter-item");

      for (let i = 0; i < filterItems.length; i++) {
        filterItems[i].classList.remove("active-filter");
      }
      document.getElementById(eleId).classList.add("active-filter");

      // let savedCohortFilters = JSON.parse(JSON.stringify(localStorage.getItem("savedCohortFilters")));
      // let savedCohortFilterArray = savedCohortFilters ? JSON.parse(savedCohortFilters) : []; //total saved filters
      let selectedItemArray = savedCohortFilterArray.filter(item => `filter-item-` + item.filterId === eleId);
      let selectedItem = selectedItemArray.length > 0 ? selectedItemArray[0] : {};
      selectedCohortFilterInfo = selectedItem;
      if (Object.keys(selectedItem).length > 0) {
        populateModal();
      }
      filterInfo = JSON.parse(JSON.stringify(selectedItem.filterOption));
      const sqlFragments = Object.values(selectedItem.filterOption)
        .filter(
          (item) =>
            typeof item === "object" && item !== null && "sqlFragment" in item
        )
        .map((item) => item.sqlFragment);

      let cohortModifiedFilter = {
        studyIds: selectedItem.filterOption["studyIds"] || [],
        filters: sqlFragments,
      };
      if (cohortModifiedFilter.studyIds.length > 0 || cohortModifiedFilter.filters.length > 0) {
        agGridFilterParams = generateFilterModel();
        window.agGridFilterParams = agGridFilterParams;

        document.getElementById("serverDataGrid").innerHTML = '';
        loadCohortMetrics(cohortModifiedFilter);
        initGrid(window.agGridFilterParams);
        initTomSelect();
        let options = filterCategory.options;
        for (let i = 0; i < options.length; i++) {
          options[i].disabled = false;
        }
        loadFilterCategories(filterInfo);

        // localStorage.setItem('selectedFilters', JSON.stringify(window.filterInfo));
      }
    }
  });
  filterCategory.addEventListener("change", function (event) {
    const selectedCategory = filterCategory.value;
    const selectedTitle =
      filterCategory[filterCategory.selectedIndex].innerText;
    if (selectedCategory) {
      document.getElementById("filter_manadatory").classList.add("hidden");

      // Check if the category has already been added
      if (document.querySelector(`[data-category="${selectedCategory}"]`)) {
        alert(`The ${selectedCategory} filter has already been added.`);
        return;
      }

      let filterDiv = createFilterDiv(selectedCategory, selectedTitle);

      // Disable the selected option in the filter category dropdown
      filterCategory.querySelector(
        `option[value="${selectedCategory}"]`
      ).disabled = true;
      filterCategory.value = "";
    } else {
      document.getElementById("filter_manadatory").classList.remove("hidden");
    }
  });

  document.getElementById("clear-all").addEventListener("click", function () {
    filterInfo = {};
    clearFilter = true;
    // Clear all dynamic filters
    document.getElementById("dynamic-filters").innerHTML = "";
    document.getElementById("applied-filters").innerHTML = "";
    document.getElementById("gender").value = '';
    window.studiesToInclude.clear();
    document.getElementById("filter-category").querySelectorAll("option").forEach(option => {
      option.disabled = false; // or option.removeAttribute('disabled');
    });

    initFilter = false;
    agGridFilterParams = window.agGridFilterParams = {}
    clearCohortMetrics();
    clearGrid();
    document.getElementById("serverDataGrid").innerHTML = '<div class="text-center align-middle text-3xl text-[gray] mx-0 my-[18%]">No data available</div>';
    let filterItems = document.getElementsByClassName("filter-item");

    for (let i = 0; i < filterItems.length; i++) {
      filterItems[i].classList.remove("active-filter");
    }
    clearFilter = false;
    clearModal();
    //initGrid();
  });

  document.getElementById("filterName").addEventListener("change", function () {
    let filterName = document.getElementById("filterName").value;
    if (filterName.trim() == "") {
      document.getElementById("filterName").classList.add("input-mandatory");
    }
    else {
      document.getElementById("filterName").classList.remove("input-mandatory");
    }
  });

  document.getElementById("save-filter").addEventListener("click", function () {
    let filterName = document.getElementById("filterName").value;
    if (filterName.trim() == "") {
      document.getElementById("filterName").classList.add("input-mandatory");
    }
    else {
      document.getElementById("filterName").classList.remove("input-mandatory");

      let filterDescription = document.getElementById("filterDescription").value;
      let viewMode = document.getElementById("viewMode").value;
      let filterId = document.getElementById("filterId").value || "";
      let filterItem = selectedCohortFilterInfo;
      let isNewFilter = true;

      if (filterId != "") {
        isNewFilter = false;
      }
      if (!filterInfo.studyIds) {
        filterInfo.studyIds = [];
      }
      if (!filterInfo.filters) {
        filterInfo.filters = {};
      }

      let newFilter = {
        "filterId": filterId != "" ? filterId : filterItem.filterId,
        "filterName": filterName,
        "filterDescription": filterDescription,
        "viewMode": viewMode,
        "filterOption": filterInfo
      }
      let existingCohortFilterArray = JSON.parse(JSON.stringify(savedCohortFilterArray))

      //selectedCohortFilterInfo = newFilter;
      if (isNewFilter === true && existingCohortFilterArray.length > 0) {
        existingCohortFilterArray.unshift(newFilter)
      }
      else if (isNewFilter === true) {
        existingCohortFilterArray.push(newFilter)
      }

      let saveCohortParams = {
        "viewMode": viewMode,
        "createdBy": loggedInUser,
        "updatedBy": loggedInUser,
        "filterName": filterName,
        "filterDescription": filterDescription,
        "filterOption": {
          "studyIds": filterInfo.studyIds,
          "filters": filterInfo.filters
        }
      }

      if (loggedInUser != "anonymousUser") {
        if (filterId == "") {
          saveCohortFilter(saveCohortParams);
        }
        else {
          updateCohortFilter(saveCohortParams, filterId);
        }
      }

      document.getElementById("close-filter-modal").click();
      //loadSavedFilters();
    }
  });

  document.getElementById("saved-filter").addEventListener("click", function () {
    const filterListCtr = document.getElementById("filter-list-ctr");

    if (filterListCtr.classList.contains("hidden")) {
      filterListCtr.classList.remove("hidden");
      document.getElementById("expand-ctr").classList.add("hidden");
    } else {
      filterListCtr.classList.add("hidden");
      document.getElementById("expand-ctr").classList.remove("hidden");
    }
  });
  document.getElementById("expand-ctr").addEventListener("click", function () {
    this.classList.add("hidden")
    const filterListCtr = document.getElementById("filter-list-ctr");
    filterListCtr.classList.remove("hidden");
  });
});


async function initializeFilters() {
  await getAllFilters();
  if (loggedInUser === "anonymousUser") {
    var button = document.getElementById("show-filter-modal");
    button.disabled = true; // Disable the button if the user is anonymous
    button.classList.add('opacity-50', 'cursor-not-allowed'); // Optional: add classes to style the disabled state
    button.setAttribute('data-tooltip-target', 'tooltip-animation'); // Add the data attribute
  }
}

function getLoggedInUser() {
  // Reply to the user
  let loginId = loggedInUser;
  const scriptTag = document.querySelector('script[src="/js/cohort.js"]');
  if (scriptTag) {
    const userIdData = scriptTag.getAttribute('data-userid');
    const loginMatch = userIdData.match(/login=([^,]+)/);
    if (loginMatch) {
      loginId = loginMatch[1]; // The captured login value
    } else {
      console.log("Login not found");
    }
  }
  return loginId;
}

// Function to initialize/reinitialize TomSelect
function initTomSelect() {
  // Destroy the existing instance if it exists
  if (window.studiesToInclude && window.studiesToInclude instanceof TomSelect) {
    window.studiesToInclude.destroy();  // Only call destroy if it's a valid TomSelect instance
  }

  // Initialize or reinitialize TomSelect
  window.studiesToInclude = new TomSelect("#studiesToInclude", {
    plugins: ["remove_button"],
    create: false,
    sortField: {
      field: "text",
      direction: "asc",
    },
    onInitialize: function () {
      // the onInitialize callback is invoked once the control is completely initialized.
      this.setValue(filterInfo["studyIds"] ? filterInfo["studyIds"] : defaultStudiesList);
    },
    onChange: function (value) {
      handleStudySelection(value)
    },
    onItemRemove: function (value, $item) {
      if (filterInfo["studyIds"] && filterInfo["studyIds"].length == 0) {
        delete filterInfo.studyIds;
        //document.getElementById("filter-count").innerHTML = `(` + Object.keys(window.filterInfo).length + `)`;
      }
    }
  });
}
function loadFilterCategories(filterInfo) {
  if (filterInfo.filters["gender"] && filterInfo.filters["gender"].values && filterInfo.filters["gender"].values.length > 0) {
    document.querySelector('#gender').value = filterInfo.filters["gender"].values[0];
  }
  else {
    document.querySelector('#gender').value = "";
  }
  appliedFilters.innerHTML = "";
  for (const [key, filter] of Object.entries(filterInfo.filters)) {
    if (typeof filter === "object" && key != 'studyIds' && key != 'gender') {
      generateCategoryFilter(key, filterInfo.filters[key]["type"], filterInfo.filters[key]["values"][0], filterInfo.filters[key]["values"][1] ? ` ${filterInfo.filters[key]["values"][1]}` : "")
    }
  }
  if (clearFilter == false) {
    applyFilter();
  }
}
function generateCategoryFilter(selectedCategory, opText, valueText, valueText2) {
  let operatorTextArr = optionsData.find(opt => opt.value === opText);
  const filterText = `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)
    } ${operatorTextArr.text} ${valueText}${valueText2}`;
  // Create a display text for the applied filter
  const appliedFilterDiv = document.createElement("div");
  appliedFilterDiv.id = `${selectedCategory}-filter-text`;
  appliedFilterDiv.className =
    "applied-filter-div box-border h-[30px] bg-[#edf5ff] border rounded-[6px] cursor-pointer border-solid border-[#A7BFDE]  font-normal text-xs leading-5 text-[#557dae] flex justify-between items-center px-2 mr-1 self-center";
  appliedFilterDiv.innerHTML = `<span class="applied-filter-span" data-id="${selectedCategory}">${filterText}</span>`;
  filterInfo.filters = filterInfo.filters ? filterInfo.filters : {};
  filterInfo.filters[selectedCategory] = generateFilterObject(
    selectedCategory,
    opText,
    "number",
    [valueText, valueText2]
  );
  appliedFilters.prepend(appliedFilterDiv);

  filterCategory.querySelector(
    `option[value="${selectedCategory}"]`
  ).disabled = true;

  if (clearFilter == false) {
    applyFilter();
  }
}

function populateModal() {
  // prepopulate modal
  document.getElementById("filterId").value = selectedCohortFilterInfo.filterId;
  document.getElementById("filterName").value = selectedCohortFilterInfo.filterName;
  document.getElementById("filterDescription").value = selectedCohortFilterInfo.filterDescription;
  document.getElementById("viewMode").value = selectedCohortFilterInfo.viewMode;
}

function clearModal() {
  // prepopulate modal
  document.getElementById("filterId").value = "";
  document.getElementById("filterName").value = "";
  document.getElementById("filterDescription").value = "";
  document.getElementById("viewMode").value = "";
}

function generateFilterModel() {
  // Initialize the filterParams object
  let filterParams = {};

  // Helper function to handle single and multiple conditions
  const setFilterCondition = (key, type, values) => {
    if (values.length === 1) {
      filterParams[key] = {
        filterType: "text",
        type: "equals",
        filter: values[0]
      };
    } else if (values.length > 1) {
      filterParams[key] = {
        filterType: "text",
        operator: "OR",
        conditions: values.map(value => ({
          filterType: "text",
          type: "equals",
          filter: value
        }))
      };
    }
  };

  // Add studyIds filter
  if (filterInfo.studyIds?.length) {
    setFilterCondition('study_id', 'text', filterInfo.studyIds);
  }

  // Add filter for gender
  if (filterInfo?.filters?.gender) {
    filterParams.gender = {
      filterType: "text",
      type: "equals",
      filter: filterInfo.filters.gender.values[0]
    };
  }

  // Helper function for range filters like age, tir, etc.
  const setRangeFilter = (key, filterObj) => {
    filterParams[key] = {
      filterType: "text",
      type: filterObj.type,
      filter: filterObj.values[0]
    };
    if (filterObj.type === "between") {
      filterParams[key].secondFilter = filterObj.values[1];
    }
  };

  // List of filters that follow the same "range" pattern
  const rangeFilters = ['age', 'tir', 'tbr', 'tar', 'wear_time_percentage', 'gri'];
  if (filterInfo?.filters) {
    rangeFilters.forEach(filterKey => {
      if (filterInfo?.filters[filterKey]) {
        setRangeFilter(filterKey, filterInfo?.filters[filterKey]);
      }
    });
  }

  return filterParams;
}

function loadCohortMetrics(cohortFilter) {

  let cohortFilterParams = cohortFilter ? cohortFilter : filterParamDefault;

  Promise.all(
    cohortMetrics.map((metric) =>
      fetch(metric.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cohortFilterParams)
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok ' + response.statusText);
          }
          return response.text(); // Parse the JSON response
        })
        .then(data => {
          document.getElementById(metric.eleId).innerHTML = data;
        })
        .catch(error => {
          console.error('There was a problem with the fetch operation:', error);
        })
    )
  )
}

function clearCohortMetrics() {
  cohortMetrics.map((metric) => {
    document.getElementById(metric.eleId).innerHTML = 0;
  })
}

function initGrid(filterModel) {

  let gridFilterParams = filterModel ? filterModel : window.agGridFilterParams;
  let sortModel = [];

  const agGridInstance = new AGGridAideBuilder()
    .withColumnDefs([
      {
        headerName: "Study ID",
        field: "study_id",
        filter: false,
        sortable: true,
        cellRenderer: function (params) {
          if (params.value) {
            const link = document.createElement("a");
            link.href = "/study/" + params.value;
            link.innerText = params.value;
            return link;
          } else {
            return null;
          }
        },
      },
      {
        headerName: "Participant ID",
        field: "participant_id",
        filter: false,
        sortable: true,
        cellRenderer: function (params) {
          if (params.value) {
            const link = document.createElement("a");
            link.href = "/participant/" + params.data.study_id + "/" + params.value;
            link.innerText = params.value;
            return link;
          } else {
            return null;
          }
        },
      },
      {
        headerName: "Gender",
        field: "gender",
        filter: false,
      },
      { headerName: "Age", field: "age", filter: false, sortable: true },
      {
        headerName: "Study Arm",
        field: "study_arm",
        filter: false,
      },
      {
        headerName: "Baseline HbA1c",
        field: "baseline_hba1c",
        filter: false,
      },
      { headerName: "TIR", field: "tir", filter: false },
      { headerName: "TAR", field: "tar", filter: false },
      {
        headerName: "TAR(VH)",
        field: "tar_vh",
        filter: false,
      },
      {
        headerName: "TAR(H)",
        field: "tar_h",
        filter: false,
      },
      { headerName: "TBR", field: "tbr", filter: false },
      {
        headerName: "TBR(L)",
        field: "tbr_l",
        filter: false,
      },
      {
        headerName: "TBR(VL)",
        field: "tbr_vl",
        filter: false,
      },
      { headerName: "GMI", field: "gmi", filter: false },
      {
        headerName: "%GV",
        field: "percent_gv",
        filter: false,
      },
      { headerName: "GRI", field: "gri", filter: false },
      {
        headerName: "Days Of Wear",
        field: "days_of_wear",
        filter: false,
      },
      {
        headerName: "%Wear Time",
        field: "wear_time_percentage",
        filter: false,
      },
      {
        headerName: "Data Start Date",
        field: "data_start_date",
        filter: false,
      },
      {
        headerName: "Data End Date",
        field: "data_end_date",
        filter: false,
      },
    ])
    .withServerSideDatasource(
      window.shell.serverSideUrl(
        `/api/ux/tabular/jooq/all_participant_dashboard_cached.json`
      ),
      ((data, valueCols) => {
        return valueCols.map((col) => ({
          headerName: col.displayName,
          field: col.field,
        }));
      }),
      {
        beforeRequest: async (reqPayload, dataSourceUrl) => {
          // Add custom parameters here
          reqPayload.body = {
            ...reqPayload.body,
            // "rowGroupCols": [],
            // "valueCols": [],
            // "pivotCols": [],
            // "pivotMode": false,
            // "groupKeys": [],
            "filterModel": gridFilterParams,
            //"sortModel": sortModel
          };
          return reqPayload;
          // return reqPayload;
        },
        beforeSuccess: async (serverRespPayload, respMetrics, dataSourceUrl) => {
          // Update lastResponseLength
          lastResponseLength = serverRespPayload.data.length;
          // Clear the grid if there's no more data
          if (lastResponseLength === 0 || !hasMoreData) {
            clearGrid();
          }
        },
      }
    )
    // .withEventHandler("onSortChanged", function (event) {
    //   let colList = event.columns;
    //   let len = colList.length;
    //   if (event.type === "sortChanged") {
    //     if (colList[len - 1].sort == null) {
    //       sortModel = [];
    //     }
    //     else {
    //       sortModel = [{ sort: colList[len - 1].sort, colId: colList[len - 1].colId }];
    //     }
    //   }
    // })
    .withGridDivStyles({ height: "750px", width: "100%" })
    .build();

  agGridInstance.init("serverDataGrid");
  window.agGridInstance = agGridInstance;
  window.agGridFilterParams = agGridFilterParams;
}

function applyFilter() {
  initFilter = true;
  let sqlFragments = [];
  if (filterInfo?.filters) {
    sqlFragments = Object.values(filterInfo?.filters)
      .filter(
        (item) =>
          typeof item === "object" && item !== null && "sqlFragment" in item
      )
      .map((item) => item.sqlFragment);
  }

  const cohortFilter = {
    studyIds: filterInfo["studyIds"] || [],
    filters: sqlFragments,
  };
  if (cohortFilter.studyIds.length > 0 || cohortFilter.filters.length > 0) {
    agGridFilterParams = generateFilterModel();
    window.agGridFilterParams = agGridFilterParams;

    document.getElementById("serverDataGrid").innerHTML = '';
    initGrid(window.agGridFilterParams);
    loadCohortMetrics(cohortFilter)
  }
}


function generateRandomId() {
  return Math.random().toString(36).substr(2, 8);
}

function createFilterDiv(selectedCategory, selectedTitle, filterData) {
  const filterId = `filter-${selectedCategory}`; // Unique ID for the filter
  let filterDiv = document.getElementById(filterId);
  if (!filterDiv || filterDiv == null) {
    // Create the container for the filter
    filterDiv = document.createElement("div");
    filterDiv.className = "flex my-2";
    filterDiv.setAttribute("data-category", selectedCategory);
    filterDiv.id = filterId;

    // Create and append the label
    const filterLabel = document.createElement("label");
    filterLabel.className =
      "text-gray-700 font-normal bg-[#f8fafd] content-center px-2 text-sm border border-solid border-[#DDDDDD] rounded-[3px] rounded-br-none rounded-tr-none";

    filterLabel.innerHTML = selectedTitle;
    filterDiv.appendChild(filterLabel);
    // Create the filter inputs
    const filterInputContainer = document.createElement("div");
    filterInputContainer.className = "flex space-x-2";

    const operatorSelect = document.createElement("select");
    operatorSelect.name = `${selectedCategory}-operator`;
    operatorSelect.className =
      "box-border w-[265px] h-9 border rounded-[3px] rounded-tl-none rounded-bl-none ml-[-1px] border-solid border-[#DDDDDD] bg-white text-sm";
    optionsData.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;  // Set the value
      optionElement.textContent = option.text;  // Set the display text
      operatorSelect.appendChild(optionElement);  // Append to select
    });
    if (filterData && filterData.operator)
      operatorSelect.value = filterData.operatorText;

    const valueInput = document.createElement("input");
    valueInput.type = "number";
    valueInput.name = `${selectedCategory}-value`;
    valueInput.placeholder =
      operatorSelect.value === "between" ? "From" : "Value";
    valueInput.className =
      "box-border w-[265px] h-9 border rounded-[3px] border-solid border-[#DDDDDD] bg-white text-sm";
    if (filterData && filterData.values && filterData.values.length > 0) {
      valueInput.value = filterData.values[0];
    }

    const valueInput2 = document.createElement("input");
    valueInput2.type = "number";
    valueInput2.name = `${selectedCategory}-value-2`;
    valueInput2.placeholder = "Value 2";
    valueInput2.className =
      "box-border w-[265px] h-9 border rounded-[3px] border-solid border-[#DDDDDD] bg-white hidden text-sm";
    if (filterData && filterData.values && filterData.values.length > 1) {
      valueInput2.value = filterData.values[1];
      valueInput2.classList.remove("hidden")
    }

    // Apply button (hidden initially)
    const applyBtn = document.createElement("span");
    applyBtn.className =
      "add-btn ml-2 cursor-pointer text-green-500 hover:text-green-700 hidden";
    applyBtn.innerHTML = applyButtonHtml;
    if (filterData && filterData.values) {
      if (operatorSelect.value == "between" && filterData.values.length > 1) {
        applyBtn.classList.remove("hidden")
      }
      else if (operatorSelect.value != "between" && filterData.values.length > 0) {
        applyBtn.classList.remove("hidden")
      }
    }

    applyBtn.onclick = function () {

      const opText =
        operatorSelect.options[operatorSelect.selectedIndex].value;
      const valueText = valueInput.value;
      const valueText2 = valueInput2.classList.contains("hidden")
        ? ""
        : ` ${valueInput2.value}`;
      generateCategoryFilter(selectedCategory, opText, valueText, valueText2)
      filterDiv.style.display = "none";
      // document.getElementById("filter-count").innerHTML = `(` + Object.keys(window.filterInfo).length + `)`;
    };

    // Show or hide the second value input based on the operator
    operatorSelect.addEventListener("change", function () {
      if (operatorSelect.value === "between") {
        valueInput2.classList.remove("hidden");
      } else {
        valueInput2.classList.add("hidden");
      }
      toggleApplyButton(); // Check if the apply button should be shown
    });

    // Function to toggle the apply button
    function toggleApplyButton() {
      if (
        valueInput.value !== "" &&
        (valueInput2.classList.contains("hidden") ||
          valueInput2.value !== "")
      ) {
        applyBtn.classList.remove("hidden");
      } else {
        applyBtn.classList.add("hidden");
      }
    }

    // Add event listeners to the inputs to show/hide the apply button
    valueInput.addEventListener("input", toggleApplyButton);
    valueInput2.addEventListener("input", toggleApplyButton);

    const removeBtn = document.createElement("span");
    removeBtn.className =
      "remove-btn ml-2 cursor-pointer text-red-500 hover:text-red-700";
    removeBtn.innerHTML = removeButtonHtml;
    removeBtn.onclick = function () {
      filterDiv.remove();
      filterCategory.querySelector(
        `option[value="${selectedCategory}"]`
      ).disabled = false;

      delete filterInfo.filters[`${selectedCategory}`];
      applyFilter();
      //document.getElementById("filter-count").innerHTML = `(` + Object.keys(window.filterInfo).length + `)`;

    };

    // Append inputs and remove button to the filter container
    filterInputContainer.appendChild(operatorSelect);
    filterInputContainer.appendChild(valueInput);
    filterInputContainer.appendChild(valueInput2);
    filterInputContainer.appendChild(applyBtn);
    filterInputContainer.appendChild(removeBtn);
    filterDiv.appendChild(filterInputContainer);
    dynamicFilters.appendChild(filterDiv);
  }
  return filterDiv;
}

function handleStudySelection(values) {
  filterInfo["studyIds"] = values;
  //document.getElementById("filter-count").innerHTML = `(` + Object.keys(window.filterInfo).length + `)`;
  const selectElement = document.querySelector('#studiesToInclude');
  // Loop over the select options and mark the ones in selectedStudies as selected
  Array.from(selectElement.options).forEach(option => {
    if (values.includes(option.value)) {
      option.selected = true;
    }
  });
  if (clearFilter == false) {
    applyFilter();
  }

  // Refresh TomSelect to reflect the pre-selected values
}

function generateFilterObject(selCat, operatorText, filterType, values) {
  let op = getOperator(operatorText);
  let fieldValues = values.filter((val) => val != "");
  let filterObj = {
    filterType: filterType,
    type: operatorText,
    operator: op,
    values: fieldValues,
    sqlFragment: generateConditionalQueryFragment(selCat, op, fieldValues),
  };
  return filterObj;
}

function getOperator(opval) {
  switch (opval) {
    case "equals":
      return "=";
    case "greaterThan":
      return ">";
    case "greatersOrEqual":
      return ">=";
    case "lessThan":
      return "<";
    case "lessOrEqual":
      return "<=";
    case "between":
      return "between";
  }
}

const generateConditionalQueryFragment = (field, op = "=", values) => {
  switch (op) {
    case "between":
      return (
        field + " > " + values[0] + " AND " + field + " < " + values[1]
      );
    default:
      return field + "  " + op + " '" + values[0] + "'";
  }
};

function handleGenderSelection(value) {
  filterInfo.filters = filterInfo.filters ? filterInfo.filters : {};
  filterInfo.filters["gender"] = generateFilterObject(
    "gender",
    "equals",
    "text",
    [value]
  );
  if (value == "") {
    delete filterInfo.filters.gender;
  }
  //document.getElementById("filter-count").innerHTML = `(` + Object.keys(window.filterInfo).length + `)`;
  if (clearFilter == false) {
    applyFilter();
  }
}

function createFilterList() {
  const ul = document.getElementById('filter-list');
  savedCohortFilterArray.forEach(function (filterItem, index) {
    const li = document.createElement('li');
    li.classList.add(
      'filter-item', 'box-border', 'border', 'cursor-pointer', 'border-solid', 'border-gray-300', 'hover:bg-blue-100', 'font-medium', 'text-sm', 'text-gray-800', 'hover:text-blue-600', 'px-2', 'py-1', 'mb-1', 'text-[13px]'
    );
    if (index == 0) {
      li.classList.add("active-filter");
    }
    li.innerHTML = `${filterItem.filterName}`;
    li.id = `filter-item-${filterItem.filterId}`;
    ul.appendChild(li);
  });
}

function assignValues(eleClass, value) {
  document.querySelectorAll("." + eleClass).forEach((element) => {
    element.innerHTML = value;
  });
}

function loadSavedFilters() {
  // savedCohortFilters = localStorage.getItem("savedCohortFilters");
  // savedCohortFilterArray = savedCohortFilters ? JSON.parse(savedCohortFilters) : []; //total saved filters
  // selectedCohortFilterInfo = savedCohortFilterArray.length > 0 ? savedCohortFilterArray[0] : {}; //Selected filter
  // filterInfo = Object.keys(selectedCohortFilterInfo).length > 0 ? JSON.parse(JSON.stringify(selectedCohortFilterInfo.filterOption)) : {};
  // if (savedCohortFilterArray.length > 0) {
  //   document.getElementById('filter-list').innerHTML = "";
  //   createFilterList();
  // }
  // else {
  //   document.getElementById("filter-list").innerHTML = '<div class="text-sm italic box-border border border-solid border-gray-300 font-medium text-gray-600 px-2 py-1 mb-1 text-[14px]">No saved filters yet</div>'
  // }
  document.getElementById("filter-list").innerHTML = ""
  selectedCohortFilterInfo = savedCohortFilterArray.length > 0 ? savedCohortFilterArray[0] : {}; //Selected filter
  filterInfo = Object.keys(selectedCohortFilterInfo).length > 0 ? JSON.parse(JSON.stringify(selectedCohortFilterInfo.filterOption)) : { "studyIds": defaultStudiesList };
  if (savedCohortFilterArray.length > 0) {
    createFilterList();
  }
  else {
    document.getElementById("filter-list").innerHTML = '<div class="text-sm italic box-border border border-solid border-gray-300 font-medium text-gray-600 px-2 py-1 mb-1 text-[14px]">No saved filters yet</div>'
  }

  if (Object.keys(selectedCohortFilterInfo).length > 0) {
    populateModal();
  }
  if (Object.keys(filterInfo).length > 0) {
    let totalFilterArray = filterInfo;
    selectedStudies = totalFilterArray["studyIds"];
    const filterSqlFragments = Object.values(totalFilterArray.filters)
      .filter(
        (item, key) =>
          typeof item === "object" && key !== 'studyIds' && item !== null && "sqlFragment" in item
      )
      .map((item) => item.sqlFragment);
    selectedAdvancedFilters = filterSqlFragments;
    window.agGridFilterParams = agGridFilterParams = generateFilterModel();
    initTomSelect();
    loadFilterCategories(filterInfo);

  }
  filterParamDefault = { "studyIds": selectedStudies, "filters": selectedAdvancedFilters };

  window.defaultStudiesList = defaultStudiesList;

}

async function getAllFilters() {
  return new Promise((resolve, reject) => {
    fetchData(
      `/cohort-filter`,
      (result, error) => {
        if (!error) {
          resolve(result.data.cohortFilters);
          savedCohortFilterArray = result.data?.cohortFilters || [];
          loadSavedFilters();
        } else {
          resolve([]);
        }
      }
    );
  });
}
// Function to show the modal dynamically
function createModalContent(title, message) {
  const modal = document.getElementById("error-popup-show");
  const modalTitle = document.getElementById("modal-title");
  const modalMessage = document.getElementById("modal-message");

  // Set modal content
  modalTitle.innerText = title;
  modalMessage.innerText = message;


  // Event listeners for modal buttons

}
document.getElementById("close-modal").onclick = () => {
  const modalElement = new Modal(document.getElementById("error-popup-show"));
  modalElement.hide();
};
document.getElementById("dismiss-modal").onclick = () => {
  const modalElement = new Modal(document.getElementById("error-popup-show"));
  modalElement.hide();
};

// Updated `saveCohortFilter` function
function saveCohortFilter(params) {
  const modalElement = new Modal(document.getElementById("error-popup-show"));
  postData(`/cohort-filter.json`, params, (res) => {
    if (res && res.message === "Success") {
      initializeFilters();
    } else {
      const errorMessage = res?.errors || "Please try again later.";
      createModalContent("Save Filter Failed", errorMessage);
      modalElement.show();
    }
  });
}

function updateCohortFilter(params, filterId) {
  const modalElement = new Modal(document.getElementById("error-popup-show"));
  updateData(`/cohort-filter/${filterId}.json`, params, (res) => {
    if (res && (res.message) == "Success") {
      initializeFilters();
    } else {
      const errorMessage = res?.errors || "Please try again later.";
      createModalContent("Update Filter Failed", errorMessage);
      modalElement.show();
    }

  });
}