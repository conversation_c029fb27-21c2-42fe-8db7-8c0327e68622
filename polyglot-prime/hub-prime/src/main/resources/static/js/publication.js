var authorTeamList; //Author Team tomselect
let sourceAuthors = [];
let defaultAuthors = [];
let teamAuthors = [];
let authorTeamCollaboration = [];
let authorTeamContent = [];
let publicationDetailsLoad = false;
let pubmedValueSaved = false;
let doiValueSaved = false;
let pubMedIdValue = '';
let doiValue = '';
let pubmedDataFetched = false;
let doiDataFetched = false;
let errorMessage = "";
let publicationList = [];

document.addEventListener("DOMContentLoaded", function () {

    document.getElementById("publicationPubmed")?.addEventListener("blur", handlePubMedBlur, true);
    document.getElementById("publicationDoi")?.addEventListener("blur", handleDOIBlur, true);

});
function isValidPubmed(pubmedId) {
    const isValidId = /^[0-9]{1,8}$/.test(pubmedId);
    let errorClass = "border-[#e23636]";
    let errorElement = document.getElementById("publicationPubmed-error");
    let element = document.getElementById("publicationPubmed");
    document.getElementById("publicationDoi").classList.remove(errorClass);
    if (!isValidId) {
        element.classList.add(errorClass);
        errorElement.textContent =
            "Invalid format. Use only digits, up to 8 characters.";
        return false;
    } else {
        element.classList.remove(errorClass);
        errorElement.textContent = "";
        return true;
    }
}
function isValidpublicationDoiId(publicationDoiId) {
    const isValidId = /^10\.\d{4,9}\/[\S]+$/.test(publicationDoiId);
    let errorClass = "border-[#e23636]";
    let errorElement = document.getElementById("publicationDoi-error");
    let element = document.getElementById("publicationDoi");
    document.getElementById("publicationPubmed").classList.remove(errorClass);
    if (!isValidId) {
        element.classList.add(errorClass);
        errorElement.textContent =
            "Invalid format. Start with '10.', followed by 4 to 9 digits, a '/' separator, and a non-empty suffix.";
        return false;
    } else {
        element.classList.remove(errorClass);
        errorElement.textContent = "";
        return true;
    }
}

async function handlePubMedBlur(event) {
    console.log("handlePubMedBlur")
    const pubmedId = DOMPurify.sanitize(document.getElementById("publicationPubmed").value);
    const pubTitleElement = document.getElementById("publicationTitle");
    const pubDateElement = document.getElementById("publicationDate");
    const pubDoiElement = document.getElementById("publicationDoi");
    if (pubmedId) {
        if (isValidPubmed(pubmedId)) {
            console.log("isValidPubmed pubmedId---", pubmedId)

            let pubmedExists = await pubmedIdExists();
            console.log("pubmedExists", pubmedExists);
            if (pubmedExists) {
                document.getElementById("publicationPubmed-error").textContent = "This Pubmed Id already exists";
                disablePubmedDataFetching();
                return;
            }

            console.log("pubMedIdValue", pubMedIdValue)
            console.log("pubmedId", pubmedId)

            if (publicationDetailsLoad && pubmedId != pubMedIdValue) {
                console.log("publicationDetailsLoad pubmedId---not equals", pubmedId)

                enablePubmedDataFetching();
            }
            else if (publicationDetailsLoad && pubmedId == pubMedIdValue) {
                console.log("publicationDetailsLoad pubmedId--- equals", pubmedId)

                disablePubmedDataFetching();
            }
            else {
                console.log("publicationDetailsLoad pubmedId--fetch pubmed data", pubmedId)

                fetchPubMedIdData();
            }
        }
        else {
            console.log("publicationDetailsLoad disablePubmedDataFetching")

            disablePubmedDataFetching();
        }

    }
    else {

        console.log("pubmed else  ")
        if (!publicationDetailsLoad) {
            console.log("pubmed else !publicationDetailsLoad  ")
            document
                .getElementById("publicationDoi")
                .addEventListener("blur", handleDOIBlur, true);

        }
    }
}
function enablePubmedDataFetching() {
    document.getElementById("fetch-pubmed-btn-ctr")?.classList.remove("hidden");
    document.getElementById("fetch-pubmed-btn")?.addEventListener("click", fetchPubMedIdData, true);
    console.log("showDataFetchAlert")
    showDataFetchAlert("PubMed")
}

function disablePubmedDataFetching() {
    document.getElementById("fetch-pubmed-btn-ctr")?.classList.add("hidden");
    document.getElementById("fetch-pubmed-btn")?.removeEventListener("click", fetchPubMedIdData, true);
    document.getElementById("data-fetch-alert")?.classList.add("hidden");
}

function enableDOIDataFetching() {
    console.log("enableDOIDataFetching DOI 111")

    document.getElementById("fetch-doi-btn-ctr")?.classList.remove("hidden");
    console.log("enableDOIDataFetching DOI 222")

    document.getElementById("fetch-doi-btn")?.addEventListener("click", fetchDOIData, true);
    console.log("enableDOIDataFetching DOI 3333")

    showDataFetchAlert("DOI")
}

function disableDOIDataFetching() {
    document.getElementById("fetch-doi-btn-ctr")?.classList.add("hidden");
    document.getElementById("fetch-doi-btn")?.removeEventListener("click", fetchDOIData, true);
    document.getElementById("data-fetch-alert")?.classList.add("hidden");
}

async function handleDOIBlur(event) {
    const publicationDoiId = DOMPurify.sanitize(document.getElementById("publicationDoi").value);
    const pubTitleElement = document.getElementById("publicationTitle");
    const pubDateElement = document.getElementById("publicationDate");
    const pubmedElement = document.getElementById("publicationPubmed");
    console.log("publicationDoiId", publicationDoiId);
    console.log("doiValue", doiValue);
    console.log("publicationDetailsLoad", publicationDetailsLoad);

    if (publicationDoiId) {
        if (isValidpublicationDoiId(publicationDoiId) && publicationDoiId != doiValue) {
            showLoading("publicationDoiLoader");

            let doiExists = await pubmedIdExists();
            console.log("doiExists", doiExists);
            if (doiExists) {
                hideLoading("publicationDoiLoader");
                document.getElementById("publicationDoi-error").textContent = "This Publication DOI already exists";
                disableDOIDataFetching();
                return;
            }
            else {
                hideLoading("publicationDoiLoader");
                document.getElementById("publicationDoi-error").textContent = "";
            }

            if (publicationDetailsLoad && publicationDoiId != doiValue) {
                console.log("if publicationDetailsLoad doiValue not equals")
                enableDOIDataFetching();
            }
            else if (publicationDetailsLoad && publicationDoiId == doiValue) {
                console.log("if publicationDetailsLoad doiValue equals")
                disableDOIDataFetching();
            }
            else {

                const publicationDoiIdValid = isValidpublicationDoiId(publicationDoiId);
                if (publicationDoiIdValid) {
                    fetchDOIData();
                }
            }
        }
        else {
            disableDOIDataFetching();
        }
    } else {
        console.log("else")
        if (!publicationDetailsLoad) {
            document
                .getElementById("publicationPubmed")
                ?.addEventListener("blur", handlePubMedBlur, true);

        }
    }
}

async function fetchDOIData() {
    const publicationDoiId = DOMPurify.sanitize(document.getElementById("publicationDoi").value);
    const pubTitleElement = document.getElementById("publicationTitle");
    const pubDateElement = document.getElementById("publicationDate");
    const pubmedElement = document.getElementById("publicationPubmed");
    if (publicationDoiId) {
        showLoading("publicationDoiLoader");
        const publicationDoiIdValid = isValidpublicationDoiId(publicationDoiId);
        if (publicationDoiIdValid) {
            doiValue = publicationDoiId;

            fetchData(
                `/pubmed/details?doi=${publicationDoiId}`,
                async (data, error) => {
                    if (!error) {
                        citationDataSource = "DOI";

                        //  if (Object.keys(result).length != 0) {
                        const result = data.data;
                        if (result?.metadata?.title != "" && result?.metadata?.title != null && result?.metadata?.title != undefined) {
                            pubTitleElement.value = result?.metadata?.title;
                            publicationDetailsLoad = true;
                        }
                        else {
                            pubTitleElement.value = "";
                        }
                        if (result?.metadata?.pubDate != "" && result?.metadata?.pubDate != null && result?.metadata?.pubDate != undefined && result?.metadata?.pubDate != "Invalid Date") {
                            pubDateElement.value = result?.metadata?.pubDate;
                        }
                        else {
                            pubDateElement.value = "";
                        }
                        if (result?.pubmedId != "" && result?.pubmedId != null && result?.pubmedId != undefined) {
                            pubmedElement.value = result?.pubmedId;
                            pubMedIdValue = pubmedElement.value;
                        }
                        else {
                            pubmedElement.value = "";
                            pubMedIdValue = pubmedElement.value;
                        }
                        if (result?.metadata?.authors.length > 0) {
                            let authorsSource = result?.metadata?.authors || [];
                            let authorsList = authorsSource.filter(item => item.authtype === "Author").map(({ name }) => ({
                                author_name: name, // Rename 'name' to 'investigator_name'
                            }));
                            let authorsSourceList = sourceAuthors.concat(authorsList);
                            authorTeamContent = authorsList.map((item) => {
                                return item.author_name;
                            });
                            initAuthorsTeam(authorsSourceList, authorTeamContent)
                            showDataFetchedMessage("DOI");
                            document.getElementById('authorTeam-error').textContent = '';

                        }
                    } else {
                        console.error("Error fetching publication info:", error);
                        document.getElementById("publicationDoi-error").textContent =
                            "Publication details not found against this DOI";

                    }
                    disableDOIDataFetching(); // To hide the buttons "Get Details" after datafetching
                    disablePubmedDataFetching();
                    hideLoading("publicationDoiLoader");
                    document
                        .getElementById("publicationPubmed")
                        ?.addEventListener("blur", handlePubMedBlur, true);
                }
            );
        } else {
            disableDOIDataFetching();
            hideLoading("publicationDoiLoader");
        }
    }
}

function fetchPubMedIdData() {
    const pubmedId = DOMPurify.sanitize(document.getElementById("publicationPubmed").value);
    const pubTitleElement = document.getElementById("publicationTitle");
    const pubDateElement = document.getElementById("publicationDate");
    const pubDoiElement = document.getElementById("publicationDoi");
    showLoading("publicationPubmedLoader");
    const pubmedIdValid = isValidPubmed(pubmedId);
    if (pubmedIdValid) {
        pubMedIdValue = pubmedId;
        fetchData(
            `/pubmed/details?pubmedId=${pubmedId}`,
            async (data, error) => {
                if (!error) {
                    citationDataSource = "PubMed";
                    pubmedDataFetched = true;
                    document.getElementById("publicationDoi-error").textContent = "";
                    document.getElementById("publicationPubmed-error").textContent = "";
                    const result = data.data;
                    if (result?.metadata?.title != "" && result?.metadata?.title != null && result?.metadata?.title != undefined) {
                        pubTitleElement.value = result?.metadata?.title;
                        publicationDetailsLoad = true;

                    }
                    else {
                        pubTitleElement.value = "";
                    }
                    if (result?.metadata?.pubDate != "" && result?.metadata?.pubDate != null && result?.metadata?.pubDate != undefined && result?.metadata?.pubDate != "Invalid Date") {
                        pubDateElement.value = result?.metadata?.pubDate;
                    }
                    else {
                        pubDateElement.value = "";
                    }
                    if (result?.metadata?.doi != "" && result?.metadata?.doi != null && result?.metadata?.doi != undefined) {
                        pubDoiElement.value = result?.metadata?.doi;
                        doiValue = DOMPurify.sanitize(pubDoiElement.value);
                    }
                    else {
                        pubDoiElement.value = "";
                        doiValue = DOMPurify.sanitize(pubDoiElement.value);
                    }

                    if (result?.metadata?.authors.length > 0) {
                        let authorsSource = result?.metadata?.authors || [];
                        console.log("authorsSource", authorsSource)
                        let authorsList = authorsSource.filter(item => item.authtype == "Author").map(({ name }) => ({
                            author_name: name,
                        }));
                        console.log("authorsList", authorsList)
                        let authorsSourceList = sourceAuthors.concat(authorsList);
                        authorTeamContent = authorsList.map(
                            (item) => {
                                return item.author_name;
                            }
                        );
                        console.log(authorsSourceList)
                        console.log(authorTeamContent)
                        initAuthorsTeam(authorsSourceList, authorTeamContent);
                        showDataFetchedMessage("pubmed");
                        document.getElementById('authorTeam-error').textContent = '';

                    }
                } else {
                    console.error("Error fetching publication info:", error);
                    document.getElementById("publicationPubmed-error").textContent = "Publication details not found against this Pubmed Id";
                }
                disablePubmedDataFetching(); // To hide the buttons "Get Details" after datafetching
                disableDOIDataFetching()
                hideLoading("publicationPubmedLoader");
                document.getElementById("publicationPubmed")?.addEventListener("blur", handlePubMedBlur, true);

            }
        );
    }
    else {
        hideLoading("publicationPubmedLoader");
        disablePubmedDataFetching();
    }
}

function showDataFetchedMessage(type = "pubmed") {
    hideDataFetchAlert();
    if (type == "pubmed") {
        document.getElementById("data-fetch-message").innerHTML = "The above filled information is based on the pubmed id- <b>" + pubMedIdValue + "</b>.";
    }
    else {
        document.getElementById("data-fetch-message").innerHTML = "The above filled information is based on the DOI- <b>" + doiValue + "</b>.";
    }
    document.getElementById("data-fetch-message").classList.remove("hidden");
}
function showDataFetchAlert(type = 'PubMed') {
    let newCitationDataSource = "PubMed Id";
    if (type == "PubMed") {
        newCitationDataSource = "PubMed Id";
    }
    else if (type == "DOI") {
        newCitationDataSource = "DOI";
    }
    hideDataFetchedMessage();
    console.log("citationDataSource", citationDataSource)
    if (citationDataSource == "DOI") {
        console.log("citationDataSource 11111111111", citationDataSource)

        document.getElementById("data-fetch-alert").innerHTML = "The above filled information is based on the DOI- <b>" + doiValue + "</b>. If you need to fetch the publication details based on the latest " + newCitationDataSource + ", please click on the 'Get Details' button.";
        document.getElementById("data-fetch-alert").classList.remove("hidden");
    }
    else if (citationDataSource == "PubMed") {
        console.log("citationDataSource 222222222", citationDataSource)

        document.getElementById("data-fetch-alert").innerHTML = "The above filled information is based on the pubmed id- <b>" + pubMedIdValue + "</b>. If you need to fetch the publication details based on the latest " + newCitationDataSource + ", please click on the 'Get Details' button.";
        document.getElementById("data-fetch-alert").classList.remove("hidden");
    }
    else {
        console.log("citationDataSource 33333333333", citationDataSource)

        document.getElementById("data-fetch-alert").innerHTML = "";
        document.getElementById("data-fetch-alert").classList.add("hidden");
    }
}

function hideDataFetchedMessage() {
    document.getElementById("data-fetch-message").innerHTML = "";
    document.getElementById("data-fetch-message").classList.add("hidden");

}
function hideDataFetchAlert() {
    document.getElementById("data-fetch-alert").innerHTML = "";
    document.getElementById("data-fetch-alert").classList.add("hidden");
}

function initAuthorsTeam(defaultAuthors, authorTeams) {
    console.log("!111111111111111")
    if (authorTeamList instanceof TomSelect) {
        console.log("02222222222222")
        authorTeamList.destroy();  // Only call destroy if it's a valid TomSelect instance
    }
    authorTeamList = new TomSelect("#authorTeam", {
        plugins: ["remove_button"],
        create: true,
        createFilter: function (input) {
            input = input.toLowerCase();
            return !(input in this.options);
        },
        maxItems: null,
        valueField: 'author_name',
        labelField: 'author_name',
        searchField: 'author_name',
        options: defaultAuthors,
        createFilter: function (input) { return input.length >= parseInt(2, 10); },
        onInitialize: function () {
            this.setValue(authorTeams);
        },
        onChange: function (value) {
            authorTeamCollaboration = value;
        },
        onItemRemove: function (value, $item) {

        }
    });
}
