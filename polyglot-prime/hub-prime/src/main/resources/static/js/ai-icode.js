document.addEventListener("DOMContentLoaded", function () {
  document.head.insertAdjacentHTML(
    "beforeend",
    '<link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.16/tailwind.min.css" rel="stylesheet">'
  );

  const style = document.createElement("style");
  style.innerHTML = `
  .hidden-chat {
    display: none;
  }
  #chat-widget-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    flex-direction: column;
  }
  #chat-popup {
    height: 70vh;
    max-height: 70vh;
    transition: all 0.3s;
    overflow: hidden-chat;
  }
  .loader {
    border: 4px solid #f3f3f3; /* Light grey */
    border-top: 4px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
  }

   @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

  @media (max-width: 768px) {
    #chat-popup {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      max-height: 100%;
      border-radius: 0;
    }
  }
  `;

  document.head.appendChild(style);

  const scriptTag = document.querySelector('script[src="/js/ai-icode.js"]');
  let userId = "";
  if (scriptTag) {
    userId = scriptTag.getAttribute("data-user-id") || "Anonymous";
  }

  loadChatHistorySample();
  onUserRequestSample();

  function onUserRequestSample() {
    if (scriptTag) {
      const apiUrl = scriptTag.getAttribute("data-api-url-sample");
      const chatElementRef = document.getElementById("chat-element");

      // Add the messagePreprocessor function
      chatElementRef.messagePreprocessor = (message) => {
        const timestamp = new Date().toUTCString();
        const readableTime = new Date(timestamp).toLocaleString(); // auto uses local timezone

        // Different styling for user and AI messages
        if (message.role === "user") {
          console.log("User message:", message.text);

          // Ensure timestamp and readableTime are defined
          const timestamp = new Date().toUTCString();
          const readableTime = new Date(timestamp).toLocaleString(); // Local timezone
          const userMessagehtml = `<div class="chat-box">
            <div style="margin-bottom: 10px; color: white; font-size: 13px;">
          <img src="/ai-user.jpg" alt="User Icon" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
            <b>${userId !== "Anonymous" ? userId : "User"}</b>
          </div>
            <div style="margin-bottom: 10px;">${message.text}</div>
            <div style="color: white; text-align: right;">${readableTime}</div>
          </div>`;
          const messageIndexArray = chatElementRef.getMessages() || [];
          const messageIndex = messageIndexArray.length - 1; // Get the index of the last message
          // Update the user message in the chat
          chatElementRef.updateMessage(
            {
              html: `
        <div class="chat-box">
          <div style="margin-bottom: 10px; color: white; font-size: 13px;">
          <img src="/ai-user.jpg" alt="User Icon" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
            <b>${userId !== "Anonymous" ? userId : "User"}</b>
          </div>
          <div style="margin-bottom: 10px;">${message.text}</div>
          <div style="color: white; text-align: right;">${readableTime}</div>
        </div>
      `,
            },
           messageIndex // Index of the message to update
          );

          // Store the user message in localStorage
          storeMessageSample(userId, userMessagehtml, "user", timestamp,userId);
        } else if (message.role === "ai") {
          console.log("ai message:", message.text);
          return {
            html: `
           <div class="chat-box">
            <div style="margin-bottom: 10px;">
            <img src="/path-to-icon1.png" alt="AI Icon" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
            <b>Tool</b>
            </div>
            <div style="margin-bottom: 10px;">${message.text}</div>
              <div style="font-size: 12px; color: #6b7280; text-align: right;">${readableTime}</div>
            </div>
          `,
            role: message.role,
          };
        }
      };


      chatElementRef.connect = {
        handler: async (body, signals) => {
          const timestamp = new Date().toUTCString();
          const readableTime = new Date(timestamp).toLocaleString(); // auto uses local timezone

          // Preprocess the user's question
          const userMessage = chatElementRef.messagePreprocessor({
            text: body.messages[0].text,
            role: "user",
          });


          try {
            // Send the user's message to the API
            const response = await fetch(apiUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer H4CZBGQ-W8FMEC1-Q02XJ16-QV5ZB0H",
              },
              body: JSON.stringify({
                message: body.messages[0].text, // The message sent by the user
                mode: "chat",
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to fetch response from the API.");
            }

            const data = await response.json();
            let htmlString = data.textResponse.replace(/\n/g, '<br>');
            htmlString = htmlString.replace(/\*/g, ''); // Ensure line breaks are preserved
            // Preprocess the AI's answer
            const aiMessage = chatElementRef.messagePreprocessor({
              text: htmlString,
              role: "ai",
            });

            storeMessageSample("ai", aiMessage.html, aiMessage.role, timestamp,userId);

            // Append AI's response to the chat
            signals.onResponse({ html: aiMessage.html });
          } catch (error) {
            console.error("Error fetching API response:", error);

            const errorMessage = `
              <div class="chat-box" style="color: #374151; border-radius: 5px; padding: 10px;">
                Sorry, something went wrong. Please try again later.
              </div>
            `;

            storeMessageSample(userId, errorMessage, "ai", timestamp,userId);
            signals.onResponse({ html: errorMessage });
          }
        },
      };
    }
  }

  function loadChatHistorySample() {
    const elementRef = document.getElementById("chat-element");
    const chatHistory = JSON.parse(localStorage.getItem("chatIcode")) || [];
    const userEntries = chatHistory.filter(entry => entry.userId == userId);
    const filteredData = userEntries.filter(item => item.html.includes('class="chat-box"'));
     elementRef.history = filteredData;
  }

  function storeMessageSample(user, html, role, timestamp,userId) {
    const chatData = JSON.parse(localStorage.getItem("chatIcode")) || [];
    chatData.push({ user, html, role, timestamp,userId });
    localStorage.setItem("chatIcode", JSON.stringify(chatData));
  }
});