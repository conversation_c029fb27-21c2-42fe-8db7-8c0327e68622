let dbfile = "";
import {
    AGGridAideBuilder,
} from "@presentation/shell/aggrid-aide.js";
import ModalAide from "@presentation/shell/modal-aide.js";
let gridFilterParams = {
    "study_id": {
        "filterType": "text",
        "type": "equals",
        "filter": studyId,
    },
    "file_category": {
        "filterType": "text",
        "type": "equals",
        "filter": "Database",
    },
};
let uploadSucceeded = false;
let dbProcessingStatus = false;
let dbExtractionStatus = false;//Initial case
let lastUploadedFile = "";
let workflowStepCount = 0;
let totalSteps = 4; // It should be 4 when meals and fitness data is added
document.addEventListener("DOMContentLoaded", async function () {
    const dropZone = document.getElementById("drop-zone");
    const fileInput = document.getElementById("uploadStudyDatabase");
    const uploadStudyFile = document.getElementById("upload-files");

    if (showDatabaseUploadStatus == true) {
        uploadSucceeded = true;
        document.getElementById("serverDataGrid").classList.remove("hidden");
        fetchDBProcessingStatus(studyId);
        fetchStudyGrid();
    }

    // Handle drag over
    dropZone.addEventListener("dragover", (e) => {
        e.preventDefault();
        dropZone.classList.add("drag-over");
    });

    // Handle drag leave
    dropZone.addEventListener("dragleave", () => {
        dropZone.classList.remove("drag-over");
    });

    // Handle file drop
    dropZone.addEventListener("drop", async (e) => {
        e.preventDefault();
        dropZone.classList.remove("drag-over");
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            dbfile = files[0];
            let fileContent = await handleFileDrop('uploadStudyDatabase', files[0]);
            dbfile = fileContent;
        }
    });

    document.getElementById("uploadStudyDatabase").addEventListener("change", async function () {
        dbfile = document.getElementById("uploadStudyDatabase").files[0];
        if (dbfile) {
            lastUploadedFile = dbfile;
        }
        let fileContent = await handleFileUpload('uploadStudyDatabase', lastUploadedFile);
        dbfile = fileContent;
    })
    document.getElementById("upload-files").addEventListener("click", async function (e) {
        e.preventDefault();
        await submitFileAsChinks(dbfile)

    });

    document.addEventListener("fileRemoved", function (event) {
        const { inputName } = event.detail;

        if (inputName === "uploadStudyDatabase") {
            dbfile = ""; // Reset the dbfile variable
            lastUploadedFile = ""; // Reset the last uploaded file
            console.log("File removed for input:", inputName);
        }
    });


});

async function submitFileAsChinks(studyDbFile) {

    if (studyDbFile && studyDbFile != "") {

        let studyId = document.getElementById("studyId").value;
        showStudyUploadLoader();
        showUploadProgress();//Progress bar show
        try {

            const chunkSize = 250 * 1024 * 1024; // 50MB per chunk
            const totalChunks = Math.ceil(studyDbFile.size / chunkSize);
            const fileName = studyDbFile.name;

            console.log(chunkSize, studyDbFile.size, studyDbFile.name, totalChunks);
            //let url = "/research-study/database/upload";
            let url = "/research-study/database/chunk/upload";
            let data = {};
            for (let chunkNumber = 1; chunkNumber <= totalChunks; chunkNumber++) {
                const start = (chunkNumber - 1) * chunkSize;
                const end = Math.min(start + chunkSize, studyDbFile.size);
                const chunk = studyDbFile.slice(start, end);

                const formData = new FormData();
                formData.append("file", chunk);
                formData.append("chunkNumber", chunkNumber);
                formData.append("totalChunks", totalChunks);
                formData.append("fileName", fileName);
                formData.append("studyId", studyId);
                formData.append(
                    "organizationPartyId",
                    localStorage.getItem("organizationPartyId"),
                );

                await fetch(url, {
                    method: "POST",
                    body: formData,
                }).then((response) => response.json())
                    .then((res) => {
                        console.log("Response:", res);
                        data = res;
                        if (res.status == "success") {
                            console.log("Chunk upload successful", res?.data?.status?.mealsOrFitnessDataExist);
                            if (res?.data?.status?.mealsOrFitnessDataExist == true) {
                                totalSteps = 4;
                                document.getElementById("mealsAndFitnes-ctr-final").classList.remove("hidden");
                                document.getElementById("cgm-ctr-middle").classList.remove("hidden");
                                document.getElementById("cgm-ctr-final").classList.add("hidden");
                            }
                            else {
                                totalSteps = 3;
                                document.getElementById("mealsAndFitnes-ctr-final").classList.add("hidden");
                                document.getElementById("cgm-ctr-middle").classList.add("hidden");
                                document.getElementById("cgm-ctr-final").classList.remove("hidden");

                            }
                            updateUploadProgress(chunkNumber, totalChunks);
                        }
                    })
                    .catch((error) => console.error("Error:", error));
            }

            console.log("File upload completed!");
            hideStudyUploadLoader();
            document.getElementById("upload-filename")?.remove();
            document.getElementById("uploadStudyDatabase").value = ""
            document.getElementById("upload-list")?.classList.add("hidden");

            if (data.status == "success") {
                uploadSucceeded = true;
                lastUploadedFile = "";
                fetchStudyGrid();
                showToast(
                    "The database upload process is successfully completed. The study data will be accessible after the review .Data migration is in progress. We appreciate your patience.",
                    data.status
                );
                showLoading("uploadStudyLoader");
                hideUploadProgress();
                document.getElementById("serverDataGrid")?.classList.remove("hidden");

                fetchDBProcessingStatus(studyId, true);

                hideLoading("uploadStudyLoader");
            }
            else if (data.status == "Inprogress") {
                console.log("Batch data upload in progress ");
            }
            else {
                showToast(data.message, data.status);
                hideUploadProgress();
                dbfile = lastUploadedFile = "";
            }
            console.log("upload data", data);

        } catch (error) {
            hideStudyUploadLoader();
            hideUploadProgress();
            console.error('Error:', error);
            console.log(error);
            dbfile = lastUploadedFile = "";
            document.getElementById("serverDataGrid")?.classList.remove("hidden");
            fetchStudyGrid();

        }
    }
    else {
        showToast("Please upload a study database", "error");

    }
}

function showUploadProgress() {
    document.getElementById("uploadProgressContainer").classList.remove("hidden");
}

function hideUploadProgress() {
    document.getElementById("uploadProgressContainer").classList.add("hidden");
    document.getElementById("uploadProgress").style.width = "0%";
    document.getElementById("uploadProgress").innerHTML = `0%`;
}

function updateUploadProgress(chunkNumber, totalChunks) {
    const progress = Math.round((chunkNumber / totalChunks) * 100);
    document.getElementById("uploadProgress").style.width = progress + "%";
    document.getElementById("uploadProgress").innerHTML = `${progress}%`;
}

function showStudyUploadLoader() {
    document.getElementById("uploadDatabaseLoader").classList.remove("hidden");
}

function hideStudyUploadLoader() {
    document.getElementById("uploadDatabaseLoader").classList.add("hidden");
}
function initUploadStudyGrid() {
    const schemaName = "drh_stateless_activity_audit";
    const viewName = "file_interaction_view";

    const modalAide = new ModalAide();
    const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
            {
                headerName: "File Name",
                field: "file_name",
                filter: "agTextColumnFilter",
            },
            {
                headerName: "Study Display Id",
                field: "study_display_id",
                filter: "agTextColumnFilter",
            },
            {
                headerName: "Action Type",
                field: "description",
                filter: "agTextColumnFilter",
            },
            {
                headerName: "Status",
                field: "interaction_status",
                filter: "agTextColumnFilter",
            }

        ])
        .withServerSideDatasource(
            window.shell.serverSideUrl(
                `/api/ux/tabular/jooq/${schemaName}/${viewName}.json`,
            ),
            (data, valueCols) =>
                valueCols.map((col) => ({
                    headerName: col.displayName,
                    field: col.field,
                })),
            {
                beforeRequest: async (reqPayload) => {
                    reqPayload.body = {
                        ...reqPayload.body,
                        "filterModel": {
                            ...reqPayload.body.filterModel,
                            ...gridFilterParams,
                        },
                    };
                    return reqPayload;
                },
                beforeSuccess: async (serverRespPayload) => {
                    if (serverRespPayload.data.length === 0) {
                        clearGrid();
                    }
                },
            },
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: "500px", width: "100%" })
        .build();
    agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

    agGridInstance.init("serverDataGrid");
}
function showMigrationWorkflow() {
    document.getElementById("migration-workflow").classList.remove("hidden");


    let stepCount = 0;
    if (workflowStepCount < totalSteps) { // It should be 4 when meals and fitness data is added
        getDatabaseExtractionStatus(studyId)
            .then(async (extractionStatus) => {

                if (extractionStatus.studyMetaData == true) {
                    stepCount++;
                    document.getElementById("studyMetaData-svg-ctr").innerHTML = '<svg class="w-3.5 h-3.5 text-blue-600 lg:w-4 lg:h-4 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5" /></svg>';
                    document.getElementById("studyMetaData-svg-ctr").classList.remove("bg-gray-100");
                    document.getElementById("studyMetaData-svg-ctr").classList.add("bg-blue-100");
                    document.getElementById("studyMetaData-svg-ctr").parentNode.classList.add("text-blue-600");
                    document.getElementById("studyMetaData-svg-ctr").parentNode.classList.add("dark:text-blue-500");

                }
                if (extractionStatus.participant == true) {
                    stepCount++;
                    document.getElementById("participant-svg-ctr").innerHTML = '<svg class="w-3.5 h-3.5 text-blue-600 lg:w-4 lg:h-4 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5" /></svg>';
                    document.getElementById("participant-svg-ctr").classList.remove("bg-gray-100");
                    document.getElementById("participant-svg-ctr").classList.add("bg-blue-100");
                    document.getElementById("participant-svg-ctr").parentNode.classList.add("text-blue-600");
                    document.getElementById("participant-svg-ctr").parentNode.classList.add("dark:text-blue-500");


                }
                if (extractionStatus.cgm == true) {
                    stepCount++;
                    document.getElementById("cgm-svg-ctr-middle").innerHTML = '<svg class="w-3.5 h-3.5 text-blue-600 lg:w-4 lg:h-4 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5" /></svg>';
                    document.getElementById("cgm-svg-ctr-middle").classList.remove("bg-gray-100");
                    document.getElementById("cgm-svg-ctr-middle").classList.add("bg-blue-100");
                    document.getElementById("cgm-svg-ctr-middle").parentNode.classList.add("text-blue-600");
                    document.getElementById("cgm-svg-ctr-middle").parentNode.classList.add("dark:text-blue-500");

                    document.getElementById("cgm-svg-ctr-final").innerHTML = '<svg class="w-3.5 h-3.5 text-blue-600 lg:w-4 lg:h-4 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5" /></svg>';
                    document.getElementById("cgm-svg-ctr-final").classList.remove("bg-gray-100");
                    document.getElementById("cgm-svg-ctr-final").classList.add("bg-blue-100");
                    document.getElementById("cgm-svg-ctr-final").parentNode.classList.add("text-blue-600");
                    document.getElementById("cgm-svg-ctr-final").parentNode.classList.add("dark:text-blue-500");


                }
                if (extractionStatus.mealsAndFitnes == true) {
                    stepCount++;
                    document.getElementById("mealsAndFitnes-svg-ctr").innerHTML = '<svg class="w-3.5 h-3.5 text-blue-600 lg:w-4 lg:h-4 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5" /></svg>';
                    document.getElementById("mealsAndFitnes-svg-ctr").classList.remove("bg-gray-100");
                    document.getElementById("mealsAndFitnes-svg-ctr").classList.add("bg-blue-100");
                    document.getElementById("mealsAndFitnes-svg-ctr").parentNode.classList.add("text-blue-600");
                    document.getElementById("mealsAndFitnes-svg-ctr").parentNode.classList.add("dark:text-blue-500");

                    //stopTimer
                }


                if (stepCount > workflowStepCount) {
                    workflowStepCount = stepCount;
                    fetchStudyGrid();
                }
                if (stepCount == totalSteps) {
                    document.querySelector(".migration-success").classList.remove("hidden");
                    document.querySelector(".migration-alert").classList.add("hidden");
                }
                else {
                    document.querySelector(".migration-alert").classList.remove("hidden");
                    document.querySelector(".migration-success").classList.add("hidden");

                }

            });
    }

}
function clearUploadStudyGrid() {
    const gridDiv = document.getElementById("serverDataGrid");
    if (gridDiv) {
        gridDiv.innerHTML = "";
    }
}
function fetchStudyGrid() {
    clearUploadStudyGrid();
    initUploadStudyGrid();
}

function getDatabaseProcessingStatus(studyId) {
    return new Promise((resolve, reject) => {
        fetchData(
            `/research-study/database/processing/status?studyId=${studyId}`,
            (res, error) => {
                if (error) {
                    reject(error); // Reject the Promise with the error
                } else {
                    resolve(res.data); // Resolve the Promise with the result
                }
            },
        );
    });
}

function getDatabaseExtractionStatus(studyId) {
    return new Promise((resolve, reject) => {
        fetchData(
            `/research-study/database/extraction/status?studyId=${studyId}`,
            (res, error) => {
                if (error) {
                    reject(error); // Reject the Promise with the error
                } else {
                    resolve(res.data); // Resolve the Promise with the result
                }
            },
        );
    });
}

function initateDbExtraction(studyId) {
    let params = {};
    return new Promise((resolve, reject) => {
        postData(
            `/research-study/database/extraction?studyId=${studyId}`,
            params,
            (res) => {
                if (res && res.status === "success") {
                    resolve(true);
                } else {
                    resolve(false);
                }
            },
        );
    });
}

function fetchDBProcessingStatus(studyId, uploadedNow = false) {
    if (uploadSucceeded == true) {
        getDatabaseProcessingStatus(studyId)
            .then(async (processingStatus) => {
                if (processingStatus?.showProcess == true) {
                    dbProcessingStatus = true;
                    if (processingStatus?.mealsAndFitnessMigrationExists == true) {
                        totalSteps = 4;
                        document.getElementById("mealsAndFitnes-ctr-final").classList.remove("hidden");
                        document.getElementById("cgm-ctr-middle").classList.remove("hidden");
                        document.getElementById("cgm-ctr-final").classList.add("hidden");
                    }
                    else {
                        totalSteps = 3;
                        document.getElementById("mealsAndFitnes-ctr-final").classList.add("hidden");
                        document.getElementById("cgm-ctr-middle").classList.add("hidden");
                        document.getElementById("cgm-ctr-final").classList.remove("hidden");

                    }

                    if (uploadedNow == true) {
                        dbExtractionStatus = true;
                        showMigrationWorkflow(); // Call instantly
                        setInterval(() => {
                            showMigrationWorkflow();
                        }, 60000); // Repeat every 1 minute
                    } else {
                        dbExtractionStatus = true;
                        showMigrationWorkflow(); // Call instantly
                        setInterval(() => {
                            showMigrationWorkflow();
                        }, 60000); // Repeat every 1 minute
                    }

                    // fileInput.disabled = true;
                    // uploadStudyFile.disabled = true;
                }
            });
    }
}
