let drawer;
let editActive = false;
let studyArchived = false;
let sourceInvestigators = [];
let primaryInvestigatorContent = [];
let nominatedPrincipalInvestigatorContent = [];
let investigatorsListContent = [];
let coinvestigatorsListContent = [];
let teamOfAuthorsContent = [];
let isSubmitting = false;
let firstLoad = true;
let publicationFragment;
let publication_count = 0;
let citation_id_value = "";

let citationDataSource = "Manual";

const formFields = [
  "deviceId",
  "deviceName",
  "sourcePlatform",
  "patientId",
  "fileFormat",
  "cgmTracingFile",
];

document.addEventListener("DOMContentLoaded", async function () {
  const paths = window.location.pathname.split("/");
  const id = paths[paths.length - 1];
  const studyId = id || "";

  const svgIcons = {
    "/studies/dashboard": `<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 6.66667H5.33333V0H0V6.66667ZM0 12H5.33333V8H0V12ZM6.66667 12H12V5.33333H6.66667V12ZM6.66667 0V4H12V0H6.66667Z" fill="#737373" />
</svg>`,
    "/studies/population": `<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7368 11.4545H3.78947C3.44211 11.4545 3.14484 11.33 2.89768 11.081C2.65011 10.8315 2.52632 10.5318 2.52632 10.1818V1.27273C2.52632 0.922727 2.65011 0.623 2.89768 0.373545C3.14484 0.124515 3.44211 0 3.78947 0H7.68947C7.85789 0 8.01853 0.0318182 8.17137 0.0954545C8.32379 0.159091 8.45789 0.249242 8.57368 0.365909L11.6368 3.45227C11.7526 3.56894 11.8421 3.70406 11.9053 3.85764C11.9684 4.01164 12 4.17348 12 4.34318V10.1818C12 10.5318 11.8764 10.8315 11.6293 11.081C11.3817 11.33 11.0842 11.4545 10.7368 11.4545ZM1.26316 14C0.915789 14 0.618526 13.8755 0.371368 13.6265C0.12379 13.377 0 13.0773 0 12.7273V4.45455C0 4.27424 0.0606317 4.123 0.181895 4.00082C0.302737 3.87906 0.452632 3.81818 0.631579 3.81818C0.810526 3.81818 0.960632 3.87906 1.08189 4.00082C1.20274 4.123 1.26316 4.27424 1.26316 4.45455V12.7273H7.57895C7.75789 12.7273 7.908 12.7884 8.02926 12.9105C8.1501 13.0323 8.21053 13.1833 8.21053 13.3636C8.21053 13.5439 8.1501 13.695 8.02926 13.8167C7.908 13.9389 7.75789 14 7.57895 14H1.26316ZM8.21053 4.45455H10.7368L7.57895 1.27273V3.81818C7.57895 3.99848 7.63958 4.14973 7.76084 4.27191C7.88168 4.39367 8.03158 4.45455 8.21053 4.45455Z" fill="#737373"/>
</svg>`,
    "/studies/all": `<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7368 11.4545H3.78947C3.44211 11.4545 3.14484 11.33 2.89768 11.081C2.65011 10.8315 2.52632 10.5318 2.52632 10.1818V1.27273C2.52632 0.922727 2.65011 0.623 2.89768 0.373545C3.14484 0.124515 3.44211 0 3.78947 0H7.68947C7.85789 0 8.01853 0.0318182 8.17137 0.0954545C8.32379 0.159091 8.45789 0.249242 8.57368 0.365909L11.6368 3.45227C11.7526 3.56894 11.8421 3.70406 11.9053 3.85764C11.9684 4.01164 12 4.17348 12 4.34318V10.1818C12 10.5318 11.8764 10.8315 11.6293 11.081C11.3817 11.33 11.0842 11.4545 10.7368 11.4545ZM1.26316 14C0.915789 14 0.618526 13.8755 0.371368 13.6265C0.12379 13.377 0 13.0773 0 12.7273V4.45455C0 4.27424 0.0606317 4.123 0.181895 4.00082C0.302737 3.87906 0.452632 3.81818 0.631579 3.81818C0.810526 3.81818 0.960632 3.87906 1.08189 4.00082C1.20274 4.123 1.26316 4.27424 1.26316 4.45455V12.7273H7.57895C7.75789 12.7273 7.908 12.7884 8.02926 12.9105C8.1501 13.0323 8.21053 13.1833 8.21053 13.3636C8.21053 13.5439 8.1501 13.695 8.02926 13.8167C7.908 13.9389 7.75789 14 7.57895 14H1.26316ZM8.21053 4.45455H10.7368L7.57895 1.27273V3.81818C7.57895 3.99848 7.63958 4.14973 7.76084 4.27191C7.88168 4.39367 8.03158 4.45455 8.21053 4.45455Z" fill="#737373"/>
</svg>`,
    "/studies/mystudies": `<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7368 11.4545H3.78947C3.44211 11.4545 3.14484 11.33 2.89768 11.081C2.65011 10.8315 2.52632 10.5318 2.52632 10.1818V1.27273C2.52632 0.922727 2.65011 0.623 2.89768 0.373545C3.14484 0.124515 3.44211 0 3.78947 0H7.68947C7.85789 0 8.01853 0.0318182 8.17137 0.0954545C8.32379 0.159091 8.45789 0.249242 8.57368 0.365909L11.6368 3.45227C11.7526 3.56894 11.8421 3.70406 11.9053 3.85764C11.9684 4.01164 12 4.17348 12 4.34318V10.1818C12 10.5318 11.8764 10.8315 11.6293 11.081C11.3817 11.33 11.0842 11.4545 10.7368 11.4545ZM1.26316 14C0.915789 14 0.618526 13.8755 0.371368 13.6265C0.12379 13.377 0 13.0773 0 12.7273V4.45455C0 4.27424 0.0606317 4.123 0.181895 4.00082C0.302737 3.87906 0.452632 3.81818 0.631579 3.81818C0.810526 3.81818 0.960632 3.87906 1.08189 4.00082C1.20274 4.123 1.26316 4.27424 1.26316 4.45455V12.7273H7.57895C7.75789 12.7273 7.908 12.7884 8.02926 12.9105C8.1501 13.0323 8.21053 13.1833 8.21053 13.3636C8.21053 13.5439 8.1501 13.695 8.02926 13.8167C7.908 13.9389 7.75789 14 7.57895 14H1.26316ZM8.21053 4.45455H10.7368L7.57895 1.27273V3.81818C7.57895 3.99848 7.63958 4.14973 7.76084 4.27191C7.88168 4.39367 8.03158 4.45455 8.21053 4.45455Z" fill="#737373"/>
</svg>`,
  };
  // Check if the pageTabs element exists
  const pageTabs = document.getElementById("pageTabs");
  if (pageTabs) {
    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];

    // Get the active route path dynamically (replace with your logic)
    const activeRoutePath = document.referrer.includes("all")
      ? "/studies/all"
      : "/studies/mystudies";

    // Helper function to create an SVG wrapper
    const createSvgWrapper = (svgHTML) => {
      const iconWrapper = document.createElement("div");
      iconWrapper.innerHTML = svgHTML;
      iconWrapper.classList.add("mr-2");
      return iconWrapper;
    };

    // Populate tabs only if empty
    if (pageTabs.innerHTML.trim() === "") {
      tabs.forEach((tab) => {
        const li = document.createElement("li");
        li.className = "mr-2 hidden";
        li.setAttribute("privilege-action-buttons-links", tab.text);
        const a = document.createElement("a");
        a.href = tab.href;
        a.textContent = tab.text;
        a.className =
          tab.href === activeRoutePath
            ? "flex items-center bg-white border border-b-0 text-gray-500 rounded-t-lg py-2 px-4 text-sm font-medium text-center mb-[-1px] active"
            : "flex items-center text-gray-500 rounded-t-lg py-2 px-4 text-sm font-medium text-center hover:text-gray-600 hover:bg-gray-50";

        // Add SVG if available
        const svgHTML = svgIcons[tab.href];
        if (svgHTML) {
          a.prepend(createSvgWrapper(svgHTML));
        }

        li.appendChild(a);
        pageTabs.appendChild(li);
      });
    } else {
      // Add SVG icons dynamically to existing tabs
      pageTabs.querySelectorAll("li a").forEach((anchor) => {
        const href = anchor.getAttribute("href");
        const svgHTML = svgIcons[href];
        if (svgHTML && !anchor.querySelector("div")) {
          // Avoid duplicate icons
          anchor.prepend(createSvgWrapper(svgHTML));
        }
      });
    }
  } else {
    console.error("#pageTabs element not found.");
  }

  document.querySelectorAll(".save-edit-field").forEach((element) => {
    element.addEventListener("click", async function (event) {
      let valid = true;
      let clickedElement = element;
      let elementId = clickedElement.getAttribute("data-id");
      let elementType = clickedElement.getAttribute("data-type");
      let categoryType = clickedElement.getAttribute("data-category");
      showLoading(elementId + "Loader");
      const inputField = document.getElementById(
        elementId + (elementType == "dropdown" ? "Dropdown" : "Input")
      );
      let newValue = DOMPurify.sanitize(inputField.value.trim());
      let apiURL;
      if (elementId == "end_date" || elementId == "start_date") {
        valid = validateDates(elementId);
        if (!valid) {
          hideLoading(elementId + "Loader");
          return;
        }
      }
      if (elementId == "nct_number" && newValue != "") {
        valid = /^NCT\d{8}$/.test(
          document.getElementById("nct_numberInput").value
        );
        if (!valid) {
          showToast(
            "NCT number should be started with 'NCT' followed by 8 digits!",
            "error"
          );
          hideLoading(elementId + "Loader");
          return;
        }
      }
      if (elementId == "title" && newValue == "") {
        showToast("Unable to delete the Study name", "error");
        hideLoading(elementId + "Loader");
        return;
      }
      if (elementId == "end_date" || elementId == "start_date") {
        newValue == "" ? (newValue = null) : "";
      }
      // if (newValue !== '') {
      if (categoryType == "basic" || categoryType == "advanced") {
        apiURL = `/research-studies/study-data/${studyId}`;
      } else if (categoryType == "investigator") {
        apiURL = `/investigator`;
      }
      let listInput;
      if (
        elementId == "primaryInvestigator" ||
        elementId == "investigatorsList"
      ) {
        listInput = JSON.stringify({
          studyId: studyId,
          practitionerNames:
            elementId == "primaryInvestigator"
              ? primaryInvestigatorContent
              : investigatorsListContent,
          type:
            elementId == "primaryInvestigator" ? "investigator" : "studyTeam",
        });
      } else if (
        elementId == "nominatedPrincipalInvestigator" ||
        elementId == "coinvestigatorsList"
      ) {
        listInput = JSON.stringify({
          studyId: studyId,
          practitionerNames:
            elementId == "nominatedPrincipalInvestigator"
              ? nominatedPrincipalInvestigatorContent
              : coinvestigatorsListContent,
          type:
            elementId == "nominatedPrincipalInvestigator"
              ? "nominatedPrincipalInvestigator"
              : "coInvestigator",
        });
      } else {
        listInput = JSON.stringify({
          [elementId]: newValue,
        });
      }
      if (valid) {
        try {
          // Send the user's message to the API
          const response = await fetch(apiURL, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: listInput,
          });

          if (!response.ok) {
            throw new Error("Failed to fetch response from the API.");
          }

          const data = await response.json();
          if (data.status === "success") {
            showToast("Study updated successfully!", "success");
            if (elementId == "primaryInvestigator") {
              await loadInvestigatorSource();
              assignValues("primaryInvestigator", newValue);
            } else if (elementId == "nominatedPrincipalInvestigator") {
              await loadInvestigatorSource();
              assignValues("nominatedPrincipalInvestigator", newValue);
            } else if (elementId == "coinvestigatorsList") {
              await loadInvestigatorSource();
              let investigatorList = document.querySelector(
                "#coinvestigatorsList"
              );
              investigatorList.innerHTML = "";
              if (coinvestigatorsListContent.length > 0) {
                coinvestigatorsListContent?.forEach((investigator) => {
                  const listItem = document.createElement("li");
                  listItem.textContent = investigator;
                  listItem.style.padding = "4px 0px 4px 0px";
                  investigatorList.appendChild(listItem);
                });
              }
              assignValues(
                "coinvestigatorsList",
                coinvestigatorsListContent[0]
              );
            } else if (elementId == "investigatorsList") {
              await loadInvestigatorSource();
              let investigatorList =
                document.querySelector("#investigatorsList");
              investigatorList.innerHTML = "";
              if (investigatorsListContent.length > 0) {
                investigatorsListContent?.forEach((investigator) => {
                  const listItem = document.createElement("li");
                  listItem.textContent = investigator;
                  listItem.style.padding = "4px 0px 4px 0px";
                  investigatorList.appendChild(listItem);
                });
              }
              assignValues("investigatorsList", investigatorsListContent[0]);
            } else {
              document.getElementById(elementId).innerText = newValue;
              assignValues(elementId, newValue);
            }
            // Hide input field, show the original element
            if (newValue == "") {
              // clickedElement.parentElement.classList.remove("hidden");
              document
                .getElementById("add" + elementId)
                ?.classList.remove("hidden");
              document.getElementById(elementId)?.classList.add("hidden");
            } else {
              document.getElementById(elementId)?.classList.remove("hidden");
              document
                .getElementById("add" + elementId)
                ?.classList?.add("hidden");
            }
            inputField.parentElement?.classList.add("hidden");
            editActive = false;
          } else {
            showToast(data.message, "error");
          }
        } catch (e) {
          console.error("Error fetching API response:", e);
          // showToast("Something went wrong!", "error");
        }
      }

      // }

      editActive = false;
      hideLoading(elementId + "Loader");
    });
  });

  document.querySelectorAll(".cancel-edit-field").forEach((element) => {
    element.addEventListener("click", function (event) {
      const clickedElement = element;
      const elementId = clickedElement.getAttribute("data-id");
      let elementType = clickedElement.getAttribute("data-type");
      const inputField = document.getElementById(
        elementId + (elementType == "dropdown" ? "Dropdown" : "Input")
      );
      clickedElement.parentElement?.classList?.remove("hidden");
      let fieldValue = DOMPurify.sanitize(
        document.getElementById(elementId).textContent
      );
      if (!fieldValue || fieldValue == "") {
        document.getElementById("add" + elementId).classList.remove("hidden");
      } else {
        document.getElementById(elementId)?.classList?.remove("hidden");
      }
      if (elementType == "dropdown") {
        document
          .getElementById(elementId + "Dropdown")
          .parentElement.classList.add("hidden");
      } else {
        inputField.parentElement.classList.add("hidden");
      }
      editActive = false;
    });
  });

  dispalyIdElement?.addEventListener("blur", handleBlur, true);

  async function handleBlur(event) {
    if (firstLoad) {
      firstLoad = false; // Allow blur after first time
      return;
    }
    if (isSubmitting) {
      event.stopPropagation(); // Stops blur from triggering
      return;
    }
    let element = document.getElementById("researchStudyIdentifier");
    let errElement = document.getElementById("researchStudyIdentifier-error");

    let studyDisplayId = DOMPurify.sanitize(element.value);
    const validField = await isValidStudyDisplayId(studyDisplayId);
    if (validField) {
      let errorClass = "border-[#e23636]";
      showStudyDisplayLoader();
      const exists = await checkIdExistence(studyDisplayId);
      if (exists) {
        hideStudyDisplayLoader();
        errElement.textContent =
          "This ID is already taken. Please choose another one.";
        element.classList.add(errorClass);
        // isValid = false;
      } else {
        hideStudyDisplayLoader();

        errElement.textContent = "";
        element.classList.remove(errorClass);
        // isValid = true;
      }
    } else {
      if (studyDisplayId != "" && studyDisplayId != undefined)
        document
          .getElementById("researchStudyIdentifier")
          .classList.add("border-[#e23636]");
    }
    hideStudyDisplayLoader();
  }
  document
    .getElementById("publicationTitle")
    ?.addEventListener("blur", async function () {
      let element = document.getElementById("publicationTitle");
      if (document.getElementById("publicationTitle")?.value != "") {
        element.classList.remove("border-[#e23636]");
      }
    });

  document
    .getElementById("update-publish-btn")
    ?.addEventListener("click", async function () {
      let isValid = true;
      let fieldsToValidate = [];
      fieldsToValidate = ["publicationTitle"];
      // Validate all fields
      fieldsToValidate.forEach((fieldId) => {
        const isFieldValid = validateField(fieldId);
        if (!isFieldValid) {
          isValid = false;
        }
      });
      let publicationTitle = DOMPurify.sanitize(
        document.getElementById("publicationTitle").value
      );
      let publicationDate = DOMPurify.sanitize(
        document.getElementById("publicationDate").value
      );
      let publicationDoi = DOMPurify.sanitize(
        document.getElementById("publicationDoi").value
      );
      let pubMedId = DOMPurify.sanitize(
        document.getElementById("publicationPubmed").value
      );
      if (isValid) {
        if (publicationDoi != "") {
          isValid = /^10\.\d{4,9}\/[\S]+$/.test(
            document.getElementById("publicationDoi").value
          );
          if (!isValid) {
            document.getElementById("publicationDoi-error").textContent =
              "Invalid format. Start with '10.', followed by 4 to 9 digits, a '/' separator, and a non-empty suffix";
            return;
          } else {
            let pubmedExists = await pubmedIdExists();
            console.log("pubmedExists", pubmedExists);
            if (pubmedExists) {
              isValid = false;
              document.getElementById("publicationPubmed-error").textContent =
                "This Pubmed Id already exists";
              disablePubmedDataFetching();
              return;
            } else {
              document.getElementById("publicationPubmed-error").textContent =
                "";
            }
          }
        }
        if (pubMedId != "") {
          isValid = isValidPubmed(pubMedId);
          if (!isValid) {
            return;
          } else {
            let doiExists = await pubmedIdExists();
            console.log("doiExists", doiExists);
            if (doiExists) {
              isValid = false;
              document.getElementById("publicationDoi-error").textContent =
                "This Publication DOI already exists";
              disableDOIDataFetching();
              return;
            } else {
              document.getElementById("publicationDoi-error").textContent = "";
            }
          }
        }
      }
      if (isValid) {
        teamOfAuthorsContent = authorTeamCollaboration;

        showLoading("publishLoader");
        if (
          citation_id_value == "" ||
          citation_id_value == undefined ||
          citation_id_value == null
        ) {
          console.log("authorTeamCollaboration", authorTeamCollaboration);
          const data = {
            publication_title: publicationTitle,
            publication_date: publicationDate,
            publication_doi: publicationDoi,
            pubmed_id: pubMedId,
            collaboration_team: {
              studyId: studyId,
              coAuthorsName: teamOfAuthorsContent,
            },
            citation_data_source: citationDataSource,
          };
          try {
            // Send the user's message to the API
            const response = await fetch("/research-study/citations", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error("Failed to fetch response from the API.");
            }

            const res = await response.json();
            let publicationClone;
            if (res.status === "success") {
              const container = document.getElementById("publicationContainer");
              await loadSourceAuthors();
              let citation_identifier_val = res.data.citationResponse.citation_id

              publicationClone = publicationFragment.cloneNode(true);

              // Set data-citation_identifier for all fields
              publicationClone.querySelectorAll(".study-edit-field").forEach((field) => {
                field.setAttribute("data-citation_identifier", citation_identifier_val);
              });

              // Dynamically populate values into the cloned fragment using the helper function
              updateElement(publicationClone, "publication_medid", "addpublication_medid", pubMedId);
              updateElement(publicationClone, "publication_doi", "addpublication_doi", publicationDoi);
              updateElement(publicationClone, "publication_title", "addpublication_title", publicationTitle);
              updateElement(publicationClone, "publication_date", "addpublication_date", publicationDate);

              // Handle authors separately
              const authorsElement = publicationClone.querySelector("#teamOfAuthors");
              const addAuthorsElement = publicationClone.querySelector("#addteamOfAuthors");
              if (authorsElement) {
                const authorsContent = teamOfAuthorsContent?.length > 0 ? teamOfAuthorsContent.join(", ") : "";
                authorsElement.textContent = authorsContent;
                if (authorsContent) {
                  authorsElement.classList.remove("hidden");
                  addAuthorsElement?.classList.add("hidden");
                } else {
                  authorsElement.classList.add("hidden");
                  addAuthorsElement?.classList.remove("hidden");
                }
              }

              // Clear container if publication_count is 0
              if (publication_count === 0) {
                container.innerHTML = "";
              }

              // Prepend the populated fragment to the container
              container.prepend(publicationClone);
              publication_count++;
              document.querySelectorAll(".study-item").forEach((element) => {
                element.addEventListener("mouseenter", function (event) {
                  if (isLoggedIn() && isProfileCompleted() && !studyArchived) {
                    const clickedElement = element.children[0].children[0];
                    if (!editActive || editActive == false)
                      clickedElement.classList.remove("hidden");
                  }
                });
                element.addEventListener("mouseleave", function (event) {
                  if (isLoggedIn() && isProfileCompleted() && !studyArchived) {
                    const clickedElement = element.children[0].children[0];
                    clickedElement.classList.add("hidden");
                  }
                });
              });
              studyPublicationsEdit();

              hideLoading("publishLoader");
              hideUnauthorizedActions();
              showToast("Publication Added successfully!", "success");
            } else {
              hideLoading("publishLoader");
              showToast("Issue in Adding Publication", "error");
            }
            hideLoading("publishLoader");
            hideDrawerPublish();
          } catch (e) {
            console.error("Error fetching API response:", e);
            showToast("Something went wrong!", "error");
            hideLoading("publishLoader");
          }
        } else {
          const data = {
            publication_title: publicationTitle,
            publication_date: publicationDate,
            publication_doi: publicationDoi,
            pubmed_id: pubMedId,
            collaboration_team: {
              studyId: studyId,
              coAuthorsName: teamOfAuthorsContent,
            },
            citation_data_source: citationDataSource,
          };
          try {
            // Send the user's message to the API
            const response = await fetch(
              "/research-study/publication-author?citationID=" +
              citation_id_value,
              {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
              }
            );

            if (!response.ok) {
              throw new Error("Failed to fetch response from the API.");
            }

            const res = await response.json();
            if (res.status === "success") {
              await loadSourceAuthors();

              let element_num;

              const childElement = document.querySelector(`[data-citation_identifier="${citation_id_value}"]`);
              const studyItemContainer = childElement.closest('.publication-items');
              // Update publication fields
              updateElement(studyItemContainer, "publication_medid", "addpublication_medid", pubMedId);
              updateElement(studyItemContainer, "publication_doi", "addpublication_doi", publicationDoi);
              updateElement(studyItemContainer, "publication_title", "addpublication_title", publicationTitle);
              updateElement(studyItemContainer, "publication_date", "addpublication_date", publicationDate);
              // Update authors field
              const authorsElement = studyItemContainer.querySelector("#teamOfAuthors");
              const addAuthorsElement = studyItemContainer.querySelector("#addteamOfAuthors");
              if (authorsElement) {
                const authorsContent = teamOfAuthorsContent?.length > 0 ? teamOfAuthorsContent.join(", ") : "";
                authorsElement.textContent = authorsContent;
                if (authorsContent) {
                  authorsElement.classList.remove("hidden");
                  addAuthorsElement?.classList.add("hidden");
                } else {
                  authorsElement.classList.add("hidden");
                  addAuthorsElement?.classList.remove("hidden");
                }
              }
              hideLoading("publishLoader");
              showToast("Publication updated successfully!", "success");
            } else {
              hideLoading("publishLoader");
              showToast("Issue in updating Publication", "error");
            }
            hideLoading("publishLoader");
            hideDrawerPublish();
          } catch (e) {
            console.error("Error fetching API response:", e);
            showToast("Something went wrong!", "error");
            hideLoading("publishLoader");
          }
        }
      }
    });
  document
    .getElementById("close-publish-drawn")
    ?.addEventListener("click", function () {
      hideDrawerPublish();
    });
});

function showValue(eleClass) {
  document.querySelectorAll("." + eleClass).forEach((element) => {
    element.style.display = "block";
  });
}
/******************/

function hideAllLoader() {
  hideLoading("titleLoader");
  hideLoading("start_dateLoader");
  hideLoading("end_dateLoader");
  hideLoading("nct_numberLoader");
  hideLoading("primaryInvestigatorLoader");
  hideLoading("descriptionLoader");
  hideLoading("study_locationLoader");
  hideLoading("treatment_modalityLoader");
  hideLoading("publication_titleLoader");
  hideLoading("publication_dateLoader");
  hideLoading("publication_doiLoader");
  hideLoading("funding_sourceLoader");
}
async function loadStudyTeamDetails(studyId) {
  showLoading("investigatorsListLoader");
  fetchData(
    `/research-study/study-team-members?studyId=` + studyId,
    async (responseData, error) => {
      if (!error) {
        const result = responseData.data.studyTeam;
        // Split the investigators string into an array

        let investigatorList = document.querySelector("#investigatorsList");
        if (result.length > 0) {
          result?.forEach((investigator) => {
            const listItem = document.createElement("li");
            listItem.textContent = investigator.name;
            listItem.setAttribute("data-value", investigator.id);
            listItem.style.padding = "4px 0 4px 0px";
            investigatorList.appendChild(listItem);
            investigatorsListContent.push(investigator.name);
          });
        }
        assignValues("investigatorsList", investigatorsListContent[0]);
      } else {
        console.error("Error fetching study info:", error);
      }
    }
  );
  hideLoading("investigatorsListLoader");
}

async function loadPrimaryInvestigator(studyId) {
  showLoading("primaryInvestigatorLoader");
  fetchData(
    `/research-study/principal-investigator?studyId=` + studyId,
    async (responseData, error) => {
      if (!error) {
        const result = responseData.data.studyTeam;
        if (result.length > 0) {
          result?.forEach((investigator) => {
            assignValues("primaryInvestigator", investigator.name);
            primaryInvestigatorContent.push(investigator.name);
          });
        }
      } else {
        console.error("Error fetching primary investigator info:", error);
      }
    }
  );
  hideLoading("primaryInvestigatorLoader");
}
async function loadNominatedPrincipalInvestigator(studyId) {
  showLoading("nominatedPrincipalInvestigatorLoader");
  fetchData(
    `/research-study/nominated-principal-investigator?studyId=` + studyId,
    async (responseData, error) => {
      if (!error) {
        const result = responseData.data.studyTeam;
        if (result.length > 0) {
          result?.forEach((investigator) => {
            assignValues("nominatedPrincipalInvestigator", investigator.name);
            nominatedPrincipalInvestigatorContent.push(investigator.name);
          });
        }
      } else {
        console.error("Error fetching primary investigator info:", error);
      }
    }
  );
  hideLoading("nominatedPrincipalInvestigatorLoader");
}
async function loadCoInvestigator(studyId) {
  showLoading("coinvestigatorsListLoader");
  fetchData(
    `/research-study/co-investigator?studyId=` + studyId,
    async (responseData, error) => {
      if (!error) {
        const result = responseData.data.studyTeam;
        let investigatorList = document.querySelector("#coinvestigatorsList");
        if (result.length > 0) {
          result?.forEach((investigator) => {
            const listItem = document.createElement("li");
            listItem.textContent = investigator.name;
            listItem.setAttribute(
              "data-value",
              investigator.practitioner_party_id
            );
            listItem.style.padding = "4px 0 4px 0px";
            investigatorList.appendChild(listItem);
            coinvestigatorsListContent.push(investigator.name);
          });
          assignValues("coinvestigatorsList", coinvestigatorsListContent[0]);
        }
      } else {
        console.error("Error fetching coinvestigators info:", error);
      }
    }
  );
  hideLoading("coinvestigatorsListLoader");
}
async function loadInvestigatorSource() {
  return new Promise((resolve, reject) => {
    fetchData(`/investigator`, async (responseData, error) => {
      if (!error) {
        let result = responseData.data.investigators;
        if (result && Object.keys(result).length != 0) {
          sourceInvestigators = result;
        }
      } else {
        console.error("Error fetching investigator info:", error);
      }
      resolve();
    });
  });
}
async function loadSourceAuthors() {
  return new Promise((resolve, reject) => {
    fetchData(`/authors`, async (responseData, error) => {
      if (!error) {
        let result = responseData.data.Authors;
        if (result && Object.keys(result).length != 0) {
          sourceAuthors = result;
        }
      } else {
        console.error("Error fetching investigator info:", error);
      }
      resolve();
    });
  });
}

async function loadStudyDetails(studyId) {
  showLoading("titleLoader");
  showLoading("start_dateLoader");
  showLoading("end_dateLoader");
  showLoading("nct_numberLoader");
  showLoading("primaryInvestigatorLoader");
  showLoading("descriptionLoader");
  showLoading("study_locationLoader");
  showLoading("treatment_modalityLoader");
  showLoading("publication_titleLoader");
  showLoading("publication_dateLoader");
  showLoading("publication_doiLoader");
  showLoading("funding_sourceLoader");
  if (studyId) {
    await fetchData(
      `/research-study?studyId=` + studyId,
      async (responseData, error) => {
        if (!error) {
          // const studyData = await responseData.json();
          const result = responseData.data.studyDetails;

          if (
            result.created_by != null &&
            result.created_by != getLoggedInUser()
          ) {
            toggleTooltip("addParticipant-btn", true);
            toggleTooltip("upload-study-btn", true);
            toggleTooltip("settings-study-btn", true);
            toggleTooltip("addPublication-btn", true);
          } else {
            if (studyArchived) {
              toggleTooltip("addParticipant-btn", true);
              toggleTooltip("upload-study-btn", true);
              toggleTooltip("settings-study-btn", false);
              toggleTooltip("addPublication-btn", true);
            } else {
              document.querySelectorAll(".study-item").forEach((element) => {
                element.addEventListener("mouseenter", function (event) {
                  if (isLoggedIn() && isProfileCompleted() && !studyArchived) {
                    const clickedElement = element.children[0].children[0];
                    if (!editActive || editActive == false)
                      clickedElement.classList.remove("hidden");
                  }
                });
                element.addEventListener("mouseleave", function (event) {
                  if (isLoggedIn() && isProfileCompleted() && !studyArchived) {
                    const clickedElement = element.children[0].children[0];
                    clickedElement.classList.add("hidden");
                  }
                });
              });

              studyPublicationsEdit();
              toggleTooltip("addParticipant-btn", false);
              toggleTooltip("upload-study-btn", false);
              toggleTooltip("settings-study-btn", false);
            }
          }

          result.title != null ? assignValues("title", result.title) : "";
          result.start_date != null
            ? assignValues("start_date", result.start_date)
            : "";
          result.end_date != null
            ? assignValues("end_date", result.end_date)
            : "";
          result.nct_number != null
            ? assignValues("nct_number", result.nct_number)
            : "";
          result.description != null
            ? assignValues("description", result.description)
            : "";
          result.site_id != null
            ? assignValues("study_location", result.site_id)
            : "";
          result.treatment_modalities != null
            ? assignValues("treatment_modality", result.treatment_modalities)
            : "";
          result.funding_source != null
            ? assignValues("funding_source", result.funding_source)
            : "";
          result.study_display_id != null
            ? assignValues("studyDisplayId", result.study_display_id)
            : "";

          getStudyVisibility()
            .then((visibilityData) => {
              visibilityData.forEach((element) => {
                const elements = document.getElementsByClassName(
                  "st-" + element.visibility_name.toLowerCase()
                );
                if (element.visibility_id == result.visibility) {
                  for (let el of elements) {
                    el.classList.remove("hidden");
                  }

                  const statusElements =
                    document.getElementsByClassName("st-status");
                  for (let el of statusElements) {
                    el.innerHTML = element.visibility_name;
                  }
                } else {
                  for (let el of elements) {
                    el.classList.add("hidden");
                  }
                }
              });
            })
            .catch((error) => {
              console.error("Error fetching data:", error);
            });
          hideAllLoader();
        } else {
          console.error("There was a problem with the fetch operation:", error);
          assignValues("title", "⚠️");
          assignValues("start_date", "⚠️");
          assignValues("end_date", "⚠️");
          assignValues("nct_number", "⚠️");
          assignValues("description", "⚠️");
          assignValues("primaryInvestigator", "⚠️");
          hideAllLoader();
        }
      }
    );
  }
  return;
}

function assignValues(eleClass, value) {
  document.querySelectorAll("." + eleClass).forEach((element) => {
    element.classList.remove("hidden");
    if (eleClass != "coinvestigatorsList" && eleClass != "investigatorsList") {
      element.innerHTML = value;
    }
    if (value !== "" && value !== undefined && value !== null) {
      hidePlaceholders(eleClass);
    }
  });
}
function hidePlaceholders(eleClass) {
  document.getElementById("add" + eleClass)?.classList?.add("hidden");
}

function clearField(field) {
  assignValues(field, "");
  document.getElementById(`add${field}`).classList.remove("hidden");
  document.getElementById(field).classList.add("hidden");
}
const createStudyForm = document.getElementById("createStudyForm");
const createSubmitButton = document.getElementById("save-study");
const dispalyIdElement = document.getElementById("researchStudyIdentifier");

if (createStudyForm) {
  showLoading("createStudyLoader");
  let container = document.querySelector(".visibility-container");
  getStudyVisibility()
    .then((visibilityData) => {
      visibilityData.forEach((element) => {
        createStudyRadioButton(element, container);
      });
      hideLoading("createStudyLoader");
    })
    .catch((error) => {
      console.error("Error fetching data:", error);
    })
    .finally(() => {
      hideLoading("createStudyLoader");
    });
  document.getElementById("title")?.addEventListener("blur", async function () {
    let element = document.getElementById("title");
    if (document.getElementById("title")?.value != "") {
      element.classList.remove("border-[#e23636]");
    }
  });

  createStudyForm.addEventListener("submit", async (e) => {
    e.preventDefault(); // Prevent the default form submission
    isSubmitting = true;
    document.getElementById("researchStudyIdentifier-error").textContent = "";
    document
      .getElementById("researchStudyIdentifier")
      .classList.remove("border-[#e23636]");
    let fieldsToValidate = [];
    let isValid = true;
    fieldsToValidate = ["title", "researchStudyIdentifier"];
    let element = document.getElementById("researchStudyIdentifier");
    let errorElement = document.getElementById("researchStudyIdentifier-error");
    let errorClass = "border-[#e23636]";
    // Validate all fields
    fieldsToValidate.forEach((fieldId) => {
      const isFieldValid = validateField(fieldId);
      if (!isFieldValid) {
        isValid = false;
      }
    });

    if (isValid) {
      const validField = await isValidStudyDisplayId(
        DOMPurify.sanitize(element.value)
      );
      if (validField) {
        showStudyDisplayLoader();
        const exists = await checkIdExistence(
          DOMPurify.sanitize(element.value)
        );
        if (exists) {
          errorElement.textContent =
            "This ID is already taken. Please choose another one.";
          element.classList.add(errorClass);
          isValid = false;
          hideStudyDisplayLoader();
        } else {
          errorElement.textContent = "";
          element.classList.remove(errorClass);
          isValid = true;
          hideStudyDisplayLoader();
        }
      } else {
        errorElement.textContent =
          "Invalid format. Use only letters and digits, up to 6 characters.";
        isValid = false;
      }
    }

    if (isValid) {
      //Submit
      const formData = new FormData(createStudyForm);
      const data = Object.fromEntries(formData.entries());
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          data[key] = DOMPurify.sanitize(data[key]);
        }
      }
      data.createdBy = localStorage.getItem("practitionerPartyId") || "";
      data.tenantId = localStorage.getItem("organizationPartyId") || "";

      postData(`/research-studies`, data, async (res) => {
        if (res && res.status === "success") {
          showToast("Study saved successfully!", "success");
          window.location.href = "/studies/mystudies";
        } else {
          //   const errorMessage = res?.errors || "Please try again later.";
          showToast("Something went wrong!", "error");
        }
      });
    }
    isSubmitting = false;
  });
  // Set flag BEFORE blur happens when clicking submit
  createSubmitButton.addEventListener(
    "mousedown",
    () => {
      isSubmitting = true;
    },
    true
  ); // Capture event early

  // Reset flag AFTER submission or if clicking outside
  document.addEventListener("mouseup", () => {
    setTimeout(() => {
      isSubmitting = false;
    }, 100);
  });
}
async function checkIdExistence(studyDisplayId) {
  return new Promise((resolve, reject) => {
    fetchRawData(`/research-study/${studyDisplayId}/exists`, (data, error) => {
      if (error) {
        reject(error); // Reject the Promise with the error
      } else {
        const exists = JSON.parse(data.data.exists);
        resolve(exists); // Resolve the Promise with the result
      }
    });
  });
}
async function isValidStudyDisplayId(studyDisplayId) {
  const isValidId = /^[a-zA-Z0-9]{1,6}$/.test(studyDisplayId);
  let errorClass = "border-[#e23636]";
  let errorElement = document.getElementById("researchStudyIdentifier-error");
  let element = document.getElementById("researchStudyIdentifier");
  if (!isValidId) {
    element.classList.add(errorClass);
    errorElement.textContent =
      "Invalid format. Use only letters and digits, up to 6 characters.";
    return false;
  } else {
    element.classList.remove(errorClass);
    errorElement.textContent = "";
    return true;
  }
}

function initTeamSelect(eleId, dataList, investigatorSourceList) {
  const ele = document.getElementById(eleId);
  if (!ele) {
    console.error(`Element with ID "${eleId}" not found.`);
    return;
  }

  if (ele.tomselect) {
    ele.tomselect.destroy();
  }

  new TomSelect("#" + eleId, {
    plugins: ["remove_button"],
    create: true,
    maxItems:
      eleId == "primaryInvestigatorDropdown" ||
        eleId == "nominatedPrincipalInvestigatorDropdown"
        ? 1
        : null,
    valueField: "investigator_name",
    labelField: "investigator_name",
    searchField: "investigator_name",
    options: investigatorSourceList,
    createFilter: function (input) {
      return input.length >= parseInt(2, 10);
    },
    onInitialize: function () {
      this.setValue(dataList);
      // }
    },
    onChange: function (value) {
      if (eleId == "primaryInvestigatorDropdown") {
        if (value != null && value.length > 0) {
          primaryInvestigatorContent = [value];
        } else {
          primaryInvestigatorContent = [];
        }
      } else if (eleId == "investigatorsListDropdown") {
        if (value != null && value.length > 0) {
          investigatorsListContent = value;
        } else {
          investigatorsListContent = [];
        }
      } else if (eleId == "nominatedPrincipalInvestigatorDropdown") {
        if (value != null && value.length > 0) {
          nominatedPrincipalInvestigatorContent = [value];
        } else {
          nominatedPrincipalInvestigatorContent = [];
        }
      } else if (eleId == "coinvestigatorsListDropdown") {
        if (value != null && value.length > 0) {
          coinvestigatorsListContent = value;
        } else {
          coinvestigatorsListContent = [];
        }
      }
    },
    onItemRemove: function (value, $item) { },
  });
}
function showStudyDisplayLoader() {
  document.getElementById("studyDisplayId-loader").classList.remove("hidden");
}

function hideStudyDisplayLoader() {
  document.getElementById("studyDisplayId-loader").classList.add("hidden");
}

function showPubmedIdLoader() {
  document.getElementById("publicationPubmedLoader").classList.remove("hidden");
}

function hidePubmedIdLoader() {
  document.getElementById("publicationPubmedLoader").classList.add("hidden");
}
function validateDates(eleId) {
  if (eleId == "start_date") {
    document.getElementById("end_dateInput").value =
      document.getElementById("end_dateInput").textContent;
  }
  if (eleId == "end_date") {
    document.getElementById("start_dateInput").value =
      document.getElementById("start_dateInput").textContent;
  }
  const startDateInput = DOMPurify.sanitize(
    document.getElementById("start_dateInput").value
  );
  const endDateInput = DOMPurify.sanitize(
    document.getElementById("end_dateInput").value
  );
  let valid = true;
  if (eleId == "end_date" && startDateInput == "") {
    valid = false;
    showToast("Please enter start date", "error");
  }
  if (eleId == "start_date" && endDateInput != "" && startDateInput == "") {
    valid = false;
    showToast("Remove end date first", "error");
  }
  if (startDateInput && endDateInput) {
    const startDate = convertToUTCFormat(startDateInput);
    const endDate = convertToUTCFormat(endDateInput);
    if (endDate > startDate) {
      valid = true;
    } else {
      valid = false;
      showToast("End date should be greater than Start date", "error");
    }
  }
  return valid;
}

function createStudyRadioButton(data, container) {
  const label = document.createElement("label");
  label.className = "flex items-start gap-2 mb-3";

  // Create the input (radio button)
  const input = document.createElement("input");
  input.type = "radio";
  input.name = "visibility";
  input.value = data.visibility_id;
  input.className = "mt-1 text-blue-500 focus:ring-blue-500";
  if (data.visibility_name == "Private") {
    input.checked = true;
  }
  const div = document.createElement("div");

  // Create the main title span
  const spanTitle = document.createElement("span");
  spanTitle.className = "block text-sm font-normal text-gray-800";
  spanTitle.textContent = data.visibility_name;

  const spanDescription = document.createElement("span");
  spanDescription.className = "block text-xs font-normal text-gray-500";
  spanDescription.textContent = data.visibility_description;
  div.appendChild(spanTitle);
  div.appendChild(spanDescription);
  label.appendChild(input);
  label.appendChild(div);
  container.appendChild(label);
}

function fetchStudyDisplayIdDetails(studyDisplayId) {
  return new Promise((resolve, reject) => {
    fetchData(`/orcid/user-details/${studyDisplayId}`, (data, error) => {
      hideStudyDisplayLoader(); // Ensure loader is hidden regardless of success or error
      if (error) {
        reject(error); // Reject the Promise with the error
      } else {
        resolve(data.data); // Resolve the Promise with the result
      }
    });
  });
}

function showDrawerPublish(eleId, citation_identifier) {
  const drawerElement = document.getElementById("drawer-publication");
  const options = {
    placement: "right",
    backdrop: true,
  };
  drawer = new Drawer(drawerElement, options);
  drawer.show();
  const publicationId = document.getElementById("publicationId");
  const publicationTitle = document.getElementById("publicationTitle");
  const publicationDate = document.getElementById("publicationDate");
  const publicationDoi = document.getElementById("publicationDoi");
  const publicationPubmed = document.getElementById("publicationPubmed");
  const publicationTitleError = document.getElementById(
    "publicationTitle-error"
  );
  const publicationPubmedError = document.getElementById(
    "publicationPubmed-error"
  );

  publicationTitle.classList.remove("border-[#e23636]");
  publicationTitleError.textContent = "";
  publicationPubmedError.textContent = "";
  hideDataFetchedMessage();
  hideDataFetchAlert();
  publicationPubmed.classList.remove("border-[#e23636]");
  publicationDoi.classList.remove("border-[#e23636]");
  disablePubmedDataFetching();
  disableDOIDataFetching();
  document.getElementById("publicationDoi-error").innerHTML = "";
  document.getElementById("publicationPubmed-error").innerHTML = "";
  authorTeamCollaboration = [];
  if (
    citation_identifier != null &&
    citation_identifier != "undefined" &&
    citation_identifier != ""
  ) {
    citation_id_value = citation_identifier;
    publicationId.value = citation_identifier;
    fetchData(
      `/research-study/citations?studyId=` + studyId,
      async (responseData, error) => {
        if (!error) {
          const result = responseData.data.citations;
          const citation = result.find(
            (c) => c.citation_id === citation_identifier
          );
          await loadSourceAuthors();
          publicationTitle.value = citation.publication_title;
          publicationDate.value = citation.publication_date;
          publicationDoi.value = citation.publication_doi;
          publicationPubmed.value = citation.pubmed_id;
          console.log("drawer result", result, publicationDetailsLoad);
          citationDataSource = citation.citation_data_source;
          if (
            citation.publication_doi != "" &&
            citation.publication_doi != null
          ) {
            publicationDetailsLoad = true;
            doiValue = citation.publication_doi;
          }
          if (citation.pubmed_id != "" && citation.pubmed_id != null) {
            publicationDetailsLoad = true;
            pubMedIdValue = citation.pubmed_id;
          }
          teamOfAuthorsContent = citation.citation_authors;
          initAuthorsTeam(sourceAuthors, teamOfAuthorsContent);

          //initTeamSelect("authorTeam", teamOfAuthorsContent, sourceAuthors);

          console.log("drawer resultend", result, publicationDetailsLoad);
        } else {
          console.error("Error fetching participant info:", error);
        }
      }
    );
  } else {
    citation_id_value = "";
    pubMedIdValue = "";
    doiValue = "";
    publicationDetailsLoad = false;
    publicationId.value = "";
    publicationTitle.value = "";
    publicationDate.value = "";
    publicationDoi.value = "";
    publicationPubmed.value = "";
    teamOfAuthorsContent = [];
    initAuthorsTeam(sourceAuthors, teamOfAuthorsContent);
    publicationDetailsLoad = false;
  }
  return;
}
function hideDrawerPublish() {
  drawer.hide();
}

async function loadPublications(studyId) {
  const container = document.getElementById("publicationContainer");

  // Fetch the publication fragment
  try {
    const response = await fetch(`/render-publication-fragment?count=${publication_count}&&citation_identifier=${citation_id_value}`);
    if (!response.ok) {
      throw new Error("Network response was not ok " + response.statusText);
    }
    const fragment = await response.text();
    const parser = new DOMParser();
    const doc = parser.parseFromString(fragment, "text/html");
    publicationFragment = doc.body.firstElementChild; // Ensure the fragment is properly set

    // Fetch publication data and populate the fragment
    return new Promise((resolve, reject) => {
      fetchData(`/research-study/citations?studyId=` + studyId, async (responseData, error) => {
        if (error) {
          console.error("Error fetching publications info:", error);
          reject(error);
          return;
        }

        const result = responseData.data.citations;
        let publicationClone;
        if (result.length !== 0) {
          showLoading("publicationDetailsLoader");

          for (const publication of result) {
            const publicationClone = publicationFragment.cloneNode(true); // Clone the fragment

            // Set data-citation_identifier for all fields
            publicationClone.querySelectorAll(".study-edit-field").forEach((field) => {
              field.setAttribute("data-citation_identifier", publication.citation_id);
            });

            // Dynamically populate values into the cloned fragment using the helper function
            updateElement(publicationClone, "publication_medid", "addpublication_medid", publication.pubmed_id);
            updateElement(publicationClone, "publication_doi", "addpublication_doi", publication.publication_doi);
            updateElement(publicationClone, "publication_title", "addpublication_title", publication.publication_title);
            updateElement(publicationClone, "publication_date", "addpublication_date", publication.publication_date);

            // Handle authors separately
            const authorsElement = publicationClone.querySelector("#teamOfAuthors");
            const addAuthorsElement = publicationClone.querySelector("#addteamOfAuthors");
            if (authorsElement) {
              const authorsContent = publication.citation_authors?.length > 0 ? publication.citation_authors.join(", ") : "";
              authorsElement.textContent = authorsContent;
              if (authorsContent) {
                authorsElement.classList.remove("hidden");
                addAuthorsElement?.classList.add("hidden");
              } else {
                authorsElement.classList.add("hidden");
                addAuthorsElement?.classList.remove("hidden");
              }
            }

            // Append the populated fragment to the container
            container.appendChild(publicationClone);
            publication_count++;
          }

          hideLoading("publicationDetailsLoader");
          hideUnauthorizedActions();
        } else {
          await loadPublicationFragment(publication_count);
          hideUnauthorizedActions();
        }

        resolve(); // Resolve the promise after processing
      });
    });
  } catch (error) {
    console.error("Error fetching publication fragment:", error);
    return; // Exit the function if the fragment cannot be fetched
  }


}

async function loadPublicationFragment(count, citation_identifier, action) {
  return new Promise(async (resolve, reject) => {
    const container = document.getElementById("publicationContainer");

    const publicationDiv = document.createElement("div");
    const res = await fetch(
      `/render-publication-fragment?count=${count}&&citation_identifier=${citation_identifier}`
    );
    const html = await res.text();
    publicationDiv.innerHTML = html;
    if (action == "prepend") {
      container.prepend(publicationDiv);
    } else {
      container.appendChild(publicationDiv);
    }
    resolve();
  });
}
function studyPublicationsEdit() {
  document.querySelectorAll(".study-edit-field").forEach((element) => {
    element.addEventListener("click", function (event) {
      const clickedElement = element;
      const elementId = clickedElement.getAttribute("data-id");
      let elementType = clickedElement.getAttribute("data-type");
      let categoryType = clickedElement.getAttribute("data-category");
      if (categoryType == "publication" || categoryType == "author") {
        let citation_id = clickedElement.getAttribute(
          "data-citation_identifier"
        );
        showDrawerPublish(elementId, citation_id);
        return;
      }

      let fieldValue = DOMPurify.sanitize(
        document.getElementById(elementId).textContent
      );

      // Hide the clicked element and show the input field
      clickedElement.classList.add("hidden");
      document.getElementById(elementId).classList.add("hidden");
      document.getElementById("add" + elementId)?.classList.add("hidden");
      const inputField = document.getElementById(
        elementId + (elementType == "dropdown" ? "Dropdown" : "Input")
      );
      inputField.parentElement.classList.remove("hidden");
      if (elementType == "dropdown") {
        if (elementId == "investigatorsList") {
          initTeamSelect(
            elementId + "Dropdown",
            investigatorsListContent,
            sourceInvestigators
          );
        } else if (elementId == "primaryInvestigator") {
          initTeamSelect(
            elementId + "Dropdown",
            primaryInvestigatorContent,
            sourceInvestigators
          );
        }
        if (elementId == "nominatedPrincipalInvestigator") {
          initTeamSelect(
            elementId + "Dropdown",
            nominatedPrincipalInvestigatorContent,
            sourceInvestigators
          );
        } else if (elementId == "coinvestigatorsList") {
          initTeamSelect(
            elementId + "Dropdown",
            coinvestigatorsListContent,
            sourceInvestigators
          );
        }
      } else {
        inputField.value = fieldValue || "";
      }
      // inputField.value = ''; // Pre-fill the input with existing text
      inputField.focus();
      editActive = true;
    });
  });
}
// Helper function to update elements
function updateElement(container, elementId, addElementId, value) {
  const element = container.querySelector(`#${elementId}`);
  const addElement = container.querySelector(`#${addElementId}`);
  if (element) {
    element.textContent = value || "";
    if (value) {
      element.classList.remove("hidden");
      addElement?.classList.add("hidden");
    } else {
      element.classList.add("hidden");
      addElement?.classList.remove("hidden");
    }
  }
}
