let uploadedFile = "";
let dbfile = ""; // Declare dbfile to avoid reference errors

async function handleFileUpload(inputname, lastUploadedFile) {
  uploadedFile = document.getElementById(inputname).files[0];
  if (lastUploadedFile) {
    uploadedFile = lastUploadedFile;

  }
  console.log(uploadedFile)
  return await uploadFile(inputname, uploadedFile);
}

async function handleFileDrop(inputname, file) {
  uploadedFile = file;
  return await uploadFile(inputname, file);
}

async function getUploadedFile() {
  console.log("getUploadedFile", uploadedFile);
  return uploadedFile;
}


async function uploadFile(inputname, file) {
  console.log("uploadFile")
  const fileInput = document.getElementById(inputname);
  const modalBody = document.querySelector("#upload-ctr"); // Replace with your container selector

  // Create a loader element
  const loader = document.createElement("div");
  loader.id = "upload-loader";
  loader.innerHTML = `
        <div class="flex items-center justify-center mt-4">
            <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
            </svg>
            <span class="ml-2 text-sm text-gray-700" id="upload-progress-text">Uploading... 0%</span>
        </div>`;
  loader.style.display = "none"; // Initially hidden
  modalBody.appendChild(loader);

  console.log("fileee", file)
  if (!file) {
    showToast("Please select a file to upload.", "error");
    return;
  }
  let allowedTypes;
  // Validate file type
  console.log("input  name", inputname);
  if (inputname == "uploadStudyDatabase") {
    allowedTypes = [
      "sqlite",
      "db"
    ];
    let filetype = file.name.split('.').pop();
    if (!allowedTypes.includes(filetype)) {
      showToast("Invalid file type. Please upload a SQLite file.", "error");
      file = uploadedFile = "";
      return;
    }
  }
  else {
    if (inputname == "CgmUpload") {
      allowedTypes = [
        "text/csv",
        "text/plain"
      ];
    }
    else {
      allowedTypes = ["text/csv"]; // Adjust this based on your requirements
    }
    if (!allowedTypes.includes(file.type)) {
      showToast("Invalid file type. Please upload a CSV file.", "error");
      file = uploadedFile = "";
      return;
    }
  }
  // Show loader
  loader.style.display = "flex";

  // Return a Promise
  return new Promise((resolve, reject) => {
    // Show loader
    loader.style.display = "flex";

    loader.style.display = "none"; // Hide loader after file is read
    if (fileInput.classList.contains("hidden")) {
      createFileItem(file.name, fileInput, inputname);
    }
    resolve(file);
  });
}


function createFileItem(file_name, fileInput, inputname) {
  const filesTitle = document.querySelector("#upload-list");
  filesTitle.classList.remove("hidden");

  let fileContainer = document.getElementById("upload-filename");
  if (fileContainer) {
    fileContainer.remove();
  }

  fileContainer = document.createElement("div");
  fileContainer.id = "upload-filename";
  fileContainer.className =
    "inline-flex items-center px-2 py-1 me-2 mt-1 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300 border";

  filesTitle.appendChild(fileContainer);

  const filename = document.createElement("span");
  filename.className = "text-sm font-normal truncate";
  filename.innerHTML = file_name;
  fileContainer.appendChild(filename);

  const cancelButton = document.createElement("button");
  cancelButton.type = "button";
  cancelButton.id = "cancel-upload-button";
  cancelButton.className = "inline-flex items-center p-1 ms-2 text-sm text-gray-700 bg-transparent rounded-xs hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-gray-300";
  cancelButton.setAttribute("data-dismiss-target", "#badge-dismiss-dark");
  cancelButton.setAttribute("aria-label", "Remove");

  const svg = createSVG();
  cancelButton.appendChild(svg);

  const srSpan = document.createElement("span");
  srSpan.className = "sr-only";
  srSpan.textContent = "Remove file";
  cancelButton.appendChild(srSpan);

  cancelButton.addEventListener("click", function () {
    const filesTitle = document.querySelector("#upload-list");
    fileInput.value = "";
    filesTitle.classList.add("hidden");
    file = uploadedFile = "";

    // Dispatch a custom event to notify file removal
    const fileRemovedEvent = new CustomEvent("fileRemoved", {
      detail: { inputName: inputname }, // Pass additional details if needed
    });
    document.dispatchEvent(fileRemovedEvent);

    if (inputname == "CgmUpload") {
      let element = document.getElementById("cgm-upload-status");
      element.classList.remove("text-red-500", "text-green-500");
      element.textContent = "";
    }
  });
  fileContainer.appendChild(cancelButton);
}

function createSVG() {
  const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  svg.classList.add("w-2", "h-2");
  svg.setAttribute("aria-hidden", "true");
  svg.setAttribute("xmlns", "http://www.w3.org/2000/svg");
  svg.setAttribute("fill", "none");
  svg.setAttribute("viewBox", "0 0 14 14");

  const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
  path.setAttribute("stroke", "currentColor");
  path.setAttribute("stroke-linecap", "round");
  path.setAttribute("stroke-linejoin", "round");
  path.setAttribute("stroke-width", "2");
  path.setAttribute("d", "m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6");

  svg.appendChild(path);
  return svg;
}
