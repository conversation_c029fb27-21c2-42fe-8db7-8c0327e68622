document.addEventListener("DOMContentLoaded", function () {
  // renderChatList();
  renderSuggestions();
  document.head.insertAdjacentHTML(
    "beforeend",
    '<link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.16/tailwind.min.css" rel="stylesheet">'
  );
  const chatElementRef = document.getElementById("chat-element");

  const style = document.createElement("style");
  style.innerHTML = `
  .hidden-chat {
    display: none;
  }
  #chat-widget-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    flex-direction: column;
  }
  #chat-popup {
    height: 70vh;
    max-height: 70vh;
    transition: all 0.3s;
    overflow: hidden-chat;
  }
  .loader {
    border: 4px solid #f3f3f3; / Light grey /
    border-top: 4px solid #3498db; / Blue /
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
  }

   @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

  @media (max-width: 768px) {
    #chat-popup {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      max-height: 100%;
      border-radius: 0;
    }
  }
  `;

  document.head.appendChild(style);
  const scriptTag = document.querySelector('script[src="/js/ai.js"]');
  var userId = "";
  if (scriptTag) {
    userId = scriptTag.getAttribute("data-user-id")
      ? scriptTag.getAttribute("data-user-id")
      : "Anonymous";
  }

  loadChatHistory();
  onUserRequest();



  function onUserRequest(message) {
    if (scriptTag) {
      const apiUrl = scriptTag.getAttribute("data-api-url");
      // const chatElementRef = document.getElementById("chat-element");
      chatElementRef.connect = {
        handler: async (body, signals) => {
          const timestamp = new Date().toISOString();
          storeMessage(userId, body.messages[0].text, "", "user", timestamp);
          console.log(body.messages[0].text);
          // Send the user's message to the API
          fetch(`${apiUrl}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              question: body.messages[0].text, // Send the user message as 'question' to the Flask API
            }),
          })
            .then((response) => response.json())
            .then((data) => {  console.log("data", data);
              if (data.error) {
                const timestamp = new Date().toISOString();
                const messageDiv = "<div class='errorMessageContainer'  style='color: #374151;background-color: #fee2e2;border-radius: 5px;padding: 10px;'>"+ data.error+"</div>";
                storeMessage(userId, "", messageDiv, "ai", timestamp);
                signals.onResponse({html : messageDiv });
                throw new Error("Failed to fetch response from the API.");
              }

              const { sql, df_html } = data;
              const timestamp = new Date().toISOString();
            let df_html_updated = df_html.replaceAll(
              /<th>/g,
              '<th class="head-table-class">'
            );
            df_html_updated = df_html_updated.replaceAll(
              /<td>/g,
              '<td class="row-table-class">'
            );
            df_html_updated = df_html_updated.replace("table-bordered", "");
            df_html_updated = df_html_updated.replace('border="1"', "");

            console.log(df_html_updated);


              const htmlContent = `
              <div class="custom-ai-response">
               <button style="display: flex; align-items: center;"
                        onclick="toggleAccordion(this)"
                        aria-label="Toggle message visibility">
                        <span>SQL</span>
                        <span class="material-icons text-sm " style="display:block">&#x25BC;</span>
                    </button>
                     <div class="custom-ai-content" style="display:none;">
                <p class="sql-container">${sql}</p> <!-- Display the SQL query -->
                </div>
                <div class="table-container"  style="overflow-x: auto; width: 100%;">
                  ${df_html_updated} <!-- Insert the table HTML -->
                </div>
              </div>
            `;
              storeMessage(userId, "", htmlContent, "ai", timestamp); // `response` should match your API response field
              signals.onResponse({ html: htmlContent }); // `response` should match your API response field
            });
        },
      };
    }
  }

  function loadChatHistory() {
    const elementRef = document.getElementById("chat-element");
    let chatHistory = JSON.parse(localStorage.getItem("chatAskDRHDev")) || [];

    chatHistory.forEach(obj => {
      if (obj.role === "ai") {
          obj.html = obj.html.replaceAll(
              /<th>/g,
              '<th class="head-table-class">'
            );
            obj.html = obj.html.replaceAll(
              /<td>/g,
              '<td class="row-table-class">'
            );
            obj.html = obj.html.replace("table-bordered", "");
            obj.html = obj.html.replace('border="1"', "");
      }
  });
    elementRef.history = chatHistory;
  }

  function storeMessage(user, text = "", html = "", role, timestamp) {
    const chatData = JSON.parse(localStorage.getItem("chatAskDRHDev")) || [];
    chatData.push({ user, text, html, role, timestamp });
    localStorage.setItem("chatAskDRHDev", JSON.stringify(chatData));
  }
});

function renderSuggestions() {
  const suggestionBox = document.getElementById("suggestion-box");
  suggestionBox.innerHTML = ""; // Clear any existing suggestions

  // Get the API URL from the script tag
  const scriptTag = document.querySelector('script[src="/js/ai.js"]');
  const apiUrl = scriptTag.getAttribute("data-suggestion-url");

  // Fetch suggestions from the API
  fetch(apiUrl, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.error) return; // Exit if there's an error

      // Create and append the header
      const headerBox = document.createElement("div");
      headerBox.className = "py-0 px-2";
      headerBox.textContent = data.header;
      suggestionBox.appendChild(headerBox);

      // Create the suggestion list container
      const listContainer = document.createElement("div");
      listContainer.className = "px-0 py-2 space-y-4 w-full";
      listContainer.id = "suggestion-list";
      headerBox.appendChild(listContainer);
      console.log(data.questions)
      // Iterate over suggestions and create list items
      data.questions.forEach((suggestion) => {
        const listItem = document.createElement("div");
        listItem.className = "suggestion-item";
        listItem.textContent = suggestion;

        // Add click event to each suggestion
        // listItem.addEventListener('click', () => handleSuggestionClick(suggestion));

        // Append list item to the container
        listContainer.appendChild(listItem);
      });
      const chatElementRef = document.getElementById("chat-element");

      chatElementRef.htmlClassUtilities = {
        ["suggestion-item"]: {
          events: {
            click: (event) => {
              const text = event.target.innerText;
              chatElementRef.submitUserMessage(text);
            },
          },
          styles: {
            default: {
              backgroundColor: "#f2f2f2",
              borderRadius: "10px",
              padding: "10px",
              cursor: "pointer",
              textAlign: "left",
              color: "#1D4ED8",
              marginTop: "10px",
              marginBottom: "10px",
            },
            hover: { backgroundColor: "#ebebeb" },
            click: { backgroundColor: "#e4e4e4" },
          },
        },
         ["table"]: {
          styles: {
            default: {
              border: "1px solid black",
              padding: "1px",
              borderCollapse: "collapse"
            }
          }
        },
        ["head-table-class"]: {
          styles: {
            default: {
              border: "0px",
              padding: "3px",
              background: "#dbdbdb"
            }
          }
        },
        ["row-table-class"]: {
          styles: {
            default: {
              border: "1px",
              padding: "3px"
            }
          }
        },
      };
    })
    .catch((error) => console.error("Error fetching suggestions:", error));
}

function toggleAccordion(button) {
  const content = button.nextElementSibling;
  if (content.style.display == "none") {
    content.style.display = "block"; // Toggle visibility
    button.querySelector('.material-icons').style.transform = "rotate(180deg)"; // Rotate arrow icon
    button.querySelector('.material-icons').style.transition = "transform 0.3s ease-in-out";
  }
  else {
    content.style.display = "none"; // Toggle visibility
    button.querySelector('.material-icons').style.transform = "rotate(0deg)"; // Rotate arrow icon
    button.querySelector('.material-icons').style.transition = "transform 0.3s ease-in-out";
  }

}
const chatElementRef = document.getElementById("chat-element");
  chatElementRef.validateInput = (text, files) => { console.log(text);
    return text || files.length > 0;
  };



