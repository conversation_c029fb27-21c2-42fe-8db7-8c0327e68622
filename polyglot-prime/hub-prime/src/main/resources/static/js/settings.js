document.addEventListener("DOMContentLoaded", function () {
  // Get the studyId from the script tag
  const scriptTag = document.querySelector('script[src="/js/settings.js"]');
  const studyId = scriptTag?.getAttribute("data-studyid");

  if (!studyId) {
    console.error("Study ID not found.");
    return;
  }

  // Define the SVG icons as strings
  const svgIcons = {
    settings: `<svg width="15" height="10" viewBox="0 0 15 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.766284 2.30769C0.54917 2.30769 0.367305 2.23385 0.22069 2.08615C0.0735633 1.93897 0 1.75641 0 1.53846C0 1.32051 0.0735633 1.13769 0.22069 0.99C0.367305 0.842821 0.54917 0.769231 0.766284 0.769231H3.06513C3.28225 0.769231 3.46437 0.842821 3.61149 0.99C3.75811 1.13769 3.83142 1.32051 3.83142 1.53846C3.83142 1.75641 3.75811 1.93897 3.61149 2.08615C3.46437 2.23385 3.28225 2.30769 3.06513 2.30769H0.766284ZM0.766284 6.15385C0.54917 6.15385 0.367305 6.08 0.22069 5.93231C0.0735633 5.78513 0 5.60256 0 5.38462C0 5.16667 0.0735633 4.98385 0.22069 4.83615C0.367305 4.68897 0.54917 4.61538 0.766284 4.61538H3.06513C3.28225 4.61538 3.46437 4.68897 3.61149 4.83615C3.75811 4.98385 3.83142 5.16667 3.83142 5.38462C3.83142 5.60256 3.75811 5.78513 3.61149 5.93231C3.46437 6.08 3.28225 6.15385 3.06513 6.15385H0.766284ZM13.7165 9.46154L11.3027 7.03846C10.9962 7.25641 10.661 7.41974 10.2973 7.52846C9.93308 7.63769 9.56577 7.69231 9.1954 7.69231C8.13538 7.69231 7.23193 7.31718 6.48506 6.56692C5.73768 5.81718 5.36398 4.91026 5.36398 3.84615C5.36398 2.78205 5.73768 1.87487 6.48506 1.12462C7.23193 0.374872 8.13538 0 9.1954 0C10.2554 0 11.1591 0.374872 11.9065 1.12462C12.6534 1.87487 13.0268 2.78205 13.0268 3.84615C13.0268 4.21795 12.9727 4.58667 12.8644 4.95231C12.7556 5.31744 12.5926 5.65385 12.3755 5.96154L14.7893 8.38461C14.9298 8.52564 15 8.70513 15 8.92308C15 9.14103 14.9298 9.32051 14.7893 9.46154C14.6488 9.60256 14.47 9.67308 14.2529 9.67308C14.0358 9.67308 13.857 9.60256 13.7165 9.46154ZM9.1954 6.15385C9.83397 6.15385 10.3768 5.92949 10.8238 5.48077C11.2708 5.03205 11.4943 4.48718 11.4943 3.84615C11.4943 3.20513 11.2708 2.66026 10.8238 2.21154C10.3768 1.76282 9.83397 1.53846 9.1954 1.53846C8.55683 1.53846 8.01405 1.76282 7.56705 2.21154C7.12005 2.66026 6.89655 3.20513 6.89655 3.84615C6.89655 4.48718 7.12005 5.03205 7.56705 5.48077C8.01405 5.92949 8.55683 6.15385 9.1954 6.15385ZM0.766284 10C0.54917 10 0.367305 9.92615 0.22069 9.77846C0.0735633 9.63128 0 9.44872 0 9.23077C0 9.01282 0.0735633 8.83026 0.22069 8.68308C0.367305 8.53539 0.54917 8.46154 0.766284 8.46154H6.89655C7.11367 8.46154 7.29579 8.53539 7.44291 8.68308C7.58953 8.83026 7.66284 9.01282 7.66284 9.23077C7.66284 9.44872 7.58953 9.63128 7.44291 9.77846C7.29579 9.92615 7.11367 10 6.89655 10H0.766284Z" fill="#393939"/>
</svg>`,
    collaboration: `<svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9 1.5V0H15V1.5H9ZM9 4.5V3H15V4.5H9ZM9 7.5V6H15V7.5H9ZM4.5 6.75C3.875 6.75 3.34375 6.53125 2.90625 6.09375C2.46875 5.65625 2.25 5.125 2.25 4.5C2.25 3.875 2.46875 3.34375 2.90625 2.90625C3.34375 2.46875 3.875 2.25 4.5 2.25C5.125 2.25 5.65625 2.46875 6.09375 2.90625C6.53125 3.34375 6.75 3.875 6.75 4.5C6.75 5.125 6.53125 5.65625 6.09375 6.09375C5.65625 6.53125 5.125 6.75 4.5 6.75ZM0 11.25V9.825C0 9.5625 0.0625 9.3125 0.1875 9.075C0.3125 8.8375 0.4875 8.65 0.7125 8.5125C1.275 8.175 1.87188 7.92188 2.50313 7.75313C3.13438 7.58438 3.8 7.5 4.5 7.5C5.2 7.5 5.86563 7.58438 6.49688 7.75313C7.12813 7.92188 7.725 8.175 8.2875 8.5125C8.5125 8.65 8.6875 8.8375 8.8125 9.075C8.9375 9.3125 9 9.5625 9 9.825V11.25H0Z" fill="#737373"/>
</svg>`,
    publications: `<svg class="w-[15px] h-[15px] text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.03v13m0-13c-2.819-.831-4.715-1.076-8.029-1.023A.99.99 0 0 0 3 6v11c0 .563.466 1.014 1.03 1.007 3.122-.043 5.018.212 7.97 1.023m0-13c2.819-.831 4.715-1.076 8.029-1.023A.99.99 0 0 1 21 6v11c0 .563-.466 1.014-1.03 1.007-3.122-.043-5.018.212-7.97 1.023"/>
</svg>

`,
  };
  // Check if the pageTabs element exists
  const pageTabs = document.getElementById("pageTabs");
  const params = new URLSearchParams(window.location.search);
  const tab = params.get("tab");
  if (pageTabs) {
    const tabs = [
      {
        text: "Study Settings",
        key: "settings",
        href: "/settings/" + studyId + "?tab=" + tab,
      },
      {
        text: "Collaboration and Teams",
        key: "collaboration",
        href: "/collaboration/" + studyId + "?tab=" + tab,
      },
      {
        text: "Publication Settings",
        key: "publications",
        href: "/publications/" + studyId + "?tab=" + tab,
      },
    ];

    // Get the active route path dynamically (replace with your logic)
    //const activeRouteKey = window.location.pathname.includes("settings") ? "settings" : "collaboration";
    // Get the active route path dynamically (replace with your logic)
    const activeRouteKey = window.location.pathname.split("/")[1];
    // Helper function to create an SVG wrapper
    const createSvgWrapper = (svgHTML) => {
      const iconWrapper = document.createElement("div");
      iconWrapper.innerHTML = svgHTML;
      iconWrapper.classList.add("mr-2");
      return iconWrapper;
    };

    // Populate tabs only if empty
    if (pageTabs.innerHTML.trim() === "") {
      tabs.forEach((tab) => {
        const li = document.createElement("li");
        li.className = "mr-2 hidden";
        li.setAttribute("data-privilege-tabs", tab.text);
        const a = document.createElement("a");
        a.href = tab.href;
        a.textContent = tab.text;
        a.className =
          tab.key === activeRouteKey
            ? "flex items-center bg-white border border-b-0 text-gray-500 rounded-t-lg py-2 px-4 text-sm font-medium text-center mb-[-1px] active"
            : "flex items-center text-gray-500 rounded-t-lg py-2 px-4 text-sm font-medium text-center hover:text-gray-600 hover:bg-gray-50";

        // Add SVG if available
        const svgHTML = svgIcons[tab.key];
        if (svgHTML) {
          a.prepend(createSvgWrapper(svgHTML));
        }

        li.appendChild(a);
        pageTabs.appendChild(li);
      });
      hideUnauthorizedTabs();
    }
  } else {
    console.error("#pageTabs element not found.");
  }
});

// Helper function to add an event listener if the element exists
function addEventListenerIfExists(elementId, eventType, callback) {
  const element = document.getElementById(elementId);
  if (element) {
    element.addEventListener(eventType, callback);
  }
}

// Function to handle the visibility modal
function handleVisibilityModal() {
  openConfirmationVisibilityModal();
  const modal = new Modal(document.getElementById("confirmation-modal"));
  modal.show();
}

// Add event listener for "update-study-visibility"
addEventListenerIfExists("update-study-visibility", "click", handleVisibilityModal);
// Event listener for the delete study button
document.getElementById("delete-study")?.addEventListener("click", function () {
  openConfirmationModal("delete");
  const modal = new Modal(document.getElementById("confirmation-modal"));
  modal.show();
});

// Event listener for the archive study button
document.getElementById("archive-study")?.addEventListener("click", function () {
  openConfirmationModal("archive");
  const modal = new Modal(document.getElementById("confirmation-modal"));
  modal.show();
});

// Event listener for title validation
document.getElementById("title")?.addEventListener("blur", function () {
  const element = document.getElementById("title");
  if (element?.value.trim() !== "") {
    element.classList.remove("border-[#e23636]");
  }
});
document.getElementById("update-study-settings")?.addEventListener("click", async () => {
  showLoader("update-setting-loader");

  const fieldsToValidate = ["title"];
  let isValid = fieldsToValidate.every(validateField);
  const start_date = DOMPurify.sanitize(
        document.getElementById("start_date").value
      );
  const end_date = DOMPurify.sanitize(
        document.getElementById("end_date").value
      );
   if (start_date != "" || end_date != "") {
   const startDateValid = validateDates("start_date", "end_date");
   if (!startDateValid) isValid = false;
   }

  const nctNumber = sanitizeInput("nct_number");
  if (nctNumber && !/^NCT\d{8}$/.test(nctNumber)) {
    document.getElementById("nct_number-error").textContent = "Invalid format. Please enter the identifier in the format NCTXXXXXXXX";
    hideLoader("update-setting-loader");
    return;
  } else {
    document.getElementById("nct_number-error").textContent = "";
  }

  if (isValid) {
    const data = {
      studyId,
      studyTitle: sanitizeInput("title"),
      description: sanitizeInput("description"),
      locationId: sanitizeInput("study_location"),
      treatmentModalities: sanitizeInput("treatment_modalities"),
      fundingSource: sanitizeInput("funding_source"),
      nctNumber: nctNumber || null,
      startDate: sanitizeInput("start_date") || null,
      endDate: sanitizeInput("end_date") || null,
      userId: localStorage.getItem("practitionerPartyId"),
    };

    postData(`/research-study-settings`, data, (res) => {
      hideLoader("update-setting-loader");
      handleApiResponse(res, "Study settings saved successfully!", document.referrer);
    });
  } else {
    hideLoader("update-setting-loader");
  }
});function sanitizeInput(id) {
  const element = document.getElementById(id);
  return element ? DOMPurify.sanitize(element.value) : "";
}

function toggleVisibility(elementId, addElementId, value) {
  const element = document.getElementById(elementId);
  const addElement = document.getElementById(addElementId);
  if (element) {
    element.textContent = value || "";
    if (value) {
      element.classList.remove("hidden");
      addElement?.classList.add("hidden");
    } else {
      element.classList.add("hidden");
      addElement?.classList.remove("hidden");
    }
  }
}

function validateField(fieldId) {
  const field = document.getElementById(fieldId);
  if (field && field.value.trim() === "") {
    field.classList.add("border-[#e23636]");
    return false;
  }
  field.classList.remove("border-[#e23636]");
  return true;
}

function validateDates(startDateId, endDateId) {
  const startDateInput = sanitizeInput(startDateId);
  const endDateInput = sanitizeInput(endDateId);
  document.getElementById(`${startDateId}-error`).textContent = "";
  document.getElementById(`${endDateId}-error`).textContent = "";

  if (!startDateInput) {
    document.getElementById(`${startDateId}-error`).textContent = "Please fill Start Date";
    return false;
  }

  if (startDateInput && endDateInput) {
    const startDate = convertToUTCFormat(startDateInput);
    const endDate = convertToUTCFormat(endDateInput);
    if (endDate <= startDate) {
      document.getElementById(`${endDateId}-error`).textContent = "End date should be greater than Start date";
      return false;
    }
  }
  return true;
}

function convertToUTCFormat(dateInput) {
  const inputDateValue = new Date(dateInput);
  return new Date(Date.UTC(inputDateValue.getFullYear(), inputDateValue.getMonth(), inputDateValue.getDate()));
}

function handleApiResponse(response, successMessage, redirectUrl) {
  if (response && response.status === "success") {
    showToast(successMessage, "success");
    if (redirectUrl) window.location.href = redirectUrl;
  } else {
    showToast(response?.message || "An error occurred. Please try again.", "error");
  }
}
function openConfirmationModal(actionType) {
  const modalTitle = document.querySelector(".modal-title-text");
  const modalBody = document.querySelector(".modal-body-text");
  const confirmButton = document.querySelector("#delete-arch-confirm-study");

  if (actionType === "delete") {
    modalTitle.textContent = "Delete Study";
    modalBody.textContent = "Are you sure you want to delete this study?";
    confirmButton.textContent = "Delete";
    confirmButton.style.backgroundColor = "#dc2626"; // Red for delete
    confirmButton.onclick = () => handleAction("delete", true);
  } else if (actionType === "archive") {
    getStudyArchiveStatus(studyId).then((archiveStatus) => {
      modalTitle.textContent = archiveStatus ? "Unarchive Study" : "Archive Study";
      modalBody.textContent = archiveStatus
        ? "Are you sure you want to unarchive this study?"
        : "Are you sure you want to archive this study?";
      confirmButton.textContent = archiveStatus ? "Unarchive" : "Archive";
      confirmButton.style.backgroundColor = "#1F883D"; // Green for archive
      confirmButton.onclick = () => handleAction("archive", !archiveStatus);
    });
  }
}
function showLoader(loaderId) {
  document.getElementById(loaderId)?.classList.remove("hidden");
}

function hideLoader(loaderId) {
  document.getElementById(loaderId)?.classList.add("hidden");
}

function showToast(message, type) {
  // Implement your toast logic here
  console.log(`${type.toUpperCase()}: ${message}`);
}
// Helper function to make API requests
async function makeApiRequest(url, method, body = null) {
  try {
    const options = {
      method,
      headers: {
        "Content-Type": "application/json",
      },
    };
    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`API error: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error during API request:", error);
    throw error;
  }
}


// Helper function to toggle button states
function toggleButtons(disabled) {
  document.getElementById("update-study-settings").disabled = disabled;
  document.getElementById("update-study-visibility").disabled = disabled;
}

// Helper function to handle modal visibility
function hideModal(modalId) {
  const modal = new Modal(document.getElementById(modalId));
  modal.hide();
}
async function handleAction(actionType, status) {
  try {
    let url, successMessage, redirectUrl;

    if (actionType === "delete") {
      url = `/research-studies/delete-study/${studyId}`;
      successMessage = "Study deleted successfully!";
      redirectUrl = "/studies/all";
    } else if (actionType === "archive") {
      url = `/research-studies/archive-status/${studyId}?isArchived=${status}`;
      successMessage = status
        ? "Study archived successfully!"
        : "Study unarchived successfully!";
      redirectUrl = `/study/info/${studyId}?tab=${tab}`;
    }

    // Make the API request
    const data = await makeApiRequest(url, "PUT");

    // Handle success or failure based on the response
    if (data.status === "success") {
      showToast(successMessage, "success");

      if (actionType === "archive") {
        const archiveButton = document.getElementById("archive-study");
        if (status) {
          archiveButton.textContent = "Unarchive this Study";
          toggleButtons(true);
        } else {
          archiveButton.textContent = "Archive this Study";
          toggleButtons(false);
        }
      }

      window.location.href = redirectUrl;
    } else {
      showToast(data.message, "error");
    }
  } catch (error) {
    showToast("An error occurred. Please try again.", "error");
  } finally {
    hideModal("confirmation-modal");
  }
}

function openConfirmationVisibilityModal() {
  const modalTitle = document.querySelector(".modal-title-text");
  const modalBody = document.querySelector(".modal-body-text");
  const confirmButton = document.querySelector("#delete-arch-confirm-study");

  modalTitle.textContent = "Update Study Visibility";
  modalBody.textContent = "Are you sure you want to update the visibility of this study?";
  confirmButton.textContent = "Confirm";
  confirmButton.style.backgroundColor = "#1F883D"; // Green for confirmation
  confirmButton.onclick = () => handleVisibilityUpdate();
}

async function handleVisibilityUpdate() {
  try {
    const studyVisibilityId = document.querySelector('input[name="visibility"]:checked')?.value;
    if (!studyVisibilityId) {
      showToast("Please select a visibility option.", "error");
      return;
    }

    const response = await fetch(`/research-study/${studyId}/visibility?studyVisibilityId=${studyVisibilityId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    if (data.status === "success") {
      showToast("Study visibility updated successfully!", "success");
       window.location = "/study/info/" + studyId + "?tab=" + tab;
    } else {
      showToast(data.message, "error");
    }
  } catch (error) {
    console.error("Error while updating visibility:", error);
    showToast("An error occurred. Please try again.", "error");
  }
}