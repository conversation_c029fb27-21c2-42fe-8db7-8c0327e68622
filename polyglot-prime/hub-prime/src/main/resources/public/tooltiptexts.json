{"study_name": "Enter the study name. Choose a descriptive name that reflects the research focus.", "study_display_id": "Enter a unique Study Display ID using only letters and digits (up to 6 characters). This ID cannot be edited after adding and is used to refer to the study on all pages.", "study_description": "Provide a brief overview of the study, including its objectives and scope in diabetes research.", "study_visibility_private": "Restricts study access to authorized users only.", "study_visibility_internal": "Allows access to users within the same organization.", "study_visibility_public": "Makes the study accessible to everyone.", "study_location_name": "Specify the geographical location where the study is being conducted.", "study_treatment_modalities": "List the treatment approaches being studied (e.g., insulin therapy, oral medications, lifestyle interventions).", "study_funding_source": "Enter the organization or institution funding the study (e.g., NIH, pharmaceutical companies, private grants).", "study_nct_number": "Enter the clinical trial registration number in the format NCTXXXXXXXX (e.g., NCT12345678). This identifier is assigned by ClinicalTrials.gov.", "study_start_date": "Enter the study start date in MM-DD-YYYY format.", "study_end_date": "Enter the study completion date in MM-DD-YYYY format.", "study_publication_title": "Enter the title of the published study or research article related to this diabetes research", "study_publication_doi": "Enter the Digital Object Identifier (DOI) of the published study or research article. This is a unique identifier for the article(e.g., 10.1234/abcd.5678).", "study_publication_date": "Enter the publication date of the study or research article in MM-DD-YYYY format.", "collab_principal_investigator": "Enter the name of the lead researcher responsible for overseeing the study.", "collab_nominated_pricipal_investigator": "The researcher responsible for overseeing the study and ensuring its successful execution.", "collab_coInvestigator": "Additional researchers who collaborate with the Principal Investigator in conducting the study and contributing to its outcomes.", "collab_study_team_members": "List the researchers, coordinators, and other key members involved in the study.", "collab_principal_author": "Enter the name of the primary author responsible for publications related to this study.", "collab_add_publication_details": "To update the list of authors, add publication details such as the Publication Title, Date, and Digital Object Identifier (DOI).", "collab_author_team": "List all contributing authors who have worked on publications associated with this study.", "participant_id": "Enter a unique identifier for the participant. This ID is used for tracking within the study.", "participant_gender": "Select the participant's gender from the available options.", "participant_age": "Enter the participant’s age in years.", "participant_bmi": "Enter the participant’s BMI, calculated as weight (kg) / height² (m²).", "participant_baseline_hba1c": "Enter the participant’s HbA1c (%) at the beginning of the study. This measures average blood sugar levels over the past three months.", "participant_race": "Select the participant’s race from the available options.", "participant_ethnicity": "Select the participant’s ethnicity from the available options.", "participant_diagnosis_icd": "Enter the ICD (International Classification of Diseases) code for the participant’s diagnosis.", "participant_med_rxnorm": "Enter the RxNorm identifier for the participant’s medication. This ensures standardized drug identification.", "participant_treatment_modalities": "Enter the treatment approaches used (e.g., insulin therapy, oral medications, lifestyle changes).", "participant_diabetes_type": "Enter the type of diabetes the participant has (e.g., Type 1, Type 2, Gestational Diabetes).", "participant_study_arm": "Enter the study arm or group to which the participant is assigned (e.g., control group, experimental group).", "auto_generated_participant_id": "Auto-generated, not editable", "upload_cgm_tracing_file": "Drop files here or browse to upload your CGM (Continuous Glucose Monitoring) data file.", "supported_file_formats": "CSV, TXT", "device_name": "Enter the name of the CGM device used for data collection.", "device_id": "Enter the unique identifier assigned to the CGM device.", "source_platform": "Select the platform from which the CGM data was sourced (e.g., Dexcom, Medtronic, Abbott).", "cgm_date_mapping": "After uploading the CGM Tracing File, select the appropriate option to map date values from the file.", "cgm_value_mapping": "After uploading the CGM Tracing File, select the appropriate option to map glucose readings from the file.", "publicationTitleDrawer": "Enter the title of the published study or research article related to this diabetes research", "publicationdoiDrawer": "Enter the Digital Object Identifier (DOI) of the published study or research article. This is a unique identifier for the article(e.g., 10.1234/abcd.5678).", "publicationdateDrawer": "Enter the publication date of the study or research article in MM-DD-YYYY format.", "principalauthorDrawer": "Enter the name of the primary author responsible for publications related to this study.", "pubmedIdDrawer": "PubMed ID (PMID) is a unique identifier for articles in PubMed. Use it to quickly find studies", "authorteamDrawer": "List all contributing authors who have worked on publications associated with this study."}