<div class="w-full border border-gray-400 rounded">
    <div class="flex p-2" style="background-color: #E3E3E2; border-bottom: 1px solid #E3E3E2;">
        <p class="text-black font-sans text-base font-bold">
            GLUCOSE STATISTICS AND TARGETS
        </p>
    </div>
    <div class="p-5">
        <div th:replace="~{fragments/loader :: loader('participantMatricsLoader')}"></div>
        <div class="w-[100%] flex row justify-between">
            <p>
                <span class="startDate">Loading...
                </span> -
                <span class="endDate">Loading...
                </span>
            </p>
            <p class="dateDiff">Loading...
            </p>

        </div>
        <div class="w-[100%] flex row justify-between pt-2">
            <p class="text-gray-800 font-bold text-sm @apply leading-[26px]">Time CGM Active</p>
            <div class="text-gray-800 font-bold text-sm">
                <!-- <div th:replace="~{fragments/loader :: loader('percentageTimeCgmActiveLoader')}"> -->
            </div>
            <div class="flex flex-row p-1">
                <div>
                    <span class="percentageTimeCgmActive"></span>
                    <span class="text-gray-800 font-normal text-xs percentageTimeCgmActiveUnit">%</span>
                </div>
                <div th:replace="~{fragments/calculatorText :: calculatorText('time_cgm_active')}"></div>
            </div>
        </div>
        <!-- </div> -->
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('time_cgm_active')}"></div>
        </div>
        <div class="w-[100%] flex row justify-between pt-2">
            <p class="text-gray-800 font-bold text-sm @apply leading-[26px]">Number of Days CGM Worn</p>
            <div class="text-gray-800 font-bold text-sm">
                <!-- <div th:replace="~{fragments/loader :: loader('daysCGMWornLoader')}"> -->
            </div>
            <div class="flex flex-row p-1">
                <div>
                    <span class="daysCGMWorn"></span>
                    <span class="text-gray-800 font-normal daysCGMWornUnit text-xs">days</span>
                </div>
                <div th:replace="~{fragments/calculatorText :: calculatorText('number_of_days_cgm_worn')}"></div>
            </div>
        </div>
        <!-- </div> -->
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('number_of_days_cgm_worn')}"></div>
        </div>
        <div class="w-[100%] flex row justify-between pt-2">
            <table
                class="table-auto text-gray-800 font-normal text-xs mt-2 mb-2 bg-zinc-200 w-full border-collapse border border-slate-500">
                <thead>
                    <tr>
                        <th colspan="2" class="p-2 bg-gray-300">
                            <p>Ranges And Targets For Type 1 or Type 2 Diabetes</p>
                        </th>
                    </tr>
                    <tr>
                        <th class="text-left p-2 bg-gray-200">
                            <p>Glucose Ranges</p>
                        </th>
                        <th class="text-left p-2 bg-gray-200 text-right">
                            <p>Targets % of Readings (Time/Day)</p>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="p-2">Target Range 70-180 mg/dL</td>
                        <td class="p-2 text-right">Greater than 70% (16h 48min)</td>
                    </tr>
                    <tr>
                        <td class="p-2">Below 70 mg/dL</td>
                        <td class="p-2 text-right">Less than 4% (58min)</td>
                    </tr>
                    <tr>
                        <td class="p-2">Below 54 mg/dL</td>
                        <td class="p-2 text-right">Less than 1% (14min)</td>
                    </tr>
                    <tr>
                        <td class="p-2">Above 180 mg/dL</td>
                        <td class="p-2 text-right">Less than 25% (6h)</td>
                    </tr>
                    <tr>
                        <td class="p-2">Above 250 mg/dL</td>
                        <td class="p-2 text-right">Less than 5% (1h 12min)</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="p-2 bg-gray-100">
                            <p class="font-mono text-center">
                                Each 5% increase in time in range (70-180 mg/dL) is clinically beneficial.
                            </p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="w-[100%] flex row justify-between pt-2">
            <p class="text-gray-800 font-bold text-sm ">Mean Glucose</p>
            <div class="text-gray-800 font-bold text-sm">
                <!-- <div th:replace="~{fragments/loader :: loader('meanGlucoseLoader')}"> -->
            </div>
            <div class="flex flex-row p-1">
                <div>
                    <span class="meanGlucose"></span>
                    <span class="text-gray-800 font-normal text-xs meanGlucoseUnit">mg/dL</span>
                </div>
                <div th:replace="~{fragments/calculatorText :: calculatorText('mean_glucose')}"></div>
            </div>
        </div>
        <!-- </div> -->
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('mean_glucose')}"></div>
        </div>
        <div class="w-[100%] flex row justify-between pt-2">
            <p class="text-gray-800 font-bold text-sm">Glucose Management Indicator (GMI)</p>
            <div class="text-gray-800 font-bold text-sm">
                <!-- <div th:replace="~{fragments/loader :: loader('GMILoader')}"> -->
            </div>
            <div class="flex flex-row p-1">
                <div>
                    <span class="GMI"></span>
                    <span class="text-gray-800 font-normal text-xs GMIUnit">%</span>
                </div>
                <div th:replace="~{fragments/calculatorText :: calculatorText('gmi')}"></div>
            </div>
        </div>
        <!-- </div> -->
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('gmi')}"></div>
        </div>
        <div class="w-[100%] flex row justify-between pt-2">
            <p class="text-gray-800 font-bold text-sm">Glucose Variability</p>
            <div class="text-gray-800 font-bold text-sm">
                <!-- <div th:replace="~{fragments/loader :: loader('coefficientOfVariantsLoader')}"> -->
            </div>
            <div class="flex flex-row p-1">
                <div>
                    <span class="coefficientOfVariants"></span>
                    <span class="text-gray-800 font-normal text-xs coefficientOfVariantsUnit">%</span>
                </div>
                <div th:replace="~{fragments/calculatorText :: calculatorText('glucose_variability')}"></div>
            </div>
        </div>
        <!-- </div> -->
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('glucose_variability')}"></div>
        </div>
        <div class="w-[100%] flex row justify-between pt-2 pb-4">
            <p class="text-gray-700 font-bold text-xs">
                Defined as percent coefficient of variation (%CV); target ≤36%
            </p>
        </div>
    </div>
</div>
<!-- </div> -->
<script>
    function getMetrics(startDateInput, endDateInput) {
        showLoader('participantMatricsLoader');
        fetchData(
            `/studies/${studyId}/participants/${participantId}/participant-metrics?startDate=` + startDateInput + `&&endDate=` + endDateInput,
            (res, error) => {
                if (!error) {
                    let result = res?.data?.participantMetrics.data;
                    const glucoseMetrics = [
                        {
                            class: "percentageTimeCgmActive",
                            datakey: "percentage_active",
                        },
                        {
                            class: "daysCGMWorn",
                            datakey: "number_of_days_cgm_worn",
                        },
                        {
                            class: "meanGlucose",
                            datakey: "mean_glucose",
                        },
                        {
                            class: "GMI",
                            datakey: "glucose_management_indicator",
                        },
                        {
                            class: "coefficientOfVariants",
                            datakey: "coefficient_of_variation",
                        },
                    ];


                    glucoseMetrics.forEach((metric) => {
                        let key = metric.datakey;
                        if (result.hasOwnProperty(key)) {
                            if (result[key] != null) {
                                assignValues(metric.class, result[key]);
                                showItem(metric.class + "Unit");
                            }
                            else {
                                assignValues(
                                    metric.class,
                                    nodataSvg
                                );
                                hideItem(metric.class + "Unit");
                            }
                        }
                        else {

                            assignValues(
                                metric.class,
                                nodataSvg
                            );
                            hideItem(metric.class + "Unit");
                        }

                    });

                }
                else {
                    console.error("Error fetching participant metrics info:", error);
                }
            }
        );
        hideLoader('participantMatricsLoader');
    }



</script>