<!-- Fragment for Calculator Text -->
<div th:fragment="calculatorText(textTitle)">
    <div  th:attr="data-name=${textTitle}" onclick="showHideCalculationText(this)" class="cursor-pointer">
        <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 5H7V7H17V5Z" fill="currentColor" />
            <path d="M7 9H9V11H7V9Z" fill="currentColor" />
            <path d="M9 13H7V15H9V13Z" fill="currentColor" />
            <path d="M7 17H9V19H7V17Z" fill="currentColor" />
            <path d="M13 9H11V11H13V9Z" fill="currentColor" />
            <path d="M11 13H13V15H11V13Z" fill="currentColor" />
            <path d="M13 17H11V19H13V17Z" fill="currentColor" />
            <path d="M15 9H17V11H15V9Z" fill="currentColor" />
            <path d="M17 13H15V19H17V13Z" fill="currentColor" />
            <path clip-rule="evenodd"
                d="M3 3C3 1.89543 3.89543 1 5 1H19C20.1046 1 21 1.89543 21 3V21C21 22.1046 20.1046 23 19 23H5C3.89543 23 3 22.1046 3 21V3ZM5 3H19V21H5V3Z"
                fill="currentColor" fill-rule="evenodd" />
        </svg>
    </div>

</div>

<!-- Fragment for Calculator Text Container -->
<div th:fragment="calculatorTextContainer(textTitle)">
    <div th:id="${textTitle}" class="hidden absolute bg-white z-[10] right-2.5 shadow-sm rounded-[3px] border border-solid border-[#dbdbdb]">
        <div class="flex justify-between px-2 py-2 items-center bg-[#e5e7eb]">
            <p class="font-semibold">Calculations</p>
            <div  th:attr="data-name=${textTitle}" onclick="hideCalculationText(this)" class="cursor-pointer">
                <svg viewBox="0 0 32 32" width="20" height="20" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <style>.cls-1 { fill: none; stroke: #000; stroke-linecap: round; stroke-linejoin: round; stroke-width: 2px; }</style>
                    </defs>
                    <title />
                    <g id="cross">
                        <line class="cls-1" x1="7" x2="25" y1="7" y2="25" />
                        <line class="cls-1" x1="7" x2="25" y1="25" y2="7" />
                    </g>
                </svg>
            </div>
        </div>
        <div class="text-sm px-3 py-2 flex flex-col gap-1" th:id="${textTitle + 'Data'}"></div>
    </div>
</div>
