<div class="w-full border border-gray-400 rounded relative">
  <div class="flex p-2" style="background-color: #E3E3E2; border-bottom: 1px solid #E3E3E2;">
    <p class="text-black font-sans text-base font-bold">Goals for Type 1 and Type 2 Diabetes</p>
    <div class="ml-auto">
      <div
        th:replace="~{fragments/calculatorText :: calculatorText('goals_for_type_1_and_type_2_diabetes_chart_metrics')}">
      </div>
    </div>
  </div>
  <div class="p-2">
    <div
      th:replace="~{fragments/calculatorText :: calculatorTextContainer('goals_for_type_1_and_type_2_diabetes_chart_metrics')}">
    </div>
  </div>
  <div class="tir-loader hidden"></div>
  <div class="tir-chart-element-error hidden">
    <div th:replace="~{fragments/chart-errors :: chartError('/chart-skeletons/tir-skel-gr.png','25rem','tir-reload')}">
    </div>
  </div>
  <div class="tir-chart-element-no-data-error hidden">
    <div
      th:replace="~{fragments/chart-errors :: chartNoDataError('/chart-skeletons/tir-skel-gr.png','25rem','tir-reload')}">
    </div>
  </div>
  <stacked-bar-chart class="p-5"></stacked-bar-chart>
</div>

<script src="/js/d3-aide.js"></script>
<script type="module" src="/js/wc/d3/stacked-bar-chart.js"></script>
<script>

  function getTimeRanges(startDateInput, endDateInput) {
    let invalidData = false;
    let noDataFound = false;
    const barChart = document.querySelector('stacked-bar-chart');
    barChart.noDataFound = false;
    fetchData(`/studies/${studyId}/participants/${participantId}/time-range-stacked-data?startDate=` + startDateInput + `&&endDate=` + endDateInput, (resdata, error) => {
      if (!error) {
        const metrics = [
          { variable: "timeBelowRangeLow", attribute: "time_below_range_low_percentage" },
          { variable: "timeBelowRangeVeryLow", attribute: "time_below_range_very_low_percentage" },
          { variable: "timeInRange", attribute: "time_in_range_percentage" },
          { variable: "timeAboveRangeVeryHigh", attribute: "time_above_vh_percentage" },
          { variable: "timeAboveRangeHigh", attribute: "time_above_range_high_percentage" },
        ];
        let setZeroTimes = false;
        const result = resdata.data.timeRangeStackedData.data;
        metrics.map((metric) => {
          if (result && result.hasOwnProperty(metric.attribute) && !setZeroTimes) {
            if (result[metric.attribute] != 0) {
              setZeroTimes = true;
            }
          }
        })
        metrics.map((metric) => {
          if (result && result.hasOwnProperty(metric.attribute) && setZeroTimes) {
            timeMetrics[metric.variable] = parseInt(result[metric.attribute]);
          } else {
            invalidData = true;

          }

        }
        )

      }
      else {
        invalidData = true;
      }
      createChart(invalidData, noDataFound);
    })

  }


  function createChart(invalidData, noDataFound) {
    clearSVG("#tir-chart", "stacked-bar-chart");
    document.querySelectorAll(".chartContainer").forEach((elem) => {
      elem.classList.remove("hidden")
    });
    document.querySelectorAll(".tir-chart-element-error").forEach((elem) => {
      elem.classList.add("hidden");
    });
    document.querySelectorAll(".tir-chart-element-no-data-error").forEach((elem) => {
      elem.classList.add("hidden");
    });
    document.querySelectorAll(".tir-loader").forEach((loader) => {
      loader.classList.add("hidden");
    });

    // Data
    const chartHeight = 400;
    const chartWidth = 700;

    const chartdata = [
      {
        category: "Very Low",
        value: timeMetrics.timeBelowRangeVeryLow,
        goal: "<1%",
        color: "#A93226",
      },
      {
        category: "Low",
        value: timeMetrics.timeBelowRangeLow,
        goal: "<4%",
        color: "#E74C3C",
      },
      {
        category: "Target",
        value: timeMetrics.timeInRange,
        goal: "≥70%",
        color: "#27AE60",
      },
      {
        category: "High",
        value: timeMetrics.timeAboveRangeHigh,
        goal: "<25%",
        color: "#F39C12",
      },
      {
        category: "Very High",
        value: timeMetrics.timeAboveRangeVeryHigh,
        goal: "<5%",
        color: "#D35400",
      },
    ];

    // Normalize data to ensure total is 100
    const total = chartdata.reduce((acc, curr) => acc + curr.value, 0);
    const barChart = document.querySelector('stacked-bar-chart');
    if (invalidData === false) {
      barChart.data = chartdata;
      barChart.chartWidth = 700;
      barChart.chartHeight = 400;
    } else {
      barChart.noDataFound = true;
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    document.getElementById("tir-reload").addEventListener("click", function (event) {
      document.querySelectorAll(".tir-loader").forEach((loader) => {
        loader.classList.remove("hidden");
      });
      document.querySelectorAll(".tir-chart-element-error").forEach((loader) => {
        loader.classList.add("hidden");
      });
      getTimeRanges(startDateInput, endDateInput);
    });
  });

</script>