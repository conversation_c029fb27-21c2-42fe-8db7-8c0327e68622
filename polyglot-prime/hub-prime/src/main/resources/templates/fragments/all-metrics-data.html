<div>
    <div class="w-[100%] px-2 float-left">
        <div
            class="flex m-2 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Glycemic Lability Index
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('liabilityIndexCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="liabilityIndexCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="liabilityIndexCdata"></span><span
                            class="text-xs liabilityIndexCdataUnit hidden">mg/dL</span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('liability_index')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('liability_index')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Hypoglycemic Episodes
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('hypoglycemicEpisodesCdataLoader')}">
            </div> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="hypoglycemicEpisodesCdata"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('hypoglycemic_episodes')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('hypoglycemic_episodes')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Euglycemic Episodes
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('euglycemicEpisodesCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="euglycemicEpisodesCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="euglycemicEpisodesCdata"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('euglycemic_episodes')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('euglycemic_episodes')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Hyperglycemic Episodes
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('hyperglycemicEpisodesCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="hyperglycemicEpisodesCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="hyperglycemicEpisodesCdata"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('hyperglycemic_episodes')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('hyperglycemic_episodes')}"></div>
        </div>

        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                M Value
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('mValueCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="mValueCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="mValueCdata"></span> <span class="text-xs mValueCdataUnit hidden">mg/dL</span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('m_value')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('m_value')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Mean Amplitude
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('meanAmplitudeCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="meanAmplitudeCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="meanAmplitudeCdata"></span> <span class="text-xs meanAmplitudeUnit"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('mean_amplitude')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('mean_amplitude')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Average Daily Risk Range
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('averageDailyRiskRangeCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="averageDailyRiskRangeCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="averageDailyRiskRangeCdata"></span> <span
                            class="text-xs averageDailyRiskRangeCdataUnit hidden">mg/dL</span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('average_daily_risk_range')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('average_daily_risk_range')}"></div>
        </div>

        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                J Index
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('jIndexCdataLoader')}">
            </div> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="jIndexCdata"></span> <span class="text-xs jIndexCdataUnit hidden">mg/dL</span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('j_index')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('j_index')}"></div>
        </div>

        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Low Blood Glucose Index
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('lowBloodGlucoseIndexCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="lowBloodGlucoseIndexCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="lowBloodGlucoseIndexCdata"></span> <span
                            class="text-xs lowBloodGlucoseIndexUnit hidden"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('low_blood_glucose_index')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('low_blood_glucose_index')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                High Blood Glucose Index
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('highBloodGlucoseIndexCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="highBloodGlucoseIndexCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="highBloodGlucoseIndexCdata"></span> <span
                            class="text-xs highBloodGlucoseIndexUnit hidden"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('high_blood_glucose_index')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('high_blood_glucose_index')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Glycemic Risk Assessment Diabetes Equation (GRADE)
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('gradeCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="gradeCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="gradeCdata"></span> <span class="text-xs gradeCdataUnit hidden"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('glycemic_risk_assessment')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('glycemic_risk_assessment')}"></div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Continuous Overall Net Glycemic Action (CONGA)
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('congaCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="congaCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="congaCdata"></span> <span class="text-xs congaCdataUnit hidden"></span>
                    </div>
                    <div
                        th:replace="~{fragments/calculatorText :: calculatorText('continuous_overall_net_glycemic_action')}">
                    </div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div
                th:replace="~{fragments/calculatorText :: calculatorTextContainer('continuous_overall_net_glycemic_action')}">
            </div>
        </div>
        <div
            class="flex m-2 xl:pt-3 pt-0 w-full h-fit pb-[1px] justify-between items-center border-b-[0.5px] border-solid border-[#ada7a7]">
            <p class="font-sans text-sm font-normal">
                Mean of Daily Differences
            </p>
            <div class="flex items-center justify-end space-x-1">
                <!-- <div th:replace="~{fragments/loader :: loader('meanOfDailyDifferencesCdataLoader')}">
            </div> -->
                <!-- <span class="loader loader-small" id="meanOfDailyDifferencesCdataLoader"></span> -->
                <div class="flex flex-row p-1">
                    <div>
                        <span class="meanOfDailyDifferencesCdata"></span> <span
                            class="text-xs meanOfDailyDifferencesCdataUnit hidden"></span>
                    </div>
                    <div th:replace="~{fragments/calculatorText :: calculatorText('mean_of_daily_differences')}"></div>
                </div>
            </div>
        </div>
        <div class="relative">
            <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('mean_of_daily_differences')}"></div>
        </div>
        <script>
            function getAllMetricsData(startDateInput, endDateInput) {

                // showLoader(metric.class)
                fetchData(`/studies/${studyId}/participants/${participantId}/advanced-metrics?startDate=` + startDateInput + `&&endDate=` + endDateInput, (resdata, error) => {

                    if (!error) {
                        let result = resdata?.data?.metrics.data;
                        const allMetricParams = [
                            {
                                class: "liabilityIndexCdata",
                                datakey: "liability_index",
                            },
                            {
                                class: "meanAmplitudeCdata",
                                datakey: "mean_amplitude",
                            },
                            {
                                class: "mValueCdata",
                                datakey: "m_value",
                            },
                            {
                                class: "timeInTightRangeCdata",
                                datakey: "time_in_tight_range_percentage",
                            },
                            {
                                class: "averageDailyRiskRangeCdata",
                                datakey: "avg_risk_score",
                            },
                            {
                                class: "jIndexCdata",
                                datakey: "j_index",
                            },
                            {
                                class: "lowBloodGlucoseIndexCdata",
                                datakey: "lbgi",
                            },
                            {
                                class: "highBloodGlucoseIndexCdata",
                                datakey: "hbgi",
                            },
                            {
                                class: "gradeCdata",
                                datakey: "GRADE",
                            },
                            {
                                class: "meanOfDailyDifferencesCdata",
                                datakey: "mean_daily_diff",
                            },
                            {
                                class: "congaCdata",
                                datakey: "conga_hourly",
                            },
                            {
                                class: "hypoglycemicEpisodesCdata",
                                datakey: "hypoglycemic_episodes",
                            },
                            {
                                class: "euglycemicEpisodesCdata",
                                datakey: "euglycemic_episodes",
                            },
                            {
                                class: "hyperglycemicEpisodesCdata",
                                datakey: "hyperglycemic_episodes",
                            }
                        ];
                        allMetricParams.forEach((metric) => {
                            if (result.hasOwnProperty(metric.datakey)) {
                                if (result[metric.datakey] != null) {
                                    if (metric.class == "timeInTightRangeCdata") {
                                        assignValues(
                                            "timeInTightRangeCdata",
                                            Number(
                                                result[metric.datakey]
                                            ).toFixed(3)
                                        );
                                        showItem("timeInTightRangeCdataUnit");
                                    } else {
                                        assignValues(
                                            metric.class,
                                            Number(result[metric.datakey]).toFixed(3)
                                        );
                                        showItem(metric.class + "Unit");
                                    }
                                }
                                else {
                                    assignRetry(
                                        metric,
                                        nodataSvg
                                    );
                                    hideItem(metric.class + "Unit");
                                }
                            }
                            else {

                                assignRetry(
                                    metric,
                                    nodataSvg
                                );
                                hideItem(metric.class + "Unit");
                            }
                        });
                    }
                    else {
                        console.error("Error fetching participant metric info:", error);
                    }

                    // hideLoader(metric.class)

                });
            }

        </script>