<style>
    .agp-ctr,
    .tir-ctr {
        position: relative;
    }

    .tooltip {
        position: absolute;
        text-align: center;
        width: 100px;
        height: 40px;
        padding: 2px;
        font: 12px sans-serif;
        background: lightsteelblue;
        border: 0px;
        border-radius: 8px;
        pointer-events: none;
    }

    .agp-loader,
    .tir-loader,
    .dgp-loader,
    .gri-loader {
        border: 8px solid #f3f3f3;
        /* Light grey */
        border-top: 8px solid #3498db;
        /* Blue */
        border-radius: 50%;
        width: 60px;
        height: 60px;
        animation: spin 2s linear infinite;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
</style>
<div class="w-full border border-gray-400 rounded relative">
    <div class="flex p-2" style="background-color: #E3E3E2; border-bottom: 1px solid #E3E3E2;">
        <p class="text-black font-sans text-base font-bold">
            AMBULATORY GLUCOSE PROFILE (AGP)
        </p>
        <div class="ml-auto">
            <div th:replace="~{fragments/calculatorText :: calculatorText('AGP_metrics')}"></div>
        </div>
    </div>
    <div class="p-2">
        <div th:replace="~{fragments/calculatorText :: calculatorTextContainer('AGP_metrics')}"></div>
    </div>

    <div class="agp-loader hidden"></div>
    <!-- <p class="agp-chart-element-error hidden text-md font-bold text-center py-6"></p> -->
    <div class="agp-chart-element-error hidden">
        <div
            th:replace="~{fragments/chart-errors :: chartError('/chart-skeletons/agp-skel-gr.png', '35rem','agp-reload')}">
        </div>
    </div>
    <div class="agp-chart-element-no-data-error hidden">
        <div
            th:replace="~{fragments/chart-errors :: chartNoDataError('/chart-skeletons/agp-skel-gr.png', '35rem','agp-reload')}">
        </div>
    </div>
    <!-- <div id="agp-chart-ctr" class="p-5">
        <svg id="agp-chart"></svg>
    </div> -->
    <agp-chart class="p-5"></agp-chart>

</div>
<script type="module" src="/js/wc/d3/agp-chart.js"></script>
<script>

    async function getGraphData(startDateInput, endDateInput, retries = 3, delay = 5000) {
        try {
            clearSVG("#agp-chart", "agp-chart");
            document.querySelectorAll("svg#agp-chart").forEach((elem) => {
                elem.classList.remove('hidden');
            });
            document
                .querySelectorAll(".agp-chart-element-error")
                .forEach((elem) => {
                    elem.classList.add("hidden");
                });
            document
                .querySelectorAll(".agp-chart-element-no-data-error")
                .forEach((elem) => {
                    elem.classList.add("hidden");
                });
            // await new Promise((resolve) => setTimeout(resolve, 3000)); // Add a 3-second delay

            const response = await fetch(
                `/studies/${studyId}/participants/${participantId}/ambulatory-glucose-profile?startDate=` + startDateInput + `&&endDate=` + endDateInput,
            );

            // Check if the request was successful
            if (!response.ok) {
                throw new Error(
                    "Network response was not ok " + response.statusText
                );
            }
            const result = await response.json();

            // Set dimensions and margins
            const margin = { top: 20, right: 30, bottom: 40, left: 60 };
            const width = 800 - margin.left - margin.right;
            const height = 500 - margin.top - margin.bottom;

            const agpChart = document.querySelector('agp-chart');
            let agpdata = result.data.ambulatoryGlucoseProfile.data;
            if (agpdata && agpdata != null) {
                agpChart.data = agpdata;
                agpChart.width = width;
                agpChart.height = height;
                agpChart.noDataFound = false;

            } else {
                agpChart.noDataFound = true;
            }
            document.querySelectorAll(".agp-loader").forEach((loader) => {
                loader.classList.add("hidden");
            });

        } catch (error) {
            console.error("Fetch error:", error);
            if (retries > 0) {
                console.log(`Retrying... Attempts left: ${retries}`);
                setTimeout(() => getGraphData(startDateInput, endDateInput, retries - 1, delay), delay);
            } else {
                console.error("Max retries reached. Could not fetch data.");
                // Hide the loader in case of error
                document.querySelectorAll(".agp-loader").forEach((loader) => {
                    loader.classList.add("hidden");
                });

            }
        }
    }

    // Function to calculate average values
    function calculateAverages(data) {
        const result = {};

        data.forEach((entry) => {
            const { hour, p5, p25, p50, p75, p95 } = entry;

            if (!result[hour]) {
                result[hour] = {
                    count: 0,
                    totalP5: 0,
                    totalP25: 0,
                    totalP50: 0,
                    totalP75: 0,
                    totalP95: 0,
                };
            }

            result[hour].count++;
            result[hour].totalP5 += p5;
            result[hour].totalP25 += p25;
            result[hour].totalP50 += p50;
            result[hour].totalP75 += p75;
            result[hour].totalP95 += p95;
        });

        return Object.keys(result).map((hour) => {
            const { count, totalP5, totalP25, totalP50, totalP75, totalP95 } =
                result[hour];
            return {
                hour,
                avgp5: totalP5 / count,
                avgp25: totalP25 / count,
                avgp50: totalP50 / count,
                avgp75: totalP75 / count,
                avgp95: totalP95 / count,
            };
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        document.getElementById("agp-reload").addEventListener("click", function (event) {
            document.querySelectorAll(".agp-loader").forEach((loader) => {
                loader.classList.remove("hidden");
            });
            getGraphData(startDateInput, endDateInput);
        });
    });
</script>