<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<body>
  <div th:fragment="chartError(imageUrl,height,reloadBtnId)">
    <div th:classappend="${'bg-[url('+imageUrl+')] h-['+height+']'}"
      class="flex justify-center z-[1] w-full h-full relative text-md font-bold text-center bg-center bg-no-repeat bg-contain">
      <div class="content-center">
        <div class="rounded-[5px] w-full p-6 bg-[#a3b8d3ab] text-white" style="border: 1px solid #557dae">
          <p>There is an error encountered while generating the chart.</p>
          <button th:id="${reloadBtnId}" class="text-white bg-[#557dae] px-3 py-2 rounded-[5px] mt-3">
            RELOAD
          </button>
        </div>
      </div>
    </div>
  </div>
  <div th:fragment="chartNoDataError(imageUrl,height,reloadBtnId)">
    <div th:classappend="${'bg-[url('+imageUrl+')] h-['+height+']'}"
      class="flex justify-center z-[1] w-full h-full relative text-md font-bold text-center bg-center bg-no-repeat bg-contain">
      <div class="content-center">
        <div class="rounded-[5px] w-full p-6 bg-[#a3b8d3ab] text-white" style="border: 1px solid #557dae">
          <p>No data Found, Try again with another date range.</p>
        </div>
      </div>
    </div>
  </div>
</body>

</html>