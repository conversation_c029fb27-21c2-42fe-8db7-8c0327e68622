<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />

  <!-- if JSON Viewer is not already in the layout, add the following -->
  <!-- <script src="https://unpkg.com/@alenaksu/json-viewer@2.0.0/dist/json-viewer.bundle.js"></script> -->

  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'Cohort',
        breadcrumbs: [
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "DCLP1";
    // Now you can use studyId in your JavaScript code
    var initFilter = false;
    const infoSvg = `<img src="/images/info.svg" alt="Info Icon">`;
  </script>
  <script th:src="@{/js/utils.js}"></script>
  <script type="module" src="/js/cohort.js" th:attr="data-userid=${#authentication.getPrincipal()}"></script>
</head>

<body>
  <div layout:fragment="content">
    <!-- Introduction Section (Full Width) -->
    <div class="flex justify-between flex-col pb-4">
      <div clas="mb-2"
        th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription},pagesubdescriptiontitle=${pagesubdescriptiontitle}, pagesubdescription=${pagesubdescription}, pageattributestitle=${pageattributestitle}, pageattributes=${pageattributes},notes=null,font='font-semibold')}">
      </div>
    </div>

    <!-- Filter Section (Full Width) -->
    <div class="w-full flex bg-[#f1f3f5] px-5 py-4 rounded-[5px]">
      <div th:replace="~{fragments/cohortFilter}"></div>
    </div>

    <!-- Main Content Section (Metrics & DataGrid) -->
    <div class="w-full flex flex-col">
      <div class="w-full">
        <div>
          <!-- Metrics Section (Responsive) -->
          <div class="flex flex-wrap justify-center py-4 mb-4 gap-4">
            <div class="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4"
              th:id="cohortMetrics">
              <!-- Each metric adjusts its width depending on screen size -->
              <div class="w-full"
                th:replace="~{fragments/metricsWithTitleCohortCard :: serverTextStat('Total Participants With Data', null, @{/svm/cohort/totalParticipant.html}, 'totalParticipants')}">
              </div>
              <div class="w-full"
                th:replace="~{fragments/metricsWithTitleCohortCard :: serverTextStat('Total CGM Files', null, @{/study/allstudy-total-cgm-files.html}, 'totalCgmFiles')}">
              </div>
              <div class="w-full"
                th:replace="~{fragments/metricsWithTitleCohortCard :: serverTextStat('% Female', null, @{/study/allstudy-vanity-metrics/percent_female.html}, 'totalFemalePercent')}">
              </div>
              <div class="w-full"
                th:replace="~{fragments/metricsWithTitleCohortCard :: serverTextStat('Average Age', null, @{/study/allstudy-vanity-metrics/average_age.html}, 'totalAverageAge')}">
              </div>
            </div>
          </div>

          <!-- DataGrid Section -->
          <div>
            <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main modal -->
    <div id="save-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white modal-title-text">
              Save Filter
            </h3>
            <button type="button" id="close-filter-modal"
              class="cancel-filter-modal end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
              data-modal-hide="save-modal">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close modal</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="p-4 md:p-5">
            <div class="grid gap-4 mb-4 grid-cols-2">
              <div class="col-span-2">
                <label for="filterName" class="block flex mb-2 text-sm font-medium text-gray-900 dark:text-white">Filter
                  Name<span class="text-red-500">*</span>
                  <svg data-tooltip-target="tooltip-animation-name" data-tooltip-placement="top"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="size-5 ml-0.5 text-gray-400 focus:outline-0">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                  </svg>

                </label>

                <div id="tooltip-animation-name" role="tooltip"
                  class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-normal text-white transition-opacity duration-300 bg-gray-700 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                  Enter a unique name for the filter. This name will help you identify the filter when selecting it from
                  the 'Saved Filters' list later.
                  <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
                <input type="text" name="filterName" id="filterName"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white"
                  placeholder="New Filter" required />
              </div>
              <div class="col-span-2">
                <label for="filterDescription"
                  class="block flex mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  Filter Description
                  <svg data-tooltip-target="tooltip-animation-desc" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                    class="size-5 ml-0.5 text-gray-400 focus:outline-0">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                  </svg>
                </label>
                <div id="tooltip-animation-desc" role="tooltip"
                  class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-normal text-white transition-opacity duration-300 bg-gray-700 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                  Provide a brief description of what this filter does. This will help you or others understand the
                  purpose and specifics of the filter when viewing the saved filters.
                  <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
                <textarea name="filterDescription" id="filterDescription" rows="4"
                  class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  placeholder="Write filter description here"></textarea>
              </div>
              <div class="col-span-2">
                <label for="viewMode"
                  class="block flex mb-2 text-sm font-medium text-gray-900 dark:text-white">Viewers<svg
                    data-tooltip-target="tooltip-animation-viewers" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                    class="size-5 ml-0.5 text-gray-400 focus:outline-0">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                  </svg></label>
                <div id="tooltip-animation-viewers" role="tooltip"
                  class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-normal text-white transition-opacity duration-300 bg-gray-700 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                  Who can view this filter? Select who can access this filter:
                  <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
                <select name="viewMode" id="viewMode"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                  <option selected value="">Select</option>
                  <option value="Public">Public (Visible to all users of the application)</option>
                  <option value="Private">Private (Only visible to you (the logged-in user))</option>
                  <!-- <option value="Machine">Machine (Only visible on the device/machine you're currently using)</option> -->
                </select>
              </div>
              <!-- <div class="col-span-2">
                <label for="viewMode"
                  class="block flex mb-2 text-sm font-medium text-gray-900 dark:text-white">Save as new filter</label>
                <label class="inline-flex items-center cursor-pointer">
                  <input type="checkbox" value="" class="sr-only peer">
                  <div
                    class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                  </div>
                  <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">New Filter</span>
                </label>
              </div> -->
            </div>
            <input type="hidden" name="filterId" id="filterId" />
            <button data-modal-hide="popup-modal" type="button" id="save-filter"
              class="text-white bg-green-600 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
              Save Filter
            </button>
            <button data-modal-hide="save-modal" type="button"
              class="cancel-filter-modal py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
              Cancel</button>

          </div>
        </div>
      </div>
    </div>
    <!-- Modal HTML Structure -->
    <div id="error-popup-show" data-modal-backdrop="static" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <div class="flex justify-between items-center p-4 border-b rounded-t dark:border-gray-600">
            <h3 id="modal-title" class="text-xl font-semibold text-gray-900 dark:text-white">
              Modal Title
            </h3>
            <button id="close-modal" data-modal-hide="error-popup-show"
              class="text-gray-400 hover:text-gray-900 dark:hover:text-white" aria-label="Close">
              <svg class="w-5 h-5" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <div class="p-4">
            <p id="modal-message" class="text-gray-700 dark:text-gray-300">
              Modal Message
            </p>
          </div>
          <div class="flex justify-end p-4 border-t dark:border-gray-600">
            <button id="dismiss-modal" data-modal-hide="error-popup-show"
              class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
  </div>
</body>


</html>