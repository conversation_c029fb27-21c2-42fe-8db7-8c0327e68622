<script type="module" src="https://unpkg.com/deep-chat@2.1.1/dist/deepChat.bundle.js"></script>
<!-- This example is for Vanilla JS and should be tailored to your framework (see Frameworks) -->

<deep-chat id="chat-element" style="border-radius: 10px; height: calc(100vh - 350px); width:60vw !important;  padding-top: 10px; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
font-size: 14px!important" textInput='{"placeholder":{"text": "Ask me a question about your data, and I will find the answer for you!", "style": {"padding": "1em .9em","min-height": "50px"}},
  "styles": {
    "container": {"width": "100%", "backgroundColor": "#efefef","border": "2px solid #d1d5db","box-shadow": "#959da533 0 0px 0px"},
    
    "text": {"padding": "1em .9em","min-height": "50px"}
}}' messageStyles='{"default": {"shared": {"innerContainer": {"fontSize": ".875rem"}},
  "user": {
    "innerContainer": {"width": "100%"},
    "bubble": {"padding":"10px", "borderRadius": "10px","color":"white", "backgroundColor":"#1F2937", "marginTop":"15px","marginRight":"25px" ,"marginBottom":"10px","maxWidth":"90%","minWidth":"90%","borderTopRightRadius":"0" }
  },
  "ai": {
   "bubble": {"backgroundColor": "#f3f4f6", "border": "1px solid #efefef", "borderRadius": "10px", "maxWidth": "100%", "boxShadow": "none", "padding": "15px","marginLeft":"25px","marginRight":"25px","maxWidth":"90%","minWidth":"90%","borderTopLeftRadius":"0" },
    "innerContainer": {
      "width": "100%"
    }
  }}
}' inputAreaStyle='{"fontSize": "0.9rem","marginBottom":"5px","marginRight":"5px","marginLeft":"5px","width":"auto"}'
  submitButtonStyles='{
        "submit": {
          "container": {
            "default": {"backgroundColor": "#0084ff","width": "2em",
            "height":"2em","color":"white","marginBottom": "10px",
            "marginRight": "10px"},
            "hover": {"backgroundColor": "#0058d5"}
          },
          "svg": {
            "styles": {
              "default": {
                "filter":"none",
                "width": "1.4rem"
              }
            }
          }
        },
        "loading": {
          "svg": {
            "styles": {
              "default": {
                "filter":"brightness(0) saturate(100%) invert(70%) sepia(70%) saturate(4438%) hue-rotate(170deg) brightness(92%) contrast(98%)",
                "width": ".2em",
                "height":".2em"
              }
            }
          }
        }
      }'></deep-chat>