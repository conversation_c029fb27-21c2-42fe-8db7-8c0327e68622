<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
  <title>Welcome</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <style>
    .hanging-indent {
      padding-left: 1.5rem;
      text-indent: -1.5rem;
    }

    deep-chat#chat-element {
      border: 1px solid rgb(202, 202, 202) !important;
      margin: 0 auto !important;
      /* width: 100% !important; */
    }
  </style>
  <script th:src="@{/js/utils.js}"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";

    var tabTitle = "";
    const activeTab = document.querySelector('.tab-button.active');
    tabTitle = activeTab ? activeTab.textContent : null;
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'Ask DRH',
        breadcrumbs: [
          {
            text: "Ask DRH",
            href: "/ai",
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
      document.getElementById('text-input').innerHTML = "Ask any questions related to DRH in plain English";
    });
  </script>
  <script type="module" src="https://unpkg.com/deep-chat@2.1.1/dist/deepChat.bundle.js"></script>

  <script th:if="${chatAiBaseUrl != null} and ${chatAiBaseUrl} != ''" src="/js/ai-general.js"
    th:with="isOAuth2=${#authentication.getPrincipal() instanceof T(org.springframework.security.oauth2.core.user.OAuth2User)}"
    th:attr="data-api-url=${chatAiBaseUrl}+'api/v0/ask_question_and_run_query',data-api-url-sample='https://drh-ai-research.netspective.com/api/v1/workspace/drh-website/chat',data-suggestion-url=${chatAiBaseUrl}+'api/v0/generate_questions', data-user-id=${isOAuth2 ? (#authorization.expression('isAuthenticated()') ? #authentication.principal.attributes['login'] : ''):username}"></script>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">

  <div layout:fragment="content">

    <div id="searchDrhSample" class="py-4">
      <div>
        <div class="mx-auto max-w-9xl">
          <div class="flex justify-between flex-col">
            <div
              th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription},pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes=null,font='')}">
            </div>
          </div>
          <div class="w-full flex flex-col h-screen">
            <div th:replace="~{page/askdrh/chatcomponent}">

            </div>
            <!-- <deep-chat id="chat-element" style="border-radius: 10px; width: 92vw; height: calc(100vh - 400px); padding-top: 10px; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
font-size: 14px!important" textInput='{"placeholder":{"text": "Ask me a question about your data, and I will find the answer for you!", "style": {"padding": "1em .9em","min-height": "50px"}},
  "styles": {
    "container": {"width": "100%", "backgroundColor": "#efefef","border": "0px","box-shadow": "#959da533 0 0px 0px"},
    
    "text": {"padding": "1em .9em","min-height": "50px"}
}}' messageStyles='{"default": {"shared": {"innerContainer": {"fontSize": ".875rem"}},
  "user": {
    "innerContainer": {"width": "100%"},
    "bubble": {"marginRight":"25px","padding":"10px", "borderRadius": "5px","color":"#333", "backgroundColor":"rgb(219 234 254)", "fontSize":"15px", "marginTop":"15px", "marginBottom":"5px" }
  },
  "ai": {
    "bubble": {"backgroundColor": "rgb(255 255 255)", "border": "1px solid #efefef", "borderRadius": "5px", "maxWidth": "100%", "boxShadow": "none", "padding": "15px","marginRight":"25px","maxWidth":"90%"},
    "innerContainer": {
      "width": "100%"
    }
  }}
}' inputAreaStyle='{"fontSize": "0.9rem"}' submitButtonStyles='{
        "submit": {
          "container": {
            "default": {"backgroundColor": "#0084ff","width": "2em",
            "height":"2em","color":"white","marginBottom": "10px",
            "marginRight": "10px"},
            "hover": {"backgroundColor": "#0058d5"}
          },
          "svg": {
            "styles": {
              "default": {
                "filter":"none",
                "width": "1.4rem"
              }
            }
          }
        },
        "loading": {
          "svg": {
            "styles": {
              "default": {
                "filter":"brightness(0) saturate(100%) invert(70%) sepia(70%) saturate(4438%) hue-rotate(170deg) brightness(92%) contrast(98%)",
                "width": ".2em",
                "height":".2em"
              }
            }
          }
        }
      }'></deep-chat> -->
          </div>
        </div>
      </div>

    </div>
  </div>


</body>



</html>