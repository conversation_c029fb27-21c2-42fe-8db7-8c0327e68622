<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <title>Welcome</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <style>
        .hanging-indent {
            padding-left: 1.5rem;
            text-indent: -1.5rem;
        }

        deep-chat#chat-element {
            /* width: 80vw !important; */
            border: 1px solid rgb(202, 202, 202) !important;
            margin: 0 auto !important;
        }
    </style>
    <script th:src="@{/js/utils.js}"></script>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";

        var tabTitle = "";
        const activeTab = document.querySelector('.tab-button.active');
        tabTitle = activeTab ? activeTab.textContent : null;
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'Ask DRH iCODE',
                breadcrumbs: [
                    {
                        text: "Ask DRH",
                        href: "/ai",
                    },
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
        });
    </script>

    <script th:if="${chatResearchAiBaseUrl != null} and ${chatResearchAiBaseUrl} != ''" src="/js/ai-icode.js"
        th:with="isOAuth2=${#authentication.getPrincipal() instanceof T(org.springframework.security.oauth2.core.user.OAuth2User)}"
        th:attr="data-api-url=${chatAiBaseUrl}+'api/v0/ask_question_and_run_query',data-api-url-sample=${chatResearchAiBaseUrl}+'api/v1/workspace/icode/chat',data-suggestion-url=${chatAiBaseUrl}+'api/v0/generate_questions', data-user-name=${username},data-user-id=${authProviderId}">
        </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">

    <div layout:fragment="content">

        <div id="searchDrhSample" class="py-4">
            <div>
                <div class="mx-auto max-w-9xl">
                    <div class="flex justify-between flex-col">
                        <div
                            th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription},pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes=null,font='')}">
                        </div>
                    </div>
                    <div class="w-full flex flex-col h-screen">
                        <div th:replace="~{page/askdrh/chatcomponent}">
                        </div>
                        <!-- <deep-chat id="chat-element" style="width: 85vw; border: none;" textInput='{"placeholder":{"text": "Ask me a question about your data that I can turn into SQL and provide answer"},"styles": {
                                "container": {"width": "84.9vw"}
                              }}' chatStyle='{"height": "500px","border":"none","width":}'
                            inputAreaStyle='{"fontSize":"0.9rem", "width":"85vw"}' submitButtonStyles='{
                                "submit": {
                                  "container": {
                                    "default": {"right": "0"}
                                  },
                                },
                                "alwaysEnabled": true,
                                "position": "outside-right",
                              }' messageStyles='{
                                "default": {
                                  "user": {
                                    "innerContainer": {
                                        "width": "100%"
                                      }
                                    },
                                  "ai": {
                                    "innerContainer": {
                                      "width": "100%"
                                    }
                                  }
                                }
                              }'></deep-chat> -->
                    </div>
                </div>
            </div>

        </div>
    </div>


</body>



</html>