<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />

  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    // Now you can use studyId in your JavaScript code
  </script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    var studyId = /*[[${studyId}]]*/ "";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: studyId,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/summary/studies",
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>

  <script type="module">
    import {
      AGGridAide,
      AGGridAideBuilder,
    } from "@presentation/shell/aggrid-aide.js";
    import ModalAide from "@presentation/shell/modal-aide.js";

    const viewName = "study_combined_dashboard_participant_metrics_view";
    document.addEventListener("DOMContentLoaded", function () {
      getStudyDetails();
      const modalAide = new ModalAide();
      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          {
            headerName: "Participant ID",
            field: "participant_id",
            filter: "agTextColumnFilter",
            cellRenderer: function (params) {
              if (params.value) {
                const link = document.createElement("a");
                link.href = "/participant/" + studyId + "/" + params.value;
                link.innerText = params.value;
                return link;
              } else {
                return null;
              }
            },
          },
          {
            headerName: "Gender",
            field: "gender",
            filter: "agTextColumnFilter",
          },
          { headerName: "Age", field: "age", filter: "agTextColumnFilter" },
          {
            headerName: "Study Arm",
            field: "study_arm",
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Baseline HbA1c",
            field: "baseline_hba1c",
            filter: "agTextColumnFilter",
            valueFormatter: (params) => {
              // Check if the value is a number and append "%" symbol
              return params.value != null ? `${params.value}%` : '';
            }
          },
          {
            headerName: "TIR", field: "tir", filter: "agTextColumnFilter", valueFormatter: (params) => {
              // Check if the value is a number and append "%" symbol
              return params.value != null ? `${params.value}%` : '';
            }
          },
          {
            headerName: "TAR(VH)",
            field: "tar_vh",
            filter: "agTextColumnFilter", valueFormatter: (params) => {
              // Check if the value is a number and append "%" symbol
              return params.value != null ? `${params.value}%` : '';
            }
          },
          {
            headerName: "TAR(H)",
            field: "tar_h",
            filter: "agTextColumnFilter", valueFormatter: (params) => {
              // Check if the value is a number and append "%" symbol
              return params.value != null ? `${params.value}%` : '';
            }
          },
          {
            headerName: "TBR(L)",
            field: "tbr_l",
            filter: "agTextColumnFilter", valueFormatter: (params) => {
              // Check if the value is a number and append "%" symbol
              return params.value != null ? `${params.value}%` : '';
            }
          },
          {
            headerName: "TBR(VL)",
            field: "tbr_vl",
            filter: "agTextColumnFilter", valueFormatter: (params) => {
              // Check if the value is a number and append "%" symbol
              return params.value != null ? `${params.value}%` : '';
            }
          },
          {
            headerName: "GMI", field: "gmi", filter: "agTextColumnFilter"
            // , valueFormatter: (params) => {
            //   // Check if the value is a number and append "%" symbol
            //   return params.value != null ? `${params.value}mg/dL` : '';
            // }
          },
          {
            headerName: "%GV",
            field: "percent_gv",
            filter: "agTextColumnFilter",
          },
          {
            headerName: "GRI", field: "gri", filter: "agTextColumnFilter"
            // , valueFormatter: (params) => {
            //   // Check if the value is a number and append "%" symbol
            //   return params.value != null ? `${params.value}%` : '';
            // }
          },
          {
            headerName: "Days Of Wear",
            field: "days_of_wear",
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Data Start Date",
            field: "data_start_date",
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Data End Date",
            field: "data_end_date",
            filter: "agTextColumnFilter",
          },
          {
            headerName: "CGM Devices",
            field: "cgm_devices",
            filter: "agTextColumnFilter",
            tooltipValueGetter: (params) => `${params.value}`
          },
          {
            headerName: "CGM Files",
            field: "cgm_files",
            filter: "agTextColumnFilter",
            tooltipValueGetter: (params) => `${params.value}`
          },
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(
            `/api/ux/tabular/jooq/` +
            studyId +
            `/participant_dashboard_cached.json`
          ),
          (data, valueCols) => {
            return valueCols.map((col) => ({
              headerName: col.displayName,
              field: col.field,
            }));
          }
        )
        .withModalAide(modalAide)
        .withGridDivStyles({ height: "750px", width: "100%" })
        // .withGridOptions({
        //     onRowClicked: function (event) {
        //         // Example: Open a modal with study details
        //         const participantId = event.data.participant_id;
        //         const link = document.createElement("a");
        //         link.href = '/participant/' + studyId + '/' + participantId;
        //         link.click()
        //     },
        // })
        .build();

      agGridInstance.init("serverDataGrid");

      /****Loader********/
      // function showLoader(loaderId) {
      //   document.getElementById(loaderId).style.display = "block";
      // }

      // function hideLoader(loaderId) {
      //   document.getElementById(loaderId).style.display = "none";
      // }

      function showValue(eleClass) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.style.display = "block";
        });
      }
      /******************/

      function hideAllLoader() {
        hideLoading("studyNameLoader");
        hideLoading("studyDateLoader");
        hideLoading("endDateLoader");
        hideLoading("nctNumberLoader");
        hideLoading("primaryInvestigatorLoader");
        hideLoading("primaryInvestigatorTeamLoader");
        hideLoading("studyDescriptionLoader");
      }

      function getStudyDetails() {
        showLoading("studyNameLoader");
        showLoading("studyDateLoader");
        showLoading("endDateLoader");
        showLoading("nctNumberLoader");
        showLoading("primaryInvestigatorLoader");
        showLoading("primaryInvestigatorTeamLoader");
        showLoading("studyDescriptionLoader");

        fetch(`/study/each-study-details/` + studyId)
          .then((response) => {
            if (!response.ok) {
              throw new Error(
                "Network response was not ok " + response.statusText
              );
            }
            hideAllLoader();
            return response.json();
          })
          .then((data) => {
            assignValues("studyName", data.study_name);
            assignValues("startDate", data.start_date);
            assignValues("endDate", data.end_date);
            assignValues("nctNumber", data.nct_number);
            assignValues("studyDescription", data.study_description);
            assignValues("nctNumber", data.nct_number);

            // Split the investigators string into an array
            let investigatorsArray = data.investigators.split(",");
            let investigatorList =
              document.querySelector("#investigatorsList");
            investigatorsArray.forEach((investigator) => {
              const listItem = document.createElement("li");
              listItem.textContent = investigator;
              listItem.style.padding = "4px 0 4px 16px";
              investigatorList.appendChild(listItem);
            });
            assignValues("primaryInvestigator", investigatorsArray[0]);
            hideAllLoader();
            //document.getElementById('modal').style.display = 'block';
          })
          .catch((error) => {
            console.error(
              "There was a problem with the fetch operation:",
              error
            );
            assignValues("studyName", "⚠️");
            assignValues("startDate", "⚠️");
            assignValues("endDate", "⚠️");
            assignValues("nctNumber", "⚠️");
            assignValues("studyDescription", "⚠️");
            assignValues("nctNumber", "⚠️");
            assignValues("primaryInvestigator", "⚠️");
            hideAllLoader();
          });
      }

      function assignValues(eleClass, value) {
        document.querySelectorAll("." + eleClass).forEach((element) => {
          element.innerHTML = value;
        });
      }
    });
  </script>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
</head>

<body>
  <div layout:fragment="content">
    <article class="grid grid-cols-1 md:grid-cols-12 gap-0 md:gap-5">
      <div class="col-span-1 md:col-span-8 lg:col-span-9 pb-4">
        <div clas="mb-2"
          th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=${pagesubdescriptiontitle},pagesubdescription=${pagesubdescription},pageattributestitle=${pageattributestitle}, pageattributes=${pageattributes}, notes=null,font='font-semibold')}">
        </div>
      </div>
      <div class="col-span-1 md:col-span-4 lg:col-span-3 md:text-right">
        <a class="text-white min-w-[225px] max-h-9 bg-blue-500 hover:bg-blue-600 font-bold py-2 px-4 mb-2 rounded text-sm inline-block"
          th:href="${'/svm/files/'+studyId}">
          View imported CSV sources
        </a>
      </div>
    </article>
    <!-- <div class="flex justify-between">
      <div th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, notes=null)}"></div>
      <a class="text-white min-w-[225px] max-h-9 bg-blue-500 hover:bg-blue-600 font-bold py-2 px-4 mb-4 rounded text-sm inline-block"
        th:href="${'/svm/files/'+studyId}">
        View imported CSV sources
      </a>
    </div> -->
    <div class="grid grid-cols-1 md:grid-cols-12 lg:grid-cols-12 gap-">
      <div class="col-span-1 md:col-span-9 lg:col-span-10">
        <div>
          <div class="flex justify-between w-full p-4 border border-gray-200 bg-gray-50 rounded">
            <div class="w-[30%] p-2">
              <p class="text-black-500 font-bold text-sm">Study Name</p>
              <span class="loader loader-small" id="studyNameLoader"></span>
              <p class="text-black-500 font-normal text-sm studyName"></p>
            </div>
            <div class="p-2 mx-6">
              <p class="text-black-500 font-bold text-sm">Start Date</p>
              <span class="loader loader-small" id="studyDateLoader"></span>
              <p class="text-black-500 font-normal text-sm startDate"></p>
            </div>
            <div class="p-2 mx-6">
              <p class="text-black-500 font-bold text-sm">End Date</p>
              <span class="loader loader-small" id="endDateLoader"></span>
              <p class="text-black-500 font-normal text-sm endDate"></p>
            </div>
            <div class="p-2 mx-6">
              <p class="text-black-500 font-bold text-sm">NCT Number</p>
              <span class="loader loader-small" id="nctNumberLoader"></span>
              <p class="text-black-500 font-normal text-sm nctNumber"></p>
            </div>
          </div>
          <div class="flex justify-center py-4 mb-1 mt-2">
            <div class="w-[100%] flex justify-between grid grid-cols-1 md:grid-cols-2 gap-4 lg:grid-cols-4">
              <div
                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total Participants', null, @{'/study/each-study-total-participants/'+${studyId}+'.html'})}">
              </div>
              <div
                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total CGM Files', '/svm/cgm/files/list/'+${studyId}, @{'/study/allstudy-total-cgm-files/'+${studyId}+'.html'})}">
              </div>
              <div
                th:replace="~{fragments/metricsWithTitle :: serverTextStat('% Female', null, @{'/study/each-study-percentage-female/'+${studyId}+'.html'})}">
              </div>
              <div
                th:replace="~{fragments/metricsWithTitle :: serverTextStat('Average Age', null, @{'/study/each-study-average-age/'+${studyId}+'.html'})}">
              </div>
            </div>
          </div>
          <div class="w-[100%] py-2 mb-4">
            <p class="text-black-500 font-bold text-sm">Study Description</p>
            <p class="text-2xl font-bold text-blue-500 flex border-slate-300 p-2">
              <span class="loader loader-small" id="studyDescriptionLoader"></span>
            </p>

            <p class="text-black-500 font-normal text-sm studyDescription"></p>
          </div>
          <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
        </div>
      </div>
      <div class="col-span-1 md:col-span-3 lg:col-span-2">
        <div class="p-2 ml-6">
          <p class="text-black-500 font-bold text-sm">
            Study Primary Investigator
          </p>
          <span class="loader loader-small" id="primaryInvestigatorLoader"></span>
          <p class="text-black-500 font-normal text-sm primaryInvestigator py-1 pl-4"></p>
          <div class="py-2">
            <p class="text-black-500 font-bold text-sm">Study Team</p>
            <span class="loader loader-small" id="primaryInvestigatorTeamLoader"></span>
            <ul class="text-black-500 font-normal text-sm" id="investigatorsList"
              style="height: 1000px; overflow: auto"></ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>