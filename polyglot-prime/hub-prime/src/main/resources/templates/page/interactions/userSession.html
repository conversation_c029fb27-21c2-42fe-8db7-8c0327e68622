<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/sundry-multi-level}">

<head>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css">
  <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
  <script src="https://www.jsviews.com/download/jsrender.js"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'Activity Log',
        breadcrumbs: [{ text: "Interactions", href: "/interactions", }
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
    });
  </script>
  <script type="module">
    import { AGGridAide, AGGridAideBuilder } from '@presentation/shell/aggrid-aide.js';
    import ModalAide from '@presentation/shell/modal-aide.js';

    // Move modalAide declaration outside
    let modalAide;

    function formatDate(value) {
      if (!value) return ''; // Handle null or undefined values

      // Create a Date object from the input string (assumed to be in UTC)
      const date = new Date(value);

      // Extract components of the date in local time zone
      const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
      const day = String(date.getDate()).padStart(2, '0');
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      // Format the date string
      return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`;
    }

    function showActivityData(data) {
      const modal = document.getElementById("activityDataModal");
      const modalContent = document.getElementById("modalContent");

      if (modalContent) {
        modalContent.innerText = data ? data : 'No data available'; // Set the activity data to the modal content
        modal.style.display = "block"; // Show the modal
      } else {
        console.error("Modal content not found");
      }
    }

    // Close the modal
    function closeModal() {
      document.getElementById("activityDataModal").style.display = "none";
    }

    const schemaName = 'drh_stateless_activity_audit';
    const viewName = 'vw_activity_log';
    const notEqColumn = 'session_unique_id';
    const notEqColumnValue = 'metadata';

    const questionAnswersView = 'fhir_patient_screening_questions_answers';
    const questionAnswersColumnDefs = [
      // { headerName: "User Session", field: "session_id", filter: "agTextColumnFilter" },
      { headerName: "Activity Type", field: "activity_type", filter: "agTextColumnFilter" },
      { headerName: "Activity", field: "activity_name", filter: "agTextColumnFilter" },
      { headerName: "Description", field: "activity_description", filter: "agTextColumnFilter" },
      {
        headerName: "Path", field: "request_url", filter: "agTextColumnFilter",
        cellRenderer: AGGridAide.modalCellRenderer((params) => {
          const activityId = params.data.activity_id;
          console.log("ActivityID: " + activityId)
          modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/column/${schemaName}/${viewName}/activity_id/${activityId}.json?columns=activity_data,request_url,activity_description,activity_hierarchy`));
        }, modalAide)
      },
      // { headerName: "IP Address", field: "ip_address", filter: "agTextColumnFilter" },
      // { headerName: "App Version", field: "app_version", filter: "agDateColumnFilter" },
      {
        headerName: "Activity Data",
        field: "activity_data",
        filter: "agTextColumnFilter",
        cellRenderer: AGGridAide.modalCellRenderer((params) => {
          const activityId = params.data.activity_id;
          console.log("ActivityID: " + activityId)
          modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/column/${schemaName}/${viewName}/activity_id/${activityId}.json?columns=activity_data,request_url,activity_description`));
        }, modalAide)
      },
      {
        headerName: "Request Time", field: "created_at", filter: "agDateColumnFilter", sort: "desc", hide: false,
        valueFormatter: (params) => formatDate(params.value)
      },
    ];

    function getQuestionAndAnswerGridData(params) {
      const patientMRN = params.data.patient_mrn;
      const sessionUniqueId = params.data.session_unique_id;
      const createdBy = params.data.created_by;
      fetch(window.shell.serverSideUrl(`/api/ux/tabular/jooq/${schemaName}/${viewName}/session_unique_id/${sessionUniqueId}.json`))
        .then(response => response.json())
        .then(response => {
          params.successCallback(response);
        })
        .catch(error => {
          console.error('Error fetching details data' + error);
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
      modalAide = new ModalAide(); // Initialize modalAide here
      const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs([
          {
            headerName: "ID",
            field: "session_unique_id",
            filter: "agTextColumnFilter",
            rowGroup: false,
            hide: false,
            cellRenderer: 'agGroupCellRenderer', // Enable expand/collapse for master-detail
          },
          {
            headerName: "Request Time",
            field: "created_at",
            sort: "desc",
            filter: "agDateColumnFilter",
            hide: true,
            valueFormatter: (params) => formatDate(params.value)
          },
          {
            headerName: "Login ID", field: "created_by", filter: "agTextColumnFilter", rowGroup: false,
            hide: false,
          },
          {
            headerName: "Full Name", field: "user_name", filter: "agTextColumnFilter", rowGroup: false,
            hide: false,
          valueGetter: (params) => params.data?.user_name || "Anonymous"
          },
          {
            headerName: "Start Time", field: "min_created_at", filter: "agTextColumnFilter", rowGroup: false,
            hide: false,
            valueFormatter: (params) => formatDate(params.value)
          }
        ])
        .withServerSideDatasource(
          window.shell.serverSideUrl(`/api/ux/tabular/jooq/distinct/audit/${schemaName}/${viewName}.json?columns=session_unique_id,created_by,ip_address,app_version`),
          (data, valueCols) => {
            return valueCols.map(col => ({
              headerName: col.displayName,
              field: col.field
            }));
          },
        )
        .withMasterDetail(true)
        .withDetailCellRendererParams({
          detailGridOptions: {
            columnDefs: questionAnswersColumnDefs,
            defaultColDef: {
              flex: 1
            }
          },
          getDetailRowData: params => {
            getQuestionAndAnswerGridData(params);
          }
        })
        .withDetailRowAutoHeight(false)
        .withModalAide(modalAide)
        .withGridDivStyles({ height: "750px", width: "100%" })
        .build();

       agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

      agGridInstance.init('serverDataGrid');
    });        
  </script>

</head>

<body>
  <div layout:fragment="content">
    <div class="grid-description" id="date-range">
      <div class="flex justify-between flex-col">
        <div clas="mb-2"
          th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes=null,font='')}">
        </div>
      </div>
    </div>
    <div id="serverDataGrid" class="ag-theme-alpine pt-4"></div>
  </div>
</body>

</html>