<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

    <head>
        
        <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'DRH OpenAPI UI',
                breadcrumbs: [
                    {
                        text: "Documentation",
                        href: "/docs/swagger",
                    },
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
        });
    </script>
        <script>
            
            function finalizeDisplay(iframe) {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                let checkAndDisplayIntervalID = null;
    
                function displayImportantGroups() {
                    const expandBtn = iframeDocument.querySelector(`#operations-tag-DRH_Hub_Endpoints > button`);
                    if (expandBtn) {
                        expandBtn.click();
                        clearInterval(checkAndDisplayIntervalID); // Stop checking once the button is found and clicked
                    }
                }
    
                function checkAndDisplay() {
                    checkAndDisplayIntervalID = setInterval(() => {
                        displayImportantGroups();
                    }, 250); // Check every 250 milliseconds until expand button is available
                }
    
                if (iframeDocument.readyState === 'complete') {
                    checkAndDisplay();
                } else {
                    iframeDocument.addEventListener('DOMContentLoaded', checkAndDisplay);
                }
            }
        </script>
    </head>

<body>
    <div layout:fragment="content">
        <div class="flex justify-between flex-col">
            <div clas="mb-2" th:replace="~{fragments/introduction :: introduction(pagedescription=${pagedescription}, pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes=null,font='')}">
            </div>
          </div>
        <iframe th:src="@{/docs/api/interactive/index.html}" onload="finalizeDisplay(this)"
            class="w-full min-h-screen"></iframe>
    </div>
</body>

</html>