<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
    <script th:inline="javascript">
        var studyId = /*[[${studyId}]]*/ "";
        // Now you can use studyId in your JavaScript code
    </script>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";
        var studyId = /*[[${studyId}]]*/ "";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: "Files - " + studyId,
                breadcrumbs: [
                    {
                        text: "Administration",
                        href: "/administration",
                    },
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
        });
    </script>

    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
</head>

<body>
    <div layout:fragment="content">
        <div
            th:replace="~{fragments/introduction :: introduction(pagedescription = ${pagedescription},pagesubdescriptiontitle=null,pagesubdescription=null,pageattributestitle=null,  pageattributes=null,  notes = null,font='')}">
        </div>
        <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300 mt-3 table-files"></div>
    </div>
</body>

</html>