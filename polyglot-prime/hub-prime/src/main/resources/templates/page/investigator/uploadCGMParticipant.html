<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <title>Welcome</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <script th:src="@{/js/utils.js}"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var fileName = /*[[${fileName}]]*/ "";
    var tableName = /*[[${tableName}]]*/ "";
    var participantId =/*[[${participantId}]]*/"";
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    var participantDisplayId = /*[[${participantDisplayId}]]*/ "";
    var studyArchived = false;
    var tab = /*[[${tab}]]*/ "";
  </script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: participantDisplayId,
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies",
          },
          {
            text: tab,
            href: tabs.find(t => t.text === tab)?.href,
          },
          {
            text: studyDisplayId,
            href: "/study/info/" + studyId + "?tab=" + tab,
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", async function () {
      window.layout.initActiveRoute();
      if (!isLoggedIn()) {
        showModal();
        //window.location.href = "/studies/all"
      }
      else if (!isProfileCompleted()) {
        showProfilePromptModal();
      }
      else {
        if (studyId) {
          let studyOwner = await getStudyOwner(studyId);

          let archiveStatus = await getStudyArchiveStatus(studyId);
          console.log("archiveStatus", archiveStatus);
          if (archiveStatus == true || studyOwner != getLoggedInUser()) {
            toggleTooltip("upload-participant-cgm", true);
            [...document.getElementsByClassName("participant-upload-fields")].forEach((element) => {
              element.disabled = true;
            });
            document.querySelector(".archive-alert").classList.remove("hidden");
            studyArchived = true;
          } else {
            let privilegeListArray = [];
            privilegeListArray =
              JSON.parse(localStorage.getItem("userPrivilegeInfoArray")) || "[]";
            if (privilegeListArray.includes("Upload Cgm Data")) {
              toggleTooltip("upload-participant-cgm", false);
              [...document.getElementsByClassName("participant-upload-fields")].forEach((element) => {
                element.disabled = false;
              });
              document.querySelector(".archive-alert").classList.add("hidden");
            }
            else {
              toggleTooltip("upload-participant-cgm", true);
              [...document.getElementsByClassName("participant-upload-fields")].forEach((element) => {
                element.disabled = true;
              });
              document.querySelector(".archive-alert").classList.remove("hidden");
            }
            studyArchived = false;
          }
        }

      }
    });
    // Function to show the modal
    function showProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }


    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }
  </script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script th:src="@{/js/upload.js}"></script>
  <script type="module" src="/js/cgm.js"></script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
  <div layout:fragment="content">
    <div class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative w-full max-w-2xl max-h-full">
        <div th:replace="~{page/investigator/fragments/archiveInfoAlert}">
        </div>
        <div class="px-1 py-2">
          <form id="uploadCgmDataForm">
            <div
              class="px-3 py-1 font-medium rtl:text-right text-gray-900 border border-gray-200 focus:ring-0 dark:border-gray-700 dark:text-gray-400 bg-gray-100 hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-800 gap-3 rounded-t">
              <h3 class="text-base font-semibold text-gray-900 ">
                CGM Data
              </h3>
            </div>
            <div class="px-5  pt-5 pb-1 border dark:border-gray-600">
              <div class="grid gap-4 mb-4 grid-cols-2 px-8">
                <div class="col-span-2">
                  <div class="flex felx-col">
                    <label for="patientID" class="w-1/3 block flex mb-2 text-sm font-normal text-gray-900 ">
                      Participant ID
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('auto_generated_participant_id')}">
                      </div>
                    </label>
                    <div class="w-2/3 relative">
                      <input type="text" name="patientID" id="patientID"
                        class="border border-gray-300 text-gray-500 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-upload-fields"
                        readonly />
                    </div>
                  </div>
                </div>
                <div class="col-span-2">
                  <div class="flex felx-col">
                    <label for="sourcePlatform" class="w-1/3 block flex mb-2 text-sm font-normal text-gray-900 ">
                      Source Platform <span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('source_platform')}">
                      </div>
                    </label>
                    <div class="w-2/3 relative">
                      <select name="sourcePlatform" id="sourcePlatform"
                        class="border border-gray-300 text-gray-500 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-upload-fields"
                        placeholder="">
                        <option value="">Select</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="col-span-2">
                  <div class="flex felx-col">
                    <label for="deviceName" class="block flex mb-2 text-sm font-normal text-gray-900  w-1/3">
                      Device Name <span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('device_name')}">
                      </div>
                    </label>
                    <div class="w-2/3 relative">
                      <select name="deviceName" id="deviceName"
                        class="border border-gray-300 text-gray-500 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-upload-fields"
                        placeholder="">
                        <option value="">Select</option>
                      </select>
                    </div>
                  </div>
                </div>


                <div class="col-span-2">
                  <div class="flex felx-col">
                    <label for="Cgm" class="w-1/3 block flex mb-2 text-sm font-normal text-gray-900">Upload
                      CGM Tracing File <span class="text-red-500">*</span>
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('upload_cgm_tracing_file')}">
                      </div>
                    </label>
                    <div class="w-2/3 relative" id="drop-zone">
                      <div class="border rounded dark:border-gray-600 col-span-2" id="cgmUploadBorder">
                        <label for="CgmUpload" class="px-4 py-5 block">
                          <div class="flex items-center justify-center">
                            <svg width="17" height="17" class="inline-block mt-1 mr-1" viewBox="0 0 17 17" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M13.7063 5.05521C13.2246 2.62583 11.0783 0.802029 8.5 0.802029C6.45292 0.802029 4.675 1.95687 3.78958 3.64687C1.6575 3.87221 0 5.66784 0 7.84372C0 10.1745 1.90542 12.0687 4.25 12.0687H13.4583C15.4133 12.0687 17 10.4914 17 8.54789C17 6.68888 15.5479 5.18196 13.7063 5.05521ZM13.4583 10.6604H4.25C2.68458 10.6604 1.41667 9.39994 1.41667 7.84372C1.41667 6.40017 2.50042 5.19604 3.93833 5.04817L4.69625 4.97071L5.05042 4.30175C5.72333 3.01312 7.04083 2.21037 8.5 2.21037C10.3558 2.21037 11.9567 3.52012 12.3179 5.32984L12.5304 6.38609L13.6142 6.46355C14.7192 6.53397 15.5833 7.45643 15.5833 8.54789C15.5833 9.70977 14.6271 10.6604 13.4583 10.6604ZM9.52708 5.02704H7.47292V7.13955H5.66667L8.5 9.95623L11.3333 7.13955H9.52708V5.02704Z"
                                fill="#737373" />
                            </svg>
                            <span class="text-[13px] text-[#737373]">Drop files here or
                              browse</span>
                          </div>
                        </label>

                        <input type="file" name="CgmUpload" id="CgmUpload" accept=".csv,.txt,.xls,.xlsx"
                          class="hidden h-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm  focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-upload-fields"
                          placeholder="Drop files here or browse" />

                        <div id="upload-ctr" class="mt-4"></div>
                      </div>
                      <div id="upload-list" class="mt-4 text-sm font-semibold hidden">Uploaded Files</div>
                      <div class="col-span-2 mb-4 mt-3  hidden" id="uploadCgmLoader">
                        <button disabled type="button"
                          class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                          <svg aria-hidden="true" role="status"
                            class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                            viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                              fill="currentColor" />
                            <path
                              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                              fill="#1C64F2" />
                          </svg>
                          Uploading ...
                        </button>
                      </div>
                      <span class="block text-xs font-normal text-gray-500">CSV, TXT are
                        supported.</span>
                      <span id="cgm-upload-status" class="text-xs font-normal mt-2 mb-2"></span>
                    </div>
                  </div>
                </div>

                <div class="col-span-2">
                  <div class="flex felx-col">
                    <label for="cgmDateMapping" class="w-1/3 block flex mb-2 text-sm font-normal text-gray-900 ">
                      CGM Date Mapping <span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('cgm_date_mapping')}">
                      </div>
                    </label>
                    <div class="w-2/3 relative">
                      <select name="cgmDateMapping" id="cgmDateMapping"
                        class="border border-gray-300 text-gray-500 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-upload-fields"
                        placeholder="">
                        <option value="">Select</option>
                      </select>
                      <span class="block text-xs font-normal text-gray-500">After uploading the CGM Tracing File,
                        please
                        select an option from the dropdown above to proceed.</span>
                    </div>
                  </div>
                </div>
                <div class="col-span-2">
                  <div class="flex felx-col">
                    <label for="cgmValueMapping" class="w-1/3 block flex mb-2 text-sm font-normal text-gray-900 ">
                      CGM Value Mapping <span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('cgm_value_mapping')}">
                      </div>
                    </label>
                    <div class="w-2/3 relative">
                      <select name="cgmValueMapping" id="cgmValueMapping"
                        class="border border-gray-300 text-gray-500 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-upload-fields"
                        placeholder="">
                        <option value="">Select</option>
                      </select>
                      <span class="block text-xs font-normal text-gray-500">After uploading the CGM Tracing File,
                        please
                        select an option from the dropdown above to proceed.</span>
                    </div>

                  </div>

                </div>
              </div>

            </div>

        </div>
        <div class="flex justify-end pt-3 mb-3">
          <input type="hidden" name="studyId" id="studyId" th:value="${studyId}" />
          <input type="hidden" name="participantId" id="participantId" th:value="${participantId}" />
          <button type="submit" id="upload-participant-cgm" data-tooltip-target="archive-participant-cgm"
            data-privilege-buttons="Upload Cgm Data" data-tooltip-placement="top"
            class="text-white bg-[#1F883D] focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
            Submit
          </button>
          <span id="archive-participant-cgm" role="tooltip"
            class="tooltip-txt hidden bg-white z-10 border border-solid border-[#dbdbdb] p-1 mt-1 rounded-md text-xs text-left  w-48 right-0">Edit
            permissions are limited.</span>
        </div>
        <div class="tooltip-arrow" data-popper-arrow></div>
        <div class="my-4 text-right hidden" id="submitCgmLoader">
          <button disabled type="button"
            class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
            <svg aria-hidden="true" role="status"
              class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
              fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor" />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="#1C64F2" />
            </svg>
            Uploading CGM data...
          </button>
        </div>
        </form>
      </div>

    </div>
    <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>


    <div th:replace="~{page/investigator/fragments/loginPrompt::loginPrompt('Please login to upload cgm data.')}">
    </div>
    <div
      th:replace="~{page/investigator/fragments/profilePrompt::profilePrompt('Please complete your profile information to upload cgm data.')}">
    </div>

  </div>

  </div>


  </div>
</body>

</html>