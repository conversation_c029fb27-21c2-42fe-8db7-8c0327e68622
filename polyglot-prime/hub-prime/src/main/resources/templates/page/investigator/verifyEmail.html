<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <title>Edit Profile</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>

    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <!-- <script th:src="@{/js/profile.js}"></script> -->

    <script th:inline="javascript" type="module">

        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'Verify Email',
                breadcrumbs: [
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };


        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
            if (!isLoggedIn()) {
                showModal();
            }

            // getProfileDetails().then((profile) => {
            //     if (profile) {
            //         document.getElementById("userEmail").value = profile.user_account_primary_email || "";
            //     } else {
            //         showToast("Failed to load profile details.", "error");
            //     }
            // });

            document.getElementById("send-verification-email").addEventListener("click", async function () {
                //Action yes
                await sendVerificationEmail();
            });

            document.getElementById("cancel-verification").addEventListener("click", async function () {
                //Action no
                hideMsgModal();
            });
        });

        document.getElementById("check-email-availability").addEventListener("click", async function () {
            checkAvailability();
        });
        document.getElementById("userEmail")?.addEventListener("blur", async function () {
            checkAvailability();
        });

        async function checkAvailability() {
            //Action yes
            let userEmail = document.getElementById("userEmail").value;
            if (userEmail != "" && userEmail != undefined) {

                if (isValiduserEmail(userEmail)) {
                    const uniqueEmail = await emailExistenceCheck(userEmail);
                    if (uniqueEmail) {
                        document.getElementById("userEmail").classList.remove("border-[#e23636]");
                        document.getElementById("userEmail-error").innerHTML = "";
                        document.getElementById("account-modal-title").innerHTML = "Create Account Confirmation";
                        document.getElementById("linking-confirm-message").innerHTML = "No existing practitioner profile was found for the provided email address. Would you like to create a new account using this email ? If you proceed, a One - Time Password(OTP) will be sent to your email for verification.";
                        showMsgModal();
                    }
                    else {
                        document.getElementById("userEmail").classList.add("border-[#e23636]");
                        document.getElementById("userEmail-error").innerHTML = "Email already exists";
                        document.getElementById("account-modal-title").innerHTML = "Account Linking Confirmation";
                        document.getElementById("linking-confirm-message").innerHTML = "There is already an account linked to this email address. Do you want to link this email to the existing account? If you proceed, a One - Time Password(OTP) will be sent to your email for verification";
                        showMsgModal();
                    }
                }
                else {
                    document.getElementById("userEmail-error").innerHTML = "Please enter a valid email address";
                    return;
                }
            }
        }

        function isValiduserEmail(userEmail) {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailPattern.test(userEmail);
        }
        async function emailExistenceCheck(userEmail) {
            return new Promise((resolve, reject) => {
                let isUniqueEmail = true;
                document.getElementById("userEmail").classList.remove("border-[#e23636]");
                showuserEmailLoader();
                fetchData(
                    `/practitioners/unique-email?email=${encodeURIComponent(userEmail)}`,
                    async (res, error) => {
                        if (!error) {
                            if (res.status === "success") {
                                if (res.data.isUniqueEmail) {
                                    document.getElementById("userEmail").classList.remove("border-[#e23636]");
                                    document.getElementById("userEmail-error").innerHTML = "";
                                }
                                else {
                                    isUniqueEmail = false;
                                    document.getElementById("userEmail-error").innerHTML = "Email already exists";
                                }
                            }
                        }
                        hideuserEmailLoader();
                        resolve(isUniqueEmail);
                    })
            })
        }
        function showuserEmailLoader() {
            document.getElementById("userEmail-loader")?.classList.remove("hidden");
        }

        function hideuserEmailLoader() {
            document.getElementById("userEmail-loader")?.classList.add("hidden");
        }
        // Function to show the modal
        function showMsgModal() {
            const msgModalEle = document.getElementById("email-linking-confirm-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const msgModal = new Modal(msgModalEle, options)
            msgModal.show();
            return false;
        }

        // Function to hide the modal
        function hideMsgModal() {
            const msgModalEle = document.getElementById("email-linking-confirm-modal");
            const msgModal = new Modal(msgModalEle)
            msgModal.hide();
        }

        async function sendVerificationEmail() {


            const userEmail = document.getElementById("userEmail").value.trim();


            // Validate required fields
            if (!userEmail) {
                document.getElementById("userEmail-error").innerText = "Email is required.";
                return false;
            } else {
                document.getElementById("userEmail-error").innerText = "";
            }

            showLoading("sendVerificationEmail-Loader");

            try {
                // Send the user's message to the API

                // Prepare data for submission
                const verifyParams = {
                    "email": userEmail
                }

                console.log("Sending verification email with params:", verifyParams);
                const response = await fetch('/email', {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(verifyParams),
                });

                if (!response.ok) {
                    throw new Error("Failed to fetch response from the API.");
                }

                const data = await response.json();
                if (data.status === "success") {
                    hideLoading("sendVerificationEmail-Loader");
                    showToast(data.message, "success");
                    setTimeout(function () {
                        window.location.href = "/verifyOtp";
                    }, 1500); // 1.5 seconds delay
                }
            } catch (e) {
                hideLoading("sendVerificationEmail-Loader");
                console.error("Error fetching API response:", e);
            }

        }




        // Function to show the modal
        function showModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }

    </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">

    <div layout:fragment="content">

        <div id="verify-email-modal"
            class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full ">
            <div class="relative w-full max-h-full border border-gray-200">
                <!-- Modal content -->


                <div class="flex items-center justify-center p-8" sec:authorize="isAuthenticated()">
                    <div class="bg-gray-50 p-8 rounded-lg w-full max-w-lg ">
                        <div class="flex flex-col items-center p-6">
                            <!-- Envelope Icon -->
                            <svg class="w-16 h-16 mb-4 text-gray-700" fill="none" stroke="currentColor" stroke-width="2"
                                viewBox="0 0 24 24">
                                <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2"
                                    fill="none" />
                                <path d="M3 7l9 6 9-6" stroke="currentColor" stroke-width="2" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </svg>

                            <h2 class="text-2xl font-semibold mb-2 text-center" id="verifyEmailTitle">Welcome! Please
                                confirm your
                                email</h2>
                            <p class="text-gray-600 text-center mb-6" id="verifyEmailDesc">To complete your login via
                                <span
                                    th:text="${userProvider.toLowerCase() == 'orcid' ? userProvider.toUpperCase() : userProvider}"></span>,
                                we need your email address. We’ll
                                send you an
                                OTP to confirm it’s really you.
                            </p>

                            <!-- Email Input -->
                            <div class="w-full mb-6">
                                <input type="text" name="userEmail" id="userEmail"
                                    class="w-full h-12 border rounded text-center text-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                <span id="userEmail-error" class="text-red-500 text-sm"></span>
                                <div class="flex flex-row pt-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15px" height="20px"
                                        viewBox="0 0 576 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.-->
                                        <path
                                            d="M569.5 440C588 472 564.8 512 527.9 512H48.1c-36.9 0-60-40.1-41.6-72L246.4 24c18.5-32 64.7-32 83.2 0l239.9 416zM288 354c-25.4 0-46 20.6-46 46s20.6 46 46 46 46-20.6 46-46-20.6-46-46-46zm-43.7-165.3l7.4 136c.3 6.4 5.6 11.3 12 11.3h48.5c6.4 0 11.6-5 12-11.3l7.4-136c.4-6.9-5.1-12.7-12-12.7h-63.4c-6.9 0-12.4 5.8-12 12.7z" />
                                    </svg>
                                    <div class="text-gray-500 text-sm ml-1"> If you don't verify your email now, you'll
                                        be
                                        asked again
                                        on your next login.</div>
                                </div>

                            </div>
                            <div class="grid grid-cols-1  mb-4 -mt-3 hidden" id="userEmail-loader">
                                <button disabled type="button"
                                    class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                    <svg aria-hidden="true" role="status"
                                        class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                            fill="currentColor" />
                                        <path
                                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                            fill="#1C64F2" />
                                    </svg>
                                    Checking if Email Exists...
                                </button>
                            </div>

                            <!-- Verify Button -->
                            <button id="check-email-availability"
                                class="w-full bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 text-white py-2 rounded">
                                Proceed</button>


                        </div>
                    </div>
                </div>
            </div>

            <div id="login-prompt-modal" data-modal-backdrop="static" tabindex="-1"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

                        <div class="p-4 md:p-5 text-center">
                            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please login to
                                proceed with email verification.
                            </h3>
                            <a href="/">
                                <button type="button"
                                    class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                                    Login
                                </button>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div id="email-linking-confirm-modal" data-modal-backdrop="static" tabindex="-1"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-2xl max-h-full bg-white rounded-lg">

                    <div class="w-full bg-white  p-6 text-center" sec:authorize="isAuthenticated()">
                        <div class="flex justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" fill="none"
                                viewBox="0 0 36 36">
                                <circle cx="18" cy="18" r="18" fill="#1F883D" />
                                <path d="M11 18.5l5 5 9-10" stroke="#fff" stroke-width="2.5" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold mb-2" id="account-modal-title">Account Linking Confirmation
                        </h2>
                        <p class="text-gray-700 my-4" id="linking-confirm-message">
                            No existing practitioner profile was found for the provided email address.
                            Would you like to create a new account using this email?
                            If you proceed, a One-Time Password (OTP) will be sent to your email for verification
                        </p>

                        <div class="grid grid-cols-4 gap-4">
                            <div class="cols col-span-1"></div>

                            <button id="send-verification-email"
                                class="w-full bg-[#1F883D] hover:bg-green-700 text-white font-medium py-2 rounded">
                                Confirm
                            </button>
                            <button id="cancel-verification"
                                class="w-full bg-gray-600 hover:bg-gray-800 text-white font-medium py-2 rounded">
                                Change Email
                            </button>
                        </div>
                        <div class="col-span-2 mb-4 mt-3  hidden" id="sendVerificationEmail-Loader">
                            <button disabled type="button"
                                class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                <svg aria-hidden="true" role="status"
                                    class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                    viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="currentColor" />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="#1C64F2" />
                                </svg>
                                Sending OTP...
                            </button>
                        </div>

                    </div>
                </div>
            </div>
            <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
        </div>


</body>



</html>