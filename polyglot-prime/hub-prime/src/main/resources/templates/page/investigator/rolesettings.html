<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <!-- Include Tom Select CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

    <!-- Include Tom Select JS -->
    <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <title>Welcome</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>
    <script th:inline="javascript">
        var studyId = /*[[${studyId}]]*/ "";
        var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
        // Now you can use studyId in your JavaScript code
    </script>
    <script>
        function toggleModal() {
            document.getElementById("assignRolesModal").classList.toggle("hidden");
        }

        function toggleAll(source) {
            let checkboxes = document.querySelectorAll('.privilege-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = source.checked);
        }

        function updateRoles() {
            let selectedRoles = [];
            document.querySelectorAll('.privilege-checkbox:checked').forEach(checkbox => {
                selectedRoles.push(checkbox.dataset.role);
            });
            alert("Selected Roles: " + selectedRoles.join(", "));
        }
    </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
    <div layout:fragment="content">
        <!-- 🏠 Roles List (Main Page) -->
        <div class="container mx-auto bg-white rounded-lg">
            <h1 class="text-2xl font-semibold mb-4">Role Settings</h1>

            <!-- Search Box -->
            <div class="flex space-x-4 mb-4">
                <input type="text" placeholder="Search Role..."
                    class="w-half px-4 py-2 border border-gray-200 rounded-md">
                <button class="bg-[#1F883D] text-white px-4 py-2 rounded-md">Search</button>
            </div>

            <div class="relative overflow-x-auto ">
                <table
                    class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 border border-gray-200 rounded-sm">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">
                                Role
                            </th>
                            <th scope="col" class="px-6 py-3">
                                Privillage
                            </th>
                            <th scope="col" class="px-6 py-3">
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                            <th scope="row"
                                class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                Admin
                            </th>
                            <td class="px-6 py-4">
                                <button class="px-3 py-1 rounded-md" onclick="toggleModal()">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="2" th:attr="data-tooltip-target=${id}" stroke="currentColor"
                                        class="size-4 ml-1 text-blue-600 field-label-info">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M17 10v1.126c.367.095.714.24 1.032.428l.796-.797 1.415 1.415-.797.796c.188.318.333.665.428 1.032H21v2h-1.126c-.095.367-.24.714-.428 1.032l.797.796-1.415 1.415-.796-.797a3.979 3.979 0 0 1-1.032.428V20h-2v-1.126a3.977 3.977 0 0 1-1.032-.428l-.796.797-1.415-1.415.797-.796A3.975 3.975 0 0 1 12.126 16H11v-2h1.126c.095-.367.24-.714.428-1.032l-.797-.796 1.415-1.415.796.797A3.977 3.977 0 0 1 15 11.126V10h2Zm.406 3.578.016.016c.354.358.574.85.578 1.392v.028a2 2 0 0 1-3.409 1.406l-.01-.012a2 2 0 0 1 2.826-2.83ZM5 8a4 4 0 1 1 7.938.703 7.029 7.029 0 0 0-3.235 3.235A4 4 0 0 1 5 8Zm4.29 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h6.101A6.979 6.979 0 0 1 9 15c0-.695.101-1.366.29-2Z" />
                                    </svg>
                                </button>
                            </td>

                            <td class="px-6 py-4">
                                <a href="#"
                                    class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Edit</a>
                            </td>
                        </tr>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                            <th scope="row"
                                class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                Patient
                            </th>
                            <td class="px-6 py-4">
                                <button class="px-3 py-1 rounded-md" onclick="toggleModal()">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="2" th:attr="data-tooltip-target=${id}" stroke="currentColor"
                                        class="size-4 ml-1 text-blue-600 field-label-info">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M17 10v1.126c.367.095.714.24 1.032.428l.796-.797 1.415 1.415-.797.796c.188.318.333.665.428 1.032H21v2h-1.126c-.095.367-.24.714-.428 1.032l.797.796-1.415 1.415-.796-.797a3.979 3.979 0 0 1-1.032.428V20h-2v-1.126a3.977 3.977 0 0 1-1.032-.428l-.796.797-1.415-1.415.797-.796A3.975 3.975 0 0 1 12.126 16H11v-2h1.126c.095-.367.24-.714.428-1.032l-.797-.796 1.415-1.415.796.797A3.977 3.977 0 0 1 15 11.126V10h2Zm.406 3.578.016.016c.354.358.574.85.578 1.392v.028a2 2 0 0 1-3.409 1.406l-.01-.012a2 2 0 0 1 2.826-2.83ZM5 8a4 4 0 1 1 7.938.703 7.029 7.029 0 0 0-3.235 3.235A4 4 0 0 1 5 8Zm4.29 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h6.101A6.979 6.979 0 0 1 9 15c0-.695.101-1.366.29-2Z" />
                                    </svg>
                                </button>
                            </td>

                            <td class="px-6 py-4">
                                <a href="#"
                                    class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Edit</a>
                            </td>
                        </tr>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
                            <th scope="row"
                                class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                Researcher
                            </th>
                            <td class="px-6 py-4">
                                <button class="px-3 py-1 rounded-md" onclick="toggleModal()">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="2" th:attr="data-tooltip-target=${id}" stroke="currentColor"
                                        class="size-4 ml-1 text-blue-600 field-label-info">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M17 10v1.126c.367.095.714.24 1.032.428l.796-.797 1.415 1.415-.797.796c.188.318.333.665.428 1.032H21v2h-1.126c-.095.367-.24.714-.428 1.032l.797.796-1.415 1.415-.796-.797a3.979 3.979 0 0 1-1.032.428V20h-2v-1.126a3.977 3.977 0 0 1-1.032-.428l-.796.797-1.415-1.415.797-.796A3.975 3.975 0 0 1 12.126 16H11v-2h1.126c.095-.367.24-.714.428-1.032l-.797-.796 1.415-1.415.796.797A3.977 3.977 0 0 1 15 11.126V10h2Zm.406 3.578.016.016c.354.358.574.85.578 1.392v.028a2 2 0 0 1-3.409 1.406l-.01-.012a2 2 0 0 1 2.826-2.83ZM5 8a4 4 0 1 1 7.938.703 7.029 7.029 0 0 0-3.235 3.235A4 4 0 0 1 5 8Zm4.29 5H7a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h6.101A6.979 6.979 0 0 1 9 15c0-.695.101-1.366.29-2Z" />
                                    </svg>
                                </button>
                            </td>

                            <td class="px-6 py-4">
                                <a href="#"
                                    class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Edit</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>


        </div>

        <!-- 📌 Assign Roles Modal -->
        <div id="assignRolesModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden">
            <div class="bg-white rounded-lg shadow-lg w-auto max-w-3xl">
                <div class="flex justify-between items-center bg-[#F6F8FA] text-black p-4 rounded-t-lg">
                    <h2 class="text-lg font-semibold">Assign Roles</h2>
                    <button class="text-black text-xl" onclick="toggleModal()">×</button>
                </div>
                <div class="p-4 overflow-auto">

                    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                            <thead
                                class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400 border-b dark:border-gray-700 border-gray-200">
                                <tr>
                                    <th scope="col" class="px-6 py-3 border-r dark:border-gray-700 border-gray-200">
                                        Privilege Group
                                    </th>
                                    <th scope="col" class="px-6 py-3 border-r dark:border-gray-700 border-gray-200">
                                        Privilege Name
                                    </th>
                                    <th scope="col" class="px-6 py-3 border-r dark:border-gray-700 border-gray-200">
                                        Privilege Description
                                    </th>
                                    <th scope="col" class="px-6 py-3 border-r dark:border-gray-700 border-gray-200">
                                        <input type="checkbox" onclick="toggleAll(this)">
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td scope="row" rowspan="4"
                                        class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        User</td>
                                    <td class="px-6 py-4 border">Add User</td>
                                    <td class="px-6 py-4 border">To add user</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Add User">
                                    </td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td class="px-6 py-4 border">Edit User</td>
                                    <td class="px-6 py-4 border">To edit user</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Edit User">
                                    </td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td class="px-6 py-4 border">Delete User</td>
                                    <td class="px-6 py-4 border">To delete user</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Delete User"></td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td class="px-6 py-4 border">View User</td>
                                    <td class="px-6 py-4 border">To view user</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="View User">
                                    </td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td scope="row" rowspan="5"
                                        class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        Role</td>
                                    <td class="px-6 py-4 border">Add Role</td>
                                    <td class="px-6 py-4 border">To add role</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Add Role">
                                    </td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td class="px-6 py-4 border">Edit Role</td>
                                    <td class="px-6 py-4 border">To edit role</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Edit Role">
                                    </td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td class="px-6 py-4 border">Delete Role</td>
                                    <td class="px-6 py-4 border">To delete role</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Delete Role"></td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td class="px-6 py-4 border">Assign Privilege</td>
                                    <td class="px-6 py-4 border">To assign privilege</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Assign Privilege"></td>
                                </tr>
                                <tr
                                    class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200">
                                    <td class="px-6 py-4 border">Role User List</td>
                                    <td class="px-6 py-4 border">To list all users under a role</td>
                                    <td class="px-6 py-4 border"><input type="checkbox" class="privilege-checkbox"
                                            data-role="Role User List"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="flex justify-end p-4 srounded-b-lg">
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md mr-2"
                            onclick="toggleModal()">Cancel</button>
                        <button class="bg-[#1F883D] text-white px-4 py-2 rounded-md"
                            onclick="updateRoles()">Update</button>
                    </div>
                </div>
            </div>
        </div>


    </div>
</body>

</html>