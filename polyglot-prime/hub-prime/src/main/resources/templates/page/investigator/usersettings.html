<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <!-- Include Tom Select CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

    <!-- Include Tom Select JS -->
    <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/simple-datatables@9.0.3"></script>
    <title>Welcome</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>
    <script th:inline="javascript">
        var studyId = /*[[${studyId}]]*/ "";
        var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
        // Now you can use studyId in your JavaScript code
    </script>
    <script>
        let selectedUserId = null;
        let selectedUserPartyId = null;
        let userList = [];
        let roleList = [];

        document.addEventListener("DOMContentLoaded", function () {
            if (!isLoggedIn()) {
                showModal();
                //window.location.href = "/studies/all"
            }
            else if (isLoggedIn()) {
                let userRole = localStorage.getItem("userRole");
                if (userRole != "Admin" && userRole != "Super Admin") {
                    window.location.href = "/home";
                }
                else if (userRole == "Admin" && !isProfileCompleted()) {
                    showProfilePromptModal();
                }
            }

            // Call the function to populate the table
            fetchUserList();
            getUserRoles()
                .then((roledata) => {
                    roleList = getUniqueRoles(roledata);
                })
                .catch((error) => {
                    console.error("Error fetching data:", error);
                });

            document.getElementById("update-user-role").addEventListener("click", function () {
                updateUserRole();
                //toggleEditModal('');
            });

        });
        // Function to show the modal
        function showProfilePromptModal() {
            const modalEle = document.getElementById("profile-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideProfilePromptModal() {
            const modalEle = document.getElementById("profile-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }


        // Function to show the modal
        function showModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }
        async function toggleEditModal(id) {
            selectedUserId = id;
            const modal = document.getElementById("assignRolesModal");

            if (!modal.classList.contains("hidden")) {
                modal.classList.add("hidden"); // Close modal
                return;
            }
            else {
                generateRoleList(roleList)
                try {
                    const user = userList.find(usr => usr.user_id === selectedUserId);
                    if (!user) {
                        console.error("User not found");
                        return;
                    }
                    const userRoles = user.user_roles.map(role => role.role_name);
                    console.log("User Roles:", userRoles);
                    document.getElementById("userName").innerHTML = user.first_name;
                    let userRole = localStorage.getItem("userRole") || "";
                    document.querySelectorAll('input[name="selectRole"]').forEach(checkbox => {
                        checkbox.checked = userRoles.includes(checkbox.nextElementSibling.innerText.trim());
                        if (checkbox.checked && checkbox.nextElementSibling.innerText.trim() == "Admin" && userRole != "Super Admin") {
                            checkbox.setAttribute("disabled", "true");
                        }

                    });
                    modal.classList.remove("hidden");

                } catch (error) {
                    console.error("Error fetching user data:", error);
                }
            }
        }

        async function togglePrivilegeModal(selectedPartyId) {
            const modal = document.getElementById("assignRolesPrivilegeModal");
            selectedUserPartyId = selectedPartyId;
            if (!modal.classList.contains("hidden")) {
                modal.classList.add("hidden"); // Close modal
                return;
            }
            else {
                generatePrivilegeTable();
                modal.classList.remove("hidden");
            }

        }

        function getUniqueRoles(roleData) {

            const uniqueRoles = roleData.filter(entry => {
                return entry.is_system_role == false;
            }).map(entry => {
                return {
                    role_id: entry.role_id,
                    role_name: entry.role_name,
                    role_type_id: entry.role_type_id
                };
            });


            // const seenRoles = new Set();
            // const uniqueRoles = [];

            // for (const entry of roleData) {
            //     const roleName = entry.role_name;
            //     if (!seenRoles.has(roleName) && roleName != "Super Admin" && roleName != "Patient" && entry.is_system_role == false) {
            //         uniqueRoles.push({
            //             role_id: entry.role_id,
            //             role_name: entry.role_name,
            //             role_type_id: entry.role_type_id
            //         });
            //         seenRoles.add(roleName);
            //     }
            // }
            return uniqueRoles;
        }
        async function generatePrivilegeTable() {
            const container = document.getElementById("privillegeContainer");
            container.innerHTML = ""; // Clear previous content
            try {
                await fetchData(
                    `/permissions/by-roles?userPartyId=${selectedUserPartyId}`,
                    async (data, error) => {
                        if (!error) {
                            if (data.status == "success") {
                                // Fetch data from the local JSON file
                                const userPrivilegeInfo = data.data.response;
                                for (const [category, permissions] of Object.entries(userPrivilegeInfo.userPermissions)) {
                                    permissions.forEach((permission, index) => {
                                        const row = document.createElement("tr");
                                        row.className = "odd:bg-white even:bg-gray-50 border-b border-gray-200";

                                        if (index === 0) {
                                            // Only add category cell for the first permission in each category
                                            const categoryCell = document.createElement("td");
                                            categoryCell.setAttribute("scope", "row");
                                            categoryCell.setAttribute("rowspan", permissions.length);
                                            categoryCell.className = "px-6 py-4 font-medium text-gray-900 whitespace-nowrap";
                                            categoryCell.textContent = category;
                                            row.appendChild(categoryCell);
                                        }

                                        // Permission name
                                        const actionCell = document.createElement("td");
                                        actionCell.className = "px-6 py-4 border";
                                        actionCell.textContent = permission;
                                        row.appendChild(actionCell);

                                        // Description (optional or placeholder)
                                        const descriptionCell = document.createElement("td");
                                        descriptionCell.className = "px-6 py-4 border";
                                        descriptionCell.textContent = "Description for " + permission; // You can replace this with real data
                                        row.appendChild(descriptionCell);

                                        container.appendChild(row);
                                    });
                                }
                            }
                        }
                    })
            }
            catch (error) {
                console.error("Error fetching user data:", error);
            }

        }

        function updateUserRole() {
            showLoading("updateRoleLoader");
            let userId = selectedUserId;
            const checkboxes = document.querySelectorAll('input[name="selectRole"]:checked');
            const values = Array.from(checkboxes).map(cb => cb.value);
            assignUserRole(userId, values);
        }

        function assignUserRole(userId, roleIds) {
            const data = {
                userId: userId,
                roleIds: roleIds
            };
            updateData(`/user-role`, data, (res) => {
                showToast(res.message, res.status);
                if (res && res.status == "success") {
                    fetchUserList();
                    toggleEditModal('');
                }

                hideLoading("updateRoleLoader");

            });
        }
        async function fetchUserList() {
            try {
                await fetchData(
                    `/user-list`,
                    async (dataJson, error) => {
                        if (!error) {
                            if (dataJson.status == "success") {
                                // Fetch data from the local JSON file
                                const userRoleData = dataJson.data.Users;
                                userList = userRoleData;
                                const tableBody = document.getElementById('user-table').querySelector('tbody');
                                tableBody.innerHTML = '';
                                userRoleData.forEach(item => {
                                    const row = document.createElement('tr');
                                    const roles = item.user_roles.map(role => role.role_name).join(", ");
                                    const thisUserId = item.user_id;
                                    const selectedPartyId = item.party_id;
                                    row.className = "odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200";
                                    row.id = "row-" + thisUserId;
                                    row.innerHTML = `
                <td class="px-6 py-4">${item.first_name}</td>
                <td class="px-6 py-4">${item.email}</td>
                <td class="px-6 py-4">${roles}</td>
                <td class="px-6 py-4 hidden org-name">${item.organization_name}</td>
                <td class="px-6 py-4">
                    <button class="p-1.5 text-[#2196f3]" onclick="toggleEditModal('${thisUserId}')" privilege-action-buttons-links ="Change User Roles">
            <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/>
</svg>


                        
                        </button>
                    <button class="p-1.5" onclick="togglePrivilegeModal('${selectedPartyId}')">
                         <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.5" d="M10 19H5a1 1 0 0 1-1-1v-1a3 3 0 0 1 3-3h2m10 1a3 3 0 0 1-3 3m3-3a3 3 0 0 0-3-3m3 3h1m-4 3a3 3 0 0 1-3-3m3 3v1m-3-4a3 3 0 0 1 3-3m-3 3h-1m4-3v-1m-2.121 1.879-.707-.707m5.656 5.656-.707-.707m-4.242 0-.707.707m5.656-5.656-.707.707M12 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                            </svg>

                    </button>
                </td>
            `;
                                    tableBody.appendChild(row);
                                });
                                // Re-initialize Flowbite DataTable
                                let userRole = localStorage.getItem("userRole");
                                if (userRole == "Super Admin" && isLoggedIn()) {
                                    document.getElementById("organizationName").classList.remove("hidden");
                                    document.querySelectorAll(".org-name").forEach((ele) => {
                                        ele.classList.remove("hidden");
                                    });
                                }
                                else {
                                    document.getElementById("organizationName").classList.add("hidden");
                                    document.querySelectorAll(".org-name").forEach((ele) => {
                                        ele.classList.add("hidden");
                                    });
                                }
                                const dataTable = new simpleDatatables.DataTable("#user-table", {
                                    searchable: true,
                                    sortable: false
                                });
                            } else {
                                console.error("Failed to fetch data");
                                return;
                            }
                        }
                        else {
                            console.error("Error:", error);
                        }
                    }
                );
            } catch (error) {
                console.error("Error:", error);
            }
        }
        function generateRoleList(roles) {
            const roleContainer = document.getElementById("roleContainer");
            roleContainer.innerHTML = ""; // Clear previous content

            roles.forEach(role => {
                if (role.role_type_code == 'patient') {
                    return;
                }
                const label = document.createElement("label");
                label.className = "flex items-center space-x-2 text-gray-700";

                const input = document.createElement("input");
                input.type = "checkbox";
                input.name = "selectRole";
                input.className = "w-4 h-4 border-gray-300 focus:ring-blue-500";
                input.value = role.role_id;
                // input.onclick = function () { selectOnlyOne(this); };


                const span = document.createElement("span");
                span.textContent = role.role_name;

                label.appendChild(input);
                label.appendChild(span);
                roleContainer.appendChild(label);
            });
        }


        function getUserRoles() {
            return new Promise((resolve, reject) => {
                fetchRawData(`/user-roles`, (res) => {
                    try {
                        const data = res.data.UserRoles;
                        console.log("User Roles:", data);
                        resolve(data); // Resolve the promise with the fetched data
                    } catch (error) {
                        reject(error); // Reject the promise in case of an error
                    }
                });
            });
        }


    </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
    <div layout:fragment="content">
        <!-- 🏠 Roles List (Main Page) -->
        <div class="">
            <div class="flex py-4 gap-1 flex-wrap">
                <h6 class="text-md font-medium grow">User Settings</h6>
            </div>
            <div class="relative overflow-x-auto shadow-md sm:rounded-lg p-4">
                <table class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400" id="user-table">
                    <thead class="text-md text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-md">
                                Name
                            </th>
                            <th scope="col" class="px-6 py-3">
                                Email
                            </th>
                            <th scope="col" class="px-6 py-3">
                                Role
                            </th>
                            <th scope="col" class="px-6 py-3 hidden" id="organizationName">
                                Organization Name
                            </th>
                            <th scope="col" class="px-6 py-3">
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody id="table-user-list" class="text-sm">
                    </tbody>
                </table>
            </div>


        </div>

        <!-- 📌 Assign Roles Modal -->
        <div id="assignRolesModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden">
            <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl">
                <div class="flex justify-between items-center bg-[#F6F8FA] text-black p-4 rounded-t-lg">
                    <h2 class="text-lg font-semibold">Update User Role</h2>
                    <button class="text-black text-xl" onclick="toggleEditModal('')">×</button>
                </div>
                <div class="p-4 overflow-auto">

                    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                        <div class="p-6 border rounded-lg dark:border-gray-600">
                            <div class="grid gap-4 mb-4 grid-cols-2">
                                <div class="col-span-2">
                                    <label for="userName" class="block mb-2 text-sm font-medium text-gray-900 ">
                                        User Name
                                    </label>
                                    <span id="userName"></span>
                                    <span id="userName-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                                </div>

                                <div class="col-span-2">
                                    <label for="userRole" class="block text-sm font-medium text-gray-900 mb-2">
                                        Role
                                    </label>
                                    <div class="space-y-2 bg-white p-3 " id="roleContainer">
                                    </div>
                                    <span id="title-error" class="text-xs font-medium text-red-500 mt-2 block"></span>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end p-4 srounded-b-lg">
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md mr-2"
                            onclick="toggleEditModal('')">Cancel</button>
                        <button class="bg-[#1F883D] text-white px-4 py-2 rounded-md"
                            data-privilege-buttons="Change User Roles" id="update-user-role">Update</button>

                    </div>
                    <div class="col-span-2 mb-4 mt-3  hidden" id="updateRoleLoader">
                        <button disabled type="button"
                            class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                            <svg aria-hidden="true" role="status"
                                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="currentColor" />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="#1C64F2" />
                            </svg>
                            Updating User Role...
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 📌 Assign Roles Modal -->
        <div id="assignRolesPrivilegeModal"
            class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden">
            <div class="bg-white rounded-lg shadow-lg w-auto max-w-3xl">
                <div class="flex justify-between items-center bg-[#F6F8FA] text-black p-4 rounded-t-lg">
                    <h2 class="text-lg font-semibold">Role Privileges</h2>
                    <button class="text-black text-xl" onclick="togglePrivilegeModal('')">×</button>
                </div>
                <div class="p-4 overflow-auto">
                    <div class="max-h-[500px] overflow-y-auto">
                        <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400"
                                id="privilege-table">
                                <thead
                                    class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400 border-b dark:border-gray-700 border-gray-200">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 border-r dark:border-gray-700 border-gray-200">
                                            Privilege Group
                                        </th>
                                        <th scope="col" class="px-6 py-3 border-r dark:border-gray-700 border-gray-200">
                                            Privilege Name
                                        </th>
                                        <th scope="col" class="px-6 py-3 border-r dark:border-gray-700 border-gray-200">
                                            Privilege Description
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="privillegeContainer">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="login-prompt-modal" data-modal-backdrop="static" tabindex="-1"
            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

                    <div class="p-4 md:p-5 text-center">
                        <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                        </svg>
                        <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please login to continue.
                        </h3>
                        <a href="/">
                            <button type="button"
                                class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                                Login
                            </button>

                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div id="profile-prompt-modal" data-modal-backdrop="static" tabindex="-1"
            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

                    <div class="p-4 md:p-5 text-center">
                        <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                        </svg>
                        <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please complete your
                            profile to continue.
                        </h3>
                        <a href="/userprofile/info">
                            <button type="button"
                                class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                                Complete your profile
                            </button>

                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
    </div>
</body>

</html>