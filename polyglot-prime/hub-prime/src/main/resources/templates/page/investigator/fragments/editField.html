<div th:fragment="editField(type,id,inputText,inputValue,placeholderText,category,citation_identifier)">

    <div class="study-edit-field hidden absolute z-10 top-2 right-2 " th:data-id="${id}" th:data-type="${type}"
        th:data-citation_identifier="${citation_identifier}" th:data-category="${category}">
        <button type="button" privilege-action-buttons-links ="Edit Study Inline" 
            class="hidden text-[#3E3E3E] border border-gray-200 bg-white  shadow-md focus:outline-none focus:ring-0font-medium rounded p-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-4">
                <path stroke-linecap="round" stroke-linejoin="round"
                    d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125" />
            </svg>
        </button>
    </div>
    <div class="">
        <div class="readonly-fields">
            <p class="text-black-500 font-bold text-sm" th:text="${inputText}"></p>
            <span class="loader loader-small" th:id="${id + 'Loader'}"></span>

            <p th:if="${id!= 'investigatorsList' && id!= 'coinvestigatorsList'}"
                th:classappend="${(inputValue == '' ? 'hidden ' : '') + 'text-black-500 font-normal text-sm  overflow-wrap-anywhere break-words study-field ' + id + ' ' + ((id == 'primaryInvestigator' ||id == 'nominatedPrincipalInvestigator' || id == 'coinvestigatorsList' || id== 'investigatorsList')? 'pl-4 mt-1':'mt-2')}"
                th:id="${id}" th:text="${inputValue}" th:data-id="${id}" th:data-category="${category}">
            </p>
            <p th:classappend="${(inputValue != '' ? 'hidden ' : '') + 'text-black-500 italic text-sm font-normal text-gray-500 ' + ((id == 'primaryInvestigator'||id == 'nominatedPrincipalInvestigator'  || id == 'coinvestigatorsList' || id== 'investigatorsList')? 'pl-4 mt-1':'mt-2')}"
                th:id="${'add' + id }" th:data-id="${id}" th:text="${placeholderText}" th:data-type="${type}">
            </p>
            <ul th:if="${id == 'investigatorsList' ||  id == 'coinvestigatorsList'}" th:id="${ id }"
                th:classappend="${'text-black-500 font-normal lading-5 text-sm  pl-4 max-h-72 '+id}"
                style="overflow: auto">
            </ul>
        </div>


        <div class="editable-fields flex hidden flex-col mt-1">
            <!-- Conditional logic for rendering input or dropdown -->
            <textarea th:if="${type == 'textarea'}" th:id="${id + 'Input'}" th:name="${id + 'Input'}" th:type="${type}"
                th:value="${inputValue}" rows="5"
                th:classappend="${'w-full h-full rounded px-3 py-1.5 mr-2 text-sm font-normal text-gray-900 border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 '+id}">
                </textarea>

            <input th:if="${type == 'text' || type == 'date' || type == 'number'}" th:id="${id + 'Input'}"
                th:name="${id + 'Input'}" th:type="${type}" th:value="${inputValue}"
                th:classappend="${'rounded px-3 py-1.5 text-sm font-normal text-gray-900 border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 '+id}" />

            <select th:if="${type == 'dropdown'}" th:id="${id + 'Dropdown'}" th:name="${id + 'Dropdown'}"
                th:classappend="${'rounded text-sm font-normal text-gray-900 focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 '+id}">
                <!-- <option th:each="option : ${options}" th:value="${option.value}" th:text="${option.text}">
            </option> -->
            </select>
            <div class="flex mt-1.5 justify-end">
                <button type="button" th:data-id="${id}" th:data-type="${type}" th:data-category="${category}"
                    class="save-edit-field text-[white] border border-gray-200 bg-gray-700 h-fit  focus:outline-none focus:ring-0font-medium rounded p-1 mr-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="3"
                        stroke="currentColor" class="size-4">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
                    </svg>
                </button>
                <button type="button" th:data-id="${id}" th:data-type="${type}"
                    class="cancel-edit-field text-[white] border border-gray-200 bg-gray-700 h-fit focus:outline-none focus:ring-0font-medium rounded p-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="3"
                        stroke="currentColor" class="size-4">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>