<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <script src="https://unpkg.com/ag-grid-enterprise@33.1.1/dist/ag-grid-enterprise.js"></script>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: "Dashboard",
                breadcrumbs: [
                    {
                        text: "Studies",
                        href: "/studies",
                    },
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],

            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
        });
    </script>
    <script type="module">
        import {
            AGGridAide,
            AGGridAideBuilder,
        } from "@presentation/shell/aggrid-aide.js";
        import ModalAide from "@presentation/shell/modal-aide.js";

        document.addEventListener("DOMContentLoaded", function () {
            const modalAide = new ModalAide();
            const schemaName = "dashboard";
            const viewName = "dashboard_all_research_study_view";
            const agGridInstance = new AGGridAideBuilder()
                .withColumnDefs([
                    {
                        headerName: "Study ID",
                        field: "study_display_id",
                        filter: "agTextColumnFilter",
                        enableRowGroup: true,
                        cellRenderer: function (params) {
                            console.log(params);
                            if (params.value) {
                                const link = document.createElement("a");
                                link.href = "/study/info/" + params.data.study_id;
                                link.innerText = params.value;
                                return link;
                            } else {
                                return null;
                            }
                        },
                    },
                    {
                        headerName: "Study Name",
                        field: "title",
                        enableRowGroup: true,
                        maxWidth: 500,
                        filter: "agTextColumnFilter",
                        tooltipValueGetter: (params) => `${params.value}`,
                        flex: 2
                    },
                    {
                        headerName: "NCT Number",
                        field: "nct_number",
                        enableRowGroup: true,
                        filter: "agTextColumnFilter",
                        flex: 1
                    },
                    {
                        headerName: "Description",
                        field: "description",
                        maxWidth: 600,
                        enableRowGroup: true,
                        filter: "agTextColumnFilter",
                        tooltipValueGetter: (params) => `${params.value}`,
                        flex: 2
                    },
                    {
                        headerName: "From Date",
                        field: "start_date",
                        enableRowGroup: true,
                        filter: "agTextColumnFilter",
                        flex: 1
                    },
                    {
                        headerName: "To Date",
                        field: "end_date",
                        enableRowGroup: true,
                        filter: "agTextColumnFilter",
                        flex: 1
                    },
                ])
                .withServerSideDatasource(
                    window.shell.serverSideUrl(
                        `/api/ux/tabular/jooq/study/` + schemaName + `/` + viewName + `.json`
                    ),
                    (data, valueCols) => {
                        return valueCols.map((col) => ({
                            headerName: col.displayName,
                            field: col.field,
                        }));
                    }
                )
                .withModalAide(modalAide)
                .withGridDivStyles({ height: (9 + 1) * 50 + "px", width: "100%" }) //no of rows displayed + 1(for header) multiplied by 45 (height of 1 row)
                .build();

            agGridInstance.init("serverDataGrid");


            function getSvmValues() {
                showLoading("totalParticipantsBottomLoader");
                showLoading("totalFemaleBottomLoader");
                showLoading("totalAverageBottomLoader");
            }

            function assignValues(eleClass, value) {
                document.querySelectorAll("." + eleClass).forEach((element) => {
                    element.innerHTML = value;
                });
            }

        });

    </script>
    <script type="module" src="/js/study.js"></script>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
</head>

<body>

    <div layout:fragment="content">
        <div class="grid grid-cols-2 py-4">
            <h6 class="text-md font-medium ">Dashboard</h6>
        </div>
        <div
            th:replace="~{fragments/introduction :: introduction(pagedescription = ${pagedescription},pagesubdescriptiontitle=${pagesubdescriptiontitle},pageattributestitle=${pageattributestitle},pageattributes = ${pageattributes}, pagesubdescription=${pagesubdescription}, notes = null,font='font-semibold')}">
        </div>
        <div class="flex justify-center pb-5">
            <div class="w-[100%] justify-between grid grid-cols-1 md:grid-cols-1 gap-4 lg:grid-cols-3">
                <div
                    th:replace="~{fragments/metricsWithTitle :: serverTextStat('Total Participants', null, @{/research-study/allstudy-vanity-metrics/total-number-of-participants.html})}">
                </div>

                <div
                    th:replace="~{fragments/metricsWithTitle :: serverTextStat('% Female', null, @{/research-study/allstudy-vanity-metrics/female-percent.html})}">
                </div>
                <div
                    th:replace="~{fragments/metricsWithTitle :: serverTextStat('Average Age', null, @{/research-study/allstudy-vanity-metrics/average-age.html})}">
                </div>
                <!-- <div
                    th:replace="~{fragments/metricsWithTitle :: serverTextStat('Days of wear', null, @{/study/allstudy-total-cgm-wear.html})}">
                </div> -->

            </div>
        </div>
        <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
    </div>
</body>

</html>