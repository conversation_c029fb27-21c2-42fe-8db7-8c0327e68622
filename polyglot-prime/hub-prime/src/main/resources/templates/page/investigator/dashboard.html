<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <title>Welcome</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <style>
        .hanging-indent {
            padding-left: 1.5rem;
            text-indent: -1.5rem;
        }
    </style>
    <script src="https://unpkg.com/ag-grid-enterprise@33.1.1/dist/ag-grid-enterprise.js"></script>
    <script type="module">
        import {
            AGGridAide,
            AGGridAideBuilder,
        } from "@presentation/shell/aggrid-aide.js";
        import ModalAide from "@presentation/shell/modal-aide.js";

        //const viewName = 'uniform_resource_study';
        document.addEventListener("DOMContentLoaded", function () {
            const modalAide = new ModalAide();
            const agGridInstance = new AGGridAideBuilder()
                .withColumnDefs([
                    {
                        headerName: "Study ID",
                        field: "study_id",
                        filter: "agTextColumnFilter",
                        // cellRenderer: AGGridAide.modalCellRenderer((value, modalAide) => {
                        //     modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/${viewName}/study_id/${value}.json`));
                        // }, modalAide)
                        enableRowGroup: true,
                        cellRenderer: function (params) {
                            if (params.value) {
                                const link = document.createElement("a");
                                link.href = "/study/info/" + params.value;
                                link.innerText = params.value;
                                return link;
                            } else {
                                return null;
                            }
                        },
                    },
                    {
                        headerName: "Study Name",
                        field: "study_name",
                        enableRowGroup: true,
                        maxWidth: 500,
                        filter: "agTextColumnFilter",
                        tooltipValueGetter: (params) => `${params.value}`,
                        flex: 1
                    },
                    {
                        headerName: "Description",
                        field: "study_description",
                        maxWidth: 500,
                        enableRowGroup: true,
                        filter: "agTextColumnFilter",
                        tooltipValueGetter: (params) => `${params.value}`,
                        flex: 2
                    },
                    {
                        headerName: "Start Date",
                        field: "start_date",
                        enableRowGroup: true,
                        filter: "agTextColumnFilter",
                        flex: 1
                    },
                    {
                        headerName: "End Date",
                        field: "end_date",
                        enableRowGroup: true,
                        filter: "agTextColumnFilter",
                        flex: 1
                    },
                    {
                        headerName: "Actions",
                        field: "study_id",
                        enableRowGroup: false,
                        maxWidth: 500,
                        filter: false,
                        sortable: false,
                        cellRenderer: function (params) {
                            if (params.value) {
                                const link = document.createElement("a");
                                link.href = "/svm/files/" + params.value;
                                link.className = "flex items-center";

                                // Create the main SVG element
                                const svgIcon = document.createElementNS("http://www.w3.org/2000/svg", "svg");
                                svgIcon.setAttribute("class", "w-6 h-6 text-gray-800 dark:text-white");
                                svgIcon.setAttribute("aria-hidden", "true");
                                svgIcon.setAttribute("xmlns", "http://www.w3.org/2000/svg");
                                svgIcon.setAttribute("width", "24");
                                svgIcon.setAttribute("height", "24");
                                svgIcon.setAttribute("fill", "none");
                                svgIcon.setAttribute("viewBox", "0 0 24 24");

                                // Create the path element
                                const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
                                path.setAttribute("stroke", "currentColor");
                                path.setAttribute("stroke-linecap", "round");
                                path.setAttribute("stroke-linejoin", "round");
                                path.setAttribute("stroke-width", "2");
                                path.setAttribute("d", "m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z");

                                // Append the path to the SVG
                                svgIcon.appendChild(path);

                                link.appendChild(svgIcon);

                                return link;
                            } else {
                                return null;
                            }
                        },
                        flex: 1,
                        tooltipValueGetter: function (params) {
                            return "Edit";
                        }
                    },
                ])
                .withServerSideDatasource(
                    window.shell.serverSideUrl(
                        `/api/ux/tabular/jooq/all_study_summary_cached.json`
                    ),
                    (data, valueCols) => {
                        return valueCols.map((col) => ({
                            headerName: col.displayName,
                            field: col.field,
                        }));
                    }
                )
                .withModalAide(modalAide)
                .withGridDivStyles({ height: (4 + 1) * 50 + "px", width: "100%" }) //no of rows displayed + 1(for header) multiplied by 45 (height of 1 row)
                // .withGridOptions({
                //   onRowClicked: function (event) {
                //     // Example: Open a modal with study details
                //     const studyId = event.data.study_id;
                //     const link = document.createElement("a");
                //     link.href = "/study/" + studyId;
                //     link.click()
                //   },
                // })
                .build();

            agGridInstance.init("serverDataGrid");

            // function showLoader(loaderId) {
            //     document.getElementById(loaderId).style.display = "block";
            // }

            function getSvmValues() {
                showLoading("totalParticipantsBottomLoader");
                showLoading("totalFemaleBottomLoader");
                showLoading("totalAverageBottomLoader");
            }

            function assignValues(eleClass, value) {
                document.querySelectorAll("." + eleClass).forEach((element) => {
                    element.innerHTML = value;
                });
            }

        });

    </script>
</head>

<body class="bg-gray-100">
    <div layout:fragment="content" class="flex flex-col justify-center p-2">
        <!-- <h1 class="mb-4 text-3xl font-bold text-gray-800 md:text-3xl lg:text-3xl dark:text-white">
            Welcome John Doe
        </h1> -->

        <div class="w-full mb-2">
            <div>
                <h1 class="mb-4 text-2xl font-bold text-gray-800 md:text-2xl lg:text-2xl dark:text-white">Researcher
                    Profile
                </h1>
                <div th:replace="~{page/investigator/fragments/basicInfo :: basicInfo('Institution', 'ABC')}">
                </div>
                <div th:replace="~{page/investigator/fragments/basicInfo :: basicInfo('Lab', 'XYZ')}">
                </div>
                <div th:replace="~{page/investigator/fragments/basicInfo :: basicInfo('Investigator', 'ABC')}">
                </div>
            </div>
            <div>
                <div class="col-span-1 md:col-span-4 lg:col-span-3 md:text-right">
                    <a class="text-white min-w-[225px] max-h-9 bg-blue-500 hover:bg-blue-600 font-bold py-2 px-4 mb-2 rounded text-sm inline-block"
                        th:href="${'/svm/files/'+studyId}">
                        New Study Data Collection
                    </a>
                </div>
                <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
            </div>
        </div>


    </div>
</body>

</html>