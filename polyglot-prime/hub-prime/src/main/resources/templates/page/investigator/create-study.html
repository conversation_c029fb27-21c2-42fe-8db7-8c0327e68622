<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
  <!-- Include Tom Select CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />

  <!-- Include Tom Select JS -->
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
  <title>Welcome</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script th:src="@{/js/utils.js}"></script>
  <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.3/dist/purify.min.js"></script>
  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: document.referrer.includes('all') ? "All Studies" : "My Studies",
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies/dashboard"
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
      const currentUrl = new URL(window.location.href); // Gets the current URL
      console.log(currentUrl)
      // Use URLSearchParams to parse the query parameters
      const params = new URLSearchParams(currentUrl.search);

      // Get the value of the 'prstat' parameter
      const prstatValue = params.get('prstat') || "";
      console.log(prstatValue)
      if (prstatValue == "complete") {
        if (!localStorage.getItem("practitionerPartyId") || localStorage.getItem("practitionerPartyId") == "") { //Not logged in
          showModal();
        }
        else if (!localStorage.getItem("profileStatus") || localStorage.getItem("profileStatus") != "COMPLETE") { //profile incomplete
          showProfilePromptModal();
        }
        else {
          document.getElementById('createdBy').value = localStorage.getItem("practitionerPartyId");
          document.getElementById('tenantId').value = localStorage.getItem("organizationPartyId");
        }
      }
      else if (prstatValue != "complete") {
        if (!isLoggedIn()) {
          showModal();
          //window.location.href = "/studies/all"
        }
        else if (!isProfileCompleted()) {
          showProfilePromptModal();
        }
        else {
          document.getElementById('createdBy').value = localStorage.getItem("practitionerPartyId");
          document.getElementById('tenantId').value = localStorage.getItem("organizationPartyId");
        }
      }
    });

    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }

    // Function to show the modal
    function showProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }
  </script>

  <script type="module" src="/js/study.js"></script>
  <script type="module" src="/js/organization.js"></script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
  <div layout:fragment="content">
    <ul class="flex flex-wrap border-b border-gray-200" id="pageTabs"></ul>
    <div id="new-study-modal" class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <span class="loader loader-big justify-self-center absolute z-10" id="createStudyLoader"></span>
      <div class="relative w-full max-w-xl max-h-full">
        <div class="relative bg-white ">
          <div class="pt-4 pb-2 pl-1">
            <h3 class="text-lg font-semibold">
              Create a New Study
            </h3>
          </div>

          <div class="p-1">
            <form id="createStudyForm" name="createStudyForm">
              <div class="p-6 border rounded dark:border-gray-600">
                <div class="grid gap-4 mb-4 grid-cols-2">
                  <div class="col-span-2 hidden">
                    <label for="createdBy" class="block mb-2 text-sm font-normal text-gray-900 ">
                      Study Owner
                    </label>
                    <input type="text" name="createdBy" id="createdBy"
                      class=" border rounded border-gray-300 text-gray-900 text-sm font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                      placeholder="Enter Study Owner" />
                    <span id="createdBy-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                  <div class="col-span-2 hidden">
                    <label for="tenantId" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                      Organization Name
                    </label>
                    <select name="tenantId" id="tenantId"
                      class="col-span-3 text-gray-900 text-sm  font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                      placeholder="Search Organization"></select>
                    <span id="tenantId-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                  <div class="col-span-2">
                    <label for="title" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                      Study Name<span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_name')}">
                      </div>
                    </label>
                    <input type="text" name="title" id="title"
                      class="border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                      placeholder="Enter study name" />
                    <span id="title-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                  <div class="col-span-2">
                    <label for="researchStudyIdentifier" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                      Study Display ID<span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_display_id')}">
                      </div>
                    </label>
                    <input type="text" name="researchStudyIdentifier" id="researchStudyIdentifier"
                      class="border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                      placeholder="Enter study Display ID" />
                    <span class="block text-xs font-normal text-gray-500">Use only letters and digits, up to 6
                      characters.</span>
                    <span id="researchStudyIdentifier-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                  </div>

                  <div class="col-span-2 mb-4 -mt-3 hidden" id="studyDisplayId-loader">
                    <button disabled type="button"
                      class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                      <svg aria-hidden="true" role="status"
                        class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                        fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                          fill="currentColor" />
                        <path
                          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                          fill="#1C64F2" />
                      </svg>
                      Checking Study Display Id...
                    </button>
                  </div>
                  <div class="col-span-2">
                    <label for="description" class="block flex mb-2 text-sm font-normal text-gray-900 ">
                      Study Description
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_description')}">
                      </div>
                    </label>
                    <textarea type="text" name="description" id="description"
                      class=" border border-gray-300 text-gray-900 text-sm rounded font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "
                      placeholder=""></textarea>
                    <span id="description-error" class="text-xs font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="col-span-2 visibility-container">
                </div>
              </div>
              <div class="flex justify-end pt-3">
                <button type="submit" id="save-study" data-privilege-buttons="Create New Study"
                  class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                  Create Study
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>

      <div th:replace="~{page/investigator/fragments/loginPrompt::loginPrompt('Please login to create a study.')}">
      </div>
      <div
        th:replace="~{page/investigator/fragments/profilePrompt::profilePrompt('Please complete your profile information to create study.')}">
      </div>
    </div>

  </div>

</body>



</html>