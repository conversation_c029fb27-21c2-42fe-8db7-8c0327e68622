<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
  <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <style>
    /* styles.css */
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

    .loader {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 2s linear infinite;
      display: none;
      /* Initially hidden */
    }

    .loaderBottom {
      border: 4px solid #ffffff;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 2s linear infinite;
      display: none;
      /* Initially hidden */
    }

    .loader-big {
      width: 40px;
      height: 40px;
      border-width: 3px;
    }

    div#serverDataGrid a {
      color: rgb(59 130 246) !important;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>

  <script th:inline="javascript" type="module">
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: "My Studies",
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies",
          },
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      showLoading("myStudyLoader");
      window.layout.initActiveRoute();

      loadStudyItems();
    });
    // function hideLoader(loaderId) {
    //   document.getElementById(loaderId).style.display = "none";
    // }

    // function showLoader(loaderId) {
    //   document.getElementById(loaderId).style.display = "block";
    // }
    async function loadStudyItems() {
      const targetElement = document.getElementById("studies-container"); // Replace with your target element ID
      let studyList = [];
      //const userId = "01JGDH6X5SEY4DGBNBPEH3M7AT";
      const userId = localStorage.getItem("practitionerPartyId");
      try {
        // Show loader
        showLoading("myStudyLoader");

        // Fetch study list
        const studyResponse = await fetch(
          "/research-study/my?userId=" + encodeURIComponent(userId)
        );
        if (!studyResponse.ok) {
          throw new Error(
            "Network response was not ok: " + studyResponse.status
          );
        }
        const studyData = await studyResponse.json();
        const studyDetails = studyData.data.studyDetails;
        const studyList = studyDetails.data;

        // Fetch study info fragment
        const infoResponse = await fetch("/studyInfo");
        if (!infoResponse.ok) {
          throw new Error(
            "Network response was not ok: " + infoResponse.statusText
          );
        }
        const fragment = await infoResponse.text();

        // Clear the target element
        targetElement.innerHTML = "";

        if (studyList.length === 0) {
          // Handle empty study list
          targetElement.innerHTML = "";
          const headerBox = document.createElement("div");
          headerBox.className =
            "flex flex-col items-center text-center md:py-20  lg:py-20 sm:py-5  md:px-80  lg:px-80 sm:px-5";
          targetElement.appendChild(headerBox);
          // Create the SVG element
          // Create the SVG element
          const svg = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "svg"
          );
          svg.setAttribute("width", "24");
          svg.setAttribute("height", "24");
          svg.setAttribute("viewBox", "0 0 24 28"); // Adjusted the viewBox for appropriate scaling
          svg.setAttribute("fill", "none");
          svg.setAttribute("xmlns", "http://www.w3.org/2000/svg");
          // Create the path element
          const path = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "path"
          );
          path.setAttribute(
            "d",
            "M21.4737 22.909H7.57895C6.88421 22.909 6.28968 22.66 5.79537 22.162C5.30021 21.663 5.05263 21.0635 5.05263 20.3636V2.54546C5.05263 1.84545 5.30021 1.246 5.79537 0.74709C6.28968 0.24903 6.88421 0 7.57895 0H15.3789C15.7158 0 16.0371 0.0636364 16.3427 0.190909C16.6476 0.318182 16.9158 0.498484 17.1474 0.731818L22.2737 6.90454C22.5053 7.13788 22.6842 7.40812 22.8105 7.71527C22.9368 8.02327 23 8.34697 23 8.68636V20.3636C23 21.0635 22.7526 21.663 22.2574 22.162C21.7632 22.66 21.1684 22.909 20.4737 22.909ZM2.52632 28C1.83158 28 1.23684 27.751 0.742105 27.253C0.247368 26.755 0 26.1565 0 25.4545V8.9091C0 8.54848 0.121263 8.246 0.36579 8.00163C0.608421 7.75812 0.905263 7.63636 1.26316 7.63636C1.62105 7.63636 1.91789 7.75812 2.16184 8.00163C2.40421 8.246 2.52632 8.54848 2.52632 8.9091V25.4545H15.1579C15.5158 25.4545 15.8126 25.5763 16.0566 25.819C16.299 26.0626 16.4211 26.3659 16.4211 26.7273C16.4211 27.0879 16.299 27.39 16.0566 27.6336C15.8126 27.8769 15.5158 28 15.1579 28H2.52632ZM16.4211 8.9091H21.4737L15.1579 2.54546V7.63636C15.1579 7.99697 15.2789 8.29945 15.5206 8.54382C15.7626 8.78818 16.0632 8.9091 16.4211 8.9091Z"
          );
          path.setAttribute("fill", "#737373");
          svg.appendChild(path);
          // Append the SVG to the document (e.g., body or another element)
          headerBox.appendChild(svg);
          const headingText = document.createElement("h2");
          headingText.className = "font-semibold text-2xl pt-4";
          headingText.textContent = "Welcome to My Studies!";
          headerBox.appendChild(headingText);
          const descriptionData = document.createElement("p");
          descriptionData.className = "text-sm font-normal p-2";
          descriptionData.textContent =
            "A tool for tracking and managing personal diabetes data for research purposes. Users can log health metrics, monitor trends, and analyze data to support their studies, all in an organized, searchable, and customizable format.";
          headerBox.appendChild(descriptionData);
        } else {
          // Render each study item
          studyList.forEach((study, index) => {
            const studyHTML = createStudyItem(fragment, study);

            // Remove border for the last study item
            if (index === studyList.length - 1) {
              studyHTML.classList.remove("border-b", "border-[#C6CED5]-800");
            }

            // Append the study HTML to the target element
            targetElement.appendChild(studyHTML);
          });
        }
      } catch (error) {
        const headerBox = document.createElement("div");
        headerBox.className =
          "flex flex-col items-center text-center md:py-20  lg:py-20 sm:py-5  md:px-80  lg:px-80 sm:px-5";
        targetElement.appendChild(headerBox);
        const headingText = document.createElement("h2");
        headingText.className = "font-semibold text-2xl pt-4";
        headingText.textContent = "Something went wrong!";
        headerBox.appendChild(headingText);
        const descriptionData = document.createElement("p");
        descriptionData.className = "text-sm font-normal p-2";
        descriptionData.textContent =
          "Error in loading studies. Please try again later.";
        headerBox.appendChild(descriptionData);
      } finally {
        // Hide the loader
        hideLoading("myStudyLoader");
      }
    }

    function createStudyItem(fragment, study) {
      let parser = new DOMParser();
      let doc = parser.parseFromString(fragment, "text/html");
      let element = doc.body.firstChild;
      element.querySelector(".study-detail-link").innerHTML = study.title;
      const descriptionElement = element.querySelector(".st-description");
      const description = study.description || "";

      // Trimmed description logic
      const truncatedDescription =
        description.length > 500
          ? description.substring(0, 500) + "..."
          : description;

      // Initial content setup
      descriptionElement.innerHTML = `
  <span class="description-text">${truncatedDescription}</span>
  ${description.length > 500 ? '<button class="toggle-btn italic text-gray-500">More</button>' : ""}
`;

      // Event listener for Show More / Show Less
      descriptionElement
        .querySelector(".toggle-btn")
        ?.addEventListener("click", (event) => {
          const descriptionText =
            descriptionElement.querySelector(".description-text");
          const toggleButton = event.target;

          if (toggleButton.textContent === "More") {
            descriptionText.textContent = description; // Show full description
            toggleButton.textContent = "Less"; // Update button text
          } else {
            descriptionText.textContent = truncatedDescription; // Show truncated description
            toggleButton.textContent = "More"; // Update button text
          }
        });
      const studyProgress = 50;
      element.querySelector(".st-progress").innerHTML = studyProgress + "%";
      element.querySelector(".st-progress-bar").style.width =
        studyProgress + "%";

      let refDate = (study.updated_at !== '' && study.updated_at !== undefined && study.updated_at !== null) ? study.updated_at : study.created_at;

      if (refDate !== '' || refDate !== undefined) {
        const date = new Date(refDate);
        const currentDate = new Date();
        const istToUtcOffset = -5.5 * 60 * 60 * 1000; // IST is UTC +5:30
        const nowInUTC = new Date(currentDate.getTime() + istToUtcOffset);
        // Calculate the difference in milliseconds
        const timeDifference = nowInUTC - date;



        // Convert milliseconds to days
        const daysDifference = timeDifference / (1000 * 60 * 60 * 24);

        // Conditional formatting
        if (daysDifference < 1) {
          // Less than 1 day: Show time only
          const hours =
            Math.floor(Math.abs(timeDifference) / (1000 * 60 * 60)) % 24;
          const minutes =
            Math.floor(Math.abs(timeDifference) / (1000 * 60)) % 60;
          if (hours < 1) {
            element.querySelector(".st-updated").innerHTML =
              "Updated " + minutes + " minutes ago";
          } else {
            element.querySelector(".st-updated").innerHTML =
              "Updated " + hours + " hours ago";
          }
        } else if (daysDifference <= 14) {
          element.querySelector(".st-updated").innerHTML =
            "Updated " + Math.ceil(daysDifference) + " days ago";
        } else {
          element.querySelector(".st-updated").innerHTML =
            "Updated on " + date.toDateString(); // Or use `toLocaleDateString()` for localized formatting
        }
      }
      element.querySelector(".st-status").innerHTML = study.visibility_name;
      console.log("study.visibility_name", study.visibility_name)
      if (study.visibility_name == 'Public') {
        element.querySelector(".st-public").classList.remove("hidden");
      }
      else if (study.visibility_name == 'Private') {
        element.querySelector(".st-private").classList.remove("hidden");
      }
      else if (study.visibility_name == 'Internal') {
        element.querySelector(".st-internal").classList.remove("hidden");
      }
      element.querySelector(".study-detail-link").href =
        "/study/info/" + study.id + "?tab=My Studies";
      return element;
    }
  </script>
  <script type="module" src="/js/study.js"></script>
  <script th:src="@{/js/utils.js}"></script>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
</head>

<body>
  <div layout:fragment="content">
    <div class="grid grid-cols-2 py-4">
      <h6 class="text-md font-medium">My Studies</h6>
      <a th:href="'/studies/create-study'" class="place-self-end hidden" id="create-study-link"
        th:if="${isAuthenticated && isProfileCompleted}" privilege-action-buttons-links="Create New Study">
        <button type="button"
          class="w-36 bg-[#1F883D] text-sm text-white px-5 py-1.5 rounded hover:bg-[#1F883D] font-bold">
          Add New Study
        </button>
      </a>
      <span class="loader loader-big justify-self-center" id="myStudyLoader"></span>
    </div>

    <div class="border border-[C6CED5]-800 rounded-md" id="studies-container"></div>
  </div>
</body>

</html>