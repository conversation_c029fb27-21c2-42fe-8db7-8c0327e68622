<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css" />
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css" />
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <script th:inline="javascript">
        var studyId = /*[[${studyId}]]*/ "";
        var participantId = /*[[${participantId}]]*/ "";
        var currentPage = /*[[${currentPage}]]*/ "";
        var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
        var participantDisplayId = /*[[${participantDisplayId}]]*/ "";
        var tab = /*[[${tab}]]*/ "";
    </script>
    <script th:inline="javascript" type="module">
        import * as sh from "@presentation/shell/shell-aide.js";
        const tabs = [
            { text: "Dashboard", href: "/studies/dashboard" },
            { text: "Population Percentage", href: "/studies/population" },
            { text: "All Studies", href: "/studies/all" },
            { text: "My Studies", href: "/studies/mystudies" },
        ];
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: participantDisplayId,
                breadcrumbs: [
                    {
                        text: "Studies",
                        href: "/studies/dashboard",
                    },
                    {
                        text: tab,
                        href: tabs.find(t => t.text === tab)?.href,
                    },
                    {
                        text: studyDisplayId,
                        href: "/study/info/" + studyId + "?tab=" + tab,
                    },
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };

        document.addEventListener("DOMContentLoaded", function () {
            hideLoading("cgmParticipantLoader");
            window.layout.initActiveRoute();
        });
    </script>

    <script src="https://unpkg.com/ag-grid-enterprise@33.1.1/dist/ag-grid-enterprise.js"></script>

    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>
    <script type="module" src="/js/participants.js"
        th:attr="data-studyid=${studyId},data-fileName=${fileName},data-tableName=${tableName}"></script>
    <script type="module" src="/js/mealsfitness.js"
        th:attr="data-studyid=${studyId},data-fileType=Fitness,data-fileName=${fileName},data-tableName=${tableName}"></script>
    <script th:src="@{/js/upload.js}"></script>
</head>

<body>
    <div layout:fragment="content">
        <div th:replace="~{page/investigator/fragments/participant}"></div>
        <ul class="flex flex-wrap border-b border-gray-200 mb-4" id="pageTabs"></ul>
        <div
            th:replace="~{fragments/introduction :: introduction(pagedescription = ${pagedescription},pagesubdescriptiontitle=${pagesubdescriptiontitle},pagesubdescription=${pagesubdescription},pageattributestitle=${pageattributestitle},pageattributes=null,notes = null,font='')}">
        </div>
        <div id="fitness-upload-form" privilege-action-buttons-links="Edit Fitness Data"
            class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full hidden">
            <div class="relative w-full max-w-2xl max-h-full">
                <div th:replace="~{page/investigator/fragments/archiveInfoAlert}">
                </div>
                <!-- <div class="flex px-1 py-2">
          <h3 class="text-lg font-semibold text-gray-900 ">
            Upload Fitness Data
          </h3>
        </div> -->

                <div class="form-ctr ">
                    <div class="border rounded dark:border-gray-600">
                        <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-gray-200">
                            <h3 class="text-base font-semibold"> Upload Fitness Data</h3>
                        </div>
                        <div class="px-10 py-6 ">
                            <div class="gap-1 mb-4">
                                <div class="flex felx-col">
                                    <label for="patientID"
                                        class="w-1/3 block flex mb-2 text-sm font-normal text-gray-900 ">
                                        Participant ID
                                        <div
                                            th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('auto_generated_participant_id')}">
                                        </div>
                                    </label>
                                    <div class="w-2/3 relative">
                                        <input type="text" name="patientID" id="patientID"
                                            class="border border-gray-300 text-gray-500 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-upload-fields"
                                            readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="gap-1 mb-4 ">
                                <span class="block mb-4 text-sm font-normal text-gray-900 ">
                                    If you have a file with participant's fitness data, please upload it here.
                                    Alternatively, you
                                    can

                                    <a href="/participant/fitness-file/template"
                                        download="Sample_Participant_Fitness_Template.csv"
                                        class="text-blue-500 underline hover:text-blue-700 mx-1">
                                        download the template
                                    </a>
                                    for participant's fitness file upload, modify the data inside the provided CSV file,
                                    and
                                    upload it here.
                                </span>
                                <div class="grid grid-cols-3">
                                    <label for="uploadMealsFitnessData" class="text-sm font-normal text-gray-900  py-6">
                                        Choose File
                                    </label>
                                    <div class="border rounded dark:border-gray-600 col-span-2" id="drop-zone">
                                        <label for="uploadMealsFitnessData" class="px-4 py-5 block">
                                            <div class="flex items-center justify-center">
                                                <svg width="17" height="13" class="inline-block mt-1 mr-2"
                                                    viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M13.7063 5.05521C13.2246 2.62583 11.0783 0.802029 8.5 0.802029C6.45292 0.802029 4.675 1.95687 3.78958 3.64687C1.6575 3.87221 0 5.66784 0 7.84372C0 10.1745 1.90542 12.0687 4.25 12.0687H13.4583C15.4133 12.0687 17 10.4914 17 8.54789C17 6.68888 15.5479 5.18196 13.7063 5.05521ZM13.4583 10.6604H4.25C2.68458 10.6604 1.41667 9.39994 1.41667 7.84372C1.41667 6.40017 2.50042 5.19604 3.93833 5.04817L4.69625 4.97071L5.05042 4.30175C5.72333 3.01312 7.04083 2.21037 8.5 2.21037C10.3558 2.21037 11.9567 3.52012 12.3179 5.32984L12.5304 6.38609L13.6142 6.46355C14.7192 6.53397 15.5833 7.45643 15.5833 8.54789C15.5833 9.70977 14.6271 10.6604 13.4583 10.6604ZM9.52708 5.02704H7.47292V7.13955H5.66667L8.5 9.95623L11.3333 7.13955H9.52708V5.02704Z"
                                                        fill="#737373" />
                                                </svg>
                                                <span class="text-sm text-[#737373]">Drop files here or browse</span>
                                            </div>
                                        </label>
                                        <input type="file" name="uploadMealsFitnessData" id="uploadMealsFitnessData"
                                            accept=".csv"
                                            class="hidden h-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm  focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 participant-field"
                                            placeholder="Drop files here or browse" />
                                        <input type="hidden" name="studyId" id="studyId" th:value="${studyId}" />
                                        <div id="upload-ctr" class="mt-4"></div>
                                    </div>
                                    <div id="upload-list" class="mt-4 text-sm font-semibold hidden">Uploaded Files</div>
                                </div>

                            </div>

                        </div>
                    </div>
                    <div class="my-4 text-right">
                        <button type="button" id="upload-files" data-tooltip-target="tooltip-upload-participant"
                            data-tooltip-placement="top"
                            class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
                            Upload
                        </button>
                        <span id="tooltip-upload-participant" role="tooltip"
                            class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
                            permissions are limited.</span>
                        <div class="tooltip-arrow" data-popper-arrow></div>
                        <div class="col-span-2 mb-4 mt-3  hidden" id="participantMealFitnessLoader">
                            <button disabled type="button"
                                class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
                                <svg aria-hidden="true" role="status"
                                    class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600"
                                    viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="currentColor" />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="#1C64F2" />
                                </svg>
                                Uploading Participant's Fitness Data...
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="serverDataGrid" class="ag-theme-alpine rounded border border-slate-300"></div>
        <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
    </div>
</body>

</html>