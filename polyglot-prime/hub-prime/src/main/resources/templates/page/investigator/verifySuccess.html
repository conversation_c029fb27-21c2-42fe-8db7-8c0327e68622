<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <title>success</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <link th:href="@{/css/style.css}" rel="stylesheet" />
    <script th:src="@{/js/utils.js}"></script>

    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <script th:src="@{/js/profile.js}"></script>

    <script th:inline="javascript" type="module">

        import * as sh from "@presentation/shell/shell-aide.js";
        new sh.TwoLevelHorizontalLayoutAide()
            .setActiveRoute({
                isHomePage: /*[[${isHomePage}]]*/ false,
                uri: /*[[${activeRoutePath}]]*/ "",
                title: 'Success',
                breadcrumbs: [
                ],
                tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
            })
            .global("layout"); // register as window.layout

        document.sandboxConsoleWatch = {
            activeRoute: window.layout.activeRoute,
        };


        document.addEventListener("DOMContentLoaded", function () {
            window.layout.initActiveRoute();
            if (!isLoggedIn()) {
                showModal();
            }

        });


        // Function to show the modal
        function showModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const options = {
                backdrop: 'static', // Prevents closing when clicking outside
                backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
            };
            const modal = new Modal(modalEle, options)
            modal.show();
            return false;
        }

        // Function to hide the modal
        function hideModal() {
            const modalEle = document.getElementById("login-prompt-modal");
            const modal = new Modal(modalEle)
            modal.hide();
        }

    </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">

    <div layout:fragment="content">

        <div id="verificatio-success-modal"
            class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full ">
            <div class="relative w-full max-h-full border border-gray-200">
                <!-- Modal content -->


                <div class="flex items-center justify-center p-8" sec:authorize="isAuthenticated()">
                    <div class="bg-gray-50 p-8 rounded-lg w-full max-w-md ">
                        <div class="flex flex-col items-center p-6">
                            <svg width="50px" height="50px" viewBox="0 0 50 50" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <circle cx="25" cy="25" r="20" fill="#4CAF50" />
                                <path d="M14.1,27.2l7.1,7.1l17.7-17.7" stroke="#fff" stroke-width="4"
                                    stroke-linecap="round" stroke-linejoin="round" fill="none" />
                            </svg>

                            <h2 class="text-2xl font-semibold mb-2">Verification Successful</h2>
                            <p class="text-gray-600 text-center mb-6">Your email address has been successfully
                                verified.You may now continue.</p>


                            <!-- Verify Button -->
                            <button id="sucess-button"
                                class="w-24 bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 text-white py-2 rounded">
                                OK</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="login-prompt-modal" data-modal-backdrop="static" tabindex="-1"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

                        <div class="p-4 md:p-5 text-center">
                            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please login to
                                proceed with Email verification.
                            </h3>
                            <a href="/">
                                <button type="button"
                                    class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                                    Login
                                </button>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
        </div>


</body>



</html>