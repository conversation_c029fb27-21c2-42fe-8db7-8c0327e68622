<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <title>Welcome</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>
  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script th:src="@{/js/utils.js}"></script>
  <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.3/dist/purify.min.js"></script>
  <script th:inline="javascript">
    var studyId = /*[[${studyId}]]*/ "";
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    var tab = /*[[${tab}]]*/ "";
    // Now you can use studyId in your JavaScript code
  </script>
  <script th:inline="javascript" type="module">
    var studyDisplayId = /*[[${studyDisplayId}]]*/ "";
    const tabs = [
      { text: "Dashboard", href: "/studies/dashboard" },
      { text: "Population Percentage", href: "/studies/population" },
      { text: "All Studies", href: "/studies/all" },
      { text: "My Studies", href: "/studies/mystudies" },
    ];
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: "Study Settings",
        breadcrumbs: [
          {
            text: "Studies",
            href: "/studies/dashboard",
          },
          {
            text: tab,
            href: tabs.find(t => t.text === tab)?.href,
          },
          {
            text: studyDisplayId,
            href: "/study/info/" + studyId + "?tab=" + tab,
          },

        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };

    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
      console.log(isLoggedIn())
      if (!isLoggedIn()) {
        showModal();
        //window.location.href = "/studies/all"
      }
      else if (isLoggedIn() && !isProfileCompleted()) {
        showProfilePromptModal();
      }


      const fieldMappings = [
        { label: "title", field: "title" },
        { label: "start_date", field: "start_date" },
        { label: "end_date", field: "end_date" },
        { label: "nct_number", field: "nct_number" },
        { label: "study_location", field: "study_location" },
        { label: "funding_source", field: "funding_source" },
        { label: "treatment_modalities", field: "treatment_modalities" },
        // { label: "publication_title", field: "publication.title" },
        // { label: "publication_date", field: "publication.publication_date" },
        // { label: "doi", field: "publication.doi" },
        { label: "description", field: "description" },
        { label: "principalAuthor", field: "publication.principalAuthor" }
      ];

      const datepickerStart = document.getElementById('start_date');
      const datepicker1 = new Datepicker(datepickerStart, {
        format: 'mm-dd-yyyy', // Customize the date format
        autohide: true,       // Automatically close the calendar after date selection
      });
      const datepickerEnd = document.getElementById('end_date');
      const datepicker2 = new Datepicker(datepickerEnd, {
        format: 'mm-dd-yyyy', // Customize the date format
        autohide: true,       // Automatically close the calendar after date selection
      });
      // const datepickerPub = document.getElementById('publication_date');
      // const datepicker3 = new Datepicker(datepickerPub, {
      //   format: 'mm-dd-yyyy', // Customize the date format
      //   autohide: true,       // Automatically close the calendar after date selection
      // });
      const updateField = (label, value) => {
        const element = document.querySelector(`#${label}`);

        if (element) {
          if (value) {
            if (label == 'start_date') {
              datepicker1.setDate(new Date(value))
            }
            else if (label == 'end_date') {
              datepicker2.setDate(new Date(value))
            }
            else {
              element.value = value;
            }

          }
        }
      };

      // // Iterate over the field mappings and update the fields
      // fieldMappings.forEach(({ label, field }) => {
      //   const value = field.split('.').reduce((obj, key) => obj?.[key], studyDetail);
      //   console.log(value)
      //   updateField(label, value);
      // });

      let container = document.querySelector(".visibility-container");
      getStudyVisibility()
        .then((visibilityData) => {
          visibilityData.forEach((element) => {
            createRadioButton(element, container);
          });
          getStudySettings();
        })
        .catch((error) => {
          console.error("Error fetching data:", error);
        });

    });

    function getStudySettings() {
      fetchRawData(`/research-study?studyId=${studyId}`, (res) => {
        try {
          const data = res.data.studyDetails;
          let loggedInUser = getLoggedInUser();
          console.log(data.created_by, loggedInUser)
          if (data.created_by != null && data.created_by == getLoggedInUser()) {
            document.getElementById("form-archive")?.classList.remove("hidden");
            document.getElementById("form-delete")?.classList.remove("hidden");
          }

          data.title != null ? document.getElementById("title").value = data.title : '';
          data.description != null ? document.getElementById("description").value = data.description : '';
          data.site_id != null ? document.getElementById("study_location").value = data.site_id : '';
          data.treatment_modalities != null ? document.getElementById("treatment_modalities").value = data.treatment_modalities : '';
          data.funding_source != null ? document.getElementById("funding_source").value = data.funding_source : '';
          data.nct_number != null ? document.getElementById("nct_number").value = data.nct_number : '';
          data.start_date != null ? document.getElementById("start_date").value = data.start_date : '';
          data.end_date != null ? document.getElementById("end_date").value = data.end_date : '';
          // data.publication_title != null ? document.getElementById("publication_title").value = data.publication_title : '';
          // data.publication_date != null ? document.getElementById("publication_date").value = data.publication_date : '';
          // data.publication_doi != null ? document.getElementById("publication_doi").value = data.publication_doi : '';
          const visibilities = document.querySelectorAll('input[name="visibility"]');
          visibilities.forEach(radio => {
            if (radio.value == data.visibility) {
              radio.checked = true;
            }
          });

          if (data.archive_status == true) {
            document.getElementById("archive-study-title").textContent = "Unarchive this study";
            document.getElementById("archive-study-desc").textContent = "Unarchive this study and make it editable.";
            document.getElementById("archive-study").textContent = "Unarchive this study";
            toggleTooltip("update-study-settings", data.archive_status);
            toggleTooltip("update-study-visibility", data.archive_status);
            [...document.getElementsByClassName("study-settings-field")].forEach((element) => {
              element.disabled = true;
            });
            document.querySelector(".archive-alert").classList.remove("hidden");
          }
          else {
            if (
              data.created_by != null &&
              data.created_by != getLoggedInUser()
            ) {
              toggleTooltip("update-study-settings", true);
              toggleTooltip("update-study-visibility", true);
              toggleTooltip("archive-study", true);
              toggleTooltip("delete-study", true);
              [...document.getElementsByClassName("study-settings-field")].forEach((element) => {
                element.disabled = true;
              });
              document.querySelector(".archive-alert").classList.remove("hidden");
            }
          }
        } catch (error) {
          console.error("Error parsing data:", error);
        }
      });
    }
    // Function to show the modal
    function showProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideProfilePromptModal() {
      const modalEle = document.getElementById("profile-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }


    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }
    // Event listener to close the modal
    document.getElementById("close-confirm-modal").addEventListener("click", () => {
      const modal = new Modal(document.getElementById('confirmation-modal'));
      modal.hide();
    });

    function createRadioButton(data, container) {
      const wrapper = document.createElement("div");
      wrapper.className = "flex items-start gap-2 pb-2";

      // Create the input (radio button)
      const input = document.createElement("input");
      input.type = "radio";
      input.id = data.visibility_name;
      input.name = "visibility";
      input.value = data.visibility_id;
      input.className = "text-sm font-normal text-gray-900 study-settings-field";

      // Create the label
      const label = document.createElement("label");
      label.htmlFor = data.visibility_name;
      label.className = "text-sm font-normal text-gray-900 ";
      const labelText = document.createTextNode(data.visibility_name);
      label.appendChild(labelText);

      // Create the span for the description
      const span = document.createElement("span");
      span.className = "text-gray-500";
      span.textContent = ` - ${data.visibility_description}`;
      label.appendChild(span);
      wrapper.appendChild(input);
      wrapper.appendChild(label);
      container.appendChild(wrapper);
    }
  </script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script type="module" src="/js/settings.js" th:attr="data-studyid=${studyId}"></script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col justify-between">
  <div layout:fragment="content">
    <ul class="flex flex-wrap border-b border-gray-200" id="pageTabs"></ul>
    <div id="new-participant-form"
      class="flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full pt-8">
      <div class="relative w-full max-w-2xl max-h-full">
        <div th:replace="~{page/investigator/fragments/archiveInfoAlert}">
        </div>
        <div class="form-ctr pt-2">
          <div class="border rounded dark:border-gray-600">
            <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-gray-200">
              <h3 class="text-base font-semibold">Study Settings</h3>
            </div>
            <div class="px-10 py-6 ">
              <div class="gap-1">
                <div class="grid grid-cols-3  mb-4">
                  <label for="title" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Study Name<span class="text-red-500">*</span>
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_name')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="title" id="title" rows="4"
                      class="block w-full rounded p-1.5 text-sm font-normal text-gray-900  border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 study-settings-field"
                      placeholder="Enter Study Name" />
                    <span id="title-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="description" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Study Description
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_description')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <textarea type="text" name="description" id="description" rows="4"
                      class="border border-gray-300 text-gray-900 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 study-settings-field"
                      placeholder=""></textarea>
                    <span id="description-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>

                <div class="grid grid-cols-3  mb-4">
                  <label for="study_location" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Study Location
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_location_name')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <textarea type="text" name="study_location" id="study_location"
                      class="border border-gray-300 text-gray-900 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 study-settings-field"
                      placeholder=""></textarea>
                    <span id="study_location-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="treatment_modalities" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Treatment Modalities
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_treatment_modalities')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="treatment_modalities" id="treatment_modalities" rows="4"
                      class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 study-settings-field"
                      placeholder="Enter Treatment Modalities" />
                    <span id="treatment_modalities-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="funding_source" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Funding Source
                      <div
                        th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_funding_source')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="funding_source" id="funding_source" rows="4"
                      class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900  border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 study-settings-field"
                      placeholder="Enter Funding Source" />
                    <span id="funding_source-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="nct_number" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      NCT Number
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_nct_number')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="nct_number" id="nct_number" rows="4"
                      class="block  w-full rounded px-3 py-1.5 text-sm font-normal text-gray-900   border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400  dark:focus:ring-blue-500 dark:focus:border-blue-500 study-settings-field"
                      placeholder="Enter NCT Number" />
                    <span class="block text-xs font-normal text-gray-500">The identifier should be in the format
                      NCTXXXXXXXX (NCT followed by 8 digits)</span>
                    <span id="nct_number-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
                <div class="grid grid-cols-3  mb-4">
                  <label for="start_date" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      Start Date
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_start_date')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="start_date" id="start_date" type="text"
                      class="border border-gray-300 text-gray-900 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 study-settings-field"
                      placeholder="MM-DD-YYYY" />
                    <span id="start_date-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>

                <div class="grid grid-cols-3  mb-4">
                  <label for="end_date" class="text-sm font-normal text-gray-900  py-2">
                    <div class="flex flex-row">
                      End Date
                      <div th:replace="~{page/investigator/fragments/formtooltip :: formtooltip('study_end_date')}">
                      </div>
                    </div>
                  </label>
                  <div class="col-span-2">
                    <input name="end_date" id="end_date" type="text"
                      class="border border-gray-300 text-gray-900 text-sm font-normal rounded focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 study-settings-field"
                      placeholder="MM-DD-YYYY" />
                    <span id="end_date-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <div class="my-4 text-right relative">
            <button type="button" id="update-study-settings" data-tooltip-target="archive-study-settings-tooltip"
              data-privilege-buttons="Study Settings" data-tooltip-placement="top"
              class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
              Update Study
            </button>
            <span id="archive-study-settings-tooltip" role="tooltip"
              class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-left text-black transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
              permissions are limited.</span>
            <div class="tooltip-arrow" data-popper-arrow></div>
          </div>
          <div class="my-4 text-right hidden" id="update-setting-loader">
            <button disabled type="button"
              class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
              <svg aria-hidden="true" role="status"
                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor" />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="#1C64F2" />
              </svg>
              Updating Study Settings...
            </button>
          </div>
        </div>

        <div class="form-ctr">
          <div class="border rounded dark:border-gray-600">
            <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-gray-200">
              <h3 class="text-base font-semibold">Change Visibility</h3>
            </div>
            <div class="mb-4 px-10 visibility-container">
              <div class="col-span-2">
                <div class="text-sm font-normal py-4">Select the visibility level for this project
                </div>
              </div>
            </div>
          </div>
          <div class="my-4 text-right relative">
            <button type="button" id="update-study-visibility" data-tooltip-target="archive-visibility-tooltip"
              data-privilege-buttons="Study Settings" data-tooltip-placement="top"
              class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
              Change Visibility
            </button>
            <span id="archive-visibility-tooltip" role="tooltip"
              class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
              permissions are limited.</span>
            <div class="tooltip-arrow" data-popper-arrow></div>
          </div>
          <div class="my-4 text-right hidden" id="update-visibility-loader">
            <button disabled type="button"
              class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
              <svg aria-hidden="true" role="status"
                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor" />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="#1C64F2" />
              </svg>
              Updating visibility...
            </button>
          </div>
        </div>

        <div class="form-archieve hidden" id="form-archive">
          <div class="border rounded dark:border-gray-600">
            <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-gray-200">
              <h3 class="text-base font-semibold" id="archive-study-title">Archive this study</h3>
            </div>
            <div class="mb-4 px-10">
              <div class="col-span-2">
                <div class="text-sm font-normal py-4" id="archive-study-desc">Mark this study as archived and read-only.
                </div>
              </div>
            </div>
          </div>
          <div class="my-4 text-right">
            <button type="button" id="archive-study" data-privilege-buttons="Archive Unarchive Study"
              class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
              Archive this study
            </button>
            <span id="archive-visibility-owner-tooltip" role="tooltip"
              class="tooltip-txt hidden absolute z-50 invisible inline-block px-2 py-1 text-xs font-normal text-black text-left transition-opacity duration-300 bg-gray-100 rounded-lg shadow-xs opacity-0 tooltip dark:bg-white border w-48 right-0">Edit
              permissions are limited.</span>
            <div class="tooltip-arrow" data-popper-arrow></div>
          </div>
          <div class="my-4 text-right hidden" id="archive-study-loader">
            <button disabled type="button"
              class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
              <svg aria-hidden="true" role="status"
                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor" />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="#1C64F2" />
              </svg>
              Archiving Study...
            </button>
          </div>
        </div>

        <div class="form-delete hidden" id="form-delete">
          <div class="border rounded dark:border-red-600 border-red-200">
            <div class="bg-[#F6F8FA] px-2 py-1 rounded-t border-b border-red-200">
              <h3 class="text-base font-semibold">Delete this study</h3>
            </div>
            <div class="mb-4 px-10">
              <div class="col-span-2">
                <div class="text-sm font-normal py-4">Once you delete a study, there is no going back. Please be
                  certain.
                </div>
              </div>
            </div>
          </div>
          <div class="my-4 text-right">
            <button type="button" id="delete-study"
              class="text-red-500 bg-gray-100 border border-gray-300 focus:outline-none hover:bg-red-500 hover:text-white focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5 ">
              Delete this study
            </button>
          </div>
          <div class="my-4 text-right hidden" id="delete-study-loader">
            <button disabled type="button"
              class="col-start-5 col-span-3 text-center  text-sm font-medium text-gray-900  inline-flex items-center">
              <svg aria-hidden="true" role="status"
                class="inline w-4 h-4 me-3 text-gray-200 animate-spin dark:text-gray-600" viewBox="0 0 100 101"
                fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor" />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="#1C64F2" />
              </svg>
              Deleting Study...
            </button>
          </div>
        </div>


      </div>
    </div>
    <!-- Main modal -->
    <div id="confirmation-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between px-4 py-2 border-b rounded-t dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900  modal-title-text">
            </h3>
            <button type="button" id="close-confirm-modal"
              class="cancel-filter-modal end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
              data-modal-hide="confirmation-modal">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close modal</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="px-4 py-3">
            <p class="text-sm font-normal text-gray-900  modal-body-text">
            </p>
          </div>
          <div class="my-4 mx-4 pb-4 pr-4 text-right">
            <button type="button" id="delete-arch-confirm-study"
              class="text-white bg-gray-100 border border-gray-300 focus:outline-none hover:bg-red-500 hover:text-white focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm px-5 py-1.5"></button>
          </div>
        </div>
      </div>
    </div>

    <div th:replace="~{page/investigator/fragments/loginPrompt::loginPrompt('Please login to update study.')}"></div>
    <div
      th:replace="~{page/investigator/fragments/profilePrompt::profilePrompt('Please complete your profile information to update study.')}">
    </div>
    <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
  </div>
</body>

</html>