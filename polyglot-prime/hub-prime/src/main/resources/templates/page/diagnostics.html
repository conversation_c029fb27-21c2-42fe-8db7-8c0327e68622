<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/prime}">

<head>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css">

    <!-- if JSON Viewer is not already in the layout, add the following -->
    <!-- <script src="https://unpkg.com/@alenaksu/json-viewer@2.0.0/dist/json-viewer.bundle.js"></script> -->

    <title>Admin Diagnostics</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>
</head>

<body class="bg-gray-100 h-screen">
    <div layout:fragment="content">
        <div id="myGrid" class="ag-theme-alpine"></div>

        <!-- Modal -->
        <div id="modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <json-viewer id="json"></json-viewer>
            </div>
        </div>

        <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                let originalData = [];
                let pageSize = 15;
                let currentPage = 0;
                let totalRecords = 0;
                let lastPage;

                const columnDefs = [
                    {
                        headerName: "Artifact", field: "artifactId", sortable: true, filter: true, sort: "desc", enablePivot: true,
                        cellRenderer: function (params) {
                            const link = document.createElement('a');
                            link.href = '#';
                            link.innerText = params.value;
                            link.addEventListener('click', function (e) {
                                e.preventDefault();
                                showDetails(params.data.artifactId);
                            });
                            return link;
                        },
                    },
                    { headerName: "Engine", field: "engine", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Valid", field: "valid", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "QE", field: "qe", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Validated At", field: "initiatedAt", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Issue Message", field: "issueMessage", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Issue Severity", field: "issueSeverity", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Issue Location Line", field: "issueLocationLine", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Issue Location Column", field: "issueLocationColumn", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Issue Diagnostics", field: "issueDiagnostics", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                    { headerName: "Profile URL", field: "profileUrl", sortable: true, filter: true, enablePivot: true, enableRowGroup: true },
                ];

                const gridOptions = {
                    columnDefs,
                    defaultColDef: {
                        flex: 1,
                        minWidth: 100,
                        resizable: true,
                        sortable: true,
                        enablePivot: true
                    },
                    pagination: true,
                    paginationPageSize: pageSize,
                    onPaginationChanged: onPaginationChanged,
                    autoSizeStrategy: { type: "fitCellContents" },
                    sideBar: true,
                };

                const eGridDiv = document.querySelector('#myGrid');
                new agGrid.Grid(eGridDiv, gridOptions);
                // TODO: figure out why this is required (otherwise Grid goes to height of 1 pixel)
                eGridDiv.style.height = "750px"

                function fetchPageData(page, size) {
                    fetch(ssrServletUrl(`/diagnostics.json?page=${page}&size=${size}`))
                        .then(response => response.json())
                        .then(data => {
                            originalData = data;
                            totalRecords = data.totalElements;
                            const rowData = data.content.map(validation => ({
                                artifactId: validation.artifactId,
                                profileUrl: validation.profileUrl,
                                engine: validation.engine,
                                valid: validation.valid,
                                qe: validation.qe,
                                initiatedAt: validation.initiatedAt,
                                issueMessage: validation.issueMessage,
                                issueSeverity: validation.issueSeverity,
                                issueLocationLine: validation.issueLocationLine,
                                issueLocationColumn: validation.issueLocationColumn,
                                issueDiagnostics: validation.issueDiagnostics,
                            }));
                            gridOptions.api.setRowData(rowData);
                        })
                        .catch(error => {
                            console.error('Error fetching interaction data:', error);
                        });
                }

                function onPaginationChanged() {
                    if (gridOptions.api) {
                        currentPage = gridOptions.api.paginationGetCurrentPage();
                        if (currentPage !== lastPage) {
                            lastPage = currentPage;
                            fetchPageData(currentPage, pageSize);
                        }
                    }
                }

                fetchPageData(currentPage, pageSize);

                document.querySelector('.close').onclick = function () {
                    document.getElementById('modal').style.display = 'none';
                };

                window.onclick = function (event) {
                    if (event.target == document.getElementById('modal')) {
                        document.getElementById('modal').style.display = 'none';
                    }
                };
                function showDetails(artifactId) {
                    // Ensure originalData.content is an array
                    if (originalData && Array.isArray(originalData.content)) {
                        // Find the artifact in the content array
                        const artifact = originalData.content.find(item => item.artifactId === artifactId);
                        if (artifact) {
                            const processedArtifact = JSON.parse(JSON.stringify(artifact, (key, value) => {
                                return value === null ? '' : value;
                            }));
                            document.querySelector('#json').data = processedArtifact;
                            document.getElementById('modal').style.display = 'block';
                        }
                    } else {
                        console.error('originalData.content is not an array:', originalData.content);
                    }
                }
            });
        </script>
</body>

</html>