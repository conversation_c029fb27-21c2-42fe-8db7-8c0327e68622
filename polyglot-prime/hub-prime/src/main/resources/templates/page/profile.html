<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
  layout:decorate="~{layout/prime}">

<head>
  <script src="https://unpkg.com/htmx.org/dist/htmx.min.js"></script>
  <title>My Profile</title>
  <th:block th:insert="fragments/favicon :: favicon"></th:block>

  <link th:href="@{/css/style.css}" rel="stylesheet" />
  <script th:src="@{/js/utils.js}"></script>
  <script th:src="@{/js/organization.js}"></script>
  <!-- Include Tom Select CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/css/tom-select.css" rel="stylesheet" />
  <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/css/intlTelInput.css'>
  <!-- Include Tom Select JS -->
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.1.0/dist/js/tom-select.complete.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>


  <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/intlTelInput.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/country-region-data@1.6.0/data.min.js"></script>
  <style>
    .iti {
      display: block !important;
    }
  </style>

  <script th:src="@{/js/profile.js}"></script>
  <script th:inline="javascript" type="module">
    const input = document.querySelector("#phone_code");
    let datepicker1;
    // init plugin
    const iti = window.intlTelInput(input, {
      initialCountry: "us",
      separateDialCode: true,
      loadUtils: () => import("/intl-tel-input/js/utils.js?1743167482095") // for formatting/placeholders etc
    });
    import * as sh from "@presentation/shell/shell-aide.js";
    new sh.TwoLevelHorizontalLayoutAide()
      .setActiveRoute({
        isHomePage: /*[[${isHomePage}]]*/ false,
        uri: /*[[${activeRoutePath}]]*/ "",
        title: 'Edit User Profile',
        breadcrumbs: [
        ],
        tabs: /*[[${T(org.diabetestechnology.drh.conf.Configuration).objectMapper.valueToTree(siblingLinks)}]]*/[],
      })
      .global("layout"); // register as window.layout

    document.sandboxConsoleWatch = {
      activeRoute: window.layout.activeRoute,
    };
    const countryData = window.intlTelInputGlobals.getCountryData();

    const stateSelect = document.getElementById("state");
    const addressDropdown = document.querySelector("#country");


    document.addEventListener("DOMContentLoaded", function () {
      window.layout.initActiveRoute();
      if (!isLoggedIn()) {
        showModal();
      }
      if (document.getElementById('birthDate')) {
        const datepickerStart = document.getElementById('birthDate');
        datepicker1 = new Datepicker(datepickerStart, {
          format: 'mm-dd-yyyy', // Customize the date format
          autohide: true,       // Automatically close the calendar after date selection
          maxDate: new Date(),  // Prevent future dates
        });
      }
      getProfileDetails().then((profile) => {
        if (profile) {

          document.getElementById("username").innerHTML = profile.name || "";
          document.getElementById("birthdate").innerHTML = profile.birth_date || "";
          document.getElementById("useremail").innerHTML = profile.user_account_primary_email || "";
          document.getElementById("organization").innerHTML = profile.organization_name || "";
          document.getElementById("useraddress1").innerHTML = profile.line1 || "";
          document.getElementById("useraddress2").innerHTML = profile.line2 || "";
          document.getElementById("usercity").innerHTML = profile.city || "";
          document.getElementById("userzipcode").innerHTML = profile.postal_code || "";

          let country_code = profile.country || "";
          let state_code = profile.state || "";

          let selectedCountryData = countryData.find(country => country.iso2 === country_code.toLowerCase());
          if (selectedCountryData) {
            document.getElementById("usercountry").innerHTML = selectedCountryData.name || "";
            let countryData = countryRegionData.find(c => c.countryShortCode === country_code.toUpperCase());
            let stateList = countryData ? countryData.regions : [];
            if (stateList.length > 0 && state_code != '') {
              let selectedStateData = stateList.find(c => c.shortCode === state_code); // Default to US if no regions found
              document.getElementById("userstate").innerHTML = selectedStateData.name || "";
            }
          } else {
            //document.getElementById("userphone").innerHTML = cleanPhoneNumber(profile.telecom[1]?.telecom_value || "");
          }
          let telecomValue = '';
          const phoneCodeInput = document.querySelector("#phone_code");
          let phoneNumber = '';

          if (profile.telecom && profile.telecom.length > 0) {

            let telecomDet = profile.telecom.find(item => item.contact_point_system_value === 'phone');
            if (telecomDet) {
              telecomValue = telecomDet.telecom_value || '';
              iti.setNumber(telecomValue); // Set the phone number in the intl-tel-input plugin
              phoneNumber = cleanPhoneNumber(telecomValue);
            }
            console.log("telecomValue", telecomValue);


          }
          document.getElementById("userphone").innerHTML = telecomValue;

          document.getElementById("fullName").value = profile.name || "";
          document.getElementById("birthDate").value = profile.birth_date || "";
          document.getElementById("addressLine1").value = profile.line1 || "";
          document.getElementById("addressLine2").value = profile.line2 || "";
          document.getElementById("city").value = profile.city || "";
          document.getElementById("country").value = profile.country || "";
          if (document.getElementById("country").value != null && document.getElementById("country").value != "")
            document.getElementById("country").dispatchEvent(new Event('change'));
          document.getElementById("state").value = profile.state || "";
          document.getElementById("zipcode").value = profile.postal_code || "";

          phoneCodeInput.value = phoneNumber; // Remove the dial code
          loadStatesForCountry();
        }
      });


      // Country and State dropdowns

      // // get the country data from the plugin
      // const countryData = window.intlTelInput.getCountryData();
      // const input = document.querySelector("#phone_code");
      // const addressDropdown = document.querySelector("#country");


      // populate the country dropdown
      for (let i = 0; i < countryData.length; i++) {
        const country = countryData[i];
        const optionNode = document.createElement("option");
        optionNode.value = country.iso2;
        const textNode = document.createTextNode(country.name);
        optionNode.appendChild(textNode);
        addressDropdown.appendChild(optionNode);
      }


      // set address dropdown's initial value
      addressDropdown.value = iti.getSelectedCountryData().iso2;

      // listen to the telephone input for changes
      input.addEventListener('countrychange', () => {
        addressDropdown.value = iti.getSelectedCountryData().iso2;
      });

      // listen to the address dropdown for changes
      addressDropdown.addEventListener('change', () => {
        loadStatesForCountry();

      });
      document.getElementById("update-profile").addEventListener("click", async function () {
        await submitProfile();
      });

      document.getElementById("edit-profile-btn")?.addEventListener("click", function () {
        document.getElementById("profile-view").classList.add("hidden");
        document.getElementById("profile-edit").classList.remove("hidden");
      });
      document.getElementById("cancel-edit-profile-btn")?.addEventListener("click", function () {
        document.getElementById("profile-view").classList.remove("hidden");
        document.getElementById("profile-edit").classList.add("hidden");
      });

    });

    function loadStatesForCountry() {
      iti.setCountry(addressDropdown.value);

      const selectedCode = addressDropdown.value;
      console.log("selectedCode", selectedCode);
      const country = countryRegionData.find(c => c.countryShortCode === selectedCode.toUpperCase());

      stateSelect.innerHTML = ""; // Clear previous

      if (country && country.regions.length > 0) {
        country.regions.forEach(region => {
          const opt = document.createElement("option");
          opt.value = region.shortCode || region.name;
          opt.textContent = region.name;
          stateSelect.appendChild(opt);
        });
      }
    }
    function getProfileDetails() {
      return new Promise((resolve, reject) => {
        fetch('/profile-details')
          .then(response => {
            if (!response.ok) {
              throw new Error("Failed to fetch profile details.");
            }
            return response.json();
          })
          .then(data => {
            if (data.status === "success") {
              resolve(data.data.profile);
            } else {
              showToast(data.message, "error");
              resolve(null);
            }
          })
          .catch(error => {
            console.error("Error fetching profile details:", error);
            reject(error);
          });
      });
    }

    async function submitProfile() {

      let isValid = true;
      const fullName = document.getElementById("fullName").value.trim();
      const birthDate = document.getElementById("birthDate").value;
      const addressLine1 = document.getElementById("addressLine1").value.trim();
      const addressLine2 = document.getElementById("addressLine2").value.trim();
      const city = document.getElementById("city").value.trim();
      const country = document.getElementById("country").value;
      const state = document.getElementById("state").value;
      const phoneCode = document.getElementById("phone_code").value.trim() || "";
      const zipcode = document.getElementById("zipcode").value;
      const input = document.querySelector("#phone_code");
      // Get the dial code from the `iti` instance
      // init plugin
      const itiDialCode = `+${iti.getSelectedCountryData().dialCode}`;
      let fullPhoneNumber = `${itiDialCode}${phoneCode}`; // Example: '+91 1234567890'

      // Validate required fields
      if (!fullName) {
        document.getElementById("fullName-error").innerText = "Full Name is required.";
        isValid = false;
        return;
      } else {
        document.getElementById("fullName-error").innerText = "";
      }
      if (zipcode) {
        const isValidZipcode = /^[0-9\-]{1,10}$/.test(zipcode); // Only numbers and hyphen, length 1-10
        const zipcodeErrorElement = document.getElementById("zipcode-error");

        if (!isValidZipcode) {
          zipcodeErrorElement.innerText = "Zipcode must be 1-10 characters, numbers and hyphen only.";
          isValid = false;
          return;
        } else {
          zipcodeErrorElement.innerText = "";
        }
      }
      // Add min/max length validation for phone number (excluding dial code)

      if (phoneCode && phoneCode != "") {
        if (phoneCode.length < 10 || phoneCode.length > 15) {
          document.getElementById("phone_code-error").innerText = "Phone number must be between 10 and 15 digits.";
          isValid = false;
          return;
        } else {
          document.getElementById("phone_code-error").innerText = "";
        }
      } else {
        fullPhoneNumber = "";
        document.getElementById("phone_code-error").innerText = "";
      }
      // if (fullPhoneNumber && !iti.isValidNumber()) {
      //   document.getElementById("phone_code-error").innerText = "Phone number is invalid.";
      //   isValid = false;
      //   return;
      // } else {
      //   document.getElementById("phone_code-error").innerText = "";
      // }



      if (isValid) {

        try {
          // Send the user's message to the API

          // Prepare data for submission
          const profileParams = {
            "name": fullName,
            "tenant_id": localStorage.getItem("tenant_id"),
            "birth_date": birthDate ? convertToUTCFormat(birthDate) : null,
            "line1": addressLine1,
            "line2": addressLine2,
            "city": city,
            "state": state,
            "country": country,
            "postal_code": zipcode,
            "telecom": [
              {
                "contact_point_system_value": "phone",
                "contact_point_use_type_value": "work",
                "telecom_value": fullPhoneNumber
              }
            ]
          }
          console.log("Profile Params:", profileParams);
          const response = await fetch('/profile', {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(profileParams),
          });

          if (!response.ok) {
            throw new Error("Failed to fetch response from the API.");
          }

          const data = await response.json();
          if (data.status === "success") {
            showToast(data.message, "success");
            window.location.href = "/profile/info";
          }
        } catch (e) {
          console.error("Error fetching API response:", e);
        }
      }
    }



    // Function to show the modal
    function showModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const options = {
        backdrop: 'static', // Prevents closing when clicking outside
        backdropClasses: 'bg-gray-900 bg-opacity-50 fixed inset-0 z-40',
      };
      const modal = new Modal(modalEle, options)
      modal.show();
      return false;
    }

    // Function to hide the modal
    function hideModal() {
      const modalEle = document.getElementById("login-prompt-modal");
      const modal = new Modal(modalEle)
      modal.hide();
    }
    function cleanPhoneNumber(phoneNumber) {

      const dialCode = `+${iti.getSelectedCountryData().dialCode}`; // Get the dial code
      console.log("dialCode---", dialCode)
      console.log("phoneNumber---", phoneNumber)
      if (phoneNumber.startsWith(dialCode)) {
        return phoneNumber.slice(dialCode.length).trim(); // Remove the dial code
      }
      return phoneNumber; // Return the original phone number if no dial code is present
    }

  </script>
</head>

<body>
  <div layout:fragment="content">
    <div class="w-full bg-white" id="profile-container">
      <div sec:authorize="isAuthenticated()">


        <div class="flex items-center pb-6 px-2">
          <img
            th:with="isOAuth2=${#authentication.getPrincipal() instanceof T(org.springframework.security.oauth2.core.user.OAuth2User)}"
            th:src="${isOAuth2 ? #authentication.getPrincipal().attributes['avatar_url'] : '/user.jpg'}"
            class="h-24 w-24 rounded-full" alt="User Profile" id="principal_image" />

        </div>

        <div class="relative w-full max-w-screen max-h-full">
          <!-- Modal content -->
          <div class="relative bg-white">
            <!-- Modal header -->
            <div class="flex items-center justify-between px-1 pt-4 pb-2">
              <h3 class="text-lg font-semibold text-gray-900 ">
                Edit User Profile
              </h3>
            </div>
            <!-- Modal body -->

            <div id="profile-view" class="p-4 overflow-auto rounded-3xl border dark:border-gray-600">
              <div class="flex justify-between items-center text-black p-4 ">
                <h2 class="text-lg font-semibold">General Information</h2>
                <button type="button" privilege-action-buttons-links="Edit Profile" id="edit-profile-btn"
                  class="text-[#3E3E3E] border border-gray-200 bg-white  shadow-md focus:outline-none focus:ring-0font-medium rounded p-1">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="size-4">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125" />
                  </svg>
                </button>
              </div>

              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label class="block mb-1 text-base font-normal text-gray-400 ">
                      Name
                    </label>
                    <div id="username" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>

                  <div class="">
                    <label class="block mb-1 text-base font-normal text-gray-400 ">
                      Birth date
                    </label>
                    <div id="birthdate" class="border-b dark:border-gray-800 pb-3 h-8"></div>
                  </div>
                </div>
              </div>

              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="useremail" class="block mb-1 text-base font-normal text-gray-400 ">
                      Email
                    </label>
                    <div id="useremail" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>
                  <div class="">
                    <label for="organization" class="block mb-1 text-base font-normal text-gray-400 ">
                      Organization
                    </label>
                    <div id="organization" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>

                </div>
              </div>
              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="useraddress1" class="block mb-1 text-base font-normal text-gray-400 ">
                      Address line 1
                    </label>
                    <div id="useraddress1" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>
                  <div class="">
                    <label for="useraddress2" class="block mb-1 text-base font-normal text-gray-400 ">
                      Address line 2
                    </label>
                    <div id="useraddress2" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>

                </div>
              </div>
              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="usercountry" class="block mb-1 text-base font-normal text-gray-400 ">
                      Country
                    </label>
                    <div id="usercountry" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>

                  <div class="">
                    <label for="userstate" class="block mb-1 text-base font-normal text-gray-400 ">
                      State
                    </label>
                    <div id="userstate" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>

                </div>
              </div>
              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="usercity" class="block mb-1 text-base font-normal text-gray-400 ">
                      City
                    </label>
                    <div id="usercity" class="border-b dark:border-gray-800 pb-3 h-8">
                    </div>
                  </div>
                  <div class="">
                    <label for="userzipcode" class="block mb-1 text-base font-normal text-gray-400 ">
                      ZIP Code
                    </label>
                    <div id="userzipcode" class="border-b dark:border-gray-800 pb-3 h-8"> </div>
                  </div>

                </div>
              </div>
              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="userphone" class="block mb-1 text-base font-normal text-gray-400 ">
                      Phone Number
                    </label>
                    <div id="userphone" class="pb-3 h-8">
                    </div>
                  </div>


                </div>
              </div>
            </div>

            <div id="profile-edit" class="hidden p-4 overflow-auto rounded-3xl border dark:border-gray-600">
              <div class="flex justify-between items-center text-black p-4 ">
                <h2 class="text-lg font-semibold">General Information</h2>
                <button type="button" privilege-action-buttons-links="Edit Profile" id="cancel-edit-profile-btn"
                  class="text-[#3E3E3E] border border-gray-200 bg-white  shadow-md focus:outline-none focus:ring-0font-medium rounded p-1">
                  <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                  </svg>
                </button>
              </div>

              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="username" class="block mb-1 text-base font-normal text-gray-400 ">
                      Name
                    </label>
                    <input type="text" name="fullName" id="fullName"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="fullName-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>

                  </div>

                  <div class="">
                    <label for="birthDate" class="block mb-1 text-base font-normal text-gray-400 ">
                      Birth Date
                    </label>
                    <input type="text" name="birthDate" id="birthDate"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="birthDate-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
              </div>

              <div class="px-4 py-2 overflow-auto hidden">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="email" class="block mb-1 text-base font-normal text-gray-400 ">
                      Email Address
                    </label>
                    <input type="text" name="email" id="email"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="email-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                  <div class="">
                    <label for="organization" class="block mb-1 text-base font-normal text-gray-400 ">
                      Organization
                    </label>
                    <input type="text" name="organization" id="organization"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="organization-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>

                </div>
              </div>
              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="addressLine1" class="block mb-1 text-base font-normal text-gray-400 ">
                      Address Line1
                    </label>
                    <input type="text" name="addressLine1" id="addressLine1"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="addressLine1-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>

                  </div>
                  <div class="">
                    <label for="addressLine2" class="block mb-1 text-base font-normal text-gray-400 ">
                      Address Line2
                    </label>
                    <input type="text" name="addressLine2" id="addressLine2"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="addressLine2-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>

                </div>
              </div>

              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="country" class="block mb-1 text-base font-normal text-gray-400 ">
                      Country
                    </label>
                    <select name="country" id="country"
                      class="rounded border border-gray-300 text-gray-900 text-sm  font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "></select>
                    <span id="country-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>

                  <div class="">
                    <label for="state" class="block mb-1 text-base font-normal text-gray-400 ">
                      State
                    </label>
                    <select name="state" id="state"
                      class="rounded border border-gray-300 text-gray-900 text-sm  font-normal focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 "></select>
                    <span id="state-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>

                  </div>

                </div>
              </div>

              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="city" class="block mb-1 text-base font-normal text-gray-400 ">
                      City
                    </label>
                    <input type="text" name="city" id="city"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="city-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>

                  </div>
                  <div class="">
                    <label for="zipcode" class="block mb-1 text-base font-normal text-gray-400 ">
                      ZIP Code
                    </label>
                    <input type="text" name="zipcode" id="zipcode" maxlength="10" minlength="1"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " required />
                    <span id="zipcode-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>

                  </div>
                </div>
              </div>
              <div class="px-4 py-2 overflow-auto">
                <div class="grid gap-4 mb-2 grid-cols-2">
                  <div class="">
                    <label for="phone_code" class="block mb-1 text-base font-normal text-gray-400 ">
                      Phone Number
                    </label>
                    <input type="tel" id="phone_code" name="phone_code"
                      class="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder=" " pattern="[0-9]*" inputmode="numeric" maxlength="15" minlength="10"
                      oninput="this.value=this.value.replace(/[^0-9]/g,'');" />
                    <span id="phone_code-error" class="text-sm font-normal mt-2 mb-2 text-red-500"></span>
                  </div>
                </div>
              </div>

              <div class="px-4 py-2 overflow-auto flex justify-end">
                <button type="submit" id="update-profile"
                  class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-bold rounded text-sm inline-flex items-center px-4 py-2 text-center">
                  Update User Profile
                </button>
              </div>
            </div>

          </div>

          <div id="login-prompt-modal" data-modal-backdrop="static" tabindex="-1"
            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
              <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">

                <div class="p-4 md:p-5 text-center">
                  <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>
                  <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Please login to
                    update
                    Participant.
                  </h3>
                  <a href="/">
                    <button type="button"
                      class="text-white bg-[#1F883D]  focus:ring-4 focus:outline-none font-bold  rounded text-sm inline-flex items-center px-4 py-1.5 text-center">
                      Login
                    </button>

                  </a>
                </div>
              </div>
            </div>
          </div>
          <div id="toasterContainer" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>
        </div>
      </div>
      <div sec:authorize="!isAuthenticated()">
        <div class="items-center justify-center min-h-[300px]">
          <div
            class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-8 w-full text-center">
            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-300" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
            <h2 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-100">Please log in to view your profile
            </h2>
            <p class="mb-6 text-gray-500 dark:text-gray-300">You need to be authenticated to access your profile
              information.</p>
            <a href="/"
              class="inline-block text-white bg-[#1F883D] hover:bg-[#176c2e] focus:ring-4 focus:outline-none font-bold rounded text-sm px-6 py-2 transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>