<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/experiment}">

<head>
    <script src='https://unpkg.com/htmx.org/dist/htmx.min.js'></script>
    <title>Application Routes</title>
    <th:block th:insert="fragments/favicon :: favicon"></th:block>

    <style>
        h2 {
            padding-top: 12px
        }
    </style>
</head>

<body>
    <div layout:fragment="content">
        <h2 class="font-semibold leading-6 text-gray-900">Authorized User</h1>
            <table>
                <tr>
                    <td>Authorized GitHub User</td>
                    <td th:text="${authUser.get().ghUser}">authUser.ghUser</td>
                </tr>
                <tr>
                    <td>oAuth Principal</td>
                    <td th:text="${authUser.get().principal}">authUser.principal</td>
                </tr>
            </table>
    </div>
</body>

</html>