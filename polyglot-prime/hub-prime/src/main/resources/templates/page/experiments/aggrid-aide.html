<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
    layout:decorate="~{layout/experiment}">

<head>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-alpine.css">
    <!-- <script src="https://unpkg.com/@alenaksu/json-viewer@2.0.0/dist/json-viewer.bundle.js"></script> -->
    <script src="https://unpkg.com/ag-grid-enterprise@33.0.1/dist/ag-grid-enterprise.js"></script>
    <script type="module">
        import { ShellAide } from '@presentation/shell/shell-aide.js';

        // this script block is a separate module since it's only used for "experiment" modules
        // not in the main application
        window.agGridAideExperimentalInspect = {
            beforeRequest: async (reqPayload, dataSourceUrl) => {
                document.querySelector('#debug-request').data = { dataSourceUrl, payload: reqPayload };
                document.querySelector('#debug-response').data = "Fetching...";
                document.querySelector('#sql-from-server').innerText = "Fetching...";
                document.querySelector('#ux-reportable-error').innerText = "Fetching...";
            },
            beforeSuccess: async (serverRespPayload, respMetrics, reqPayload, dataSourceUrl) => {
                document.querySelector('#debug-response').data = { serverRespPayload, respMetrics };
                document.querySelector('#sql-from-server').innerText = serverRespPayload?.fromSQL?.dynamicSQL
                    ? serverRespPayload.fromSQL.dynamicSQL
                    : (serverRespPayload?.provenance?.fromSQL
                        ? serverRespPayload?.provenance?.fromSQL
                        : "No result.fromSQL.dynamicSQL available.");
                document.querySelector('#ux-reportable-error').innerText = serverRespPayload?.uxReportableError
                    ? serverRespPayload?.uxReportableError
                    : "No UX-reportable Server-side Error";
            },
        };

        // manually setup window.shell in experiments, automatically done automatically in the app
        new ShellAide().global(); 
    </script>
    <script type="module">
        import { AGGridAide, AGGridAideBuilder } from '@presentation/shell/aggrid-aide.js';
        import ModalAide from '@presentation/shell/modal-aide.js';

        document.addEventListener('DOMContentLoaded', function () {
            const modalAide = new ModalAide();
            const agGridInstance = new AGGridAideBuilder()
                .withColumnDefs([
                    {
                        headerName: "Request Time",
                        field: "interaction_created_at",
                        sort: "desc",
                        filter: "agNumberColumnFilter",
                        valueFormatter: AGGridAide.dateTimeValueFormatter()
                    },
                    {
                        headerName: "Interaction ID",
                        field: "interaction_id",
                        filter: "agTextColumnFilter",
                        cellRenderer: AGGridAide.modalCellRenderer((value, modalAide) => {
                            modalAide.viewFetchedJsonValue(window.shell.serverSideUrl(`/api/ux/tabular/jooq/interaction_http_request/interaction_id/${value}.json`));
                        }, modalAide)
                    },
                    { headerName: "Tenant ID", field: "tenant_id", filter: "agTextColumnFilter" },
                    { headerName: "URI", field: "uri", filter: "agTextColumnFilter" },
                    { headerName: "Nature", field: "nature", filter: "agTextColumnFilter" },
                    { headerName: "Custom", field: "custom_content" },
                    { headerName: "From State", field: "from_state", filter: "agTextColumnFilter" },
                    { headerName: "To State", field: "to_state", filter: "agTextColumnFilter" },
                    { headerName: "IP Address", field: "client_ip_address", filter: "agTextColumnFilter" },
                    { headerName: "User Agent", field: "user_agent", filter: "agTextColumnFilter" }
                ])
                .withServerSideDatasource(
                    window.shell.serverSideUrl('/api/ux/tabular/jooq/interaction_http_request.json'),
                    (data, valueCols) => {
                        return valueCols.map(col => ({
                            headerName: col.displayName,
                            field: col.field
                        }));
                    }, {
                    // customizedContent is only used when the rowData is to be joined or merged
                    customizedContent: async (gridContent) => {
                        // you can run `await fetch(...)` or get other join data from the server and "merge" it;
                        // in our case we're just creating sample rows
                        gridContent.rowData.forEach((row, index) => row.custom_content = `custom${index}`);
                        // must return after modification or supply replacement
                        return gridContent;
                    },
                    ...window.agGridAideExperimentalInspect, // remove this if not running in "experimental"
                })
                .withModalAide(modalAide)
                .withGridDivStyles({ height: "500px", width: "100%" })
                .build();

            agGridInstance.init('serverDataGrid');
        });
    </script>
</head>

<body>
    <div layout:fragment="content">
        <div id="serverDataGrid" class="ag-theme-alpine"></div>
        <p>&nbsp;</p>
        <div>Request</div>
        <json-viewer id="debug-request"></json-viewer>
        <p>&nbsp;</p>
        <div>Response</div>
        <json-viewer id="debug-response"></json-viewer>
        <p>&nbsp;</p>
        <div style="color:#999999">SQL generated by server</div>
        <pre id="sql-from-server"></pre>
        <p>&nbsp;</p>
        <div style="color:#999999">Error in SQL generated by server</div>
        <pre id="ux-reportable-error"></pre>
    </div>
</body>

</html>