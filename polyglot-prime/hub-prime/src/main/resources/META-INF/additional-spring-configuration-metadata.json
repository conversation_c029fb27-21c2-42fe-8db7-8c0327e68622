{"properties": [{"name": "org.diabetestechnology.drh.service.http.hub.prime.version", "type": "java.lang.String", "description": "The active version, injected from pom.xml in application.properties via @project.version@"}, {"name": "org.diabetestechnology.drh.service.http.hub.prime.default-sdoh-fhir-profile-url", "type": "java.lang.String", "description": "The default FHIR Profile used for principal validation of SDoH data"}, {"name": "org.diabetestechnology.drh.service.http.interactions.default-persist-strategy", "type": "java.lang.String", "description": "Default persistence strategy for all HTTP request/response JSON content."}, {"name": "org.diabetestechnology.drh.udi.prime.jdbc.driver-class-name", "type": "java.lang.String", "description": "The UDI primary JDBC Driver Class Name"}, {"name": "org.diabetestechnology.drh.udi.prime.jdbc.url", "type": "java.lang.String", "description": "The UDI primary JDBC URL used by most poolers (except <PERSON><PERSON>, see below)"}, {"name": "org.diabetestechnology.drh.udi.prime.jdbc.jdbcUrl", "type": "java.lang.String", "description": "The UDI primary JDBC URL for <PERSON><PERSON> pooler (same as usual jdbc.url)"}, {"name": "org.diabetestechnology.drh.udi.prime.jdbc.username", "type": "java.lang.String", "description": "The UDI primary JDBC URL's username"}, {"name": "org.diabetestechnology.drh.udi.prime.jdbc.password", "type": "java.lang.String", "description": "The UDI primary JDBC URL's password"}, {"name": "org.diabetestechnology.drh.orchestrate.sftp.account.orchctlts", "type": "java.util.List<org.diabetestechnology.drh.orchestrate.sftp.SftpConfigMeta.SftpAccount>", "description": "DRH SFTP orchestration egress configuration for one or more tenants"}, {"name": "org.diabetestechnology.drh.service.http.hub.prime.defaultDatalakeApiUrl", "type": "java.lang.String", "description": "The default datalake API URL"}, {"name": "org.diabetestechnology.drh.service.http.interactions.persist.db.uri-matcher.regex", "type": "java.util.List<java.util.List<Object>>", "description": "Matching rules for persisting HTTP interactions to the database"}]}