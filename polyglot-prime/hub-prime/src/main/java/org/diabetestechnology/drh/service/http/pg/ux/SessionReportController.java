package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.session.SessionDetails;
import org.diabetestechnology.drh.service.http.hub.prime.session.SessionTracker;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.SessionReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@RequestMapping("/sessions")
@Tag(name = "DRH Hub Session Management API Endpoints")
public class SessionReportController {

    @Autowired
    private SessionTracker sessionTracker;
    @Autowired
    private SessionRegistry sessionRegistry;

    private final SessionReportService sessionReportService;

    public SessionReportController(SessionReportService sessionReportService) {
        this.sessionReportService = sessionReportService;
    }

    @GetMapping("/report")
    @ResponseBody
    @Operation(summary = "Session Report", description = "Get a report of all active sessions in the system.")
    public List<SessionDetails> getSessionReport() {
        return sessionTracker.getAllSessions();
    }

    @GetMapping("/active")
    @ResponseBody
    @Operation(summary = "Active Sessions", description = "Get a list of all active user sessions.")
    public List<Object> listLoggedUsers() {
        return sessionRegistry.getAllPrincipals();
    }

    @GetMapping("/details")
    @ResponseBody
    @Operation(summary = "Session Details", description = "Get detailed information about all active sessions.")
    public Response getSessionDetails() {
        Object result = sessionReportService.getSessionDetails();
        return Response.builder()
                .data(Map.of("data",
                        result))
                .status("success")
                .message("Successfully Fetched Session Details")
                .errors(null)
                .build();
    }

}
