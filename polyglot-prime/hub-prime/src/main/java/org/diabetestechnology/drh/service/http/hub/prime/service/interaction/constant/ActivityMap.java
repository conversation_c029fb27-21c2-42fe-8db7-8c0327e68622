package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

import java.util.HashMap;
import java.util.Map;

public class ActivityMap {
    Map<String, ActivityDetails> activityMap = new HashMap<>();

    public Map<String, ActivityDetails> getActivityMap() {
        activityMap.put("/", new ActivityDetails(ActivityType.LOGIN, "Login Page loaded"));
        activityMap.put("/home", new ActivityDetails(ActivityType.HOME, "Home Page loaded"));
        activityMap.put("/cohort/info", new ActivityDetails(ActivityType.COHORT, "Cohort Page loaded"));
        activityMap.put("/cohort", new ActivityDetails(ActivityType.COHORT, "Cohort Menu Clicked"));
        activityMap.put("/activity-log/info",
                new ActivityDetails(ActivityType.ACTIVITY_LOG, "Activity Log Page loaded"));
        activityMap.put("/console/project",
                new ActivityDetails(ActivityType.CONSOLE, "Project from Console tab Selected"));
        activityMap.put("/console/health-info",
                new ActivityDetails(ActivityType.CONSOLE, "Health Information from Console tab Selected"));
        activityMap.put("/console/schema",
                new ActivityDetails(ActivityType.CONSOLE, "Schema from Console tab Selected"));
        activityMap.put("/docs", new ActivityDetails(ActivityType.DOCUMENTATION, "Documentation Clicked"));
        activityMap.put("/docs/swagger",
                new ActivityDetails(ActivityType.DOCUMENTATION, "Swagger from Documentation tab Selected"));
        activityMap.put("/docs/announcements", new ActivityDetails(ActivityType.DOCUMENTATION,
                "Announcements Swagger from Documentation tab Selected"));
        activityMap.put("/error", new ActivityDetails("Error", "Error Page loaded", LogLevel.ERROR));
        activityMap.put("/skip-login",
                new ActivityDetails("Skip Login", "Skin LogIn Clicked by Anonymous User.", LogLevel.LOGIN));
        activityMap.put("/studies", new ActivityDetails(ActivityType.STUDIES, "Clicked Studies Menu"));
        activityMap.put("/studies/dashboard", new ActivityDetails(ActivityType.STUDIES, "Clicked Study Dashboard"));
        activityMap.put("/studies/all", new ActivityDetails(ActivityType.STUDIES, "Clicked All Studies"));
        activityMap.put("/studies/population", new ActivityDetails(ActivityType.STUDIES, "Clicked Study Population"));
        activityMap.put("/studies/mystudies", new ActivityDetails(ActivityType.STUDIES, "Clicked My Studies"));
        activityMap.put("/activity-log", new ActivityDetails(ActivityType.ACTIVITY_LOG, "Clicked Activity Log Menu"));
        activityMap.put("/profile",
                new ActivityDetails(ActivityType.USER, "Selected User Profile", LogLevel.PROFILE));
        activityMap.put("/profile/info",
                new ActivityDetails(ActivityType.USER, "Loaded User Profile", LogLevel.PROFILE));
        activityMap.put("/docs/api/openapi",
                new ActivityDetails(ActivityType.DOCUMENTATION, "Swagger from Documentation tab Selected"));
        activityMap.put("/docs/api/openapi/swagger-config",
                new ActivityDetails(ActivityType.DOCUMENTATION, "Swagger from Documentation tab Selected"));
        activityMap.put("/docs/api/interactive/swagger-ui/index.html",
                new ActivityDetails(ActivityType.DOCUMENTATION, "Swagger from Documentation tab Selected"));
        activityMap.put("/docs/api/interactive/index.html",
                new ActivityDetails(ActivityType.DOCUMENTATION, "Swagger from Documentation tab Selected"));
        return activityMap;

    }

    public ActivityDetails getActivityData(String requestUrl) {
        Map<String, ActivityDetails> activityMap = getActivityMap();
        ActivityDetails activityDetails = activityMap.getOrDefault(requestUrl, null);
        return activityDetails;
    }
}
