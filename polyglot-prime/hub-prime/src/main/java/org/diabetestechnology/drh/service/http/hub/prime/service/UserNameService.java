package org.diabetestechnology.drh.service.http.hub.prime.service;

import org.diabetestechnology.drh.service.http.OauthUsersService;
import org.diabetestechnology.drh.service.http.pg.service.AuthUserDetailsService;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.stereotype.Service;

@Service
public class UserNameService {

    private final AuthUserDetailsService authUserDetailsService;
    private final OauthUsersService oauthUsersService;
    private final PartyService partyService;
    private final Logger LOG = LoggerFactory.getLogger(UserNameService.class);

    public UserNameService(OauthUsersService oauthUsersService, PartyService partyService,
            AuthUserDetailsService authUserDetailsService) {
        this.oauthUsersService = oauthUsersService;
        this.partyService = partyService;
        this.authUserDetailsService = authUserDetailsService;
    }

    public String getUserName() {
        Object principal = getUserPrincipal();

        String userName;
        if (principal instanceof DefaultOAuth2User) {
            // Cast only if principal is of type DefaultOAuth2User
            final var userPrincipal = (DefaultOAuth2User) principal;
            userName = (String) userPrincipal.getAttribute("name") != null
                    ? (String) userPrincipal.getAttribute("name")
                    : "Anonymous";
        } else if (principal instanceof String) {
            // Handle the case where the principal is a String
            userName = (String) principal; // This may need further handling depending on your use case
        } else if (principal instanceof UserDetails) {
            userName = authUserDetailsService.getEmailLoginUserFullName();
        } else {
            userName = "Anonymous"; // Default fallback
        }
        if ("anonymousUser".equals(userName))
            userName = "Anonymous";
        return userName;
    }

    public String getUserId() {
        String userId;
        Object principal = getUserPrincipal();
        if (principal instanceof DefaultOAuth2User) {
            // Cast only if principal is of type DefaultOAuth2User
            final var userPrincipal = (DefaultOAuth2User) principal;
            userId = (String) userPrincipal.getAttribute("login") != null
                    ? (String) userPrincipal.getAttribute("login")
                    : "Anonymous";
        } else if (principal instanceof UserDetails) {
            // Cast only if principal is of type DefaultOAuth2User
            final var userPrincipal = (UserDetails) principal;
            userId = (String) userPrincipal.getUsername() != null
                    ? (String) userPrincipal.getUsername()
                    : "Anonymous";
        } else {
            userId = "Anonymous"; // Default fallback
        }
        if ("anonymousUser".equals(userId))
            userId = "Anonymous";
        return userId;
    }

    public String getUserEmail() {
        Object principal = getUserPrincipal();

        String userEmail;
        if (principal instanceof DefaultOAuth2User) {
            // Cast only if principal is of type DefaultOAuth2User
            final var userPrincipal = (DefaultOAuth2User) principal;
            userEmail = (String) userPrincipal.getAttribute("email") != null
                    ? (String) userPrincipal.getAttribute("email")
                    : "";
        } else {
            userEmail = ""; // Default fallback
        }

        return userEmail;
    }

    public String getUserInstitution() {
        Object principal = getUserPrincipal();

        String userInstitution;
        if (principal instanceof DefaultOAuth2User) {
            // Cast only if principal is of type DefaultOAuth2User
            final var userPrincipal = (DefaultOAuth2User) principal;
            userInstitution = (String) userPrincipal.getAttribute("institution") != null
                    ? (String) userPrincipal.getAttribute("institution")
                    : "";
        } else {
            userInstitution = ""; // Default fallback
        }

        return userInstitution;
    }

    public String getUserProvider() {
        Object principal = getUserPrincipal();

        String userProvider;
        if (principal instanceof DefaultOAuth2User) {
            // Cast only if principal is of type DefaultOAuth2User
            final var userPrincipal = (DefaultOAuth2User) principal;
            userProvider = (String) userPrincipal.getAttribute("provider") != null
                    ? (String) userPrincipal.getAttribute("provider")
                    : "";
        } else if (principal instanceof UserDetails) {
            userProvider = "Email";
        } else {
            userProvider = ""; // Default fallback
        }

        return userProvider;
    }

    public Object getUserPrincipal() {
        final var authentication = SecurityContextHolder.getContext().getAuthentication();
        Object principal = authentication.getPrincipal();
        return principal;
    }

    public boolean isAdmin() {
        LOG.info("Checking if user is admin");
        final var userId = getUserId();
        final var provider = getUserProvider();
        boolean isAdmin = oauthUsersService.hasAdminMenuDB(userId, provider);
        LOG.info("User is admin: {}", isAdmin);
        return isAdmin;
    }

    public String getCurrentuserPartyId() {
        final var userId = getUserId();
        final var partyId = partyService.getPartyIdByUserId(userId);
        return partyId;
    }

    public String getCurrentUserOrganizationPartyId() {
        final var userId = getUserId();
        final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
        return organizationPartyId;
    }

    public boolean isAuthenticatedUser() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && !"anonymousUser".equalsIgnoreCase(authentication.getPrincipal().toString());
    }

    public boolean isSuperAdmin() {
        return authUserDetailsService.isSuperAdmin();
    }

}
