package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantRowFileRequest;
import org.diabetestechnology.drh.service.http.pg.service.ParticipantRowFileService;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;

@Controller
@Tag(name = "DRH Research Study Paricipant CGM Row File APIs")
public class ParticipantRowFileController {

    private final ParticipantRowFileService participantRowFileService;
    private final ResearchStudyService researchStudyService;
    private final UserNameService userNameService;

    public ParticipantRowFileController(
            ParticipantRowFileService PparticipantRowFileService, ResearchStudyService researchStudyService,
            UserNameService userNameService) {
        this.participantRowFileService = PparticipantRowFileService;
        this.researchStudyService = researchStudyService;
        this.userNameService = userNameService;
    }

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantRowFileController.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @PostMapping(value = "/study-participant/cgm/file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Save a CGM Row File")
    @ResponseBody
    public Response saveCgmRowFile(@RequestPart("file") @Nonnull MultipartFile file,
            @Nonnull @RequestParam("studyId") String studyId,
            @Nonnull @RequestParam("organizationPartyId") String organizationPartyId,
            @Nonnull @RequestParam("participantId") String participantId) throws Exception {
        LOG.info("save cgm row file request for study {}: ", studyId);
        Map<String, Object> response = participantRowFileService.saveCgmRowFile(file, studyId, organizationPartyId,
                participantId);
        return Response.builder()
                .data(response)
                .status("success")
                .message("File saved successfully")
                .errors(null)
                .build();
    }

    @SuppressWarnings("unchecked")
    @PostMapping("/study-participant/cgm/data")
    @Operation(summary = "Save a CGM Row data to the database")
    @ResponseBody
    public Response saveCgmRowData(@RequestBody ParticipantRowFileRequest request) throws Exception {
        LOG.info("save cgm row data from request: {}", request);
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(request.studyId()))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        request.studyId());
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            Map<String, Object> response = participantRowFileService.saveCgmRowData(request);
            Object resultObj = response.get("result");

            if ("failure".equals(response.get("status"))) {
                Map<String, Object> errorDetails = (Map<String, Object>) response.get("result");
                String contextMessage = (String) errorDetails.getOrDefault("context", "Error context not available.");
                String errorMessage = (String) response.getOrDefault("message", "Failed to save CGM data");

                LOG.error("CGM save failed  Message: {}", errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message((String) response.getOrDefault("message", "Failed to save CGM data"))
                        .errors(contextMessage)
                        .build();
            }
            Map<String, Object> result = (resultObj instanceof Map)
                    ? (Map<String, Object>) resultObj
                    : OBJECT_MAPPER.convertValue(resultObj, new TypeReference<>() {
                    });

            String resultJsonString = OBJECT_MAPPER.writeValueAsString(result);
            Map<String, Object> responseData = Map.of("result", resultJsonString);
            return Response.builder()
                    .data(responseData)
                    .message("Data saved successfully")
                    .status("success")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Unexpected error saving CGM data", e);
            Map<String, String> errorDetails = new HashMap<>();
            errorDetails.put("message", e.getMessage());
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Unexpected error occurred while saving CGM data")
                    .errors(errorDetails)
                    .build();
        }
    }

}
