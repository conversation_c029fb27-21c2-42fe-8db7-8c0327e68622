package org.diabetestechnology.drh.service.http.pg.service;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Locale;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class PubMedService {
    private static final String NCBI_API_PUBMED_URL = "https://www.ncbi.nlm.nih.gov/pmc/utils/idconv/v1.0/?ids=";
    private static final String NCBI_API_METADATA_URL = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=pubmed&id=";
    private static final String CROSSREF_API_METADATA_URL = "https://api.crossref.org/works/";

    private static final Logger LOG = LoggerFactory.getLogger(PubMedService.class);

    public Object getPubmedId(String doi) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            String url = NCBI_API_PUBMED_URL + doi;
            LOG.info("getPubmedId url: {}", url);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                if (responseBody == null || responseBody.isEmpty()) {
                    return null;
                }

                // Parse XML response
                Map<String, String> data = extractPubMedDetails(responseBody);
                if (!data.isEmpty()) {
                    LOG.info("PubMed data: {}", data);
                    return data;
                }
            }
            return null;
        } catch (Exception e) {
            LOG.error("Error in reading pubmed Id: {}", e.getMessage(), e);
            return null;
        }

    }

    private Map<String, String> extractPubMedDetails(String xmlResponse) {
        Map<String, String> data = new HashMap<>();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(xmlResponse.getBytes()));

            NodeList recordNodes = doc.getElementsByTagName("record");
            if (recordNodes.getLength() > 0) {
                Node record = recordNodes.item(0);
                if (record.getAttributes().getNamedItem("pmid") != null) {
                    data.put("pubmedId", record.getAttributes().getNamedItem("pmid").getNodeValue());
                }
                if (record.getAttributes().getNamedItem("pmcid") != null) {
                    data.put("pmcId", record.getAttributes().getNamedItem("pmcid").getNodeValue());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public Object getMetadata(String pubmedId) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            String url = NCBI_API_METADATA_URL + pubmedId + "&retmode=json";
            LOG.info("getMetadata url: {}", url);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                if (responseBody == null || responseBody.isEmpty()) {
                    return null;
                }

                // Parse JSON response
                Map<String, Object> data = extractMetadata(responseBody, pubmedId);
                if (!data.isEmpty()) {
                    LOG.info("Metadata: {}", data);
                    return data;
                }
            }
            return null;
        } catch (Exception e) {
            LOG.error("Error in reading metadata: {}", e.getMessage(), e);
            return null;
        }

    }

    private Map<String, Object> extractMetadata(String jsonResponse, String pubmedId) {
        Map<String, Object> data = new HashMap<>();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(jsonResponse);
            JsonNode articleNode = rootNode.path("result").path(pubmedId);

            if (!articleNode.isMissingNode()) {
                data.put("title", articleNode.path("title").asText());
                data.put("journal", articleNode.path("fulljournalname").asText());
                data.put("pubDate", convertDateFormat(articleNode.path("pubdate").asText()));
                data.put("authors", JsonUtils.jsonStringToMapOrList(articleNode.path("authors").toString()));
                data.put("doi", getArticleId(articleNode, "doi"));
                data.put("pmcid", getArticleId(articleNode, "pmc"));
                data.put("pubtype", JsonUtils.jsonStringToMapOrList(articleNode.path("pubtype").toString()));
                data.put("volume", articleNode.path("volume").asText());
                data.put("issue", articleNode.path("issue").asText());
                data.put("pages", articleNode.path("pages").asText());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    private String getArticleId(JsonNode articleNode, String idType) {
        for (JsonNode idNode : articleNode.path("articleids")) {
            if (idNode.path("idtype").asText().equals(idType)) {
                return idNode.path("value").asText();
            }
        }
        return "";
    }

    public Object getCrossrefMetadata(String doi) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            String url = CROSSREF_API_METADATA_URL + doi;
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                if (responseBody == null || responseBody.isEmpty()) {
                    return null;
                }

                // Parse JSON response
                Map<String, Object> data = extractMetadata(responseBody);
                if (!data.isEmpty()) {
                    return data;
                }
            }

            return null;

        } catch (Exception e) {
            LOG.error("Error in reading metadata: {}", e.getMessage(), e);
            return null;
        }
    }

    private Map<String, Object> extractMetadata(String jsonResponse) {
        Map<String, Object> data = new HashMap<>();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(jsonResponse);
            JsonNode messageNode = rootNode.path("message");

            if (!messageNode.isMissingNode()) {
                data.put("title", messageNode.path("title").get(0).asText());
                data.put("journal", messageNode.path("container-title").get(0).asText());
                data.put("publicationDate", extractPublicationDate(messageNode));
                data.put("authors", extractAuthors(messageNode));
                data.put("doi", messageNode.path("DOI").asText());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    private String extractPublicationDate(JsonNode messageNode) {
        JsonNode dateNode = messageNode.path("published-print");
        if (dateNode.isMissingNode()) {
            dateNode = messageNode.path("published-online");
        }
        return dateNode.has("date-parts") ? dateNode.path("date-parts").get(0).toString() : "N/A";
    }

    private String extractAuthors(JsonNode messageNode) {
        JsonNode authorsNode = messageNode.path("author");
        StringBuilder authors = new StringBuilder();
        if (authorsNode.isArray()) {
            for (JsonNode author : authorsNode) {
                authors.append(author.path("given").asText())
                        .append(" ")
                        .append(author.path("family").asText())
                        .append(", ");
            }
        }
        return authors.length() > 0 ? authors.substring(0, authors.length() - 2) : "N/A";
    }

    public String extractPubmedId(Object pubmedIdObj) {

        if (pubmedIdObj == null) {
            LOG.warn("PubMed ID object is null");
            return "";
        }
        String responseStr = pubmedIdObj.toString().trim();
        if (responseStr.isBlank()) {
            LOG.warn("PubMed ID response is empty");
            return "";
        }
        LOG.info("Extracting PubMed ID from response: {}", responseStr);
        Matcher matcher = Pattern.compile("pubmedId=(\\d+)").matcher(responseStr);

        if (matcher.find()) {
            String pubmedId = matcher.group(1);
            LOG.info("Extracted PubMed ID: {}", pubmedId);
            return pubmedId;
        }
        LOG.warn("No PubMed ID found in response");
        return "";
    }

    private String convertDateFormat(String inputDate) {
        try {
            String adjustedInput = inputDate + " 01";
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy MMM dd", Locale.ENGLISH);
            // Parse the input date (default to the first day of the month)
            LocalDate date = LocalDate.parse(adjustedInput, inputFormatter);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
            return date.format(outputFormatter);
        } catch (DateTimeParseException e) {
            LOG.error("Failed to parse date: '{}'. Expected format: 'yyyy MMM' (e.g., '1979 Dec')", inputDate, e);
            return "Invalid Date";
        }
    }

}
