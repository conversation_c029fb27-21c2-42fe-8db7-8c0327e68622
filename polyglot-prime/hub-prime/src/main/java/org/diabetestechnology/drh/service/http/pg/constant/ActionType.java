package org.diabetestechnology.drh.service.http.pg.constant;

public class ActionType {

    // DATABASE File
    public static final String DB_FILE_UPLOAD = "DB FILE UPLOAD";
    public static final String S3_FILE_UPLOAD = "S3 BUCKET UPLOAD";
    public static final String CONTENT_VERIFICATION = "CONTENTS VERIFICATION";
    public static final String COPY_DUCK_DB = "DUCKDB COPY";
    public static final String SAVE_DB_CONTENT = "SAVE DB";
    public static final String STUDY_METADATA_MIGRATION = "STUDY METADATA MIGRATION";
    public static final String PARTICIPANT_MIGRATION = "PARTICIPANT MIGRATION";
    public static final String CGM_MIGRATION = "CGM MIGRATION";
    public static final String MEAL_AND_FITNESS_MIGRATION = "MEAL AND FITNESS MIGRATION";
    public static final String SUBMIT_FOR_VERIFICATION = "SUBMIT FOR CONTENT VERIFICATION";
    public static final String DATABASE_MIGRATION = "DATABASE MIGRATION";
    public static final String CGM_PARTITION_MIGRATION = "CGM PARTITION MIGRATION";
    public static final String MEAL_MIGRATION = "MEAL MIGRATION";
    public static final String MEAL_PARTITION_MIGRATION = "MEAL PARTITION MIGRATION";
    public static final String FITNESS_MIGRATION = "FITNESS MIGRATION";
    public static final String FITNESS_PARTITION_MIGRATION = "FITNESS PARTITION MIGRATION";

    // CGM File
    public static final String UPLOAD_CGM_FILE = "UPLOAD CGM FILE";
    public static final String VALIDATE_CGM_FILE = "VALIDATE CGM FILE";
    public static final String COPY_FILE_FOR_PROCESSING = "COPY FILE FOR PROCESSING";
    public static final String PREPARE_METADATA = "PREPARE AND VALIDATE METADATA";
    public static final String VALIDATE_CGM_DATE_AND_VALUE = "VALIDATE CGM DATE AND VALUE";
    public static final String SAVE_CGM_FILE_DATA = "SAVE CGM FILE DATA";

    // Participant File
    public static final String UPLOAD_PARTICIPANT_FILE = "UPLOAD PARTICIPANT FILE";
    public static final String VALIDATE_PARTICIPANT_FILE = "VALIDATE PARTICIPANT FILE";
    public static final String VALIDATE_PARTICIPANT_FILE_TYPE = "VALIDATE PARTICIPANT FILE TYPE";
    public static final String PARTICIPANT_CONTENT_VALIDATION = "PARTICIPANT CONTENT VALIDATION";

    // Study
    public static final String CREATE_STUDY = "CREATE STUDY";
    public static final String UPDATE_STUDY = "UPDATE STUDY";
    public static final String DELETE_STUDY = "DELETE STUDY";
    public static final String VIEW_STUDY = "VIEW STUDY";

    // Collaboration team
    public static final String SAVE_COLLABORATION_TEAM = "SAVE COLLABORATION TEAM";
    public static final String UPDATE_COLLABORATION_TEAM = "UPDATE COLLABORATION TEAM";

    // Participant
    public static final String CREATE_PARTICIPANT = "CREATE PARTICIPANT";
    public static final String UPDATE_PARTICIPANT = "UPDATE PARTICIPANT";
    public static final String DELETE_PARTICIPANT = "DELETE PARTICIPANT";
    public static final String VIEW_PARTICIPANT = "VIEW PARTICIPANT";

    // Meals
    public static final String UPLOAD_MEALS_FILE = "UPLOAD MEALS FILE";
    public static final String VALIDATE_MEALS_FILE_CONTENT = "VALIDATE MEALS FILE CONTENT";
    public static final String VALIDATE_MEALS_FILE_TYPE = "VALIDATE MEALS FILE TYPE";
    public static final String MEALS_CONTENT_VALIDATION = "PARTICIPANT MEALS VALIDATION";

    // Fitness
    public static final String UPLOAD_FITNESS_FILE = "UPLOAD FITNESS FILE";
    public static final String VALIDATE_FITNESS_FILE_CONTENT = "VALIDATE FITNESS FILE CONTENT";
    public static final String VALIDATE_FITNESS_FILE_TYPE = "VALIDATE FITNESS FILE TYPE";
    public static final String FITNESS_CONTENT_VALIDATION = "PARTICIPANT FITNESS VALIDATION";

}
