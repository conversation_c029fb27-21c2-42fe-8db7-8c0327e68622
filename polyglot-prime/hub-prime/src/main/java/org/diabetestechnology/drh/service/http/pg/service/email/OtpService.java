package org.diabetestechnology.drh.service.http.pg.service.email;

import java.security.SecureRandom;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.diabetestechnology.drh.service.http.pg.constant.email.OtpValidationResponse;
import org.diabetestechnology.drh.service.http.pg.constant.email.OtpVerificationState;
import org.springframework.stereotype.Service;

@Service
public class OtpService {
    private final ConcurrentMap<String, OtpVerificationState> otpStore = new ConcurrentHashMap<>();

    public String generateAndSendOtp(String email) {
        String otp = String.format("%06d", new SecureRandom().nextInt(999999));
        OtpVerificationState state = new OtpVerificationState(otp, 10, 3); // 10 min expiry, 3 retries
        otpStore.put(email, state);
        // Send email using mailSender (omitted here)
        System.out.println("OTP for " + email + " is: " + otp);
        return otp;
    }

    public String verifyOtp(String email, String enteredOtp) {
        OtpVerificationState state = otpStore.get(email);
        if (state == null)
            return OtpValidationResponse.OTP_NOT_FOUND;

        if (state.isExpired()) {
            otpStore.remove(email);
            return OtpValidationResponse.OTP_EXPIRED;
        }

        if (!state.canRetry()) {
            otpStore.remove(email);
            return OtpValidationResponse.OTP_MAX_ATTEMPTS_EXCEEDED;
        }

        if (!state.getOtp().equals(enteredOtp)) {
            state.incrementRetry();
            return OtpValidationResponse.OTP_VERIFICATION_FAILURE;
        }

        otpStore.remove(email);
        return OtpValidationResponse.OTP_VERIFICATION_SUCCESS;
    }
}