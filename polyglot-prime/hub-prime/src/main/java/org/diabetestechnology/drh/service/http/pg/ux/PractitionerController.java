package org.diabetestechnology.drh.service.http.pg.ux;

import org.diabetestechnology.drh.service.http.pg.request.PractitionerRequest;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.Response;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Tag(name = "DRH Practitioner APIs")
public class PractitionerController {

    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyController.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final PractitionerService practitionerService;
    private final Presentation presentation;

    public PractitionerController(PractitionerService practitionerService,
            Presentation presentation) {
        this.practitionerService = practitionerService;
        this.presentation = presentation;
    }

    @PostMapping("/practitioners")
    @ResponseBody
    @Operation(summary = "Create Practitioner")
    public Response createPractitionerProfile(
            @RequestBody PractitionerRequest pratitionerRequest) {
        try {
            for (String email : pratitionerRequest.email()) {
                if (!practitionerService.isUniqueEmail(email)) {
                    LOG.warn("Email already exists: {}", email);
                    return Response.builder()
                            .data(Map.of())
                            .status("error")
                            .message("Email already exists")
                            .errors("Email already exists")
                            .build();
                }
            }
            if (!("{}".equals(practitionerService.getUserDetails()))) {
                LOG.warn("User details found.: {}", practitionerService.getUserDetails());
                return Response.builder()
                        .data(Map.of())
                        .status("success")
                        .message("Practitioner with the same provider details already exists.")
                        .errors(null)
                        .build();
            }

            JSONB result = practitionerService.createPractitionerProfile(pratitionerRequest);

            if (result != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(result.data());
                // Extract values
                String message = jsonNode.get("message").asText();
                return Response.builder()
                        .data(Map.of("practitionerProfile", result.toString()))
                        .status("success")
                        .message(message)
                        .errors(null)
                        .build();
            } else
                return Response.builder()
                        .data(Map.of())
                        .status("success")
                        .message("Practitioner profile creation failed")
                        .errors(null)
                        .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error inserting practitioner profile")
                    .errors("Error details: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/practitioners/existence")
    @ResponseBody
    @Operation(summary = "Fetch User Profile if logged in user profile exists")
    public Response checkUserExistence() {
        Object result = practitionerService.getLoggedInUserDetails();
        try {
            return Response.builder()
                    .data(Map.of("userDetails", result))
                    .status("success")
                    .message("Successfully Fetched user details")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to Fetch  user details")
                    .errors("Error in reading user details: " +
                            e.getMessage())
                    .build();

        }
    }

    @GetMapping("/practitioners/unique-email")
    @Operation(summary = "Check if email is unique")
    @ResponseBody
    public Response checkEmailUniqueness(@RequestParam("email") String email) {
        LOG.info("Checking email uniqueness for: {}", email);
        try {
            boolean isUnique = practitionerService.isUniqueEmail(email);

            LOG.debug("Exiting checkEmailUniqueness endpoint with {} response for email: {}",
                    isUnique ? "success" : "error", email);

            return Response.builder()
                    .status("success")
                    .message(isUnique ? "Email is unique" : "Email already exists")
                    .data(Map.of("isUniqueEmail", isUnique)) // Wrap boolean in a Map
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Exception occurred while checking email uniqueness", e);
            LOG.debug("Exiting checkEmailUniqueness endpoint with exception for email: {}", email);

            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to check email uniqueness")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
        }
    }

    @PutMapping("/profile")
    @ResponseBody
    @Operation(summary = "Update profile Details")
    public Response updateProfileDetails(
            @RequestBody JsonNode jsonInput) {
        try {
            Object resultObj = practitionerService.updateProfileDetails(JSONB.valueOf(jsonInput.toString()));
            if (resultObj == null) {
                LOG.error("updateProfileDetails: Received null result from DB function");
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("No response from update function")
                        .errors("Null result from database")
                        .build();
            }
            JsonNode responseJson = OBJECT_MAPPER.readTree(resultObj.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                LOG.error("Error updating archive status : " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            LOG.info("updateProfileDetails: Success response = {}", responseJson);

            return Response.builder()
                    .data(Map.of("updateStatus", responseJson))
                    .status("success")
                    .message("Profile updated successfully")
                    .errors(null)
                    .build();

        } catch (Exception e) {
            LOG.error("updateProfileDetails: Exception occurred", e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Unexpected error while updating profile")
                    .errors(e.getMessage())
                    .build();
        }
    }

    @GetMapping("/profile-details")
    @ResponseBody
    @Operation(summary = "Get profile details")
    public Response getProfileDetails() {
        try {
            LOG.info("getProfileDetails: Fetching profile details  for logged-in user");
            Object resultObj = practitionerService.getProfileDetails();
            if (resultObj == null) {
                LOG.warn("getProfileDetails: No data found  for the logged-in user");
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Profile not found")
                        .errors("No profile data returned from database")
                        .build();
            }

            JsonNode responseJson = OBJECT_MAPPER.readTree(resultObj.toString());

            LOG.info("getProfileDetails: Success response = {}", responseJson);

            return Response.builder()
                    .data(Map.of("profile", responseJson))
                    .status("success")
                    .message("Profile fetched successfully")
                    .errors(null)
                    .build();

        } catch (Exception e) {
            LOG.error("getProfileDetails: Exception occurred", e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Unexpected error while fetching profile")
                    .errors(e.getMessage())
                    .build();
        }
    }

    @GetMapping("/user-name")
    @ResponseBody
    @Operation(summary = "Get user name")
    public Response getUserName(HttpServletRequest request) {
        LOG.info("getUserName: Fetching user name for logged-in user");
        final var name = presentation.getUsername();
        return Response.builder()
                .data(Map.of("userName", name))
                .status("success")
                .message("User name fetched successfully")
                .errors(null)
                .build();
    }

}