package org.diabetestechnology.drh.service.http.pg.service;

import java.util.Map;
import org.jooq.Condition;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;

import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class SessionReportService {

    private static final Logger LOG = LoggerFactory.getLogger(SessionReportService.class);
    private final DSLContext dsl;
    private final Presentation presentation;
    private final UserNameService userNameService;
    private final PartyService partyService;

    public SessionReportService(@Qualifier("secondaryDsl") DSLContext dsl, Presentation presentation,
            UserNameService userNameService, PartyService partyService) {
        this.dsl = dsl;
        this.presentation = presentation;
        this.userNameService = userNameService;
        this.partyService = partyService;

    }

    public Object getSessionDetails() {

        try {

            boolean isSuperAdmin = presentation.isSuperAdmin();
            boolean isAdmin = userNameService.isAdmin();
            String userId = null;
            String userPartyId = null;

            if (!isSuperAdmin && !isAdmin) {
                userId = userNameService.getUserId();
                userPartyId = partyService.getPartyIdByUserId(userId);

                if (userPartyId == null || userPartyId.isBlank()) {
                    LOG.warn("User party ID is null or blank, returning empty result.");
                    return Map.of();
                }
            }
            LOG.info("Session details requested by userId: {}, isAdmin: {}, isSuperAdmin: {}, userPartyId: {}",
                    userId, isAdmin, isSuperAdmin, userPartyId);

            Field<JSONB> jsonAggField = DSL.field(
                    "jsonb_agg(" +
                            "jsonb_build_object(" +
                            "'start_time', to_char(start_time, 'HH24:MI:SS'), " +
                            "'end_time', to_char(end_time, 'HH24:MI:SS'), " +
                            "'user_party_id', user_party_id, " +
                            "'username', username, " +
                            "'organization_name', organization_name, " +
                            "'org_party_id', org_party_id, " +
                            "'duration_seconds', duration_seconds" +
                            ")" +
                            ")",
                    JSONB.class);

            var selectStep = dsl.select(jsonAggField).from("drh_stateless_activity_audit.session_duration_view");

            // Build condition dynamically
            Condition condition = DSL.trueCondition(); // base condition

            if (isAdmin && !isSuperAdmin) {
                String orgPartyId = userNameService.getCurrentUserOrganizationPartyId();
                LOG.info("Admin access: restricting session query to org_party_id = {}", orgPartyId);
                condition = condition.and(DSL.field("org_party_id").eq(orgPartyId));
            } else if (!isSuperAdmin) {
                LOG.info("Normal user access: restricting session query to user_party_id = {}, username = {}",
                        userPartyId, userId);
                condition = condition.and(
                        DSL.field("user_party_id").eq(userPartyId)
                                .and(DSL.field("username").eq(userId)) // Ensure this mapping is correct
                );
            }

            var query = selectStep.where(condition);

            LOG.info("Executing session details query: {}", query);
            JSONB result = query.fetchOneInto(JSONB.class);
            return result != null ? result : Map.of();
        } catch (Exception e) {
            LOG.error("Error fetching session details", e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch session details")
                    .errors(e.getMessage())
                    .build();
        }
    }

}
