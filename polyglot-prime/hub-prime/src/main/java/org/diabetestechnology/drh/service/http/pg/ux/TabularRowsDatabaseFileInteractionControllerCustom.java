package org.diabetestechnology.drh.service.http.pg.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.pg.filter.CustomTabularFilter;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.diabetestechnology.drh.udi.UdiSecondaryDbConfig;
import org.diabetestechnology.drh.pg.udi.auto.jooq.ingress.Tables;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Nonnull;
import lib.aide.tabular.JooqRowsSupplier;

import lib.aide.tabular.TabularRowsRequest;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.jooq.*;

@Controller
@Tag(name = "DRH Hub Custom Tabular Database File Interaction API Endpoints for AG Grid")
@org.springframework.context.annotation.Configuration
public class TabularRowsDatabaseFileInteractionControllerCustom {
    static private final Logger LOG = LoggerFactory.getLogger(
            TabularRowsDatabaseFileInteractionControllerCustom.class);

    private final UdiSecondaryDbConfig udiPrimeDbConfig;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final CustomTabularFilter customTabularFilter;

    @Bean
    public com.fasterxml.jackson.databind.Module jsonbModule() {
        com.fasterxml.jackson.databind.module.SimpleModule module = new SimpleModule();
        module.addSerializer(org.jooq.JSONB.class, new JsonSerializer<org.jooq.JSONB>() {
            @Override
            public void serialize(org.jooq.JSONB value, JsonGenerator gen, SerializerProvider serializers)
                    throws IOException {
                gen.writeRawValue(value.data()); // or gen.writeString(value.data());
            }
        });
        return module;
    }

    public TabularRowsDatabaseFileInteractionControllerCustom(final UdiSecondaryDbConfig udiPrimeDbConfig,
            UserNameService userNameService,
            PartyService partyService, CustomTabularFilter customTabularFilter) {
        this.udiPrimeDbConfig = udiPrimeDbConfig;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.customTabularFilter = customTabularFilter;
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "Study Database Interaction")
    @PostMapping(value = "/api/ux/tabular/jooq/database/file/interaction/parent/{schemaName}/{masterTableNameOrViewName}.json")
    @ResponseBody
    public Object parentDatabaseFileInteraction(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp)
            throws SQLException {

        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);

            var bindValues = new ArrayList<Object>();

            final var userId = userNameService.getUserId();
            final var organizationId = partyService.getOrganizationPartyIdByUser(userId);
            Condition finalCondition = payload.filterModel() != null
                    ? customTabularFilter.createCondition(payload.filterModel())
                    : null;
            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .selectDistinct(DSL.field("study_id"),
                            DSL.field("study_display_id"),
                            DSL.field("study_title"),
                            DSL.field("file_category"),
                            DSL.field("organization_name"),
                            DSL.field(
                                    "organization_party_id")) // Selecting the specified fields
                    .from(typableTable.table())
                    .where("1=1");
            if (finalCondition != null) {
                query = query.and(finalCondition);
            }
            query = query.and(((DSL.field("interaction_hierarchy")
                    .eq(DSL.value("null")))
                    .or(DSL.field("interaction_hierarchy").isNull()))
                    .and(DSL.field("study_id").isNotNull())
                    .and(DSL.field("study_display_id").isNotNull())
                    .and(DSL.field("organization_party_id").eq(DSL.value(organizationId)))
                    .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE))));

            bindValues.add("null");
            bindValues.add(organizationId);
            bindValues.add(FileType.DATABASE);
            if (!userNameService.isAdmin()) {
                final var currentUserId = userNameService.getCurrentuserPartyId();
                query = query.and(DSL.field("created_by").eq(DSL.value(currentUserId)));
                bindValues.add(currentUserId);

            }
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@org.jetbrains.annotations.NotNull SelectConditionStep<Record6<Object, Object, Object, Object, Object, Object>>) query
                        .orderBy(
                                customTabularFilter.createSortCondition(payload.sortModel(),
                                        typableTable));
            }

            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "List of Database file uploaded against a study")
    @GetMapping("/api/ux/tabular/jooq/database/file/interaction/sub/{schemaName}/{masterTableNameOrViewName}/study_id/{studyId}.json")
    @ResponseBody
    public Object subDatabseFileInteraction(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String studyId) {

        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where((typableTable.column("study_id").eq(DSL.value(
                        studyId)))
                        .and((DSL.field("interaction_hierarchy")
                                .eq(DSL.val("null"))))
                        .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE))));

        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @Operation(summary = "Detailed interaction log against a databse file")
    @GetMapping("/api/ux/tabular/jooq/database/file/interaction/child/{schemaName}/{masterTableNameOrViewName}/file_interaction_id/{fileInteractionId}.json")
    @ResponseBody
    public Object childDatabaseFileInteraction(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String fileInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where((typableTable.column("file_interaction_id").eq(DSL.value(
                        fileInteractionId)))
                        .or((DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                                .eq(DSL.val("\"" + fileInteractionId + "\"")))));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @Operation(summary = "SQL rows for a specific file_interaction_id value")
    @GetMapping("/api/ux/tabular/jooq/database/file/interaction/child/modal/{schemaName}/{masterTableNameOrViewName}/file_interaction_id/{fileInteractionId}.json")
    @ResponseBody
    public Object childDatabaseFileInteractionModal(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable String fileInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where(typableTable.column("file_interaction_id").eq(DSL.value(
                        fileInteractionId)));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

}
