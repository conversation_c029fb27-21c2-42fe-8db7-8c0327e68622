package org.diabetestechnology.drh.service.http.pg.service;

import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MasterService {

    private final DSLContext dsl;
    private static final Logger LOG = LoggerFactory.getLogger(MasterService.class);

    public MasterService(@Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    @Transactional
    public Object getStudyVisibility() {
        LOG.info("Fetching study visibility");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'visibility_id', visibility_id, " +
                                "'visibility_name', visibility_name, " +
                                "'visibility_description', visibility_description, " +
                                "'rec_status_id', rec_status_id " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.study_visibility_view")
                .where(DSL.condition(
                        "(deleted_at IS NULL )"));
        LOG.info("Query for Fetching study visibility: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}"; // Return empty JSON if the query result is null
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getRaceType() {
        LOG.info("Fetching race type");
        JSONB jsonbResult = readRaceType();
        if (jsonbResult == null) {
            return "{}"; // Return empty JSON if the query result is null
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getEthnicityType() {
        LOG.info("Fetching ethnicity type");
        JSONB jsonbResult = readEthnicityType();
        if (jsonbResult == null) {
            return "{}"; // Return empty JSON if the query result is null
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getGenderType() {
        LOG.info("Fetching gender type");
        JSONB jsonbResult = readGenderType();
        if (jsonbResult == null) {
            return "{}"; // Return empty JSON if the query result is null
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getProfileStatusType() {
        LOG.info("Fetching profile status type");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'profile_status_type_id', profile_status_type_id, " +
                                "'code', code, " +
                                "'description', description ) " +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.profile_status_type_view");

        LOG.info("Query for Fetching ProfileStatusType: {}", query);

        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            return "{}"; // Return empty JSON if the query result is null
        }
        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());

    }

    @Transactional
    public Object getCitationStatus() {
        LOG.info("Fetching citation status");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'code', code, " +
                                "'display_name', display_name, " +
                                "'definition', definition, " +
                                "'system_url', system_url" +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.citation_status_master_view");
        LOG.info("Query for Fetching citation status: {}", query);

        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getContactPointSystem() {
        LOG.info("Fetching contact point system");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'code', code, " +
                                "'system', system, " +
                                "'value', value, " +
                                "'description', description " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.contact_point_system_view")
                .where(DSL.condition("(deleted_at IS NULL)"));
        LOG.info("Query for Fetching contact point system: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getInvestigatorStudyRole() {
        LOG.info("Fetching investigator study role");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'code', code, " +
                                "'role', role ) " +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.investigator_study_role_view");
        LOG.info("Query for Fetching investigator study role: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());

    }

    @Transactional
    public Object getLoincCodes() {
        LOG.info("Fetching Loinc codes");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'loinc_code_id', loinc_code_id, " +
                                "'loinc_code', loinc_code, " +
                                "'loinc_description', loinc_description, " +
                                "'loinc_class', loinc_class, " +
                                "'loinc_type', loinc_type, " +
                                "'rec_status_id', rec_status_id " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.loinc_codes_view")
                .where(DSL.field("deleted_at").isNull());
        LOG.info("Query for Fetching Loinc codes: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());

    }

    @Transactional
    public Object getMetricDefinitions() {
        LOG.info("Fetching metric definitions");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'metric_id', metric_id, " +
                                "'metric_name', metric_name, " +
                                "'metric_info', metric_info " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.metric_definitions_view");
        LOG.info("Query for Fetching metric definitions: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getOrganizationType() {
        LOG.info("Fetching organization type");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'organization_type_id', organization_type_id, " +
                                "'code', code, " +
                                "'system_uri', system_uri, " +
                                "'display', display, " +
                                "'description', description, " +
                                "'rec_status_id', rec_status_id " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.organization_type_view")
                .where(DSL.field("deleted_at").isNull());
        LOG.info("Query for Fetching organization type: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getResearchStudyCondition() {
        LOG.info("Fetching research study condition");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'coding_system', coding_system, " +
                                "'code', code, " +
                                "'display', display, " +
                                "'rec_status_id', rec_status_id " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.research_study_condition_view")
                .where(DSL.field("deleted_at").isNull());
        LOG.info("Query for Fetching research study condition: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getResearchStudyFocus() {
        LOG.info("Fetching research study focus");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'coding_system', coding_system, " +
                                "'code', code, " +
                                "'display', display, " +
                                "'rec_status_id', rec_status_id " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.research_study_focus_view")
                .where(DSL.field("deleted_at").isNull());
        LOG.info("Query for Fetching research study focus: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getResearchStudyPartyRole() {
        LOG.info("Fetching research study party role");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'study_party_role_id', study_party_role_id, " +
                                "'code', code, " +
                                "'system', system, " +
                                "'display', display, " +
                                "'description', description, " +
                                "'rec_status_id', rec_status_id " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.research_study_party_role_view")
                .where(DSL.field("deleted_at").isNull());
        LOG.info("Query for Fetching research study party role: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getResearchSubjectStatus() {
        LOG.info("Fetching research subject status");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'code', code, " +
                                "'display_name', display_name, " +
                                "'definition', definition, " +
                                "'system_url', system_url" +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.research_subject_status_master_view");
        LOG.info("Query for Fetching research subject status: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getStudyStatus() {
        LOG.info("Fetching study status");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'code', code, " +
                                "'display_name', display_name, " +
                                "'definition', definition, " +
                                "'system_url', system_url" +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.study_status_master_view");
        LOG.info("Query for Fetching study status: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getContactpointuse() {
        LOG.info("Fetching contact point uses");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +

                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'code', code, " +
                                "'system', system, " +
                                "'description', description " + ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.contact_point_use_view")
                .where(DSL.condition("deleted_at IS NULL"));
        LOG.info("Query for Fetching contact point uses: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public Object getContactPointAddressuse() {
        LOG.info("Fetching contact point address use");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'code', code, " +
                                "'system', system, " +
                                "'value', value, " +
                                "'description', description " +

                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.contact_point_address_use_view")
                .where(DSL.condition("(deleted_at IS NULL)"));

        LOG.info("Query for Fetching contact point address use: {}", query);
        JSONB jsonbResult = query.fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());

    }

    @Transactional
    public Object getcontactpointuseview() {
        LOG.info("Fetching contact point use view");
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'code', code, " +
                                "'system', system, " +
                                "'value', value, " +
                                "'description', description" +

                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.contact_point_use_view")
                .where(DSL.condition("(deleted_at IS NULL)"));
        LOG.info("Query for Fetching contact point use view: {}", query);
        JSONB jsonbResult = query
                .fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());

    }

    @Transactional
    public JSONB readGenderType() {
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'gender_type_id', gender_type_id, " +
                                "'code', code, " +
                                "'value', value " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.gender_type_view")
                .where(DSL.condition(
                        "(deleted_at IS NULL)"));
        LOG.info("Query for Fetching genderType : {}", query);
        return query.fetchOneInto(JSONB.class);
    }

    @Transactional
    public JSONB readRaceType() {
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'race_type_id', race_type_id, " +
                                "'code', code, " +
                                "'system_uri', system_uri, " +
                                "'system_oid', system_oid, " +
                                "'display', display, " +
                                "'race_text', race_text " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.race_type_view")
                .where(DSL.condition(
                        "(deleted_at IS NULL)"));
        LOG.info("Query for Fetchting raceType: {}", query);
        return query.fetchOneInto(JSONB.class);
    }

    @Transactional
    public JSONB readEthnicityType() {
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'ethnicity_type_id', ethnicity_type_id, " +
                                "'code', code, " +
                                "'system_uri', system_uri, " +
                                "'system_oid', system_oid, " +
                                "'display', display, " +
                                "'ethnicity_text', ethnicity_text " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.ethnicity_type_view")
                .where(DSL.condition(
                        "(deleted_at IS NULL)"));
        LOG.info("Query for Fetchting ethinicityType: {}", query);
        return query.fetchOneInto(JSONB.class);
    }

    @Transactional
    public Object getRaceIdByName(String race) {
        final var query = dsl
                .select(DSL.field("race_type_id"))
                .from("drh_stateless_master.race_type_view")
                .where(DSL.field("display").eq(DSL.val(race)));
        return query.fetchOneInto(String.class);

    }

    @Transactional
    public Object getEthnicityIdByName(String ethnicity) {
        final var query = dsl
                .select(DSL.field("ethnicity_type_id"))
                .from("drh_stateless_master.ethnicity_type_view")
                .where(DSL.field("display").eq(DSL.val(
                        ethnicity)));
        LOG.info("Fetching ethnicity type id Query: {}", query);
        return query.fetchOneInto(String.class);

    }

    @Transactional
    public String getCollaboratorStudyRole(String code) {
        final var query = dsl
                .select(DSL.field("role"))
                .from("drh_stateless_master.investigator_study_role_view")
                .where(DSL.field("code").eq(DSL.val(
                        code)));

        LOG.info("Fetching investigator study role Query: {}", query);
        return query.fetchOneInto(String.class);

    }

    @Transactional
    public int getActiontype(String actionType) {
        LOG.info("Get ActionType");
        final var query = dsl.selectDistinct(DSL.field(
                "id"))
                .from("drh_stateless_master.interaction_action_type_view")
                .where(DSL.field("title").eq(DSL.val(actionType)));
        LOG.info("Get Action Type Query: {}", query);
        final var actionTypeId = query.fetchOneInto(Integer.class);
        LOG.info("Get Action Type Response: {}", actionTypeId);
        return actionTypeId;
    }

    @Transactional
    public int getInteractionStatus(String status) {
        LOG.info("Get Interaction Status");
        final var query = dsl.selectDistinct(DSL.field(
                "id"))
                .from("drh_stateless_master.interaction_status_view")
                .where(DSL.field("title").eq(DSL.val(status)));
        LOG.info("Get Interaction Status Query: {}", query);
        final var InteractionStatusId = query.fetchOneInto(Integer.class);
        LOG.info("Get Interaction Status Response: {}", InteractionStatusId);
        return InteractionStatusId;
    }

    @Transactional
    public JSONB readFileContentType() {
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'id', id, " +
                                "'title', title, " +
                                "'description', description " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.file_content_type_view")
                .where(DSL.condition(
                        "(deleted_at IS NULL)"));
        LOG.info("Query for Fetchting ethinicityType: {}", query);
        return query.fetchOneInto(JSONB.class);
    }

    @Transactional
    public String getFileContentTypeId(String fileType) {
        final var query = dsl
                .select(DSL.field("id"))
                .from("drh_stateless_master.file_content_type_view")
                .where(DSL.field("title").equalIgnoreCase(DSL.value(fileType)))
                .and(DSL.condition(
                        "(deleted_at IS NULL)"))
                .limit(1);
        LOG.info("Query for Fetchting ethinicityType: {}", query);
        return query.fetchOneInto(String.class);
    }

    @Transactional
    public Object getStudyVisibilityById(int visibilityId) {
        LOG.info("Fetching study visibility");
        final var query = dsl
                .select(DSL.field("visibility_name"))
                .from("drh_stateless_master.study_visibility_view")
                .where(DSL.condition(
                        "(deleted_at IS NULL )"))
                .and(DSL.field("visibility_id").eq(DSL.value(visibilityId)));
        LOG.info("Query for Fetching study visibility: {}", query);
        String result = query.fetchOneInto(String.class);

        return result;
    }

    @Transactional
    public Object getMetricDefinitions(String metricId) {
        LOG.info("Fetching metric definitions corresponds to : {}", metricId);
        final var query = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'metric_id', metric_id, " +
                                "'metric_name', metric_name, " +
                                "'metric_info', metric_info " +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_master.metric_definitions_view")
                .where(DSL.field("metric_id").eq(DSL.value(metricId)));
        LOG.info("Query for Fetching metric definitions: {}", query);

        JSONB jsonbResult = query.fetchOneInto(JSONB.class);

        if (jsonbResult == null) {
            return "{}";
        }

        return JsonUtils.jsonStringToMapOrList(jsonbResult.data());
    }

    @Transactional
    public String getMetricActivityTypebyTitle(String title) {
        LOG.info("Fetching activity type corresponds to : {}", title);
        final var query = dsl
                .select(DSL.field("id"))
                .from("drh_stateless_master.activity_type_view")
                .where(DSL.field("title").eq(DSL.value(title)));
        LOG.info("Query for Fetching activity type definitions: {}", query);

        String id = query.fetchOneInto(String.class);
        LOG.info("Activity type id: {}", id);

        if (id == null) {
            return null;
        }

        return id;
    }

    @Transactional
    public String getMetricActivityLevel(int level) {
        LOG.info("Fetching activity log level corresponds to : {}", level);
        final var query = dsl
                .select(DSL.field("level_id"))
                .from("drh_stateless_master.activity_level_view")
                .where(DSL.field("level").eq(DSL.value(level)));
        LOG.info("Query for Fetching activity log definitions: {}", query);

        String id = query.fetchOneInto(String.class);
        LOG.info("Activity log level id: {}", id);

        if (id == null) {
            return null;
        }

        return id;
    }

    @Transactional
    public String getUserVerificationStatusId(String title) {
        LOG.info("Fetching user verification status corresponds to : {}", title);
        final var query = dsl
                .select(DSL.field("id"))
                .from("drh_stateless_master.user_verification_status_view")
                .where(DSL.field("title").eq(DSL.value(title)));
        LOG.info("Query for Fetching user verification status: {}", query);

        String id = query.fetchOneInto(String.class);
        LOG.info("User verification status id: {}", id);

        if (id == null) {
            return null;
        }

        return id;
    }
}