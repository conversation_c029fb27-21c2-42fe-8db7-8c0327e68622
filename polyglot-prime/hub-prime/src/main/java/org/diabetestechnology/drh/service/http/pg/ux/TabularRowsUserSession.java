package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.ArrayList;

import org.diabetestechnology.drh.pg.udi.auto.jooq.ingress.Tables;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.filter.CustomTabularFilter;
import org.diabetestechnology.drh.udi.UdiSecondaryDbConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;
import lib.aide.tabular.JooqRowsSupplier;
import lib.aide.tabular.TabularRowsRequest;
import lib.aide.tabular.TabularRowsResponse;

@Controller
@Tag(name = "DRH Hub Custom Tabular Row User Session")
public class TabularRowsUserSession {
    static private final Logger LOG = LoggerFactory.getLogger(TabularRowsUserSession.class);
    private final UdiSecondaryDbConfig udiPrimeDbConfig;
    private final UserNameService userNameService;
    private final CustomTabularFilter customTabularFilter;

    public TabularRowsUserSession(UdiSecondaryDbConfig udiPrimeDbConfig, UserNameService userNameService,
            CustomTabularFilter customTabularFilter) {
        this.udiPrimeDbConfig = udiPrimeDbConfig;
        this.userNameService = userNameService;
        this.customTabularFilter = customTabularFilter;
        LOG.info("TabularRowsUserSession initialized with UDI Prime DB Config: {}", udiPrimeDbConfig);
    }

    @Operation(summary = "SQL distinct activity rows from activity table ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/distinct/userSession/drh_stateless_activity_audit/vw_activity_log.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> distinctTabularAuditRows(final @RequestBody @Nonnull TabularRowsRequest payload,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "vw_activity_log";
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                schemaName,
                masterTableNameOrViewName);
        var bindValues = new ArrayList<Object>();
        // bindValues.add("[]");

        var queryString = "";
        if (userNameService.isAdmin()) {
            queryString = "";
        } else if (userNameService.isAuthenticatedUser()) {
            final var userId = userNameService.getCurrentuserPartyId();
            // queryString="(select
            // session_unique_id,
            // created_by,
            // (select first_name from drh_stateless_authentication.user_profile_view where
            // practitioner_party_id = created_by) as user_name,
            // ip_address,
            // app_version,
            // min(created_at) as '"'min_created_at'"'
            // from '"'drh_stateless_activity_audit'"'.'"'vw_activity_log'"''
            // where (
            // true
            // and created_by is not null
            // and session_unique_id in (select distinct session_unique_id from
            // drh_stateless_activity_audit.vw_activity_log where created_by= '"+userId+"' )
            // )
            // group by
            // '"'session_unique_id'"',
            // '"'created_by'"',
            // '"'user_name'"',
            // '"'ip_address'"',
            // '"'app_version'"'
            // order by min_created_at desc
            // offset 0 rows
            // fetch next 100 rows only) as t";
        } else {

        }
        return null;
    }

}
