package org.diabetestechnology.drh.service.http.hub.prime.service;

import java.util.Optional;

import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class AuthenticationService {
    public boolean isUserAuthenticated(HttpServletRequest request) {
        return Optional.ofNullable(request.getSession(false))
                .map(session -> session.getAttribute("SPRING_SECURITY_CONTEXT"))
                .isPresent();
    }
}
