package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Hidden
public class AiController {
    @Value("${ORG_DRH_SERVICE_HTTP_AI_BASE_URL:}")
    private String chatAiBaseUrl;
    @Value("${ORG_DRH_SERVICE_HTTP_RESEARCH_AI_BASE_URL:}")
    private String chatResearchAiBaseUrl;
    private static final Logger LOG = LoggerFactory.getLogger(AiController.class.getName());
    private final Presentation presentation;
    private final UserNameService userNameService;

    public AiController(Presentation presentation, UserNameService userNameService) {
        this.presentation = presentation;
        this.userNameService = userNameService;
    }

    @Hidden
    @GetMapping("/ai/info")
    @RouteMapping(label = "Ask DRH Data", title = "Ask DRH Data", siblingOrder = 30)
    public String svmFinal(Model model, final HttpServletRequest request) {
        LOG.info("Populate Ask DRH Data");
        String[] pageDescription = {
                "Users can ask questions in simple language and get answers quickly."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("chatAiBaseUrl", chatAiBaseUrl);
        model.addAttribute("chatResearchAiBaseUrl", chatResearchAiBaseUrl);
        model.addAttribute("authProviderId", userNameService.getUserId());
        model.addAttribute("username", presentation.getUsername());
        return presentation.populateModel("page/askdrh/data", model, request);
    }

    @RouteMapping(label = "Ask DRH", title = "Ask DRH", siblingOrder = 20)
    @GetMapping("/ai/general")
    public String drhGeneral(Model model, final HttpServletRequest request) {
        LOG.info("Populate Ask DRH General");
        String[] pageDescription = {
                "Ask any questions related to the DRH application in plain English."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("chatAiBaseUrl", chatAiBaseUrl);
        model.addAttribute("chatResearchAiBaseUrl", chatResearchAiBaseUrl);
        model.addAttribute("authProviderId", userNameService.getUserId());
        model.addAttribute("username", presentation.getUsername());
        return presentation.populateModel("page/askdrh/askDrhsite", model, request);
    }

    @RouteMapping(label = "Ask DRH Research Journal", title = "Ask DRH Research Journal", siblingOrder = 40)
    @GetMapping("/ai/research")
    public String research(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This widget helps users ask questions in plain English about the contents of journals in the Journal of Diabetes Science and Technology (JDST). The tool uses Anything-LLM to generate responses, providing relevant academic insights and references."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("chatAiBaseUrl", chatAiBaseUrl);
        model.addAttribute("chatResearchAiBaseUrl", chatResearchAiBaseUrl);
        model.addAttribute("authProviderId", userNameService.getUserId());
        model.addAttribute("username", presentation.getUsername());
        return presentation.populateModel("page/askdrh/researchJournal", model, request);
    }

    @RouteMapping(label = "Ask DRH iCODE", title = "Ask DRH iCODE", siblingOrder = 50)
    @GetMapping("/ai/workspace")
    public String workspace(final Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "This widget helps users ask questions in plain English about the contents of papers from The Integration of Continuous Glucose Monitoring Data into the Electronic Health Record (iCoDE) Project. The tool uses Anything-LLM to generate responses, providing relevant academic insights and references"
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("chatAiBaseUrl", chatAiBaseUrl);
        model.addAttribute("chatResearchAiBaseUrl", chatResearchAiBaseUrl);
        model.addAttribute("authProviderId", userNameService.getUserId());
        model.addAttribute("username", presentation.getUsername());
        return presentation.populateModel("page/askdrh/askICODE", model, request);
    }

    @Hidden
    @GetMapping("/aidev/info")
    public String drhdev(Model model, final HttpServletRequest request) {
        String[] pageDescription = {
                "Users can ask questions in plain English, which are converted into SQL queries by Vanna.ai. This feature simplifies data access, removing the need for users to write SQL queries manually."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("chatAiBaseUrl", chatAiBaseUrl);
        model.addAttribute("chatResearchAiBaseUrl", chatResearchAiBaseUrl);
        model.addAttribute("authProviderId", userNameService.getUserId());
        model.addAttribute("username", presentation.getUsername());
        return presentation.populateModel("page/askdrh/datadev", model, request);
    }

    @RouteMapping(label = "Ask DRH", siblingOrder = 70)
    @GetMapping("/ai")
    public String ai() {
        return "redirect:/ai/general";
    }

}
