package org.diabetestechnology.drh.service.http.util;

import org.apache.tika.Tika;
import org.diabetestechnology.drh.service.http.hub.prime.exception.EmptyFileException;
import org.diabetestechnology.drh.service.http.pg.ux.ParticipantRowFileController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import java.io.*;

import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;

public final class FileValidationUtil {
    private static final Logger LOG = LoggerFactory.getLogger(ParticipantRowFileController.class);

    private FileValidationUtil() {
    }

    public static void validateFileHasData(MultipartFile file) throws IOException {

        if (file.isEmpty()) {
            throw new EmptyFileException("Uploaded file is empty.");
        }

        var tika = new Tika();
        var mimeType = tika.detect(file.getInputStream());

        var fileName = file.getOriginalFilename();
        var extension = (fileName != null) ? fileName.substring(fileName.lastIndexOf(".") + 1) : "";

        if (mimeType.equals("text/csv") || mimeType.equals("text/plain") || extension.equalsIgnoreCase("csv")
                || extension.equalsIgnoreCase("txt")) {
            validateTextFile(file);
        } else if (mimeType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                mimeType.equals("application/vnd.ms-excel") ||
                extension.equalsIgnoreCase("xlsx") || extension.equalsIgnoreCase("xls")) {
            validateExcelFile(file);
        } else {
            throw new IllegalArgumentException("Invalid file type. Only CSV, TXT, and Excel files are allowed.");
        }
    }

    private static void validateTextFile(MultipartFile file) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            extractValidHeader(reader);
            // Step 2: Check for data within the first 50 lines
            if (!hasValidData(reader, 50)) {
                throw new EmptyFileException("Uploaded file contains only headers but no data.");
            }
        }
    }

    // Validate Excel File
    private static void validateExcelFile(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
                Workbook workbook = file.getOriginalFilename().endsWith(".xlsx") ? new XSSFWorkbook(inputStream)
                        : new HSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0); // Get first sheet
            if (sheet == null || sheet.getPhysicalNumberOfRows() <= 1) {
                throw new EmptyFileException("Uploaded Excel file contains only headers but no data.");
            }
        }
    }

    private static String extractValidHeader(BufferedReader reader) throws IOException {
        String header;
        do {
            header = reader.readLine();
            if (header == null) {
                throw new EmptyFileException("Uploaded file has no valid header.");
            }
        } while (header.trim().isEmpty());
        return header;
    }

    private static boolean hasValidData(BufferedReader reader, int maxLines) throws IOException {
        var dataLines = 0;
        String dataRow;
        while ((dataRow = reader.readLine()) != null && dataLines < maxLines) {
            if (!dataRow.trim().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    public static void validateMealsAndFitnessFile(MultipartFile file, String fileType) throws Exception {
        if (file.isEmpty()) {
            LOG.warn("Uploaded file is empty");
            throw new IllegalArgumentException("Uploaded file is empty");
        }
        LOG.info("Starting validation for fileType: {}", fileType);

        if ("meals".equalsIgnoreCase(fileType)) {
            validateMealFile(file);
        } else if ("fitness".equalsIgnoreCase(fileType)) {
            validateFitnessFile(file);
        } else {
            LOG.error("Unsupported fileType received: {}", fileType);

            throw new IllegalArgumentException("Unsupported fileType: " + fileType);
        }
        LOG.info("Validation completed successfully for fileType: {}", fileType);

    }

    private static void validateMealFile(MultipartFile file) throws Exception {

        LOG.debug("Validating meal file...");
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                LOG.error("Meals file is missing a header row");
                throw new IllegalArgumentException("File is missing a header row");
            }
            LOG.debug("Meals file headers: {}", headerLine);

            String[] headers = headerLine.split(",");
            List<String> expectedHeaders = List.of("meal_time", "calories", "meal_type");

            for (String requiredHeader : expectedHeaders) {
                if (Arrays.stream(headers).noneMatch(h -> h.trim().equalsIgnoreCase(requiredHeader))) {
                    LOG.error("Missing required column in meal file: {}", requiredHeader);
                    throw new IllegalArgumentException("Missing required header column: " + requiredHeader);
                }
            }
            // Check if there's at least one data row
            reader.mark(1000); // Mark position after header
            String nextLine = reader.readLine();
            if (nextLine == null || nextLine.trim().isEmpty()) {
                LOG.warn("Meals file contains only header row with no data");
                throw new IllegalArgumentException("File contains only headers and no data rows");
            }
            reader.reset(); // Go back to marked position to re-validate data row

            String line;
            int lineNum = 1;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSX");

            List<String> validMealTypes = List.of("Breakfast", "Lunch", "Dinner", "Snack");

            while ((line = reader.readLine()) != null) {
                lineNum++;
                String[] fields = line.split(",");
                if (fields.length < 3) {
                    LOG.error("Invalid data format at line {} in meals file", lineNum);
                    throw new IllegalArgumentException("Invalid data format at line " + lineNum);
                }

                String mealTime = fields[0].trim();
                String caloriesStr = fields[1].trim();
                String mealType = fields[2].trim();

                try {
                    OffsetDateTime.parse(mealTime, formatter);
                } catch (DateTimeParseException e) {
                    LOG.error("Invalid meal_time format at line {}: {}", lineNum, mealTime);
                    throw new IllegalArgumentException("Invalid meal_time format at line " + lineNum + ": " + mealTime);
                }

                try {
                    int calories = Integer.parseInt(caloriesStr);
                    if (calories < 0) {
                        LOG.error("Negative calories at line {}: {}", lineNum, calories);
                        throw new IllegalArgumentException("Calories must be non-negative at line " + lineNum);
                    }
                } catch (NumberFormatException e) {
                    LOG.error("Invalid calories value at line {}: {}", lineNum, caloriesStr);
                    throw new IllegalArgumentException(
                            "Invalid calories value at line " + lineNum + ": " + caloriesStr);
                }

                if (!validMealTypes.contains(mealType)) {
                    LOG.error("Invalid meal_type at line {}: {}", lineNum, mealType);
                    throw new IllegalArgumentException("Invalid meal_type at line " + lineNum + ": " + mealType);
                }
            }
            LOG.info("Meals file validation completed successfully");

        }
    }

    private static void validateFitnessFile(MultipartFile file) throws Exception {

        LOG.debug("Validating fitness file...");
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                LOG.error("Fitness file is missing a header row");
                throw new IllegalArgumentException("File is missing a header row");
            }
            LOG.debug("Fitness file headers: {}", headerLine);

            String[] headers = headerLine.split(",");
            List<String> expectedHeaders = List.of(
                    "date", "steps", "exercise_minutes", "calories_burned", "distance", "heart_rate");

            for (String requiredHeader : expectedHeaders) {
                if (Arrays.stream(headers).noneMatch(h -> h.trim().equalsIgnoreCase(requiredHeader))) {
                    LOG.error("Missing required column in fitness file: {}", requiredHeader);
                    throw new IllegalArgumentException("Missing required header column: " + requiredHeader);
                }
            }
            // Check if there's at least one data row
            reader.mark(1000); // Mark position after header
            String nextLine = reader.readLine();
            if (nextLine == null || nextLine.trim().isEmpty()) {
                LOG.warn("Fitness file contains only header row with no data");
                throw new IllegalArgumentException("File contains only headers and no data rows");
            }
            reader.reset(); // Go back to marked position to re-validate data row

            String line;
            int lineNum = 1;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            while ((line = reader.readLine()) != null) {
                lineNum++;
                String[] fields = line.split(",");
                if (fields.length < 6) {
                    LOG.error("Invalid data format at line {} in fitness file", lineNum);
                    throw new IllegalArgumentException("Invalid data format at line " + lineNum);
                }

                String date = fields[0].trim();
                String[] numericFields = Arrays.copyOfRange(fields, 1, 6);

                try {
                    LocalDate.parse(date, formatter);
                } catch (DateTimeParseException e) {
                    LOG.error("Invalid date format at line {}: {}", lineNum, date);
                    throw new IllegalArgumentException("Invalid date format at line " + lineNum + ": " + date);
                }

                for (int i = 0; i < numericFields.length; i++) {
                    String columnName = headers[i + 1].trim();
                    try {
                        double val = Double.parseDouble(numericFields[i].trim());
                        if (val < 0) {
                            LOG.error("Negative value at line {}, column {}: {}", lineNum, columnName, val);
                            throw new IllegalArgumentException(
                                    "Negative value at line " + lineNum + " in column '" + columnName + "'");
                        }
                    } catch (NumberFormatException e) {
                        LOG.error("Invalid data at line {}, column {}: {}", lineNum, columnName, numericFields[i]);
                        throw new IllegalArgumentException(
                                "Invalid data at line " + lineNum + " in column '" + columnName + "'");
                    }
                }
            }
            LOG.info("Fitness file validation completed successfully");
        }
    }
}
