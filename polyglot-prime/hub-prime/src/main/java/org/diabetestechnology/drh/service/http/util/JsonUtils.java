package org.diabetestechnology.drh.service.http.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class JsonUtils {
    private static final Logger LOG = LoggerFactory.getLogger(JsonUtils.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    static {
        // Register support for Java 8 LocalDate, LocalDateTime, etc.
        mapper.registerModule(new JavaTimeModule());
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    /**
     * Converts a JSON string to either a List or Map depending on if it's an array or object
     * @param jsonString The JSON string to convert
     * @return Object - Either a List or Map depending on input JSON structure
     */
    public static Object jsonStringToMapOrList(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return Map.of();
        }

        try {
            JsonNode jsonNode = mapper.readTree(jsonString);
            if (jsonNode.isArray()) {
                return mapper.readValue(jsonString, List.class);
            } else {
                return mapper.readValue(jsonString, Map.class);
            }
        } catch (Exception e) {
            LOG.error("Error while processing JSON", e);
            return jsonString.startsWith("[") ? List.of() : Map.of();
        }
    }

    public static String toJson(Object obj) {
        try {
            return obj != null ? mapper.writeValueAsString(obj) : null;
        } catch (Exception e) {
            throw new RuntimeException("Error converting object to JSON", e);
        }
    }
}