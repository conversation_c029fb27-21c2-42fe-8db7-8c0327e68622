package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.constant.ActionDescription;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.CollaborationMap;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.request.CollaborationTeamRequest;
import org.diabetestechnology.drh.service.http.pg.request.InvestigatorRequest;
import org.diabetestechnology.drh.service.http.pg.service.InteractionService;
import org.diabetestechnology.drh.service.http.pg.service.InvestigatorService;

import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH Investigator APIs")
public class ResearchStudyInvestigatorController {

    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyInvestigatorController.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final InvestigatorService investigatorService;
    private final ResearchStudyService researchStudyService;
    private final InteractionService interactionService;
    private final UserNameService userNameService;

    public ResearchStudyInvestigatorController(InvestigatorService investigatorService,
            ResearchStudyService researchStudyService,
            InteractionService interactionService, UserNameService userNameService) {
        this.investigatorService = investigatorService;
        this.researchStudyService = researchStudyService;
        this.interactionService = interactionService;
        this.userNameService = userNameService;

    }

    @GetMapping("/investigator")
    @Operation(summary = "get list of all investigators")
    @ResponseBody
    public Response getInvestigator() {
        try {
            LOG.info("get list of all investigators");
            JSONB result = investigatorService
                    .getInvestigator();
            LOG.info("getInvestigator result: {}", result);
            return Response.builder()
                    .data(Map.of("investigators", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully read investigator")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching list investigator", e);
            Response errorResponse = Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch list of investigator")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
            return errorResponse;
        }
    }

    @PostMapping("/collaboration-team")
    @ResponseBody
    @Operation(summary = "Save an investigator or author")
    public Response saveInvestigator(@RequestBody CollaborationTeamRequest request) {
        LOG.info("Save an {}: request: {}", request, request);
        if (!userNameService.getCurrentuserPartyId()
                .equalsIgnoreCase(researchStudyService.getStudyOwner(request.studyId()))) {
            LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                    request.studyId());
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Access denied: Only the study owner is permitted to edit this research study.")
                    .errors(null)
                    .build();
        }
        return investigatorService.prapareCollabRequest(request);

    }

    @PutMapping("/investigator")
    @ResponseBody
    @Operation(summary = "Update an investigator")
    public Response updateInvestigator(@RequestBody InvestigatorRequest request) {

        LOG.info("Update an {}: request: {}", request.type().name(), request);
        String hubInteractionId = interactionService.getHubIntercationIdOfStudy(request.studyId());
        CollaborationMap collaborationMap = new CollaborationMap();
        String message = collaborationMap.getCollaborationMap().get(request.type().name()).getActivityDescription();
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(request.studyId()))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        request.studyId());
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            if (researchStudyService.getResearchStudyArchiveStatus(request.studyId())) {
                LOG.warn("Access denied: Study {} is archived.", request.studyId());

                Response response = Response.builder()
                        .status("failure")
                        .message("The study is archived; edits and updates are not allowed.")
                        .build();

                return response;
            }
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, "Update",
                    ActionDescription.CREATE_OR_UPDATE_INVESTIGATOR + message
                            + ".",
                    null, "ACTIVE", JsonUtils.toJson(request),
                    null,
                    null,
                    200, "Success", ActionType.UPDATE_COLLABORATION_TEAM,
                    ActionStatus.IN_PROGRESS);

            JSONB result = investigatorService.saveInvestigator(request);
            JsonNode responseJson = OBJECT_MAPPER.readTree(result.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                interactionService.saveStudyInteraction(request.studyId(),
                        hubInteractionId, "Update",
                        ActionDescription.CREATE_OR_UPDATE_INVESTIGATOR
                                + message
                                + ".",
                        null, "ACTIVE", JsonUtils.toJson(request),
                        null, result
                                .toString(),
                        500, "Failed", ActionType.UPDATE_COLLABORATION_TEAM,
                        ActionStatus.FAILED);
                LOG.error("Error saving " + request.type().name() + ": " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            interactionService.saveStudyInteraction(request.studyId(),
                    hubInteractionId, "Update",
                    ActionDescription.CREATE_OR_UPDATE_INVESTIGATOR
                            + message
                            + ".",
                    null, "ACTIVE", JsonUtils.toJson(request),
                    result.toString(),
                    null,
                    200, "Success", ActionType.UPDATE_COLLABORATION_TEAM,
                    ActionStatus.SUCCESS);
            return Response.builder()
                    .data(Map.of("InvestigatorDetails",
                            result == null ? null : JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message(message + " created successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error creating an " + message, e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to create an " + message)
                    .errors("Error creating an " + message + ": " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/authors")
    @Operation(summary = "get list of all Authors")
    @ResponseBody
    public Response getAuthors() {
        LOG.info("get list of all Authors");
        try {
            JSONB result = investigatorService
                    .getAuthors();
            LOG.info("getAuthors result: {}", result);
            return Response.builder()
                    .data((Map.of("Authors",
                            JsonUtils.jsonStringToMapOrList(result.data()))))
                    .status("success")
                    .message("Successfully read Authors")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error fetching list Authors", e);
            Response errorResponse = Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch list of author")
                    .errors(Map.of("exception", e.getMessage()))
                    .build();
            return errorResponse;
        }
    }

}
