package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI.VannaAIRequest;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.VannaAiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Controller
@Tag(name = "DRH Vanna AI Endpoints")
public class VannaAiController {
    private final VannaAiService vannaAiService;
    private static final Logger LOG = LoggerFactory.getLogger(VannaAiController.class);

    public VannaAiController(VannaAiService vannaAiService) {
        this.vannaAiService = vannaAiService;
    }

    @PostMapping("/vanna-ai/interactions")
    public Response postMethodName(@RequestBody VannaAIRequest request) {
        LOG.info("Save Received request: {}", request);

        final var response = vannaAiService.saveVannaRequestAndResponse(request);

        return Response.builder()
                .status("success")
                .message("Request processed successfully") // Updated message
                .data(Map.of("vannaAiInteractionResponse", response)) // Wrap response in a Map
                .errors(null)
                .build();
    }

}
