package org.diabetestechnology.drh.service.http.hub.prime;

import org.diabetestechnology.drh.conf.Configuration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

@org.springframework.context.annotation.Configuration
@ConfigurationProperties(prefix = "org.diabetestechnology.service.http.hub.prime")
@ConfigurationPropertiesScan
public class AppConfig {
    public class Servlet {
        public class HeaderName {
            public class Request {
                public static final String DRH_STRUCT_DEFN_PROFILE_URI = Configuration.Servlet.HeaderName.PREFIX
                        + "DRH-Profile-URI";
                public static final String DRH_VALIDATION_STRATEGY = Configuration.Servlet.HeaderName.PREFIX
                        + "DRH-Validation-Strategy";
                public static final String DATALAKE_API_URL = Configuration.Servlet.HeaderName.PREFIX
                        + "DataLake-API-URL";
            }

            public class Response {
                // in case they're necessary
            }
        }
    }

    private String version;

    public String getVersion() {
        return version;
    }

    /**
     * Spring Boot will retrieve required value from properties file which is
     * injected from pom.xml.
     * 
     * @param version the version of the application
     */
    public void setVersion(String version) {
        this.version = version;
    }

}
