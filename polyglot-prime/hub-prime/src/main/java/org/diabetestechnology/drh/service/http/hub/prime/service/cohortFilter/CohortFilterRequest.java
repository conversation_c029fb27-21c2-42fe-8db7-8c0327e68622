package org.diabetestechnology.drh.service.http.hub.prime.service.cohortFilter;

import java.util.List;
import java.util.Map;

// @formatter:off
public record CohortFilterRequest(
        String viewMode,
        String createdBy,
        String updatedBy,
        String filterName,
        String filterDescription,
        FilterOption filterOption) {

    public record FilterOption(List<String> studyIds, // IDs of the studies
            Map<String, FilterModel> filters // Map of predefined filter properties
    ) {

        public record FilterModel(
                String filterType, // Type of the filter (text, number, etc.)
                String type, // Type of comparison (equals, contains, etc.)
                List<String> values, // Filter values for simple filters
                String operator, // Logical operator (AND/OR) for complex filters
                String sqlFragment // SQL fragment for filter
        ) {
            public FilterModel(String filterType, String type, List<String> values, String operator,
                    String sqlFragment) {
                this.filterType = filterType;
                this.type = type;
                this.values = values;
                this.operator = operator;
                this.sqlFragment = sqlFragment;
            }
        }
    }
}
// @formatter:on