package org.diabetestechnology.drh.service.http.pg.service;

import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.ux.ResearchStudyController;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.jooq.Record;

import org.springframework.beans.factory.annotation.Value;

@Service
public class SuperAdminService {
    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyController.class);

    private final PasswordEncoder passwordEncoder;
    private final DSLContext dsl;

    public SuperAdminService(PasswordEncoder passwordEncoder, @Qualifier("secondaryDsl") DSLContext dsl) {
        this.passwordEncoder = passwordEncoder;
        this.dsl = dsl;

    }

    @Value("${org.drh.service.super.admin.password}")
    private String superAdminPassword;

    @Value("${org.drh.service.super.admin.organization.party.id}")
    private String organizationPartyId;

    public boolean authenticateSuperAdmin(String email, String password, String organizationPartyId) {
        LOG.debug("Super Admin input email: {}", email);
        LOG.debug("Organization Party ID: {}", organizationPartyId);

        try {
            Record response = dsl.select()
                    .from("drh_stateless_authentication.super_admin_view")
                    .where(DSL.field("email").eq(email))
                    .and(DSL.field("organization_party_id").eq(organizationPartyId))
                    .fetchOne();

            if (response == null) {
                LOG.warn("No Super Admin found with email: {} and org ID: {}", email, organizationPartyId);
                return false;
            }
            String dbHashedPassword = response.get("password", String.class);

            LOG.debug("Fetched hashed input: {}", dbHashedPassword);

            if (passwordEncoder.matches(password, dbHashedPassword)) {
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(email,
                        null, List.of());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                return true;
            } else {
                LOG.warn("Password mismatch for Super Admin with email: {}", email);
                return false;
            }
        } catch (Exception e) {
            LOG.error("Error during Super Admin authentication", e);
            return false;
        }
    }

    public Object addSuperAdmin(String fullName, String email) {
        try {
            LOG.info("Adding Super Admin with fullName: {}", fullName);
            LOG.info("Adding Super Admin with email: {}", email);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_authentication.create_super_admin_account({0}, {1}, {2}, {3}, {4})",
                    JSONB.class,
                    DSL.val(fullName),
                    DSL.val(email),
                    DSL.val(organizationPartyId),
                    DSL.val(superAdminPassword),
                    DSL.val(true)));
            LOG.info("Save Super Admin Query: {}", query);
            final var result = query.fetchOne();
            LOG.info("Result: {}", result.toString());
            return result.value1();
        } catch (Exception e) {
            LOG.error("Failed to add Super Admin", e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to Add Super Admin")
                    .errors("Error in adding super admin: " +
                            e.getMessage())
                    .build();

        }
    }
}