package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.UserVerificationStatus;
import org.diabetestechnology.drh.service.http.pg.service.AuthUserDetailsService;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;

@Controller
public class MyProfileController {

    private final AuthUserDetailsService authUserDetailsService;

    private final Presentation presentation;
    private final PractitionerService practitionerService;
    ObjectMapper objectMapper = new ObjectMapper();
    private final UserNameService userNameService;
    private final MasterService masterService;

    public MyProfileController(Presentation presentation, PractitionerService practitionerService,
            AuthUserDetailsService authUserDetailsService, UserNameService userNameService,
            MasterService masterService) {
        this.presentation = presentation;
        this.practitionerService = practitionerService;
        this.authUserDetailsService = authUserDetailsService;
        this.userNameService = userNameService;
        this.masterService = masterService;
    }

    private static final Logger LOG = LoggerFactory.getLogger(MyProfileController.class.getName());

    @GetMapping("/profile/info")
    public String devicefinal(Model model, final HttpServletRequest request)
            throws JsonMappingException, JsonProcessingException {
        LOG.info("Populate Profile");
        if (presentation.isAuthenticatedUser()) {
            model.addAttribute("isAuthenticated", true);
            Boolean isProfileCompleted = practitionerService.isUserExists();
            model.addAttribute("isProfileCompleted", isProfileCompleted);
            final Boolean isSuperAdmin = presentation.isSuperAdmin();
            model.addAttribute("isSuperAdmin", isSuperAdmin);
            final var avatarUrl = practitionerService.getAvatarUrl();
            model.addAttribute("avatar_url", avatarUrl);
            model.addAttribute("Organization", practitionerService.getUserOrganization());
            model.addAttribute("name", authUserDetailsService.getLoginUserFullName());
            final var providerId = userNameService.getUserId();
            final var statusId = masterService.getUserVerificationStatusId(UserVerificationStatus.COMPLETED);
            Boolean isUserVerified = practitionerService.isUserVerified(providerId, statusId);
            model.addAttribute("isUserVerified", isUserVerified);
            if (!isProfileCompleted && !isSuperAdmin) {
                // return presentation.populateModel("page/investigator/profile", model,
                // request);
                return presentation.populateModel("page/investigator/verifyEmail", model, request);
            }
        } else {
            model.addAttribute("isAuthenticated", false);
            model.addAttribute("isProfileCompleted", false);
            model.addAttribute("isSuperAdmin", false);
            model.addAttribute("isUserVerified", false);
        }
        return presentation.populateModel("page/profile", model, request);
    }

    @GetMapping("/profile")
    public String docs() {
        return "redirect:/profile/info";
    }

}
