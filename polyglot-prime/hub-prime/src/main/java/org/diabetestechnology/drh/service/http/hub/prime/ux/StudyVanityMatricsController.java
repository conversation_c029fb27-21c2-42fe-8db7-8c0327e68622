package org.diabetestechnology.drh.service.http.hub.prime.ux;

// import org.springframework.stereotype.Controller;
// import org.springframework.ui.Model;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestHeader;

// import org.springframework.web.bind.annotation.ResponseBody;

// import io.swagger.v3.oas.annotations.Hidden;
// import io.swagger.v3.oas.annotations.Operation;

// import io.swagger.v3.oas.annotations.tags.Tag;

// import java.sql.SQLException;
// import java.text.DecimalFormat;
// import java.util.ArrayList;
// import java.util.Collections;
// import java.util.List;
// import java.util.Map;

// import org.springframework.http.HttpStatus;
// import org.diabetestechnology.drh.service.http.aggrid.ServerRowsRequest;
// import org.diabetestechnology.drh.service.http.aggrid.ServerRowsResponse;
// import org.diabetestechnology.drh.service.http.aggrid.SqlQueryBuilder;
// import org.diabetestechnology.drh.service.http.hub.prime.pivot.PivotSupport;
// import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
// import
// org.diabetestechnology.drh.service.http.hub.prime.service.CombineDataAccessService;
// import
// org.diabetestechnology.drh.service.http.hub.prime.service.DataAccessService;
// import org.diabetestechnology.drh.udi.UdiPrimeDbConfig;
// import org.diabetestechnology.drh.udi.auto.jooq.ingress.Tables;
// import org.jooq.exception.DataAccessException;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import jakarta.annotation.Nonnull;
// import jakarta.servlet.http.HttpServletRequest;
// import lib.aide.tabular.JooqRowsSupplier;
// import lib.aide.tabular.TabularRowsRequest;
// import lib.aide.tabular.TabularRowsResponse;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.MediaType;
// import org.springframework.http.ResponseEntity;

// import static org.jooq.impl.DSL.*;
// import org.jooq.*;

// import org.jooq.impl.SQLDataType;

// @Controller
// @Tag(name = "DRH Study UX API")
// public class StudyVanityMatricsController {
// @Autowired
// private DataAccessService dataAccessService;

// @Autowired
// private CombineDataAccessService combineDataAccessService;

// private final Presentation presentation;

// private static final Logger LOG =
// LoggerFactory.getLogger(StudyVanityMatricsController.class.getName());

// private final UdiPrimeDbConfig udiPrimeDbConfig;
// private final PivotSupport pivotSupport = new PivotSupport();

// public StudyVanityMatricsController(Presentation presentation,
// UdiPrimeDbConfig udiPrimeDbConfig) {
// this.presentation = presentation;
// this.udiPrimeDbConfig = udiPrimeDbConfig;
// }

// // @Operation(summary = "Study Vanity Matrics and Details for Populating
// Grid")
// @Hidden
// @PostMapping(value = "/svm.json", consumes =
// MediaType.APPLICATION_JSON_VALUE)
// @ResponseBody
// public ServerRowsResponse studyDetails(final @RequestBody @Nonnull
// ServerRowsRequest payload) {
// LOG.info("Read Study Vanity Matrics and Details with Payload : {}", payload);

// final Map<String, List<String>> pivotValues =
// pivotSupport.getPivotValues(payload.getPivotCols());

// final var DSL = udiPrimeDbConfig.dsl();
// final var result = DSL
// .fetch(new SqlQueryBuilder().createSql(payload,
// "study_vanity_metrics_and_details_view",
// pivotValues));

// // create response with our results
// return ServerRowsResponse.createResponse(payload, result.intoMaps(),
// pivotValues);

// }

// @Hidden
// @GetMapping("/svm/info")
// // @RouteMapping(label = "All Studies", siblingOrder = 0)
// public String svmFinal(Model model, final HttpServletRequest request) {
// LOG.info("Read Study Vanity Matrics And Details");
// // Descreption to show in UI
// String[] pageDescription = {
// "This page provides an organized, sortable list of all research studies in
// the Diabetes Research Hub, offering quick access to key metrics and detailed
// study information."
// };
// String[] pageSubDescriptiontitle = {
// "Displays essential data, such as:",
// };
// String[] pageSubDescription = {
// "Number of participants",
// "CGM files",
// "Gender distribution",
// "Average participant age",
// "Days of CGM device wear",
// "Total data points collected"
// };
// String[] pageAttributestitle = {
// "The page is powered by AG Grid, allowing users to:" };
// String[] pageAttributes = {
// "Filter, sort, and adjust column visibility for a personalized view.",
// "Access associated source files and participant metrics by clicking on
// individual study or participant IDs for further details." };
// String[] notes = {
// "The loading time for the Consolidated Metrics is currently a bit high due to
// data fetching and calculations. It needs optimization."
// };
// model.addAttribute("pageDescription", pageDescription);
// model.addAttribute("pagesubdescriptiontitle", pageSubDescriptiontitle);
// model.addAttribute("pagesubdescription", pageSubDescription);
// model.addAttribute("pageattributestitle", pageAttributestitle);
// model.addAttribute("pageattributes", pageAttributes);
// model.addAttribute("notes", notes);
// return presentation.populateModel("page/svm", model, request);
// }

// // @RouteMapping(label = "Studies", siblingOrder = 30)
// // @GetMapping("/svm")
// // public String study() {
// // return "redirect:/svm/info";
// // }

// // @Operation(summary = "Get Study Details Corresponds to a studyId")
// @Hidden
// @PostMapping("/study/each-study-details/{schemaName}")
// @ResponseBody
// public TabularRowsResponse<?> getStudyDetails(
// final @PathVariable String schemaName,
// final @RequestBody @Nonnull TabularRowsRequest payload,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required =
// false) boolean includeGeneratedSqlInResp,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required
// = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {
// try {
// String schema = schemaName.toLowerCase();
// DSLContext dsl = udiPrimeDbConfig.dsl(); // Obtain DSLContext from your
// configuration

// Table<?> s = table("`" + schema + "`.uniform_resource_study").as("s");
// Table<?> p = table("`" + schema + "`.uniform_resource_participant").as("p");
// Table<?> i = table("`" + schema + "`.uniform_resource_investigator").as("i");

// // Define fields
// Field<Integer> ageField = cast(field("p.age"), SQLDataType.INTEGER);

// // Construct the jOOQ query
// var query = dsl
// .select(
// field("s.study_id").as("study_id"),
// field("s.study_name").as("study_name"),
// field("s.study_description").as("study_description"),
// field("s.start_date").as("start_date"),
// field("s.end_date").as("end_date"),
// field("s.nct_number").as("nct_number"),
// countDistinct(field("p.participant_id"))
// .as("total_number_of_participants"),
// round(avg(ageField), 2).as("average_age"),
// floor((sum(when(field("p.gender").eq("F"), 1).otherwise(0))
// .cast(SQLDataType.FLOAT)
// .divide(count())).multiply(100))
// .as("percentage_of_females"),
// // groupConcat(field("i.investigator_name")).separator(", ")
// // .as("investigators")
// groupConcatDistinct(field("i.investigator_name"))
// .as("investigators"))
// .from(s)
// .leftJoin(p).on(field("s.study_id").eq(field("p.study_id")))
// .leftJoin(i).on(field("s.study_id").eq(field("i.study_id")))
// .where(ageField.isNotNull());

// LOG.info("Get Study Details Corresponds to the schema {}:", schema);
// LOG.info("Get Study Details Corresponds to a Query {}:", query.getSQL());

// var bindValues = new ArrayList<Object>();
// bindValues.add(2);
// bindValues.add("F");
// bindValues.add(1);
// bindValues.add(0);
// bindValues.add(100);

// return new JooqRowsSupplier.Builder()
// .withRequest(payload)
// .withQuery(Tables.class, schema, "uniform_resource_study", (Query) query,
// bindValues)
// .withDSL(udiPrimeDbConfig.dsl())
// .withLogger(LOG)
// .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
// .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
// .build()
// .response();
// } catch (DataAccessException e) {
// throw new RuntimeException("Error executing SQL query for study ID '" +
// schemaName + "'", e);
// }
// }

// private Field<String> groupConcatDistinct(Field<Object> field) {
// return field("group_concat(distinct {0})", SQLDataType.VARCHAR, field);
// }

// @Operation(summary = "Get All Study Total Data Points")
// @GetMapping(value = "/study/allstudy-total-data-points.{extension}", produces
// = MediaType.APPLICATION_JSON_VALUE)
// @ResponseBody
// public ResponseEntity<?> getAllStudyTotalDataPoints(@PathVariable String
// extension) throws SQLException {
// LOG.info("Endpoint accessed: GET /study/allstudy-total-data-points.{}",
// extension);
// try {
// Object dataPointsObj = combineDataAccessService.getAllStudyTotalDataPoints()
// .get("total_data_points");

// if (dataPointsObj == null) {
// LOG.warn("Data points not found");
// return ResponseEntity.notFound().build();
// }

// String formattedDataPoints;
// if (dataPointsObj instanceof Number) {
// double dataPoints = ((Number) dataPointsObj).doubleValue();
// DecimalFormat decimalFormat = new DecimalFormat("0.00");
// formattedDataPoints = decimalFormat.format(dataPoints / 1_000_000) + " M";
// } else {
// formattedDataPoints = dataPointsObj.toString();
// }

// return ResponseEntity.ok()
// .contentType(MediaType.APPLICATION_JSON)
// .body(formattedDataPoints);
// } catch (RuntimeException e) {
// LOG.error("Error in /study/allstudy-total-data-points: {}", e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Operation(summary = "Get All Study Total CGM Wear")
// @GetMapping(value = "/study/allstudy-total-cgm-wear.{extension}", produces =
// {
// "text/html" })
// @ResponseBody
// public ResponseEntity<?> getAllStudyTotalCgmWear(@PathVariable String
// extension) throws SQLException {
// try {
// LOG.info("Endpoint accessed: GET /get-allstudy-total-cgm-wear.{}",
// extension);
// return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
// .body(combineDataAccessService.getAllStudyTotalCgmWear().get("total_cgm_wear"));
// } catch (Exception e) {
// LOG.error("Error in /get-allstudy-total-cgm-wear: {}", e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Operation(summary = "Get Each Study Details")
// @GetMapping("/study/each-study-details/{studyId}")
// @ResponseBody
// public Object getEachStudyDetails(final @PathVariable String studyId) {
// LOG.info("Endpoint accessed: GET /study/each-study-details, studyId: {}",
// studyId);
// return dataAccessService.getStudyDetails(studyId);
// }

// @Operation(summary = "Get All Study Vanity Metrics")
// @GetMapping(value = "/study/allstudy-vanity-metrics/{metrics}.{extension}",
// produces = MediaType.APPLICATION_JSON_VALUE)
// @ResponseBody
// public ResponseEntity<?> getAllStudyVanityMetrics(@PathVariable String
// metrics, @PathVariable String extension)
// throws SQLException {
// LOG.info("Endpoint accessed: GET /allstudy-vanity-metrics/{}.{}", metrics,
// extension);

// try {
// Object metricValueObj =
// combineDataAccessService.getAllStudyVanityMetrics().get(metrics);

// if (metricValueObj == null) {
// LOG.warn("Metric not found: {}", metrics);
// return ResponseEntity.notFound().build();
// }

// String metricValueStr;
// if (metricValueObj instanceof Number) {
// double metricValue = ((Number) metricValueObj).doubleValue();
// DecimalFormat decimalFormat = new DecimalFormat(metricValue % 1 == 0 ? "0" :
// "0.0");
// metricValueStr = decimalFormat.format(metricValue);
// } else {
// metricValueStr = metricValueObj.toString();
// }

// String suffix = SUFFIX_MAP.getOrDefault(metrics, "");

// metricValueStr += suffix;

// return ResponseEntity.ok()
// .contentType(MediaType.TEXT_PLAIN)
// .body(metricValueStr);
// } catch (RuntimeException e) {
// LOG.error("Error in /study/allstudy-vanity-metrics: {}", e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// private static final Map<String, String> SUFFIX_MAP = Map.of(
// "percent_female", "%",
// "average_age", " Years");

// @Operation(summary = "Get All Study Total CGM Files")
// @GetMapping(value = "/study/allstudy-total-cgm-files.{extension}", produces =
// {
// "text/html" })
// @ResponseBody
// public ResponseEntity<?> getAllStudyTotalCgmFiles(@PathVariable String
// extension) throws SQLException {
// try {
// LOG.info("Endpoint accessed: GET /get-allstudy-total-cgm-files.{}",
// extension);
// return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
// .body(combineDataAccessService.getAllStudyTotalCgmFiles()
// .get("total_cgm_file_count"));
// } catch (Exception e) {
// LOG.error("Error in /get-allstudy-total-cgm-files: {}", e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Operation(summary = "Get Individual Study Total CGM Files")
// @GetMapping(value = "/study/allstudy-total-cgm-files/{studyId}.{extension}",
// produces = {
// "text/html" })
// @ResponseBody
// public ResponseEntity<?> getStudyTotalCgmFiles(@PathVariable String studyId,
// @PathVariable String extension)
// throws SQLException {
// LOG.info("getStudyTotalCgmFiles: Hitting API:
// /study/allstudy-total-cgm-files/{studyId}.{extension} ");
// try {
// LOG.info("Endpoint accessed: POST /get-allstudy-total-cgm-files");
// return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
// .body(dataAccessService.getStudyTotalCgmFiles(studyId.toLowerCase())
// .get("total_count"));
// } catch (Exception e) {
// LOG.error("Error in /get-all-study-total-cgm-files/{}: {}", studyId,
// e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Hidden
// @Operation(summary = "Get Each Study CGM File Names")
// @PostMapping(value = "/each-study-cgm-file_names/{studyId}", consumes =
// MediaType.APPLICATION_JSON_VALUE, produces =
// MediaType.APPLICATION_JSON_VALUE)
// @ResponseBody
// public Object getStudyCGMFileNames(
// @PathVariable(required = true) String studyId,
// @RequestBody TabularRowsRequest payload,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required =
// false) boolean includeGeneratedSqlInResp,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required
// = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp)
// throws SQLException {

// if (studyId != null)
// dataAccessService.refreshDatabase(studyId.toLowerCase());
// String tableName = "cgm_table_name_cached";
// LOG.info("getStudyCGMFileNamess, Table Name: {}.{}", studyId.toLowerCase(),
// tableName);
// return new JooqRowsSupplier.Builder()
// .withRequest(payload)
// .withTable(Tables.class, studyId.toLowerCase(), tableName)
// .withDSL(udiPrimeDbConfig.dsl())
// .withLogger(LOG)
// .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
// .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
// .build()
// .response();
// }

// @Hidden
// @Operation(summary = "Get Each Study CGM Files")
// @PostMapping(value = "/each-study-cgm-files/{studyId}/{fileName}", consumes =
// MediaType.APPLICATION_JSON_VALUE, produces =
// MediaType.APPLICATION_JSON_VALUE)
// @ResponseBody
// public Object getStudyCGMFiles(
// @PathVariable(required = true) String studyId,
// @PathVariable(required = true) String fileName,
// @RequestBody TabularRowsRequest payload,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required =
// false) boolean includeGeneratedSqlInResp,
// @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required
// = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp)
// throws SQLException {
// if (studyId != null)
// dataAccessService.refreshDatabase(studyId.toLowerCase());
// LOG.info("getStudyCGMFileNamess, Table Name: {}.{}", studyId.toLowerCase(),
// fileName);
// return new JooqRowsSupplier.Builder()
// .withRequest(payload)
// .withTable(Tables.class, studyId.toLowerCase(), fileName)
// .withDSL(udiPrimeDbConfig.dsl())
// .withLogger(LOG)
// .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
// .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
// .build()
// .response();
// }

// @Operation(summary = "Get Individual Study Total Participants")
// @GetMapping(value =
// "/study/each-study-total-participants/{studyId}.{extension}", produces = {
// "text/html" })
// @ResponseBody
// public ResponseEntity<?> getStudyTotalParticipants(@PathVariable String
// studyId, @PathVariable String extension)
// throws SQLException {
// try {
// LOG.info("Endpoint accessed: GET /study/each-study-total-participants/{}.{}",
// studyId, extension);
// dataAccessService.refreshDatabase(studyId.toLowerCase());
// return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
// .body(dataAccessService.getIndividualStudyDetails(studyId.toLowerCase())
// .get("total_number_of_participants"));
// } catch (Exception e) {
// LOG.error("Error in /get/study/each-study-total-participants: {}",
// e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Operation(summary = "Get Individual Study Total Female Percentage")
// @GetMapping(value =
// "/study/each-study-percentage-female/{studyId}.{extension}", produces = {
// "text/html" })
// @ResponseBody
// public ResponseEntity<?> getStudyTotalPercentageFemale(@PathVariable String
// studyId, @PathVariable String extension)
// throws SQLException {
// try {
// LOG.info("Endpoint accessed: GET
// /get/study/each-study-percentage-female/{}.{}", studyId, extension);
// dataAccessService.refreshDatabase(studyId.toLowerCase());
// return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN)
// .body(dataAccessService.getIndividualStudyDetails(studyId.toLowerCase())
// .get("percentage_of_females"));
// } catch (Exception e) {
// LOG.error("Error in /get/study/each-study-percentage-female: {}",
// e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Operation(summary = "Get Individual Study Average Age")
// @GetMapping(value = "study/each-study-average-age/{studyId}.{extension}",
// produces = {
// "text/html" })
// @ResponseBody
// public ResponseEntity<?> getStudyAverageAge(@PathVariable String studyId,
// @PathVariable String extension)
// throws SQLException {
// try {
// LOG.info("Endpoint accessed: GET get-/study/each-study-average-age/{}.{}",
// studyId, extension);
// dataAccessService.refreshDatabase(studyId.toLowerCase());
// return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
// .body(dataAccessService.getIndividualStudyDetails(studyId.toLowerCase()).get("average_age"));
// } catch (Exception e) {
// LOG.error("Error in get/study/each-study-average-age: {}", e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Operation(summary = "Get All Study Total CGM Wear")
// @GetMapping(value = "/study/allstudy-avg-glucose.{extension}", produces = {
// "text/html" })
// @ResponseBody
// public ResponseEntity<?> getAllStudyAvgGlucose(@PathVariable String
// extension) throws SQLException {
// try {
// LOG.info("Endpoint accessed: GET /get-allstudy-avg-glucose.{}", extension);
// return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN)
// .body(combineDataAccessService.getAllStudyAvgGlucose().get("avg_glucose"));
// } catch (Exception e) {
// LOG.error("Error in /get-allstudy-avg-glucose: {}", e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Hidden
// @GetMapping("/search-studies/info")
// // @RouteMapping(label = "Search Studies", siblingOrder = 0)
// public String studySearchInfo(Model model, final HttpServletRequest request)
// {
// LOG.info("Read Studies And Details");
// // Descreption to show in UI
// String[] pageDescription = {
// "This page allows users to filter studies based on participant
// characteristics and glycemic metrics, dynamically generating results."
// };
// String[] pageSubDescriptionTitle = {
// "Users can filter studies based on criteria such as:",
// };
// String[] pageSubDescription = {
// "Age",
// "Time in Range (TIR)",
// "Glycemic Risk Index (GRI)",
// "HbA1c",
// "Gender",
// "Days of Wear for CGM devices",
// };
// String[] pageAttributesTitle = {
// "Results are generated in real-time, displaying population percentages for
// quick insights.",
// "Users can click on individual study IDs to access in-depth information, and
// all interactions are powered by AG Grid for smooth, interactive navigation."
// };
// String[] notes = {
// "The loading time for the Consolidated Metrics is currently a bit high due to
// data fetching and calculations. It needs optimization."
// };
// model.addAttribute("pageDescription", pageDescription);
// model.addAttribute("pagesubdescription", pageSubDescription);
// model.addAttribute("pagesubdescriptiontitle", pageSubDescriptionTitle);
// model.addAttribute("pageattributestitle", pageAttributesTitle);
// model.addAttribute("notes", notes);
// return presentation.populateModel("page/cohortcombined", model, request);
// }

// // @RouteMapping(label = "Search Studies", siblingOrder = 20)
// // @GetMapping("/search-studies")
// // public String studySearch() {
// // return "redirect:/search-studies/info";
// // }

// @Operation(summary = "Get Each Metrics Details")
// @GetMapping("/study/each-metrics-details/{metric}")
// @ResponseBody
// public ResponseEntity<?> getEachMetricsDetails(@PathVariable String metric) {
// LOG.info("Endpoint accessed: GET /study/each-metrics-details, metrics: {}",
// metric);

// try {
// Map<String, Object> metricDetails =
// combineDataAccessService.getEachMetricsDetails(metric);

// if (metricDetails.isEmpty()) {
// LOG.warn("No results found for metric: {}", metric);
// return ResponseEntity.status(HttpStatus.BAD_REQUEST)
// .contentType(MediaType.APPLICATION_JSON)
// .body(Collections.singletonMap("error", "Invalid metrics ID"));
// }

// return ResponseEntity.ok()
// .contentType(MediaType.APPLICATION_JSON)
// .body(metricDetails);
// } catch (RuntimeException e) {
// LOG.error("Error in /study/each-metrics-details: {}", e.getMessage());
// return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
// .contentType(MediaType.APPLICATION_JSON)
// .body(Collections.singletonMap("error", "An error occurred while processing
// your request."));
// }
// }

// @Operation(summary = "Get Each Metrics Details From View")
// @GetMapping("/study/each-metrics-details-view/{metric}")
// @ResponseBody
// public ResponseEntity<?> getEachMetricsDetailsFromView(@PathVariable String
// metric) {
// LOG.info("Endpoint accessed: GET /study/each-metrics-details-view, metrics:
// {}", metric);

// try {
// Map<String, Object> metricDetails =
// combineDataAccessService.getEachMetricsDetailsFromView(metric);

// if (metricDetails.isEmpty()) {
// LOG.warn("No results found for metric: {}", metric);
// return ResponseEntity.status(HttpStatus.BAD_REQUEST)
// .contentType(MediaType.APPLICATION_JSON)
// .body(Collections.singletonMap("error", "Invalid metrics ID"));
// }

// return ResponseEntity.ok()
// .contentType(MediaType.APPLICATION_JSON)
// .body(metricDetails);
// } catch (RuntimeException e) {
// LOG.error("Error in /study/each-metrics-details-view: {}", e.getMessage());
// return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
// .contentType(MediaType.APPLICATION_JSON)
// .body(Collections.singletonMap("error", "An error occurred while processing
// your request."));
// }
// }

// @Operation(summary = "Get All Study Detail Metrics")
// @GetMapping(value = "/study/allstudy-detail-metrics/{metrics}.{extension}",
// produces = MediaType.APPLICATION_JSON_VALUE)
// @ResponseBody
// public ResponseEntity<?> getAllStudyDetailMetrics(@PathVariable String
// metrics, @PathVariable String extension)
// throws SQLException {
// LOG.info("Endpoint accessed: GET /allstudy-vanity-metrics/{}.{}", metrics,
// extension);

// try {
// Object metricValueObj =
// combineDataAccessService.getAllStudyDetailMetrics().get(metrics);

// if (metricValueObj == null) {
// LOG.warn("Metric not found: {}", metrics);
// return ResponseEntity.notFound().build();
// }

// String metricValueStr;
// if (metricValueObj instanceof Number) {
// double metricValue = ((Number) metricValueObj).doubleValue();
// DecimalFormat decimalFormat = new DecimalFormat(metricValue % 1 == 0 ? "0" :
// "0.0");
// metricValueStr = decimalFormat.format(metricValue);
// } else {
// metricValueStr = metricValueObj.toString();
// }

// String suffix = SUFFIX_MAP.getOrDefault(metrics, "");

// metricValueStr += suffix;

// return ResponseEntity.ok()
// .contentType(MediaType.APPLICATION_JSON)
// .body(metricValueStr);
// } catch (RuntimeException e) {
// LOG.error("Error in /study/allstudy-vanity-metrics: {}", e.getMessage());
// return ResponseEntity.badRequest().build();
// }
// }

// @Operation(summary = "Get Device Name and Count ")
// @GetMapping("/study/device-name-count/{studyId}")
// @ResponseBody
// public ResponseEntity<?> getDeviceNameAndCount(@PathVariable String studyId)
// {
// LOG.info("Endpoint accessed: GET /study/device-name-count/{studyId}");

// try {
// final var response = dataAccessService.getDeviceNameAndCount(studyId);

// if (response.isEmpty()) {
// LOG.warn("No results found for studyId: {}", studyId);
// return ResponseEntity.status(HttpStatus.BAD_REQUEST)
// .contentType(MediaType.APPLICATION_JSON)
// .body(Collections.singletonMap("error", "No Data Found"));
// }

// return ResponseEntity.ok()
// .contentType(MediaType.APPLICATION_JSON)
// .body(response);
// } catch (Exception e) {
// LOG.error("Error in /study/device-name-count: {}", e.getMessage(), e);
// return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
// .contentType(MediaType.APPLICATION_JSON)
// .body(Collections.singletonMap("error", "An error occurred while processing
// your request."));
// }
// }

// @Operation(summary = "Fetch All Participants")
// @PostMapping("/study/device-name-count/getAllParticipants/{studyId}")
// @ResponseBody
// public ResponseEntity<?> getAllParticipants(@PathVariable String studyId) {
// try {
// return ResponseEntity.ok(dataAccessService.getAllParticipants(studyId));
// } catch (RuntimeException e) {
// LOG.error("Error fetching participants for studyId {}: {}", studyId,
// e.getMessage(), e);
// return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
// .body(Map.of("error", "Unable to fetch participants", "details",
// e.getMessage()));
// }
// }

// }
