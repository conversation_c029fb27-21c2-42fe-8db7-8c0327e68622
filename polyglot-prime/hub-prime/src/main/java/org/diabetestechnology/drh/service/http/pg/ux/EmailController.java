package org.diabetestechnology.drh.service.http.pg.ux;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.GitHubUserAuthorizationFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.constant.UserVerificationStatus;
import org.diabetestechnology.drh.service.http.pg.constant.email.OtpValidationRequest;
import org.diabetestechnology.drh.service.http.pg.constant.email.OtpValidationResponse;
import org.diabetestechnology.drh.service.http.pg.request.LoggedInUserEmailDetails;
import org.diabetestechnology.drh.service.http.pg.service.EmailService;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.diabetestechnology.drh.service.http.pg.service.email.EmailNotificationService;
import org.diabetestechnology.drh.service.http.pg.service.email.OtpService;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.mail.MessagingException;

@Controller
@Tag(name = "DRH Email Endpoints")
public class EmailController {
    private static final Logger LOG = LoggerFactory.getLogger(EmailController.class);

    private final EmailService emailService;
    private final OtpService otpService;
    private final EmailNotificationService emailNotificationService;
    private final PractitionerService practitionerService;
    private final UserNameService userNameService;
    private final Presentation presentation;
    private final MasterService masterService;
    private final GitHubUserAuthorizationFilter gitHubUserAuthorizationFilter;

    public EmailController(EmailService emailService, EmailNotificationService emailNotificationService,
            PractitionerService practitionerService, UserNameService userNameService,
            OtpService otpService, Presentation presentation, MasterService masterService,
            GitHubUserAuthorizationFilter gitHubUserAuthorizationFilter) {
        this.emailNotificationService = emailNotificationService;
        this.emailService = emailService;
        this.practitionerService = practitionerService;
        this.userNameService = userNameService;
        this.otpService = otpService;
        this.presentation = presentation;
        this.masterService = masterService;
        this.gitHubUserAuthorizationFilter = gitHubUserAuthorizationFilter;
    }

    @GetMapping("/send-test-email")
    @ResponseBody
    @Operation(summary = "Send a test email to a user")
    public Response sendTestEmail(@RequestParam String email, @RequestParam String name)
            throws UnsupportedEncodingException {
        try {
            final var otp = otpService.generateAndSendOtp(email);
            emailService.sendEmail(email, name, otp);
            return Response.builder()
                    .data(Map.of("data", "success"))
                    .status("success")
                    .message("Successfully send the emil")
                    .errors(null)
                    .build();

        } catch (MessagingException e) {
            e.printStackTrace();
            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Failed to send email: " + e.getMessage())
                    .errors(null)
                    .build();
        }
    }

    @PostMapping("/verify-otp")
    @ResponseBody
    @Operation(summary = "Verify OTP for a user")
    public Response verifyOtp(@RequestBody OtpValidationRequest otpValidationRequest) {

        try {
            final var statusId = masterService.getUserVerificationStatusId(UserVerificationStatus.PENDING);
            final var providerId = userNameService.getUserId();
            final var email = practitionerService.getEmailByUserId(providerId, statusId);
            LOG.info("Email fetched from DB {}", email);
            String verificationResult = otpService.verifyOtp(otpValidationRequest.email(), otpValidationRequest.otp());
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("email", otpValidationRequest.email());
            if (OtpValidationResponse.OTP_VERIFICATION_SUCCESS.equals(verificationResult)) {
                Boolean isEmailProfileExists = practitionerService.isUserEmailExists(otpValidationRequest.email());
                String existingProvider = practitionerService.getExistingProvider(otpValidationRequest.email());
                String currentProvider = userNameService.getUserProvider();
                responseMap.put("existingProvider", existingProvider);
                responseMap.put("currentProvider", currentProvider);
                LOG.info("Is email profile exists: {}", isEmailProfileExists);
                LOG.info("Existing provider: {}", existingProvider);
                LOG.info("Current provider: {}", currentProvider);

                if (isEmailProfileExists) {
                    final var provider = userNameService.getUserProvider();
                    JSONB linkAccountResponse = practitionerService.linkExternalAuthProvider(
                            otpValidationRequest.email(), provider,
                            providerId);
                    responseMap.put("profileExists", isEmailProfileExists);
                    responseMap.put("linkAccountResponse", linkAccountResponse);
                    responseMap.put("verifyOtpResponse", verificationResult);
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode node = mapper.readTree(linkAccountResponse.toString());
                    String status = node.path("status").asText();
                    if (status.equalsIgnoreCase("Success")) {
                        responseMap.put("responsePage", "accountLinking");
                        boolean refreshSuccess = gitHubUserAuthorizationFilter.refreshUserPermissionsInSession();
                        if (refreshSuccess) {
                            LOG.info("User session permissions successfully refreshed.");
                        } else {
                            LOG.warn(
                                    "Failed to refresh user session permissions. No request or authenticated user found.");
                        }
                        return Response.builder()
                                .data(Map.of("response",
                                        responseMap))
                                .status("success")
                                .message(
                                        "A profile with this email already exists and is linked to an existing account.")
                                .errors(null)
                                .build();
                    } else {
                        responseMap.put("responsePage", "verifyEmail");
                        return Response.builder()
                                .data(Map.of("response",
                                        responseMap))
                                .status("failed")
                                .message(
                                        "OTP verification completed, however, email linking was unsuccessful. Please retry.")
                                .errors(node.path("message").asText())
                                .build();
                    }

                } else {
                    final var userName = presentation.getUsername().toString();
                    LOG.info("Username fetched from session {}", userName);
                    ;
                    JSONB response = emailNotificationService.upsertEmailDetails(otpValidationRequest.email(),
                            providerId,
                            otpValidationRequest.otp(),
                            UserVerificationStatus.COMPLETED);
                    responseMap.put("message", "OTP verified successfully");
                    responseMap.put("status", "success");
                    responseMap.put("saveUserResponse", response.data());
                    responseMap.put("responsePage", "createProfile");
                    responseMap.put("profileExists", isEmailProfileExists);
                    return Response.builder()
                            .data(Map.of("response",
                                    responseMap))
                            .status("success")
                            .message("Practitioner profile not found. Please create a new account to continue.")
                            .errors(null)
                            .build();

                }
                // OTP verification successful, proceed with further actions if needed

            } else if (OtpValidationResponse.OTP_EXPIRED.equals(verificationResult)) {
                responseMap.put("verifyOtpResponse", verificationResult);
                responseMap.put("responsePage", "verifyOtp");
                return Response.builder()
                        .data(Map.of("response", responseMap))
                        .status("error")
                        .message("Your OTP has expired. Please get a new one to proceed.")
                        .errors(null)
                        .build();
            } else if (OtpValidationResponse.OTP_NOT_FOUND.equals(verificationResult)) {
                responseMap.put("responsePage", "verifyOtp");
                responseMap.put("verifyOtpResponse", verificationResult);

                return Response.builder()
                        .data(Map.of("data", responseMap))
                        .status("error")
                        .message("We couldn't find an OTP linked to this email. Please try again.")
                        .errors(null)
                        .build();
            } else if (OtpValidationResponse.OTP_MAX_ATTEMPTS_EXCEEDED.equals(verificationResult)) {
                responseMap.put("responsePage", "verifyOtp");
                responseMap.put("verifyOtpResponse", verificationResult);

                return Response.builder()
                        .data(Map.of("data", responseMap))
                        .status("error")
                        .message("You've reached the maximum number of attempts. Please request a new OTP.")
                        .errors(null)
                        .build();
            }
            responseMap.put("verifyOtpResponse", verificationResult);
            responseMap.put("responsePage", "verifyOtp");

            // Implement OTP verification logic here
            return Response.builder()
                    .data(Map.of("response", responseMap))
                    .status("failed")
                    .message("OTP verification failed. Please try again.")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            Map<String, Object> responseMap = new HashMap<>();
            LOG.error("Error verifying OTP: {}", e.getMessage(), e);
            responseMap.put("responsePage", "verifyOtp");

            return Response.builder()
                    .data(Map
                            .of("response", responseMap))
                    .status("error")
                    .message("OTP verification failed. Please try again.")
                    .errors(e.getMessage())
                    .build();
        }
    }

    @GetMapping("/exist-email")
    @ResponseBody
    @Operation(summary = "Check if email exists")
    public Response isEmailExist(@RequestParam String email) {
        Boolean isProfileExists = practitionerService.isUserExists();
        Boolean exists = emailNotificationService.isEmailExist(email);
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("emailExists", exists);
        responseMap.put("isProfileExists", isProfileExists);
        return Response.builder()
                .data(responseMap)
                .status("success")
                .message("Email existence check completed")
                .errors(null)
                .build();
    }

    @PostMapping("/email")
    @ResponseBody
    @Operation(summary = "Check if email exists")
    public Response saveEmailDetails(@RequestBody LoggedInUserEmailDetails emailDetails) {
        try {
            final var otp = otpService.generateAndSendOtp(emailDetails.email());
            final var providerId = userNameService.getUserId();
            LOG.info("Provider ID: {}", providerId);
            final var userName = presentation.getUsername().toString();
            emailService.sendEmail(emailDetails.email(), userName, otp);
            JSONB response = emailNotificationService.upsertEmailDetails(emailDetails.email(),
                    providerId, otp,
                    UserVerificationStatus.PENDING);
            Map<String, Object> responseMap = new HashMap<>();
            if (response == null) {
                responseMap.put("status", "error");
                responseMap.put("message", "Failed to save email details");
                return Response.builder()
                        .data(new HashMap<>())
                        .status("error")
                        .message("Failed to save email details")
                        .errors(null)
                        .build();
            }
            responseMap.put("status", "success");
            responseMap.put("email", emailDetails.email());
            responseMap.put("message", "Email details saved successfully");
            Boolean isEmailProfileExists = practitionerService.isUserEmailExists(emailDetails.email());
            if (isEmailProfileExists) {
                responseMap.put("emailExists", true);
                responseMap.put("existingUserName", practitionerService.getUserNameByEmail(emailDetails.email()));
            } else {
                responseMap.put("emailExists", false);
            }
            responseMap.put("saveUserResponse", response.data());

            return Response.builder()
                    .data(responseMap)
                    .status("success")
                    .message("Email existence check completed and OTP has been sent to email")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error saving email details: {}", e.getMessage(), e);
            return Response.builder()
                    .data(new HashMap<>())
                    .status("error")
                    .message("Failed to save email details: " + e.getMessage())
                    .errors(null)
                    .build();
        }
    }
}
