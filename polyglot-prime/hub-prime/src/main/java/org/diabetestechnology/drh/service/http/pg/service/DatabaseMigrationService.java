package org.diabetestechnology.drh.service.http.pg.service;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.PostConstruct;

@Service
public class DatabaseMigrationService {
    private final S3FileUploadService s3FileUploadService;
    private static final Logger LOG = LoggerFactory.getLogger(DatabaseMigrationService.class);
    private final DSLContext duckDsl;
    private final String sqliteDbName = "sqlite_study_db";
    private final String postgresDbName = "postgres_study_db";
    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;

    public DatabaseMigrationService(S3FileUploadService s3FileUploadService, @Qualifier("duckDsl") DSLContext duckDsl,
            @Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService, PartyService partyService) {
        this.s3FileUploadService = s3FileUploadService;
        this.duckDsl = duckDsl;
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
    }

    @PostConstruct
    public void init() {
        try {
            duckDsl.execute("DROP SCHEMA IF EXISTS " + postgresDbName + " CASCADE;");
            duckDsl.execute("INSTALL postgres;");
            duckDsl.execute("LOAD postgres;");
            duckDsl.execute("INSTALL sqlite;");
            duckDsl.execute("LOAD sqlite;");

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public synchronized String uploadAndSaveDBFile(MultipartFile file, DatabaseMigrationRequest request)
            throws IOException {
        try {
            LOG.info("Uploading file: {}", file.getOriginalFilename());
            LOG.debug("SQLite Database File: {}", file.getOriginalFilename());
            final var fileURL = s3FileUploadService.uploadDBFileToS3(file, request);
            LOG.debug("S3 bucket URL after uploading SQLite DB file : {}", fileURL);
            if (fileURL == null) {
                LOG.error("Failed to upload SQLite Database File to S3.");
                throw new IllegalArgumentException("Failed to upload file to S3");
            }
            LOG.info("SQLite Database File uploaded to S3 Completed. URL is : {}", fileURL);
            boolean exist = exists(request.studyId());
            LOG.debug("Database exists status: {}", exist);
            if (exist) {
                LOG.error("Database already exists for study: {}", request.studyId());
                return ("Database already exists for study: " + request.studyId());
            }

            final var tempFilePath = s3FileUploadService.saveFileToTempLocation(file);
            LOG.debug("Temporary location to which the database file copied: {}", tempFilePath);
            if (tempFilePath == null) {
                LOG.error("Failed to save file to temporary location");
                throw new IllegalArgumentException("Failed to save file to temporary location");
            }
            LOG.debug("File saved to temporary location: {}", tempFilePath);
            detachSqliteDatabase();
            detachPostgresDatabase();
            attachPostgresDatabase();
            attachSqliteDatabase(tempFilePath);
            final var distinctDbFileIds = duckDsl.fetchOne(
                    "SELECT DISTINCT db_file_id FROM " + sqliteDbName + ".file_meta_ingest_data")
                    .get("db_file_id", String.class);
            LOG.debug("Starting to copy tables from SQLite to Postgres");
            final var originalFileName = file.getOriginalFilename();
            @SuppressWarnings("null")
            final var fileName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var uploadTimestamp = ZonedDateTime.now(ZoneOffset.UTC)
                    .format(DateTimeFormatter.ISO_INSTANT);
            JSONB dbData = JSONB.valueOf(prepareJson(
                    distinctDbFileIds, fileName, fileURL, uploadTimestamp, file.getSize(),
                    request.studyId(), userPartyId, request.organizationPartyId()));
            final var copyTableResponse = copyTablesFromSqLiteToPostgres(request.studyId(), tempFilePath,
                    distinctDbFileIds, dbData);
            if (copyTableResponse.equals("The Sqlite Tables do not contains all the required fields")) {
                return "The SQLite tables do not contain all the required fields necessary for copying to the PostgreSQL table.";
            } else if (copyTableResponse.equals("Study display id mismatch between SQLite and Postgres")) {
                return "There is a mismatch between the Study Display ID in the database file and the Study to which the data is being uploaded.";
            } else if (copyTableResponse.equals(
                    "Failed to Copy SQLITE DB File Content to Postgres DB")) {
                return "Failed to Copy SQLITE DB File Content to Postgres DB.";
            } else if (copyTableResponse
                    .equals("The Sqlite Table for Participant do not contains data for all the required fields")) {
                LOG.debug("The Sqlite Table for Participant do not contains data for all the required field");
                return "The Sqlite Table for Participant do not contains data for all the required field";
            } else if (copyTableResponse.equals("The Sqlite Tables do not contains all the required fields")) {
                return "The Sqlite Tables do not contains all the required fields";
            } else if (copyTableResponse.equals("Proceed with Database Migration")) {
                LOG.debug("Starting to copy tables from SQLite to Postgres");
                migrateDdatabase(request.studyId(), tempFilePath, distinctDbFileIds, dbData);
                return "Database Migration is in Progress";
            } else {
                return "Failed to Migrate Database.";
            }
        } catch (Exception e) {
            LOG.error("Error while uploading file: {}", e.getMessage(), e);
            throw new IllegalArgumentException("Failed to migrate database: " + e.getMessage());
        }
    }

    private void detachSqliteDatabase() {
        if (isDatabaseAttached(sqliteDbName)) {
            LOG.info("Detaching SQLite database");
            duckDsl.execute("DETACH " + sqliteDbName + ";");
            LOG.info("SQLite database detached");
        } else {
            LOG.info("SQLite database " + sqliteDbName + " is not attached. Skipping detachment.");
        }
    }

    private void detachPostgresDatabase() {
        try {
            LOG.info("Checking if Postgres database '{}' is attached", postgresDbName);

            // Check if the database is attached
            String query = "SHOW DATABASES;";
            List<String> attachedDatabases = duckDsl.fetch(query)
                    .getValues(0, String.class);

            if (attachedDatabases.contains(postgresDbName)) {
                LOG.info("Detaching Postgres database '{}'", postgresDbName);
                duckDsl.execute("DETACH DATABASE " + postgresDbName + ";");
            } else {
                LOG.info("Postgres database '{}' is not attached, skipping detach", postgresDbName);
            }
        } catch (Exception e) {
            LOG.error("Error while detaching Postgres database: {}", e.getMessage(), e);
        }
    }

    private boolean isDatabaseAttached(String database) {
        String checkQuery = "PRAGMA database_list;";
        List<Record> attachedDatabases = duckDsl.fetch(checkQuery);

        boolean isDatabaseAttached = false;

        // Check if the database is attached
        for (Record db : attachedDatabases) {
            if (db.get("name").equals(sqliteDbName)) {
                isDatabaseAttached = true;
                break;
            }
        }
        return isDatabaseAttached;
    }

    private void attachPostgresDatabase() {
        try {
            LOG.info("Attaching Postgres database as {}", postgresDbName);
            duckDsl.execute("DROP SCHEMA IF EXISTS " + postgresDbName + " CASCADE;");
            duckDsl.execute(
                    "ATTACH '' AS " + postgresDbName + " (TYPE POSTGRES);");
            LOG.info("PostgreSQL database attached as: {}", postgresDbName);
        } catch (Exception e) {
            LOG.error("Error while attaching Postgres database: {}", e.getMessage(), e);
        }
    }

    private void attachSqliteDatabase(String tempFilePath) {
        LOG.info("Attaching SQLite database: {}", tempFilePath);
        duckDsl.execute("DROP SCHEMA IF EXISTS " + sqliteDbName + " CASCADE;");
        duckDsl.execute(
                " ATTACH '" + tempFilePath + "' AS " + sqliteDbName + " (TYPE SQLITE); ");
        LOG.info("SQLite database attached as: {}", tempFilePath);
    }

    private String copyTablesFromSqLiteToPostgres(String studyId, String filePath, String distinctDbFileIds,
            JSONB dbData) {
        LOG.info("Copying tables from SQLite to Postgres");
        try {
            LOG.info("Distinct db_file_ids from SQLITE: {}", distinctDbFileIds);
            final var sqStudyDisplayId = duckDsl.fetchOne(
                    "SELECT DISTINCT study_display_id FROM " + sqliteDbName + ".participant")
                    .get("study_display_id", String.class);
            LOG.info("Study Display Id from SQLITE: {}", sqStudyDisplayId);
            final var pgStudyDisplayId = dsl
                    .selectDistinct(DSL.field("study_display_id", String.class))
                    .from("drh_stateless_research_study.research_study_view")
                    .where(DSL.field("study_id").eq(DSL.value(
                            studyId)))
                    .fetchOneInto(String.class);
            LOG.info("Study Display Id from POSTGRES: {}", pgStudyDisplayId);
            LOG.debug("dbFileId: {}", distinctDbFileIds);
            if (!(sqStudyDisplayId.toString()).equals(pgStudyDisplayId.toString())) {
                LOG.debug("Study display id mismatch between SQLite and Postgres, SQLite: {}, Postgres: {}",
                        sqStudyDisplayId, pgStudyDisplayId);
                return "Study display id mismatch between SQLite and Postgres";
            } else {
                if (!validateRequiredColumns(filePath)) {
                    LOG.error("The Sqlite Tables do not contains all the required fields");
                    return "The Sqlite Tables do not contains all the required fields";
                } else if (!validateParticipantData(filePath)) {
                    LOG.error("The Sqlite Table for Participant do not contains data for all the required fields");
                    return "The Sqlite Table for Participant do not contains data for all the required fields";
                } else {
                    LOG.info("Database is valid");

                    return "Proceed with Database Migration";

                }
            }

        } catch (Exception e) {
            LOG.error("Error while copying tables from SQLite to Postgres: {}",
                    e.getMessage(), e);
            throw new IllegalArgumentException("Failed to copy tables from SQLite to Postgres: " + e.getMessage());
        }

    }

    @Async
    private CompletableFuture<String> migrateDdatabase(String studyId, String filePath, String distinctDbFileIds,
            JSONB dbData) {
        return CompletableFuture.supplyAsync(() -> {
            LOG.info("Copying file_meta_ingest_data . : {}", "INSERT INTO " + postgresDbName
                    + ".drh_stateful_db_import_migration.file_meta_ingest_data (file_meta_id, db_file_id, participant_display_id, file_meta_data, cgm_data) "
                    +
                    "SELECT file_meta_id,db_file_id, participant_display_id, file_meta_data, cgm_data FROM "
                    + sqliteDbName
                    + ".file_meta_ingest_data; ");
            LOG.info("Copying file_meta_ingest_data, Number of records: {}", duckDsl.fetch(
                    "SELECT DISTINCT COUNT(*) FROM " + sqliteDbName + ".file_meta_ingest_data"));
            duckDsl.execute(
                    "INSERT INTO " + postgresDbName
                            + ".drh_stateful_db_import_migration.file_meta_ingest_data (file_meta_id, db_file_id, participant_display_id, file_meta_data, cgm_data) "
                            +
                            "SELECT file_meta_id,db_file_id, participant_display_id, file_meta_data, cgm_data FROM "
                            + sqliteDbName
                            + ".file_meta_ingest_data ");

            LOG.info("Copying participant . : {}", "INSERT INTO " + postgresDbName
                    + ".drh_stateful_db_import_migration.participant (db_file_id, tenant_id, study_display_id, participant_display_id, site_id, diagnosis_icd, med_rxnorm, treatment_modality, gender, race_ethnicity, age, bmi, baseline_hba1c, diabetes_type, study_arm) "
                    +
                    "SELECT db_file_id, tenant_id, study_display_id, participant_display_id, site_id, diagnosis_icd, med_rxnorm, treatment_modality, gender, race_ethnicity, age, bmi, baseline_hba1c, diabetes_type, study_arm FROM "
                    + sqliteDbName + ".participant ;");
            LOG.info("Copying participant, Number of records: {}", duckDsl.fetch(
                    "SELECT DISTINCT COUNT(*) FROM " + sqliteDbName + ".participant"));
            duckDsl.execute(
                    "INSERT INTO " + postgresDbName
                            + ".drh_stateful_db_import_migration.participant (db_file_id, tenant_id, study_display_id, participant_display_id, site_id, diagnosis_icd, med_rxnorm, treatment_modality, gender, race_ethnicity, age, bmi, baseline_hba1c, diabetes_type, study_arm) "
                            +
                            "SELECT db_file_id, tenant_id, study_display_id, participant_display_id, site_id, diagnosis_icd, med_rxnorm, treatment_modality, gender, race_ethnicity, age, bmi, baseline_hba1c, diabetes_type, study_arm FROM "
                            + sqliteDbName + ".participant ;");
            LOG.info("Tables copied from SQLite to Postgres");

            LOG.info("Fetching db_file_id from Postgres");
            Boolean exists = dsl.select(DSL.field("exists(" +
                    "select 1 from drh_stateful_db_import_migration.participant " +
                    "where db_file_id = {0})", Boolean.class, DSL.val(distinctDbFileIds)))
                    .fetchOneInto(Boolean.class);
            LOG.debug("Migrated Revords availablity status from Postgres: {}", exists);
            if (!exists) {
                LOG.error(
                        "Failed to Copy SQLITE DB File Content to Postgres DB");
                return "Failed to Copy SQLITE DB File Content to Postgres DB";
            } else {
                LOG.info("Save Data from database Query: {}", dsl
                        .select(DSL.field(
                                "drh_stateless_raw_observation.insert_cgm_raw_db ({0})",
                                String.class,
                                DSL.val(dbData))));
                JSONB response = dsl
                        .select(DSL.field(
                                "drh_stateless_raw_observation.insert_cgm_raw_db ({0})",
                                String.class,
                                DSL.val(dbData)))
                        .fetchOneInto(JSONB.class);
                LOG.info("Response from database Query: {}", response);
                LOG.debug("Response from database Query: {}", response);

                LOG.info("File copied to Postgres");
                LOG.info("Successfully copied tables from SQLite to Postgres");
                detachSqliteDatabase();
                detachPostgresDatabase();
                s3FileUploadService.deleteTempFile(filePath);
                LOG.info("Temporary file deleted: {}", filePath);
                LOG.info("Successfully migrated database");

                return "Successfully copied tables from SQLite to Postgres";
            }
        });
    }

    private String prepareJson(String fileId, String fileName, String fileURL, String uploadTimestamp, long fileSize,
            String studyId, String userPartyId, String organizationPartyId) {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode jsonObject = objectMapper.createObjectNode();
        jsonObject.put("db_file_id", fileId);
        jsonObject.put("file_name", fileName);
        jsonObject.put("file_url", fileURL);
        jsonObject.put("upload_timestamp", uploadTimestamp);
        jsonObject.put("uploaded_by", userPartyId);
        jsonObject.put("file_size", fileSize);
        jsonObject.put("study_id", studyId);
        jsonObject.put("org_party_id", organizationPartyId);
        jsonObject.put("current_user_id", userPartyId);
        return jsonObject.toString();
    }

    private boolean exists(String studyd) {
        return dsl.fetchExists(
                dsl.selectOne().from("drh_stateless_raw_data.cgm_raw_db_view").where("study_id = ?", studyd));
    }

    public DSLContext createSQLiteDSL(String sqliteFilePath) throws Exception {
        String url = "jdbc:sqlite:" + sqliteFilePath;
        Connection conn = DriverManager.getConnection(url);
        return DSL.using(conn, SQLDialect.SQLITE);
    }

    public boolean validateRequiredColumns(String filePath) throws Exception {
        // Define required columns for both tables
        Map<String, Set<String>> tableColumnsMap = Map.of(
                "file_meta_ingest_data",
                Set.of("file_meta_id", "db_file_id", "participant_display_id", "file_meta_data", "cgm_data"),
                "participant",
                Set.of("db_file_id", "tenant_id", "study_display_id", "participant_display_id", "site_id",
                        "diagnosis_icd", "med_rxnorm", "treatment_modality", "gender", "race_ethnicity", "age",
                        "bmi", "baseline_hba1c", "diabetes_type", "study_arm"));
        DSLContext sqliteDsl = createSQLiteDSL(filePath.toString());
        // Validate each table
        for (Map.Entry<String, Set<String>> entry : tableColumnsMap.entrySet()) {
            String tableName = entry.getKey();
            Set<String> requiredColumns = entry.getValue();

            String query = "PRAGMA table_info(" + tableName + ")";
            Result<Record> result = sqliteDsl.fetch(query);
            LOG.info("Record of Table : {} {}", tableName, result);

            // Extract column names
            Set<String> existingColumns = new HashSet<>();
            LOG.info("Reading existing columns of table {}", tableName);
            for (Record record : result) {
                LOG.info("Column : {}.{}", tableName, record.get("name", String.class));
                existingColumns.add(record.get("name", String.class));
            }

            // If any table is missing required columns, return false
            if (!existingColumns.containsAll(requiredColumns)) {
                Set<String> missingColumns = new HashSet<>(requiredColumns);
                missingColumns.removeAll(existingColumns); // Get missing columns

                LOG.warn("Missing required columns: {} of Table: {}", missingColumns, tableName);
                LOG.debug("Table: {}, Existing columns: {}", tableName, existingColumns);
                LOG.debug("Table: {},Required columns: {}", tableName, requiredColumns);
                LOG.debug("Table: {},Missing columns: {}", tableName, missingColumns);
                return false;
            }
        }

        return true; // Both tables have all required columns
    }

    public boolean validateParticipantData(String filePath) throws Exception {
        DSLContext sqliteDsl = createSQLiteDSL(filePath.toString());

        // Query to check if any row has NULL or empty values in required columns
        String query = "SELECT COUNT(*) FROM participant " +
                "WHERE participant_display_id IS NULL OR TRIM(participant_display_id) = '' " +
                "OR age IS NULL OR TRIM(age) = '' " +
                "OR gender IS NULL OR TRIM(gender) = ''";

        Integer count = sqliteDsl.fetchOne(query).into(Integer.class);

        if (count > 0) {
            LOG.debug(
                    "{} rows in the participant table have missing mandatory data (participant_display_id, age, gender).",
                    count);
            return false;
        }

        return true;
    }
}
