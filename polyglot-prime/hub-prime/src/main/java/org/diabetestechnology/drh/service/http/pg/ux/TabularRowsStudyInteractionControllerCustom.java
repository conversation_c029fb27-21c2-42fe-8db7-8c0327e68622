package org.diabetestechnology.drh.service.http.pg.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionStatus;
import org.diabetestechnology.drh.service.http.pg.filter.CustomTabularFilter;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.diabetestechnology.drh.udi.UdiSecondaryDbConfig;
import org.diabetestechnology.drh.pg.udi.auto.jooq.ingress.Tables;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Nonnull;
import jakarta.validation.constraints.NotNull;
import lib.aide.tabular.JooqRowsSupplier;
import lib.aide.tabular.JooqRowsSupplier.TypableTable;

import lib.aide.tabular.TabularRowsRequest;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.jooq.*;
import org.jooq.Record;

@Controller
@Tag(name = "DRH Hub Custom Tabular Row Study Interaction API Endpoints for AG Grid")
@org.springframework.context.annotation.Configuration
public class TabularRowsStudyInteractionControllerCustom {
    static private final Logger LOG = LoggerFactory.getLogger(TabularRowsStudyInteractionControllerCustom.class);

    private final UdiSecondaryDbConfig udiPrimeDbConfig;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final CustomTabularFilter customTabularFilter;

    @Bean
    public com.fasterxml.jackson.databind.Module jsonbModule() {
        com.fasterxml.jackson.databind.module.SimpleModule module = new SimpleModule();
        module.addSerializer(org.jooq.JSONB.class, new JsonSerializer<org.jooq.JSONB>() {
            @Override
            public void serialize(org.jooq.JSONB value, JsonGenerator gen, SerializerProvider serializers)
                    throws IOException {
                gen.writeRawValue(value.data()); // or gen.writeString(value.data());
            }
        });
        return module;
    }

    public TabularRowsStudyInteractionControllerCustom(final UdiSecondaryDbConfig udiPrimeDbConfig,
            UserNameService userNameService,
            PartyService partyService, CustomTabularFilter customTabularFilter) {
        this.udiPrimeDbConfig = udiPrimeDbConfig;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.customTabularFilter = customTabularFilter;
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "Successful Study Interaction")
    @PostMapping(value = "/api/ux/tabular/jooq/study/interaction/success/parent/{schemaName}/{masterTableNameOrViewName}.json")
    @ResponseBody
    public Object parentSuccessStudyInteraction(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            List<Field<?>> allFields = Arrays.asList(typableTable.table().fields());
            List<Field<?>> safeFields = allFields.stream()
                    .filter(Objects::nonNull) // Extra safety
                    .map(DSL::field)
                    .collect(Collectors.toList());
            final var selectedFields = new ArrayList<>(safeFields);
            Condition finalCondition = payload.filterModel() != null
                    ? customTabularFilter.createCondition(payload.filterModel())
                    : null;
            var bindValues = new ArrayList<Object>();
            bindValues.add("[]");
            final var organizationPartyId = userNameService.getCurrentUserOrganizationPartyId();
            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .select(selectedFields) // Selecting the specified fields
                    .from(typableTable.table())
                    .where("1=1");
            if (finalCondition != null) {
                query = query.and(finalCondition);
            }
            query = query.and(DSL.field("interaction_hierarchy", SQLDataType.JSONB)
                    .eq(DSL.val("[]", SQLDataType.JSONB))
                    .or(DSL.field("interaction_hierarchy").isNull()))
                    .and(DSL.field("study_id").isNotNull())
                    .and(DSL.field("study_display_id").isNotNull())
                    .and(DSL.field("organization_party_id").eq(DSL.val(organizationPartyId)));
            bindValues.add(organizationPartyId);

            if (!userNameService.isAdmin()) {

                final var createdBy = userNameService.getCurrentuserPartyId();
                // Construct the jOOQ query
                query = query
                        .and(DSL.field("created_by").eq(DSL.val(createdBy)));
                bindValues.add(createdBy);
            }

            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@NotNull SelectConditionStep<Record>) query.orderBy(
                        customTabularFilter.createSortCondition(payload.sortModel(),
                                typableTable));
            }
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "Interaction details of a successful study_interaction_id value")
    @GetMapping("/api/ux/tabular/jooq/study/interaction/success/child/{schemaName}/{masterTableNameOrViewName}/study_interaction_id/{studyInteractionId}.json")
    @ResponseBody
    public Object childSuccessStudyInteraction(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName, final @PathVariable String studyInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where((typableTable.column("study_interaction_id").eq(DSL.value(
                        studyInteractionId)))
                        .or((DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                                .eq(DSL.val("\"" + studyInteractionId + "\"")))))
                .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.SUCCESS)));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @Operation(summary = "Details of a successful study_interaction_id value")
    @GetMapping("/api/ux/tabular/jooq/study/interaction/child/modal/{schemaName}/{masterTableNameOrViewName}/study_interaction_id/{studyInteractionId}.json")
    @ResponseBody
    public Object childStudyInteractionModal(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName, final @PathVariable String studyInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where(typableTable.column("study_interaction_id").eq(DSL.value(
                        studyInteractionId)));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "Failed Study Interaction")
    @PostMapping(value = "/api/ux/tabular/jooq/study/interaction/failed/parent/{schemaName}/{masterTableNameOrViewName}.json")
    @ResponseBody
    public Object parentFailedStudyInteraction(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            TypableTable typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            List<Field<?>> allFields = Arrays.asList(typableTable.table().fields());
            List<Field<?>> safeFields = allFields.stream()
                    .filter(Objects::nonNull) // Extra safety
                    .map(DSL::field)
                    .collect(Collectors.toList());
            final var selectedFields = new ArrayList<>(safeFields);

            var bindValues = new ArrayList<Object>();

            final var userId = userNameService.getUserId();
            final var organizationId = partyService.getOrganizationPartyIdByUser(userId);
            Field<String> studyInteractionId = DSL.field("study_interaction_id", SQLDataType.VARCHAR);
            Field<String> studyId = DSL.field("study_id", SQLDataType.VARCHAR);
            // Field<String> interactionStatus = DSL.field("interaction_status",
            // SQLDataType.VARCHAR);
            Field<String> organizationPartyId = DSL.field("organization_party_id", SQLDataType.VARCHAR);
            Field<String> createdBy = DSL.field("created_by", SQLDataType.VARCHAR);
            Condition finalCondition = payload.filterModel() != null
                    ? customTabularFilter.createCondition(payload.filterModel())
                    : null;
            // Subquery: extract first element from interaction_hierarchy and compare
            Select<Record1<String>> subquery = DSL.select(
                    DSL.field("((jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]'))::jsonb #>> '{}')",
                            SQLDataType.VARCHAR))
                    .from(typableTable.table())
                    .where("interaction_status='" + ActionStatus.FAILED + "'");
            var query = udiPrimeDbConfig.dsl()
                    .select(selectedFields)
                    .from(typableTable
                            .table())
                    .where("1=1");
            if (finalCondition != null) {
                query = query.and(finalCondition);
            }
            query = query.and(
                    studyInteractionId.in(subquery)
                            .or(studyId.isNull()))
                    .and(organizationPartyId.eq(DSL.value(organizationId)));
            // bindValues.add(ActionStatus.FAILED);
            bindValues.add(organizationId);
            if (!userNameService.isAdmin()) {

                final var currentUser = userNameService.getCurrentuserPartyId();

                if (StringUtils.hasText(currentUser)) {
                    bindValues.add(currentUser);
                    query = query
                            .and(createdBy.eq(DSL.value(currentUser)));
                }

            }
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@NotNull SelectConditionStep<Record>) query.orderBy(
                        customTabularFilter.createSortCondition(payload.sortModel(),
                                typableTable));
            }
            LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "Details of a failed study_interaction_id value")
    @GetMapping("/api/ux/tabular/jooq/study/interaction/failed/child/{schemaName}/{masterTableNameOrViewName}/study_interaction_id/{studyInteractionId}.json")
    @ResponseBody
    public Object childFailedStudyInteraction(final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName, final @PathVariable String studyInteractionId) {

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where((typableTable.column("study_interaction_id").eq(DSL.value(
                        studyInteractionId)))
                        .or((DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                                .eq(DSL.val("\"" + studyInteractionId + "\"")))))
                .and(DSL.field("interaction_status").eq(DSL.val(ActionStatus.FAILED)));
        LOG.info("Get Study Interaction Details Corresponds to the schema {}:", schemaName);
        LOG.info("Get Study Interaction Details Corresponds to a Query {}:", query.getSQL());
        // Execute the query and return the result
        return query.fetch().intoMaps();
    }

}
