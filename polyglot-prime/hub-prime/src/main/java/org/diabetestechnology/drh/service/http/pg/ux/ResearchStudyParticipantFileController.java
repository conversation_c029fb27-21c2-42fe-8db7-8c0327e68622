package org.diabetestechnology.drh.service.http.pg.ux;

import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyParticipantFileService;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.exceptions.CsvException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;
import jakarta.validation.Valid;

import java.io.IOException;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.ParticipantFileDataRequest;

@Controller
@Tag(name = "DRH Research Study Paricipant File APIs")
public class ResearchStudyParticipantFileController {
    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyParticipantFileController.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final ResearchStudyParticipantFileService researchStudyParticipantFileService;
    private final ResearchStudyService researchStudyService;
    private final UserNameService userNameService;

    public ResearchStudyParticipantFileController(
            ResearchStudyParticipantFileService researchStudyParticipantFileService,
            ResearchStudyService researchStudyService,
            UserNameService userNameService) {
        this.researchStudyParticipantFileService = researchStudyParticipantFileService;
        this.researchStudyService = researchStudyService;
        this.userNameService = userNameService;
    }

    @PostMapping("/participant/file/upload")
    @Operation(summary = "Upload a participant data csv file to S3")
    @ResponseBody
    public Response uploadFile(@RequestPart("file") @Nonnull MultipartFile file,
            @Valid @RequestBody ParticipantFileDataRequest request) throws IOException, CsvException {

        if (!userNameService.getCurrentuserPartyId()
                .equalsIgnoreCase(researchStudyService.getStudyOwner(request.studyId()))) {
            LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                    request.studyId());
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Access denied: Only the study owner is permitted to edit this research study.")
                    .errors(null)
                    .build();
        }
        if (researchStudyService.getResearchStudyArchiveStatus(request.studyId())) {
            LOG.warn("Access denied: Study {} is archived.", request.studyId());

            Response response = Response.builder()
                    .status("failure")
                    .message("The study is archived; edits and updates are not allowed.")
                    .build();

            return response;
        }

        LOG.info("Uploading file: {}", file.getOriginalFilename());
        JSONB result = researchStudyParticipantFileService.uploadAndSaveParticipantFile(file, request);
        String responseString = result.toString();
        JsonNode responseJson = OBJECT_MAPPER.readTree(responseString);

        if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
            String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                    : "Unknown error occurred.";
            JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message(errorMessage)
                    .errors(errorDetails != null ? errorDetails.toString() : null)
                    .build();
        }
        return Response.builder()
                .data(Map.of("result", JsonUtils.jsonStringToMapOrList(result.data())))
                .status("success")
                .message(
                        "Participant data file upload completed.")
                .errors(null)
                .build();

    }

    @GetMapping("/participant/meals-file/template")
    @ResponseBody
    @Operation(summary = "Download CSV template for participant meals details")
    public ResponseEntity<byte[]> downloadParticipantMealsTemplate(
            @RequestParam(required = false, defaultValue = "csv") String format) throws IOException {

        if (!"csv".equalsIgnoreCase(format)) {
            return ResponseEntity.badRequest().build();
        }
        byte[] csvBytes = researchStudyParticipantFileService.generateParticipantMealsCsvTemplate();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"participant_meals_template.csv\"")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
                .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                .body(csvBytes);
    }

    @GetMapping("/participant/fitness-file/template")
    @ResponseBody
    @Operation(summary = "Download CSV template for participant fitness details")
    public ResponseEntity<byte[]> downloadParticipantFitnessTemplate(
            @RequestParam(required = false, defaultValue = "csv") String format) throws IOException {

        if (!"csv".equalsIgnoreCase(format)) {
            return ResponseEntity.badRequest().build();
        }
        byte[] csvBytes = researchStudyParticipantFileService.generateParticipantFitnessCsvTemplate();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"participant_fitness_template.csv\"")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
                .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                .body(csvBytes);
    }
}
