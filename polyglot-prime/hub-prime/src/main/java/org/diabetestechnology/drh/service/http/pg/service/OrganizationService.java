package org.diabetestechnology.drh.service.http.pg.service;

import org.diabetestechnology.drh.service.http.pg.request.OrganizationRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class OrganizationService {

    private final DSLContext dsl;

    public OrganizationService(@Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    private static final Logger LOG = LoggerFactory.getLogger(OrganizationService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Transactional
    public JSONB saveOrganization(OrganizationRequest request) throws JsonProcessingException {

        LOG.info("Saving organization: {}", request);
        try {
            LOG.debug("Converting identifierSystemValue to JSON...");
            String identifierSystemJson = objectMapper.writeValueAsString(request.identifierSystemValue());

            // Convert the JSON string to JSONB type
            JSONB identifierSystemJsonb = JSONB.valueOf(identifierSystemJson);
            LOG.debug("Converted identifierSystemValue: {}", identifierSystemJson);

            LOG.info("Calling database function to create organization...");
            JSONB result = dsl
                    .select(DSL.field(
                            "drh_stateless_research_study.create_organization({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11})",
                            String.class,
                            DSL.val(request.name()),
                            DSL.val(identifierSystemJsonb),
                            DSL.val(request.alias()),
                            DSL.val(request.typeCode()),
                            DSL.val(request.typeDisplay()),
                            DSL.val(request.city()),
                            DSL.val(request.state()),
                            DSL.val(request.country()),
                            DSL.val(request.websiteUrl()),
                            DSL.val(request.createdBy()),
                            DSL.val(request.latitude()),
                            DSL.val(request.longitude())))
                    .fetchOneInto(JSONB.class);
            LOG.info("Organization successfully created: {}", result);
            JSONObject jsonObject = new JSONObject(result.data());
            String message = jsonObject.optString("message", "No message found");
            String status = jsonObject.optString("status", "No status found");

            LOG.info("Extracted Message: {}", message);
            LOG.info("Extracted Status: {}", status);
            if (status.equalsIgnoreCase("success"))
                return result;
            else if (message.equalsIgnoreCase("Organization already exists")) {
                String organizationPartyId = dsl
                        .select(DSL.field(
                                "organization_party_id"))
                        .from("drh_stateless_research_study.organization_party_view")
                        .where(
                                DSL.field("organization_name").eq(DSL.val(request.name())))
                        .fetchOneInto(String.class);
                LOG.info("Organization party ID: {}", organizationPartyId);
                JSONObject response = new JSONObject();
                response.put("organizationPartyId", organizationPartyId);
                response.put("message", "Organization already exists");
                LOG.info("Organization already exists: {}", response);
                return JSONB.valueOf(response.toString());
            }
            return result;
        } catch (JsonProcessingException e) {
            LOG.error("JSON processing error while saving organization: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process organization request", e);
        } catch (Exception e) {
            LOG.error("Unexpected error while saving organization: {}", e.getMessage(), e);
            e.printStackTrace();
            return JSONB.valueOf("{}");
        }
    }

    @Transactional
    public JSONB searchOrganization(String request) {
        LOG.info("Searching organization: {}", request);
        JSONB jsonbResult = dsl
                .select(DSL.field(
                        "jsonb_agg(" +
                                "jsonb_build_object(" +
                                "'organization_id', organization_id, " +
                                "'organization_party_id', organization_party_id, " +
                                "'organization_name', organization_name, " +
                                "'organization_alias', organization_alias, " +
                                "'organization_type_code', organization_type_code, " +
                                "'organization_type_display', organization_type_display, " +
                                "'organization_city', organization_city, " +
                                "'organization_state', organization_state, " +
                                "'organization_country', organization_country, " +
                                "'organization_website_url', organization_website_url" +
                                ")" +
                                ")",
                        JSONB.class))
                .from("drh_stateless_research_study.organization_party_view")
                .where(DSL.condition(
                        "((? IS NULL OR organization_name ILIKE CONCAT('%', ?, '%')) " +
                                "OR (? IS NULL OR organization_alias ILIKE CONCAT('%', ?, '%')))",
                        DSL.val(request), DSL.val(request), DSL.val(request), DSL.val(request)))
                .fetchOneInto(JSONB.class);
        if (jsonbResult == null) {
            return JSONB.valueOf("{}"); // Return empty JSONB if the query result is null
        }
        return jsonbResult;
    }

}
