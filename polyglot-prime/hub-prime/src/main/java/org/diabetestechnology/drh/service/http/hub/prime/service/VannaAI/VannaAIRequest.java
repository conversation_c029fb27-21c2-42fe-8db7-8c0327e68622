package org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI;

import java.time.LocalDateTime;

// @formatter:off
public record VannaAIRequest(
        Long id, // Primary key for the entry, nullable
        String question, // Foreign key linking to the question, nullable
        String sql, // Name of the field (e.g., "question", "query", "response")
        String result, // Value associated with the field
        LocalDateTime timestamp // Timestamp of the entry
) {
    public VannaAIRequest(
            String question, // Name of the field (e.g., "question", "query", "response")
            String sql, // Value associated with the field
            String result,
            LocalDateTime timestamp) {
        this(null, question, sql, result, timestamp);
    }

    public VannaAIRequest(
            String question, // Name of the field (e.g., "question", "query", "response")
            LocalDateTime timestamp) {
        this(null, question, null, null, timestamp);
    }

    public VannaAIRequest(String question, String sql, String result) {
        this(null, question, sql, result, null);
    }
}
// @formatter:on