package org.diabetestechnology.drh.service.http.pg.constant;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.ActivityDetails;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.ActivityType;

public class CollaborationMap {
    Map<String, ActivityDetails> collaborationMap = new HashMap<>();

    public Map<String, ActivityDetails> getCollaborationMap() {
        collaborationMap.put("nominatedPrincipalInvestigator",
                new ActivityDetails(ActivityType.STUDIES, "Nominated Principal Investigator"));
        collaborationMap.put("studyTeam",
                new ActivityDetails(ActivityType.STUDIES, "Study Team"));
        collaborationMap.put("coInvestigator",
                new ActivityDetails(ActivityType.STUDIES, "Co-Investigator"));
        collaborationMap.put("investigator",
                new ActivityDetails(ActivityType.STUDIES, "Investigator"));
        collaborationMap.put("author",
                new ActivityDetails(ActivityType.STUDIES, "Author"));

        return collaborationMap;
    }

}
