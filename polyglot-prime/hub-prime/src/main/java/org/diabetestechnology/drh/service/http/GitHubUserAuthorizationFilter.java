package org.diabetestechnology.drh.service.http;

import java.io.IOException;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;
import java.util.List;

import org.diabetestechnology.drh.service.http.hub.prime.LoginLogger;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLog;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLogService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.diabetestechnology.drh.service.http.pg.service.RolePermissionService;
import org.jetbrains.annotations.NotNull;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.filter.OncePerRequestFilter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class GitHubUserAuthorizationFilter extends OncePerRequestFilter {
    private static final Logger LOG = LoggerFactory.getLogger(GitHubUserAuthorizationFilter.class);
    private static final String AUTH_USER_SESSION_ATTR_NAME = "authenticatedUser";

    @Autowired
    AuditService auditService;

    @Autowired
    ActivityLogService activityLogService;

    @Autowired
    RolePermissionService rolePermissionService;

    @Autowired
    private @Qualifier("secondaryDsl") DSLContext dsl;

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record AuthenticatedUser(Object principal, OauthUsersService.AuthorizedUser ghUser,
            List<String> userPermissions)
            implements Serializable {
    }

    public static final Optional<AuthenticatedUser> getAuthenticatedUser(
            final @NonNull HttpServletRequest request) {
        final var sessionUser = (AuthenticatedUser) request.getSession(true)
                .getAttribute(AUTH_USER_SESSION_ATTR_NAME);
        return Optional.ofNullable(sessionUser);
    }

    public static final Optional<AuthenticatedUser> getAuthenticatedUser() {
        var request = getCurrentHttpRequest();
        return request.flatMap(GitHubUserAuthorizationFilter::getAuthenticatedUser);
    }

    private static Optional<HttpServletRequest> getCurrentHttpRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Optional.ofNullable(attributes).map(attr -> attr.getRequest());
    }

    protected static final void setAuthenticatedUser(final @NonNull HttpServletRequest request,
            final @NonNull AuthenticatedUser authUser) {
        request.getSession(true).setAttribute(AUTH_USER_SESSION_ATTR_NAME, authUser);
    }

    private final OauthUsersService oauthUsersService;

    public GitHubUserAuthorizationFilter(final OauthUsersService oauthUsersService) {
        this.oauthUsersService = oauthUsersService;
    }

    @SuppressWarnings("null")
    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
            @NotNull FilterChain filterChain)
            throws ServletException, IOException {
        try {
            final var sessionUser = getAuthenticatedUser(request);
            if (sessionUser.isEmpty()) {
                final var authentication = SecurityContextHolder.getContext().getAuthentication();
                LOG.info("Authentication: {}", authentication);
                if (authentication != null && authentication.isAuthenticated()
                        && !"anonymousUser".equals(authentication.getPrincipal().toString())) {

                    Object principal = authentication.getPrincipal();
                    String userName = "";
                    if (principal instanceof DefaultOAuth2User) {
                        // Handle OAuth2 authentication
                        DefaultOAuth2User oAuth2User = (DefaultOAuth2User) principal;
                        final var gitHubLoginId = Optional.ofNullable(oAuth2User.getAttribute("login")).orElseThrow();
                        LoginLogger.logSuccess(gitHubLoginId.toString());
                        LOG.info("Authenticated OAuth2 User: {}", gitHubLoginId);
                        final var gitHubAuthnUser = oauthUsersService.isAuthorizedUserDB(oAuth2User);
                        List<String> userPermissions = rolePermissionService.getPermissionsForUser();
                        setAuthenticatedUser(request,
                                new AuthenticatedUser(oAuth2User, gitHubAuthnUser.orElseThrow(), userPermissions));
                        saveAuditLog(request, response, oAuth2User);
                        final var name = Optional.ofNullable(oAuth2User.getAttribute("name")).orElseThrow();
                        activityLogService.saveLoginActivity(name.toString());
                        userName = gitHubLoginId.toString();
                    } else if (principal instanceof UserDetails) {
                        // Handle database authentication
                        UserDetails userDetails = (UserDetails) principal;
                        LoginLogger.logSuccess(userDetails.getUsername());
                        LOG.info("Authenticated Database User: {}", userDetails.getUsername());

                        // Create an AuthorizedUser for database users
                        Map<String, Object> attributes = new HashMap<>();
                        attributes.put("name", userDetails.getUsername());
                        attributes.put("login", userDetails.getUsername());
                        attributes.put("provider", "Database");
                        attributes.put("email", userDetails.getUsername());

                        final var org_party_id = activityLogService.getCurrentUserOrganizationPartyId();
                        OauthUsersService.AuthorizedUser dbUser = new OauthUsersService.AuthorizedUser(
                                userDetails.getUsername(), // name
                                userDetails
                                        .getUsername(), // emailPrimary
                                "/user.jpg", // profilePicUrl
                                userDetails.getUsername(), // userId
                                org_party_id, // tenantId
                                null, // resources
                                true // hasAdminMenu
                        );
                        List<String> userPermissions = rolePermissionService.getPermissionsForUser();

                        setAuthenticatedUser(request, new AuthenticatedUser(userDetails, dbUser, userPermissions));
                        activityLogService.saveLoginActivity(userDetails.getUsername());
                        userName = userDetails.getUsername();

                    }
                    String sessionId = request.getSession().getId();
                    final var userPartyId = activityLogService.getCurrentUserPartyId();
                    if (userPartyId == null) {
                        final var query = dsl.select(DSL.field(
                                "drh_stateless_activity_audit.save_session_audit_log(?,?)", DSL.cast(DSL.val(
                                        sessionId),
                                        String.class),
                                DSL.cast(DSL.val(userName), String.class)));
                        LOG.debug("Executing query: " + query);
                        final var result = query.fetchOneInto(JSONB.class);
                        LOG.debug("Result: " + result);
                    } else {
                        final var query = dsl.select(DSL.field(
                                "drh_stateless_activity_audit.save_session_audit_log(?,?,?)", DSL.cast(DSL.val(
                                        sessionId),
                                        String.class),
                                DSL.cast(DSL.val(userName), String.class),
                                DSL.cast(DSL.val(userPartyId), String.class)));
                        LOG.debug("Executing query: " + query);
                        final var result = query.fetchOneInto(JSONB.class);
                        LOG.debug("Result: " + result);
                    }

                } else {
                    LOG.info("Anonymous User Logged In");
                }
            }
        } catch (Exception ex) {
            LOG.error("GitHubUserAuthorizationFilter:doFilterInternal, Exception : {}", ex.getMessage());
        } finally {
            filterChain.doFilter(request, response);
        }
    }

    private void saveAuditLog(HttpServletRequest request, HttpServletResponse response,
            OAuth2User user)
            throws UnknownHostException, UnsupportedEncodingException, InterruptedException, JsonProcessingException {

        String provider = (String) user.getAttributes().get("provider");

        String requestUrl = "/";
        if (provider.equalsIgnoreCase("GitHub")) {
            requestUrl = "/oauth2/authorization/github";
        } else if (provider.equalsIgnoreCase("ORCID")) {
            requestUrl = "/oauth2/authorization/orcid";
        }

        ActivityLog activityLog = activityLogService.getAuditDataFromRequestAndResponse(requestUrl);
        activityLogService.saveActivityLog(activityLog);
    }

    public boolean refreshUserPermissionsInSession() {
        return getCurrentHttpRequest().map(request -> getAuthenticatedUser(request).map(existingUser -> {
            List<String> updatedPermissions = rolePermissionService.getPermissionsForUser();
            setAuthenticatedUser(request, new AuthenticatedUser(
                    existingUser.principal(),
                    existingUser.ghUser(),
                    updatedPermissions));
            LOG.info("User session permissions refreshed for user: {}", existingUser.ghUser().userId());
            return true;
        }).orElseGet(() -> {
            LOG.warn("No authenticated user found in session. Cannot refresh permissions.");
            return false;
        })).orElseGet(() -> {
            LOG.warn("No active HTTP request context. Cannot refresh permissions.");
            return false;
        });
    }

}
