package org.diabetestechnology.drh.service.http.pg.request;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

// @formatter:off
public record ParticipantDataRequest(

        @NotBlank(message = "Study ID is required") String studyId,
        @NotBlank(message = "Organization Party ID is required") String orgPartyId,
        @NotBlank(message = "Participant Display ID is required") String participantDisplayId,
        @NotBlank(message = "Gender ID is required") String genderId,
        @NotNull(message = "Age is required") @Min(value = 0, message = "Age must be 0 or a positive integer") Integer age,
        String diagnosisIcd,
        String medRxnorm,
        String treatmentModality,
        String raceId,
        String ethnicityId,
        Double bmi,
        Double baselineHba1c,
        String diabetesType,
        String studyArm) {
}
// @formatter:on