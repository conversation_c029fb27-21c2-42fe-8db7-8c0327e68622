package org.diabetestechnology.drh.service.http.pg.service.email;

import java.io.UnsupportedEncodingException;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.diabetestechnology.drh.service.http.pg.service.DbActivityService;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.mail.MessagingException;

@Service
public class EmailNotificationService {
    private static final Logger LOG = LoggerFactory.getLogger(EmailNotificationService.class);
    private final DSLContext dsl;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final MasterService masterService;
    private final DbActivityService activityLogService;

    public EmailNotificationService(@Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService, AuditService auditService, OtpService otpService,
            MasterService masterService, DbActivityService activityLogService) {
        this.dsl = dsl;
        this.masterService = masterService;
        this.activityLogService = activityLogService;
    }

    public boolean isEmailExist(String email) {
        try {
            Boolean exists = dsl
                    .select(DSL.value(true))
                    .from("drh_stateless_authentication.user_account_verification_log")
                    .where(DSL.field("email").eq(DSL.val(email)))
                    .limit(1) // To ensure we only check if at least one row exists
                    .fetchOptional() // Returns Optional<Boolean>
                    .isPresent(); // Check if the result is present
            return exists;
        } catch (Exception e) {
            LOG.error("Error fetching user details: {}", e.getMessage(), e);
            return false;
        }
    }

    public JSONB upsertEmailDetails(String email, String providerId, String otp,
            String verificationStatus)
            throws UnsupportedEncodingException, MessagingException, JsonProcessingException {
        LOG.info("Upserting email details for user: {}, providerId: {}, otp: {}", email, providerId,
                otp);
        // fetch status from drh_stateless_master.user_verification_status_view
        final var statusId = masterService.getUserVerificationStatusId(verificationStatus);
        ObjectNode jsonNode = objectMapper.createObjectNode();
        jsonNode.put("email", email);
        jsonNode.put("provider_id", providerId);
        jsonNode.put("otp_code", otp);
        jsonNode.put("verification_status_id", statusId);
        String jsonString = objectMapper.writeValueAsString(jsonNode);
        String activityData = activityLogService.prepareActivityLogMetadata();
        var query = dsl
                .select(DSL.field(
                        "drh_stateless_authentication.upsert_user_verification_log({0}, {1})",
                        JSONB.class, DSL.cast(DSL.val(
                                jsonString),
                                JSONB.class),
                        DSL.cast(DSL.val(activityData), JSONB.class)));
        LOG.info("Executing query to upsert email details sql: {}, ", query.getSQL());
        LOG.info("Executing query to upsert email details query: {}, ", query);
        var result = query.fetchOne();
        LOG.info("Query result: {}", result);
        if (result == null || result.value1() == null) {
            LOG.error("Failed to upsert email details for user: {}", email);
            return null; // or throw an exception based on your error handling strategy
        }
        LOG.info("Email details upserted successfully for user: {}", email);
        return JSONB.valueOf(email);
    }
}
