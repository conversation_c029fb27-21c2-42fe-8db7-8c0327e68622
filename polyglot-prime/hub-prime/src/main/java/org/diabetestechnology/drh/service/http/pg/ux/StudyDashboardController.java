package org.diabetestechnology.drh.service.http.pg.ux;

import org.diabetestechnology.drh.service.http.pg.service.StudyDashboardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "Study Dashboard APIs")
public class StudyDashboardController {
    private static final Logger LOG = LoggerFactory.getLogger(StudyDashboardController.class);
    private final StudyDashboardService studyDashboardService;

    public StudyDashboardController(
            StudyDashboardService studyDashboardService) {
        this.studyDashboardService = studyDashboardService;
    }

    @GetMapping(value = "/research-study/allstudy-vanity-metrics/total-number-of-participants.{extension}", produces = {
            "text/html" })
    @ResponseBody
    @Operation(summary = "Fetch Total number of participants")
    public ResponseEntity<?> getTotalParticipants(@PathVariable String extension) {
        try {
            Object totalParticipants = studyDashboardService.getTotalParticipants();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(totalParticipants);
        } catch (Exception e) {
            LOG.error("Error in /research-study/allstudy-vanity-metrics/total-number-of-participants: {}",
                    e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping(value = "/research-study/allstudy-vanity-metrics/female-percent.{extension}", produces = {
            "text/html" })
    @ResponseBody
    @Operation(summary = "Fetch female percentage")
    public ResponseEntity<?> getFemalePercentage(@PathVariable String extension) {
        try {
            Object totalParticipants = studyDashboardService.getFemalePercentage();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(totalParticipants);
        } catch (Exception e) {
            LOG.error("Error in /research-study/allstudy-vanity-metrics/female-percent: {}",
                    e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping(value = "/research-study/allstudy-vanity-metrics/average-age.{extension}", produces = {
            "text/html" })
    @ResponseBody
    @Operation(summary = "Average age for dashboard")
    public ResponseEntity<?> getAverageAgeForDashboard(@PathVariable String extension) {
        try {
            Object AverageAgeForDashboard = studyDashboardService.getAverageAgeForDashboard();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(AverageAgeForDashboard);
        } catch (Exception e) {
            LOG.error("Error in /research-study/allstudy-vanity-metrics/average-age: {}",
                    e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
}
