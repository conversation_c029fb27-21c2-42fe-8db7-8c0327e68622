package org.diabetestechnology.drh.service.http.hub.prime.experimental;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.dao.DataAccessException;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class ExperimentService {
    @SuppressWarnings("unused")
    private static final Logger LOG = LoggerFactory.getLogger(ExperimentService.class.getName());

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Value("${${spring.profiles.active}_DRH_UDI_DS_PRIME_DB_BASE_PATH:}")
    private String dbBasePath;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private List<String> databaseAliases = new ArrayList<>();

    int databaseCount = 0;

    public List<String> getAllData() {
        StringBuilder unionQuery = new StringBuilder();

        for (String alias : databaseAliases) {
            if (unionQuery.length() > 0) {
                unionQuery.append(" UNION ALL ");
            }
            unionQuery.append("SELECT * FROM ").append(alias).append(".uniform_resource_study");
        }

        String sql = unionQuery.toString();

        return jdbcTemplate.query(sql, (rs, rowNum) -> rs.getString("study_id"));
    }

    public List<Map<String, Object>> getAllStudySummary() {
        // Build the unionQuery to combine tables from all attached databases
        StringBuilder unionQuery = new StringBuilder();
        String separator = "";

        for (int i = 0; i < databaseAliases.size(); i++) {
            String alias = databaseAliases.get(i);
            unionQuery.append(separator);
            unionQuery.append("SELECT * FROM ").append(alias).append(".uniform_resource_study");

            // Add UNION ALL if it's not the last alias
            if (i < databaseAliases.size() - 1) {
                unionQuery.append(" \nUNION ALL\n");
            }

            separator = "";
        }
        // Construct the complete SQL query with dynamic UNION ALL and WITH clause
        String sql = "WITH study_summary AS (" +
                "    SELECT " +
                "        m.study_id, " +
                "        m.study_name, " +
                "        m.start_date, " +
                "        m.end_date, " +
                "        m.nct_number, " +
                "        m.study_description " +
                "    FROM (" +
                unionQuery.toString() + // Append the unionQuery here
                ") m" +
                ")" +
                "SELECT *" +
                "FROM study_summary;";

        LOG.debug("Executing SQL query for AllStudySummary: {}", sql);

        try {
            // Execute the query and return the results
            return jdbcTemplate.queryForList(sql);

        } catch (DataAccessException e) {
            // Handle any exceptions or log the error
            LOG.error("Error executing SQL query for AllStudySummary : {}", e.getMessage(), e);
            throw new RuntimeException("Error executing SQL query", e);
        }
    }

    // specific study participant metric dashboard
    public List<Map<String, Object>> getStudyParticipantDashboard(String studyId) {

        // Convert studyName to lowercase
        String dbName = studyId.toLowerCase();

        if (!databaseAliases.contains(dbName)) {
            throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
        }

        String sql = "SELECT * FROM " + dbName + ".study_combined_dashboard_participant_metrics_view";

        try {
            return jdbcTemplate.queryForList(sql);
        } catch (DataAccessException e) {
            LOG.error("Error executing SQL query  : {}", e.getMessage(), e);
            throw new RuntimeException("Error executing SQL query for database '" + dbName + "'", e);
        }
    }

    public Map<String, Object> getStudyDetails(String studyId) {

        // Convert studyName to lowercase
        String dbName = studyId.toLowerCase();

        if (!databaseAliases.contains(dbName)) {
            throw new IllegalArgumentException("Database '" + dbName + "' is not attached.");
        }

        String sql = "SELECT s.study_id, " +
                "       s.study_name, " +
                "       s.study_description, " +
                "       s.start_date, " +
                "       s.end_date, " +
                "       s.nct_number, " +
                "       COUNT(DISTINCT p.participant_id) AS total_number_of_participants, " +
                "       ROUND(AVG(p.age), 2) AS average_age, " +
                "       FLOOR((CAST(SUM(CASE WHEN p.gender = 'F' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*)) * 100) AS percentage_of_females, "
                +
                "       GROUP_CONCAT(DISTINCT(i.investigator_name)) AS investigators " +
                "FROM `" + dbName + "`.uniform_resource_study s " +
                "LEFT JOIN `" + dbName + "`.uniform_resource_participant p ON s.study_id = p.study_id " +
                "LEFT JOIN `" + dbName + "`.uniform_resource_investigator i ON s.study_id = i.study_id";

        try {
            // Execute the query and return the results
            return jdbcTemplate.queryForMap(sql);

        } catch (DataAccessException e) {
            LOG.error("Error executing SQL query  : {}", e.getMessage(), e);
            throw new RuntimeException("Error executing SQL query for study ID '" + studyId + "'", e);
        }
    }

    public Map<String, Object> getAllStudyVanityMetrics() {
        // Build the unionQuery to combine from all attached databases
        StringBuilder unionQuery = new StringBuilder();
        String separator = "";

        for (int i = 0; i < databaseAliases.size(); i++) {
            String alias = databaseAliases.get(i);
            unionQuery.append(separator);
            unionQuery.append("SELECT * FROM ").append(alias).append(".uniform_resource_participant");

            // Add UNION ALL if it's not the last alias
            if (i < databaseAliases.size() - 1) {
                unionQuery.append(" \nUNION ALL\n");
            }

            separator = "";
        }

        // Construct the complete SQL query with dynamic UNION ALL and the participant
        // statistics calculation
        String sql = "WITH all_participants AS (" +
                unionQuery.toString() +
                ") " +
                "SELECT " +
                "    COUNT(DISTINCT participant_id) AS total_number_of_participants, " +
                "    FLOOR((CAST(SUM(CASE WHEN gender = 'F' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*)) * 100) AS percent_female, "
                +
                "    FLOOR(AVG(age)) AS average_age " +
                "FROM all_participants;";

        try {
            // Execute the query and return the results
            return jdbcTemplate.queryForMap(sql);
        } catch (DataAccessException e) {
            // Handle any exceptions or log the error
            LOG.error("Error executing SQL query  : {}", e.getMessage(), e);
            throw new RuntimeException("Error executing SQL query", e);
        }
    }

    public Map<String, Object> getAllStudyTotalCgmWear() {
        StringBuilder unionQuery = new StringBuilder();

        for (int i = 0; i < databaseAliases.size(); i++) {
            String alias = databaseAliases.get(i);
            if (i > 0) {
                unionQuery.append(" UNION ALL ");
            }
            unionQuery.append("SELECT COUNT(DISTINCT DATE(Date_Time)) AS cgm_wear FROM ")
                    .append(alias)
                    .append(".combined_cgm_tracing");
        }

        String sql = "WITH agg_cgm_wear AS (" +
                unionQuery.toString() +
                ") " +
                "SELECT SUM(cgm_wear) AS total_cgm_wear FROM agg_cgm_wear;";

        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (DataAccessException e) {
            LOG.error("Error executing SQL query  : {}", e.getMessage(), e);
            throw new RuntimeException("Error executing SQL query", e);
        }
    }

    public Map<String, Object> getAllStudyTotalDataPoints() {
        StringBuilder unionQuery = new StringBuilder();

        for (int i = 0; i < databaseAliases.size(); i++) {
            String alias = databaseAliases.get(i);
            if (i > 0) {
                unionQuery.append(" UNION ALL ");
            }
            unionQuery.append("SELECT COUNT(*) AS data_points FROM ").append(alias).append(".combined_cgm_tracing");
        }

        String sql = "WITH agg_data_points AS (" +
                unionQuery.toString() +
                ")" +
                "SELECT SUM(data_points) AS total_data_points FROM agg_data_points;";

        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (DataAccessException e) {
            LOG.error("Error executing SQL query  : {}", e.getMessage(), e);
            throw new RuntimeException("Error executing SQL query", e);
        }
    }

}
