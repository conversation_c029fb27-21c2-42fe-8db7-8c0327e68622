package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

import lombok.Data;

@SuppressWarnings("unused")
@Data
public class LogDetails {
    private String activityName;
    private String activityType;
    private String activityDescription;
    private String requestUrl;
    private int activityLogLevel;

    public LogDetails(String activityName, String activityType, String activityDescription, String requestUrl,
            int activityLogLevel) {
        this.activityName = activityName;
        this.activityType = activityType;
        this.activityDescription = activityDescription;
        this.requestUrl = requestUrl;
        this.activityLogLevel = activityLogLevel;
    }

}
