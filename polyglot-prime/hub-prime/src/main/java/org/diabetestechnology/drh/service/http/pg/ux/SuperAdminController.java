package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.diabetestechnology.drh.service.http.pg.service.SuperAdminService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "Super Admin APIs")
public class SuperAdminController {

    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyController.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final SuperAdminService superAdminService;
    private final PasswordEncoder passwordEncoder;
    private final PractitionerService practitionerService;

    public SuperAdminController(SuperAdminService superAdminService, PasswordEncoder passwordEncoder,
            PractitionerService practitionerService) {
        this.superAdminService = superAdminService;
        this.passwordEncoder = passwordEncoder;
        this.practitionerService = practitionerService;

    }

    @Hidden
    @PostMapping("/auth/super-admin")
    @Operation(summary = "Add Super Admin")
    @ResponseBody
    public Response addSuperAdmin(@RequestParam("fullName") String fullName, @RequestParam("email") String email) {

        LOG.info("Received request to add Super Admin with fullName: {}, email: {}", fullName, email);
        try {
            // Check if the email is already registered
            if (!practitionerService.isUniqueEmail(email)) {
                LOG.warn("Attempt to add Super Admin with existing email: {}", email);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Email already exists")
                        .errors("Email already exists")
                        .build();
            }
            Object response = superAdminService.addSuperAdmin(fullName, email);
            JsonNode responseJson = OBJECT_MAPPER.readTree(response.toString());
            if (responseJson.has("status")) {
                String status = responseJson.get("status").asText();
                if ("failure".equalsIgnoreCase(status) || "error".equalsIgnoreCase(status)) {
                    String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                            : "Unknown error occurred.";
                    JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details")
                            : null;
                    LOG.error("Error adding super admin : {}", errorMessage);
                    return Response.builder()
                            .data(Map.of())
                            .status("error")
                            .message(errorMessage)
                            .errors(errorDetails != null ? errorDetails.toString() : null)
                            .build();
                }
            }

            return Response.builder()
                    .data(Map.of("message", "Super Admin added successfully!"))
                    .status("success")
                    .message("Super Admin added successfully!")
                    .errors(null)
                    .build();

        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to Add Super Admin")
                    .errors("Error in adding super admin: " +
                            e.getMessage())
                    .build();
        }
    }

    @PostMapping("/hash")
    @ResponseBody
    public Map<String, String> hashPassword(@RequestParam("password") String password) {
        String hashedPassword = passwordEncoder.encode(password);
        return Map.of(
                "plainPassword", password,
                "hashedPassword", hashedPassword);
    }
}