package org.diabetestechnology.drh.service.http.pg.request;

// @formatter:off
public record InvestigatorRequest(
        String studyId,
        String[] practitionerNames,
        CollabTeamType type) {
    public enum CollabTeamType {
        investigator("principal_investigator"), nominated<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tigator(
                "nominated_principal_investigator"), study<PERSON>ea<PERSON>(
                        "study_team"), coInvestigator("co_investigator"), author("author");

        private final String value;

        CollabTeamType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return value;
        }

        public static CollabTeamType fromValue(String value) {
            for (CollabTeamType type : values()) {
                if (type.value.equalsIgnoreCase(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown value: " + value);
        }
    }
}
// @formatter:on