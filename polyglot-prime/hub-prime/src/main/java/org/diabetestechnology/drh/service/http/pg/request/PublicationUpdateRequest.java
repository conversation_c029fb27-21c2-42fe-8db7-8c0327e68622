package org.diabetestechnology.drh.service.http.pg.request;

import java.util.Date;

import org.diabetestechnology.drh.service.http.pg.constant.CitationDataSource;

import com.fasterxml.jackson.annotation.JsonFormat;

// @formatter:off
public record PublicationUpdateRequest(
        String publication_title,
        @JsonFormat(pattern = "MM-dd-yyyy") Date publication_date,
        String publication_doi,
        String pubmed_id,
        CollaborationTeamRequest collaboration_team,
        CitationDataSource citation_data_source) {
    // Custom constructor excluding collaboration_team
    public PublicationUpdateRequest(String publication_title,Date publication_date, String publication_doi , String pubmed_id) {
        this(publication_title ,publication_date, publication_doi, pubmed_id,null, null);
    }

    public PublicationUpdateRequest(String publication_title, Date publication_date, String publication_doi,
            String pubmed_id, CitationDataSource citation_data_source) {
                this(publication_title ,publication_date, publication_doi, pubmed_id,null, citation_data_source);
    }

}
// @formatter:on
