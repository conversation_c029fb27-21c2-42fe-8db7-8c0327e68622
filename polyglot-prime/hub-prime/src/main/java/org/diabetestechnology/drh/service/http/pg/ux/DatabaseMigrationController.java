package org.diabetestechnology.drh.service.http.pg.ux;

import org.diabetestechnology.drh.service.http.pg.service.DatabaseMigrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import jakarta.annotation.Nonnull;
import jakarta.validation.Valid;

import java.io.IOException;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;

@Controller
@Hidden
public class DatabaseMigrationController {
    private static final Logger LOG = LoggerFactory.getLogger(DatabaseMigrationController.class);
    private final DatabaseMigrationService databaseMigrationService;

    public DatabaseMigrationController(DatabaseMigrationService databaseMigrationService) {
        this.databaseMigrationService = databaseMigrationService;
    }

    @PostMapping("/research-study/database/upload")
    @Operation(summary = "Upload and save a research srudy Database")
    @ResponseBody
    public Response uploadDatabaseFile(@RequestPart("file") @Nonnull MultipartFile file,
            @Valid @RequestBody DatabaseMigrationRequest request) throws IOException {
        LOG.info("Received request to upload database file: {}", request);
        final var response = databaseMigrationService.uploadAndSaveDBFile(file, request);
        LOG.debug("Database Migration Response : {} , {}", response, file.getOriginalFilename());
        final var message = response.toString().equals("Successfully migrated database") ? "Database upload completed"
                : response;
        final var status = response.toString().equals("Successfully migrated database")
                || response.toString().equals("Database Migration is in Progress") ? "success"
                        : "error";

        return Response.builder()
                .data(Map.of("status", response))
                .status(status)
                .message(message)
                .errors(null)
                .build();
    }

}
