package org.diabetestechnology.drh.service.http.hub.prime.study;

public class TimeInRange {

    private String participantId;
    private int borderLine;
    private double value;
    private String timeRangeString;
    private String formattedValue;

    // Constructor
    public TimeInRange(String participantId, int borderLine, double value, String timeRangeString,
            String formattedValue) {
        this.participantId = participantId;
        this.borderLine = borderLine;
        this.value = value;
        this.timeRangeString = timeRangeString;
        this.formattedValue = formattedValue;
    }

    // Getters and Setters
    public String getParticipantId() {
        return participantId;
    }

    public void setParticipantId(String participantId) {
        this.participantId = participantId;
    }

    public int getBorderLine() {
        return borderLine;
    }

    public void setBorderLine(int borderLine) {
        this.borderLine = borderLine;
    }

    public double getValue() {
        return value;
    }

    public void setValue(double value) {
        this.value = value;
    }

    public String getTimeRangeString() {
        return timeRangeString;
    }

    public void setTimeRangeString(String timeRangeString) {
        this.timeRangeString = timeRangeString;
    }

    public String getFormattedValue() {
        return formattedValue;
    }

    public void setFormattedValue(String formattedValue) {
        this.formattedValue = formattedValue;
    }
}
