package org.diabetestechnology.drh.service.http.pg.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.diabetestechnology.drh.service.http.OauthUsersService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.request.UserRoleRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record2;
import org.jooq.Record3;
import org.jooq.SelectConditionStep;
import org.springframework.dao.DataAccessException;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class UserRoleService {

    private static final Logger LOG = LoggerFactory.getLogger(UserRoleService.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String GUEST_ROLE_NAME = "Guest";

    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final OauthUsersService oauthUsersService;
    private final Presentation presentation;
    private final DbActivityService activityLogService;

    public UserRoleService(@Qualifier("secondaryDsl") DSLContext dsl,
            UserNameService userNameService, PartyService partyService, OauthUsersService oauthUsersService,
            Presentation presentation, DbActivityService activityLogService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.oauthUsersService = oauthUsersService;
        this.presentation = presentation;
        this.activityLogService = activityLogService;
    }

    public JSONB getUserRoles() {
        try {
            final var query = dsl
                    .select(DSL.field(
                            "jsonb_agg(" +
                                    "jsonb_build_object(" +
                                    "'role_id', role_id, " +
                                    "'role_type_id', role_type_id, " +
                                    "'org_party_id', org_party_id, " +
                                    "'role_name', role_name, " +
                                    "'is_system_role', is_system_role, " +
                                    "'role_type_code', role_type_code, " +
                                    "'role_type_display', role_type_display, " +
                                    "'rec_status_id', rec_status_id" +
                                    ")" +
                                    ")",
                            JSONB.class))
                    .from("drh_stateless_master.roles_view")
                    .where(DSL.condition("rec_status_id = 1"));

            LOG.info("Query for fetching user roles: {}", query);

            JSONB jsonbResult = query.fetchOneInto(JSONB.class);
            if (jsonbResult == null) {
                LOG.warn("No user roles found.");
                return JSONB.jsonb("{}");
            }
            return jsonbResult;
        } catch (DataAccessException dae) {
            LOG.error("Database error while fetching user roles", dae);
            throw new RuntimeException("Database error occurred. Please try again later.");
        } catch (Exception e) {
            LOG.error("Unexpected error while fetching user roles", e);
            throw new RuntimeException("An unexpected error occurred.");
        }
    }

    public JSONB getUserList() {
        try {

            var subquery = DSL.select(
                    DSL.field("user_id"),
                    DSL.field("party_id"),
                    DSL.field("organization_party_id"),
                    DSL.field("organization_name"),
                    DSL.field("user_account_primary_email"),
                    DSL.field("username"),
                    DSL.field("first_name"),
                    DSL.field("last_name"),
                    DSL.field("profile_status_id"),
                    DSL.field("profile_status"),
                    DSL.field("auth_provider"),
                    DSL.field("provider_user_id"),
                    DSL.field("work_emails"),
                    DSL.field("user_roles"),
                    DSL.field("user_metadata"),
                    DSL.field("last_login_at"))
                    .distinctOn(DSL.field("party_id"))
                    .from("drh_stateless_authentication.user_list_view")
                    .orderBy(DSL.field("party_id"), DSL.field("last_login_at").desc()) // or any tiebreaker
                    .asTable("distinct_users");
            var query = dsl
                    .select(DSL.field(
                            "jsonb_agg(" +
                                    "jsonb_build_object(" +
                                    "'user_id', user_id, " +
                                    "'party_id', party_id, " +
                                    "'organization_party_id', organization_party_id, " +
                                    "'organization_name', organization_name, " +
                                    "'email', user_account_primary_email, " +
                                    "'username', username, " +
                                    "'first_name', first_name, " +
                                    "'last_name', last_name, " +
                                    "'profile_status_id', profile_status_id, " +
                                    "'profile_status', profile_status, " +
                                    "'auth_provider', auth_provider, " +
                                    "'provider_user_id', provider_user_id, " +
                                    "'work_emails', work_emails, " +
                                    "'user_roles', user_roles, " +
                                    "'user_metadata', user_metadata, " +
                                    "'last_login_at', last_login_at" +
                                    ")" +
                                    ")",
                            JSONB.class))
                    .from(subquery);
            if (!presentation.isSuperAdmin()) {
                final var organizationPartyId = userNameService.getCurrentUserOrganizationPartyId();
                SelectConditionStep<Record1<JSONB>> sqlQuery = query
                        .where(DSL.field("organization_party_id").eq(DSL.val(organizationPartyId)));

                LOG.info("Query for fetching user list: {}", sqlQuery);

                JSONB jsonbResult = sqlQuery.fetchOneInto(JSONB.class);
                if (jsonbResult == null) {
                    LOG.warn("No users found.");
                    return JSONB.jsonb("{}");
                }
                return jsonbResult;
            } else {
                LOG.info("Query for fetching user list: {}", query);

                JSONB jsonbResult = query.fetchOneInto(JSONB.class);
                if (jsonbResult == null) {
                    LOG.warn("No users found.");
                    return JSONB.jsonb("{}");
                }
                return jsonbResult;
            }
        } catch (DataAccessException e) {
            LOG.error("Database error while fetching user list: {}", e.getMessage());
            throw new RuntimeException("Database error occurred. Please try again later.");
        } catch (Exception e) {
            LOG.error("Unexpected error while fetching user list: {}", e.getMessage());
            throw new RuntimeException("An unexpected error occurred.");
        }
    }

    public Object updateUserRole(UserRoleRequest request) {
        final var userId = userNameService.getUserId();
        final var createdBy = partyService.getPartyIdByUserId(userId);
        String provider = userNameService.getUserProvider();
        LOG.info("Updating user roles for userId: {} by createdBy: {}", request.userId(), createdBy);

        try {
            if (!oauthUsersService.hasAdminMenuDB(userId, provider) && !presentation.isSuperAdmin()) {
                LOG.warn("User {} does not have permission to update roles.", userId);
                return JSONB.jsonb("{\"status\": \"error\", \"message\": \"Permission denied\"}");
            }
            String activityData = activityLogService.prepareActivityLogMetadata();
            LOG.debug("Executing query to update user roles: userId={}, roleIds={}", request.userId(),
                    request.roleIds());
            ObjectMapper objectMapper = new ObjectMapper();
            String roleIdsJson = objectMapper.writeValueAsString(request.roleIds());

            LOG.info("Query for update user role: {}", dsl.select(DSL.field(
                    "drh_stateless_authentication.update_user_roles({0}, {1}, {2}, {3})",
                    JSONB.class,
                    DSL.val(createdBy),
                    DSL.val(request.userId()),
                    DSL.val(roleIdsJson, SQLDataType.JSONB),
                    DSL.cast(DSL.val(
                            activityData),
                            JSONB.class))));
            JSONB result = dsl.select(DSL.field(
                    "drh_stateless_authentication.update_user_roles({0}, {1}, {2}, {3})",
                    JSONB.class,
                    DSL.val(createdBy),
                    DSL.val(request.userId()),
                    DSL.val(roleIdsJson, SQLDataType.JSONB),
                    DSL.cast(DSL.val(
                            activityData),
                            JSONB.class)))
                    .fetchOneInto(JSONB.class);
            LOG.info("User roles updated successfully for userId: {}", request.userId());

            return result.data();
        } catch (Exception e) {
            LOG.error("Error updating user roles for userId: {}. Exception: {}", request.userId(), e.getMessage(), e);
            return JSONB.jsonb("{\"status\": \"error\", \"message\": \"Failed to update user roles\"}");
        }
    }

    public JSONB getUserRolesAndPermissions(String userId) {

        try {

            LOG.info("Query for fetch user role and permission: {}", dsl.select(DSL.field(
                    "drh_stateless_authentication.get_user_roles_and_permissions({0} )",
                    JSONB.class,
                    DSL.val(userId))));
            JSONB result = dsl.select(DSL.field(
                    "drh_stateless_authentication.get_user_roles_and_permissions({0})",
                    JSONB.class,
                    DSL.val(userId))).fetchOneInto(JSONB.class);
            LOG.info("User roles and permissions fetched successfully for userId: {}", userId);

            return result;
        } catch (Exception e) {
            LOG.error("Error fetching user roles and permissions for userId: {}. Exception: {}", userId, e.getMessage(),
                    e);
            return JSONB.jsonb("{\"status\": \"error\", \"message\": \"Failed to fetch user roles\"}");
        }
    }

    public String getPermissionsByRoles(String userPartyId) {
        try {
            LOG.info("Fetching permissions by roles...");
            if (userPartyId == null || userPartyId.isEmpty()) {
                final var userId = userNameService.getUserId();
                userPartyId = partyService.getPartyIdByUserId(userId);
                LOG.info("User party ID is null or empty. Using current user party ID: {}", userPartyId);
            }
            List<String> roleNames;
            if (!presentation.isAuthenticatedUser()) {
                LOG.info("Guest access detected. Fetching permissions for 'Guest'");
                roleNames = List.of("Guest");

            }
            if (presentation.isSuperAdmin()) {
                LOG.info("Guest access detected. Fetching permissions for 'Guest'");
                final var query = dsl.select(DSL.field("role_name"))
                        .from(DSL.table("drh_stateless_authentication.super_admin_view"))
                        .where(DSL.field("party_id").eq(userPartyId));
                roleNames = query.fetch(DSL.field("role_name"), String.class);

            } else {
                final var query = dsl
                        .select(DSL.field("jsonb_agg(DISTINCT user_roles)", JSONB.class))
                        .from("drh_stateless_authentication.user_list_view")
                        .where(DSL.field("party_id").eq(userPartyId));
                LOG.info("Query for fetching user roles: {}", query);
                JSONB roleJson = query.fetchOneInto(JSONB.class);

                if (roleJson == null || roleJson.data() == null) {
                    LOG.warn("No roles found for userPartyId: {}", userPartyId);
                    return (Map.of("status", "error", "message", "User roles not found")).toString();
                }

                roleNames = extractRoleNamesFromJson(roleJson);
                LOG.info("Extracted role names: {}", roleNames);

            }

            final var query = dsl
                    .selectDistinct(DSL.field("permission_name", String.class),
                            DSL.field("resource_type", String.class))
                    .from("drh_stateless_authentication.role_permission_view")
                    .where(DSL.field("role_name").in(roleNames))
                    .orderBy(DSL.field("permission_name").asc());
            LOG.info("Query for fetching permissions by roles: {}", query);
            List<Record2<String, String>> results = query.fetch();

            Map<String, List<String>> permissionsByResource = results.stream()
                    .collect(Collectors.groupingBy(
                            Record2::component2,
                            LinkedHashMap::new,
                            Collectors.mapping(Record2::component1, Collectors.toList())));

            Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("userPartyId", userPartyId);
            responseMap.put("userRoles", roleNames);
            responseMap.put("userPermissions", permissionsByResource);
            String response = OBJECT_MAPPER.writeValueAsString(responseMap);
            return response;
        } catch (Exception e) {
            LOG.error("Error in getPermissionsByRoles", e);
            return "{}";
        }
    }

    public Map<String, Object> getPermissionsOfGuestRole() {
        try {
            LOG.info("Fetching permissions for 'Guest' role");

            List<Record2<String, String>> results = dsl
                    .selectDistinct(DSL.field("permission_name", String.class),
                            DSL.field("resource_type", String.class))
                    .from("drh_stateless_authentication.role_permission_view")
                    .where(DSL.field("role_name").eq(
                            GUEST_ROLE_NAME))
                    .orderBy(DSL.field("permission_name").asc())
                    .fetch();

            if (results.isEmpty()) {
                LOG.warn("No permissions found for 'Guest' role.");
                return Collections.emptyMap();
            }

            Map<String, List<String>> permissionsByResource = results.stream()
                    .collect(Collectors.groupingBy(
                            Record2::component2, // resource_type
                            LinkedHashMap::new,
                            Collectors.mapping(Record2::component1, Collectors.toList()))); // permission_name

            return Map.of("permissions", permissionsByResource);
        } catch (Exception e) {
            LOG.error("Error fetching permissions for 'Guest' role", e);
            throw new RuntimeException("Failed to fetch guest role permissions", e);
        }
    }

    private List<String> extractRoleNamesFromJson(JSONB roleJson) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        List<String> roleNames = new ArrayList<>();

        if (roleJson != null && roleJson.data() != null) {
            JsonNode root = mapper.readTree(roleJson.data());
            for (JsonNode innerArray : root) {
                for (JsonNode roleObj : innerArray) {
                    if (roleObj.has("role_name")) {
                        roleNames.add(roleObj.get("role_name").asText());
                    }
                }
            }
        }
        return roleNames;
    }

    public List<String> getFlatPermissionListByRoles(String userPartyId) {
        try {
            String jsonResponse = getPermissions(userPartyId);

            JsonNode rootNode = OBJECT_MAPPER.readTree(jsonResponse);
            JsonNode permissionsNode = rootNode.get("userPermissions");

            if (permissionsNode == null || permissionsNode.isEmpty()) {
                return List.of(); // No permissions found
            }

            List<String> flatPermissions = new ArrayList<>();

            Iterator<Map.Entry<String, JsonNode>> fields = permissionsNode.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String resourceType = entry.getKey(); // e.g., "ADMINISTRATION"
                JsonNode permissionList = entry.getValue(); // Array of strings like "Change User Roles:VIEW/EDIT"

                for (JsonNode permission : permissionList) {
                    String[] parts = permission.asText().split(":", 2); // ["Change User Roles", "VIEW/EDIT"]
                    if (parts.length == 2) {
                        String permissionName = parts[0];
                        String actionType = parts[1]; // could be "VIEW", "VIEW/EDIT", etc.
                        flatPermissions.add(resourceType + ":" + permissionName + ":" + actionType);
                    } else {
                        // fallback in case format is unexpected
                        flatPermissions.add(resourceType + ":" + permission.asText());
                    }
                }
            }

            return flatPermissions;
        } catch (Exception e) {
            LOG.error("Error parsing permissions from JSON", e);
            return List.of(); // Return empty list on failure
        }
    }

    public String getPermissions(String userPartyId) {
        try {
            LOG.info("Fetching permissions by roles...");

            List<String> roleNames;
            if (userPartyId == null || userPartyId.isEmpty()) {
                LOG.info(
                        "No userPartyId found. Assuming user is not yet registered. Fetching permissions for role: 'Guest'");
                roleNames = List.of("Guest");
            } else if (!presentation.isAuthenticatedUser()) {
                LOG.info("Guest access detected. Fetching permissions for 'Guest'");
                roleNames = List.of("Guest");
            } else if (presentation.isSuperAdmin()) {
                LOG.info("Guest access detected. Fetching permissions for 'Guest'");
                final var query = dsl.select(DSL.field("role_name"))
                        .from(DSL.table("drh_stateless_authentication.super_admin_view"))
                        .where(DSL.field("party_id").eq(userPartyId));
                roleNames = query.fetch(DSL.field("role_name"), String.class);
            } else {
                final var query = dsl
                        .select(DSL.field("jsonb_agg(DISTINCT user_roles)", JSONB.class))
                        .from("drh_stateless_authentication.user_list_view")
                        .where(DSL.field("party_id").eq(userPartyId));
                LOG.info("Query for fetching user roles: {}", query);
                JSONB roleJson = query.fetchOneInto(JSONB.class);

                if (roleJson == null || roleJson.data() == null) {
                    LOG.warn("No roles found for userPartyId: {}", userPartyId);
                    return (Map.of("status", "error", "message", "User roles not found")).toString();
                }

                roleNames = extractRoleNamesFromJson(roleJson);
                LOG.info("Extracted role names: {}", roleNames);

            }
            final var query = dsl
                    .selectDistinct(
                            DSL.field("permission_name", String.class),
                            DSL.field("resource_type", String.class),
                            DSL.field("action_type", String.class))
                    .from("drh_stateless_authentication.role_permission_view")
                    .where(DSL.field("role_name").in(roleNames))
                    .orderBy(DSL.field("permission_name").asc());

            LOG.info("Query for fetching permissions by roles: {}", query);

            List<Record3<String, String, String>> results = query.fetch();
            // Optional: log result rows
            results.forEach(record -> LOG.info("Fetched permission: {}, {}, {}",
                    record.component1(), record.component2(), record.component3()));

            Map<String, List<String>> permissionsByResource = results.stream()
                    .collect(Collectors.groupingBy(
                            Record3::component2, // resource_type
                            LinkedHashMap::new,
                            Collectors.mapping(
                                    record -> record.component1() + ":" + record.component3(), // permission_name:action_type
                                    Collectors.toList())));

            Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("userPartyId", userPartyId);
            responseMap.put("userRoles", roleNames);
            responseMap.put("userPermissions", permissionsByResource);
            String response = OBJECT_MAPPER.writeValueAsString(responseMap);
            return response;
        } catch (Exception e) {
            LOG.error("Error in getPermissionsByRoles", e);
            return "{}";
        }
    }

}
