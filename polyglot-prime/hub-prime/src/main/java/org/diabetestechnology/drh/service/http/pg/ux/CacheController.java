package org.diabetestechnology.drh.service.http.pg.ux;

import org.diabetestechnology.drh.service.http.pg.cache.CacheService;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cache")
public class CacheController {

    private final CacheService cacheService;

    public CacheController(CacheService cacheService) {
        this.cacheService = cacheService;
    }

    @DeleteMapping("/clear")
    public String clearTabularRowCache() {
        cacheService.evictTabularRowCache();
        return "Cache cleared!";
    }

    @DeleteMapping("/clear/{cacheKey}")
    public String clearSpecificCache(@PathVariable String cacheKey) {
        cacheService.evictSpecificCache(cacheKey);
        return "Cache entry cleared for key: " + cacheKey;
    }

}
