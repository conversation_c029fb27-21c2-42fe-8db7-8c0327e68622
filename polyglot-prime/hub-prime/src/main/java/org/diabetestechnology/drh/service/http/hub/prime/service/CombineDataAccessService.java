package org.diabetestechnology.drh.service.http.hub.prime.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.jdbc.JdbcResponse;
import org.diabetestechnology.drh.service.http.hub.prime.service.request.CohortRequest;
import org.jooq.exception.DataAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class CombineDataAccessService {

    private static final Logger LOG = LoggerFactory.getLogger(CombineDataAccessService.class.getName());

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    DataAccessService dataAccessService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Transactional
    @Async
    public Map<String, Object> getAllStudyTotalDataPoints() throws SQLException {
        final var sql = "SELECT total_data_points FROM agg_data_points_cached;";
        LOG.info("CombineDataAccessService:getAllStudyTotalDataPoints, Executing SQL query: {}", sql);
        return jdbcTemplate.queryForMap(sql);
    }

    @Transactional
    @Async
    public Map<String, Object> getAllStudyTotalCgmWear() throws SQLException {
        final var sql = "SELECT total_cgm_wear FROM agg_cgm_wear_cached;";
        LOG.info("CombineDataAccessService:getAllStudyTotalCgmWear, Executing SQL query: {}", sql);
        return jdbcTemplate.queryForMap(sql);
    }

    @Transactional
    @Async
    public Map<String, Object> getAllStudyVanityMetrics() throws SQLException {
        final var sql = "SELECT total_number_of_participants, percent_female, average_age FROM all_participants_cached";
        LOG.info("CombineDataAccessService:getAllStudyVanityMetrics, Executing SQL query: {}", sql);
        return jdbcTemplate.queryForMap(sql);
    }

    @Transactional
    @Async
    public Map<String, Object> getAllStudyTotalCgmFiles() throws SQLException {
        final var sql = "SELECT total_cgm_file_count FROM total_cgm_file_count_cached;";
        LOG.info("CombineDataAccessService:getAllStudyTotalCgmFiles, Executing SQL query: {}", sql);
        return jdbcTemplate.queryForMap(sql);
    }

    @SuppressWarnings("null")
    public Object getCohort(CohortRequest request) {

        final var cgmCount = getCohortCgmCount(request.studyIds());
        List<Map<String, Object>> cohortResponse = getCohortResponse(request);
        Map<String, Object> dataMap = new HashMap<>();
        if (cgmCount > 0)
            dataMap.put("totalCgmFiles", cgmCount);
        if (cohortResponse != null && cohortResponse.size() > 0)
            cohortResponse.forEach(response -> {
                dataMap.putAll(response);
            });
        return JdbcResponse.builder()
                .data(dataMap)
                .status("success")
                .message("cohort metrics successfully fetched")
                .errors((cgmCount == 0 && cohortResponse.size() == 0)
                        ? "No data available"
                        : "")
                .build();
    }

    int cohortCgmCount;

    public int getCohortCgmCount(List<String> studyIds) {
        if (studyIds == null) {
            throw new IllegalArgumentException("studyIds must not be null");
        }
        cohortCgmCount = 0;
        studyIds.forEach(studyId -> {
            cohortCgmCount = cohortCgmCount
                    + (int) dataAccessService.getStudyTotalCgmFiles(studyId.toLowerCase()).get("total_count");
        });
        return cohortCgmCount;
    }

    public List<Map<String, Object>> getCohortResponse(CohortRequest request) {
        try {
            if (request.studyIds().size() > 0) {
                final var sqlDraft = "SELECT " +
                        "COUNT(DISTINCT participant_id) AS total_number_of_participants ," +
                        "CAST( SUM( CASE WHEN gender = 'F' THEN 1 ELSE 0 END ) AS FLOAT ) as total_female," +
                        "SUM(age) as total_age";
                // Build the WHERE clause
                StringBuilder whereCondition = new StringBuilder();
                if (request.filters() != null && !request.filters().isEmpty()) {
                    whereCondition.append(" WHERE ")
                            .append(String.join(" AND ", request.filters()));
                }
                // Construct the SQL query
                StringBuilder sql = new StringBuilder();
                for (String studyId : request.studyIds()) {
                    if (sql.length() > 0) {
                        sql.append(" UNION ALL ");
                    }
                    sql.append(sqlDraft)
                            .append(" FROM ").append(studyId.toLowerCase()).append(".participant_dashboard_cached")
                            .append(whereCondition);
                }

                // Complete the query with the cohort summary
                sql.insert(0, "WITH cohort_summary AS (")
                        .append(") SELECT ")
                        .append("COALESCE(SUM(total_number_of_participants), 0) AS total_number_of_participants, ")
                        .append("COALESCE(FLOOR((SUM(total_female) / SUM(total_number_of_participants)) * 100), 0) AS percent_female, ")
                        .append("COALESCE(FLOOR(SUM(total_age) / SUM(total_number_of_participants)), 0) AS average_age ")
                        .append("FROM cohort_summary;");
                LOG.info("Final Query for COHORT: {}", sql);
                request.studyIds().forEach(studyId -> {
                    dataAccessService.refreshDatabase(studyId.toLowerCase());
                });

                return jdbcTemplate.queryForList(sql.toString());
            }
            return null;
        } catch (Exception e) {
            LOG.error("getCohortResponse: Error: {}", e.getMessage());
            return null;
        }
    }

    public Object getCohortReport(CohortRequest request) {
        try {
            StringBuilder sql = new StringBuilder();
            if (request.studyIds().size() > 0) {
                sql.append("SELECT " +
                        "study_id, " +
                        "participant_id, " +
                        "gender, " +
                        "age, " +
                        "study_arm, " +
                        "baseline_hba1c, " +
                        "tir, " +
                        "tar_vh, " +
                        "tar_h, " +
                        "tbr_l, " +
                        "tbr_vl, " +
                        "gmi, " +
                        "percent_gv, " +
                        "gri, " +
                        "days_of_wear, " +
                        "wear_time_percentage," +
                        "data_start_date, " +
                        "data_end_date " +
                        "FROM all_participant_dashboard_cached " +
                        "where study_id IN (:studyIds) ");
                StringBuilder whereCondition = new StringBuilder();
                if (request.filters() != null && !request.filters().isEmpty()) {
                    whereCondition.append("AND ")
                            .append(String.join(" AND ", request.filters()));
                }
                sql.append(whereCondition);
                Map<String, Object> params = Collections.singletonMap("studyIds", request.studyIds());
                LOG.info("CombineDataAccessService:getCohortReport, Executing SQL query: {}", sql);
                LOG.info("COHORT Parametres : {}", params);
                final var response = namedParameterJdbcTemplate.queryForList(sql.toString(), params);
                return dataAccessService.responseBuilder(response != null
                        ? Map.of("cohortReport", response)
                        : Map.of(), "success", "cohortReport", null);
            } else {
                sql.append("SELECT " +
                        "study_id, " +
                        "participant_id, " +
                        "gender, " +
                        "age, " +
                        "study_arm, " +
                        "baseline_hba1c, " +
                        "tir, " +
                        "tar_vh, " +
                        "tar_h, " +
                        "tbr_l, " +
                        "tbr_vl, " +
                        "gmi, " +
                        "percent_gv, " +
                        "gri, " +
                        "days_of_wear, " +
                        "wear_time_percentage," +
                        "data_start_date, " +
                        "data_end_date " +
                        "FROM all_participant_dashboard_cached ");
                StringBuilder whereCondition = new StringBuilder();
                if (request.filters() != null && !request.filters().isEmpty()) {
                    whereCondition.append("where ")
                            .append(String.join(" AND ", request.filters()));
                }
                sql.append(whereCondition);

                LOG.info("CombineDataAccessService:getCohortReport, Executing SQL query: {}", sql);

                final var response = jdbcTemplate.queryForList(sql.toString());
                return dataAccessService.responseBuilder(response != null
                        ? Map.of("cohortReport", response)
                        : Map.of(), "success", "cohortReport", null);
            }
        } catch (Exception ex) {
            LOG.error("Error in generating Cohort Report :{}", ex.getMessage());
            return dataAccessService.responseBuilder(Map.of(), "error", "cohortReport",
                    "Error in generating Cohort Report");
        }
    }

    public Map<String, Object> getCohortParticipantMetricsCount(CohortRequest request) {
        try {
            LOG.info(" getCohortParticipantMetricsCount");

            final var sqlDraft = "SELECT " +
                    "COALESCE(COUNT(DISTINCT participant_id), 0) AS total_number_of_participants," +
                    "COALESCE(FLOOR(CAST(SUM(CASE WHEN gender = 'F' THEN 1 ELSE 0 END) AS FLOAT) / NULLIF(COUNT(DISTINCT participant_id), 0) * 100),0)|| '%' AS percent_female,"
                    +
                    "COALESCE(FLOOR(SUM(age) / NULLIF(COUNT(DISTINCT participant_id), 0)),0)|| ' Years' AS average_age ";

            LOG.info(" Draft Query for COHORT: {}", sqlDraft);
            // Build the WHERE clause
            StringBuilder whereCondition = new StringBuilder();

            List<String> modifiedFilters = new ArrayList<>();
            if (request.filters() != null && !request.filters().isEmpty() && request.filters().size() > 0) {

                for (String filter : request.filters()) {
                    if (!filter.contains("gender")) {

                        int spaceIndex = filter.indexOf(" ");
                        if (spaceIndex != -1) {
                            String filterKey = filter.substring(0, spaceIndex);
                            filter = filter.replace(filterKey, "CAST(" + filterKey + " AS real)");
                        }
                    }
                    modifiedFilters.add(filter);
                }
                whereCondition.append(" WHERE ")
                        .append(String.join(" AND ", modifiedFilters));
            }

            LOG.info(" Draft Condition Query for COHORT: {}", whereCondition);
            boolean isFirst = true;
            if (!request.studyIds().isEmpty()) {
                for (String studyId : request.studyIds()) {
                    if (isFirst) {
                        if (whereCondition.length() == 0) {
                            whereCondition.append(" WHERE (study_id = '").append(studyId).append("' ");
                        } else
                            whereCondition.append(" AND (study_id = '").append(studyId).append("' ");
                        isFirst = false;
                    } else {
                        whereCondition.append(" OR study_id = '").append(studyId).append("' ");
                    }
                }
                whereCondition.append(")");
            }
            LOG.info(" Draft Condition Query for COHORT: {}", whereCondition);

            // Construct the SQL query
            StringBuilder sql = new StringBuilder();

            sql.append(sqlDraft)
                    .append(" FROM ").append("all_participant_dashboard_cached ")
                    .append(whereCondition);

            LOG.info("Final Query for COHORT: {}", sql);
            return jdbcTemplate.queryForMap(sql.toString());

        } catch (Exception e) {
            LOG.error("getCohortResponse: Error: {}", e.getMessage());
            return null;
        }
    }

    public Object getCohortCgmCount(CohortRequest request) {
        final var sqlDraft = "SELECT " +
                "sum(total_count) AS total_count " +
                "FROM total_study_cgm_file_count_cached ";
        StringBuilder whereCondition = new StringBuilder();
        boolean isFirst = true;
        for (String studyId : request.studyIds()) {
            if (isFirst) {
                if (whereCondition.length() == 0) {
                    whereCondition.append(" WHERE study_id = '").append(studyId).append("' ");
                } else
                    whereCondition.append(" AND study_id = '").append(studyId).append("' ");
                isFirst = false;
            } else {
                whereCondition.append(" OR study_id = '").append(studyId).append("' ");
            }
        }

        StringBuilder sql = new StringBuilder();

        sql.append(sqlDraft)
                .append(whereCondition);
        LOG.info("getCohortCgmCount: Query {}", sql.toString());
        return jdbcTemplate.queryForMap(sql.toString()).get("total_count");
    }

    @Transactional
    @Async
    public Map<String, Object> getAllStudyAvgGlucose() throws SQLException {
        final var sql = "SELECT CONCAT(ROUND(avg_glucose, 2), 'mg/dL') AS avg_glucose FROM agg_avg_glucose_cached;";
        LOG.info("CombineDataAccessService:getAllStudyAvgGlucose, Executing SQL query: {}", sql);
        return jdbcTemplate.queryForMap(sql);
    }

    @Transactional
    @Async
    public Map<String, Object> getEachMetricsDetails(String metric) {
        LOG.info("Fetching details for metric: {}", metric);

        final var sql = "SELECT * FROM metric_definitions WHERE metric_id = ?";

        try {
            Map<String, Object> result = jdbcTemplate.queryForMap(sql, new Object[] { metric });
            if (result.containsKey("metric_info")) {
                String metricInfoJson = (String) result.get("metric_info");
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> metricInfo = objectMapper.readValue(metricInfoJson,
                        new TypeReference<Map<String, Object>>() {
                        });
                result.put("metric_info", metricInfo);
            }
            return result;

        } catch (EmptyResultDataAccessException e) {
            LOG.warn("No results found for metric: {}", metric, e);
            return Collections.emptyMap();
        } catch (DataAccessException e) {
            LOG.error("Unexpected error while fetching metric details for {}: {}", metric, e.getMessage(), e);
            return Collections.emptyMap();
        } catch (JsonProcessingException e) {
            LOG.error("Error parsing metric_info JSON: {}", e.getMessage(), e);
            return Collections.emptyMap();
        } catch (Exception e) {
            LOG.error("Unexpected error while fetching metric details for {}: {}", metric, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Transactional
    @Async
    public Map<String, Object> getEachMetricsDetailsFromView(String metric) {
        LOG.info("Fetching details for metric: {}", metric);

        final var sql = "SELECT * FROM metric_info_view WHERE metric_id = ?";

        try {
            Map<String, Object> result = jdbcTemplate.queryForMap(sql, new Object[] { metric });
            return result;
        } catch (EmptyResultDataAccessException e) {
            LOG.warn("No results found for metric: {}", metric, e);
            return Collections.emptyMap();
        } catch (DataAccessException e) {
            LOG.error("Unexpected error while fetching metric details for {}: {}", metric, e.getMessage(), e);
            return Collections.emptyMap();
        } catch (Exception e) {
            LOG.error("Unexpected error while fetching metric details for {}: {}", metric, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Transactional
    @Async
    public Map<String, Object> getAllStudyDetailMetrics() throws SQLException {
        final var sql = "SELECT total_number_of_participants, percent_female, average_age FROM all_participants_metrics_cached";
        LOG.info("CombineDataAccessService:getAllStudyVanityMetrics, Executing SQL query: {}", sql);
        return jdbcTemplate.queryForMap(sql);
    }
}
