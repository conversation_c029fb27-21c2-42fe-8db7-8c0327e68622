package org.diabetestechnology.drh.udi;

// import javax.sql.DataSource;

// import org.jooq.DSLContext;
// import org.jooq.SQLDialect;

// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.boot.context.properties.ConfigurationProperties;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.context.annotation.Primary;
// import
// org.springframework.transaction.annotation.EnableTransactionManagement;

// /**
// * Configuration class for the UDI Prime database.
// */

// @Configuration
// @EnableTransactionManagement
// @ConfigurationProperties(prefix = "org.diabetestechnology.udi.prime.jdbc")
// public class UdiPrimeDbConfig {

// private static final Logger LOG =
// LoggerFactory.getLogger(UdiPrimeDbConfig.class.getName());

// @Value("${org.diabetestechnology.udi.prime.jdbc.url}")
// private String jdbcUrl;

// @Value("${org.diabetestechnology.udi.prime.jdbc.driverClassName}")
// private String driverClassName;

// @Value("${org.diabetestechnology.udi.prime.jdbc.connection.maximum-pool-size}")
// private int maxPoolSize;

// @Value("${org.diabetestechnology.udi.prime.jdbc.connection.minimum-idle}")
// private int minIdle;

// @Value("${org.diabetestechnology.udi.prime.jdbc.connection.connection-timeout}")
// private long connectionTimeout;

// @Value("${org.diabetestechnology.udi.prime.jdbc.connection.idle-timeout}")
// private long idleTimeout;

// @Value("${org.diabetestechnology.udi.prime.jdbc.connection.max-lifetime}")
// private long maxLifetime;

// @Value("${org.diabetestechnology.udi.prime.jdbc.connection.threshold}")
// private long threshold;

// @Value("${org.diabetestechnology.udi.prime.jdbc.connection.read-only}")
// private boolean readOnly;

// @Bean(name = "udiPrimaryDataSource")
// @Primary
// DataSource dataSource() {
// CustomDataSource ds = sqliteCustomDataSource();
// return ds.dataSource();
// }

// @Bean
// public DSLContext dsl() {
// return sqliteCustomDataSource().dslContext();
// }

// @Bean(name = "sqliteCustomDataSource")
// CustomDataSource sqliteCustomDataSource() {
// LOG.info("JDBC URL:: {}", jdbcUrl);
// LOG.info("Driver Class Name:: {}", driverClassName);
// return new CustomDataSource(builder -> {
// builder.jdbcDbPath(jdbcUrl);
// builder.driverClassName(driverClassName);
// builder.maximumPoolSize(maxPoolSize);
// builder.minimumIdle(minIdle);
// builder.connectionTimeout(connectionTimeout);
// builder.idleTimeout(idleTimeout);
// builder.maxLifetime(maxLifetime);
// builder.leakDetectionThreshold(threshold);
// builder.dbType(SQLDialect.SQLITE);
// });
// }
// }
