package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Hidden
public class StudyDetailController {

    private static final Logger LOG = LoggerFactory.getLogger(StudyDetailController.class.getName());

    private final Presentation presentation;
    @Autowired
    ResearchStudyService researchStudyService;

    public StudyDetailController(Presentation presentation) {
        this.presentation = presentation;
    }

    // Add the new method for detail view
    // @RouteMapping(label = "Studies", title="Studies Details")
    @Hidden
    @GetMapping("/study/detail/{studyId}")
    public String studyDetail(@PathVariable String studyId, Model model, final HttpServletRequest request) {
        LOG.info("Getting details for studyId: {}", studyId);
        model.addAttribute("studyId", studyId);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        String[] pageDescription = {
                "The Study Detail page features the calculated metrics and lists the participants enrolled in the selected study.",
                "We can navigate to each participant's report by clicking on their individual ID in the row."
        };
        String[] notes = {
                "The loading time for the Participant List may be longer due to data loading. The speed needs to be improved."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/study", model, request);
    }

    // @RouteMapping(label = "Studies Details", siblingOrder = 60)
    @Hidden
    @GetMapping("/study/{studyId}")
    public String detail(@PathVariable String studyId) {
        return "redirect:/study/detail/{studyId}";
    }
}
