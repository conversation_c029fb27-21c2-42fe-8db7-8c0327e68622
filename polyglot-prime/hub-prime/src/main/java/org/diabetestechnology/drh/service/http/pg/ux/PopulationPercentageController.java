package org.diabetestechnology.drh.service.http.pg.ux;

import org.diabetestechnology.drh.service.http.pg.service.PopulationPercentageService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH Population Percentage APIs")
public class PopulationPercentageController {

    private static final Logger LOG = LoggerFactory.getLogger(PopulationPercentageController.class);
    private final PopulationPercentageService populationPercentageService;

    public PopulationPercentageController(
            PopulationPercentageService populationPercentageService) {
        this.populationPercentageService = populationPercentageService;
    }

    @GetMapping(value = "/research-study/population/total-participants-with-data.{extension}", produces = {
            "text/html" })
    @ResponseBody
    @Operation(summary = "Fetch Total participants with data")
    public ResponseEntity<?> getTotalParticipantsWithData(@PathVariable String extension) {
        try {
            LOG.info("getTotalParticipantsWithData: {}", extension);
            Object totalParticipants = populationPercentageService.getTotalParticipantsWithData().get();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(totalParticipants);
        } catch (Exception e) {
            LOG.error("Error in /population/totalParticipantsWithData: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping(value = "/research-study/population/total-cgm-file-count.{extension}", produces = { "text/html" })
    @ResponseBody
    @Operation(summary = "Fetch Total cgm file count")
    public ResponseEntity<?> getTotalCgmFileCount(@PathVariable String extension) {
        try {
            LOG.info("getTotalCgmFileCount: {}", extension);
            Object totalCgmFileCount = populationPercentageService.getTotalCgmFileCount().get();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(totalCgmFileCount);
        } catch (Exception e) {
            LOG.error("Error in /population/totalCgmFilecount: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping(value = "/research-study/population/data-points.{extension}", produces = { "text/html" })
    @ResponseBody
    @Operation(summary = "Fetch Data Points")
    public ResponseEntity<?> getDataPoints(@PathVariable String extension) {
        try {
            LOG.info("getDataPoints: {}", extension);
            Object dataPoints = populationPercentageService.getTotalDataPoints();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(dataPoints);
        } catch (Exception e) {
            LOG.error("Error in /population/dataPoints: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}
