package org.diabetestechnology.drh.service.http.pg.request;

import java.time.OffsetDateTime;
import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;

// @formatter:off
public record ParticipantRowFileRequest(
        String fileName,
        JsonNode cgmRawDataJson,
        OffsetDateTime uploadTimestamp,
        String fileSize,
        String status,
        ParticipantRowFileMetaDataRequest fileMetadata, // Used here
        String fileType,
        String studyId,
        String orgPartyId,
        byte[] cgmRawDataByteArray,
        String participantId,
        String cgmDataXml,
        String fileFormat,
        String fileUrl) {
    public record ParticipantRowFileMetaDataRequest(
            String deviceName,
            String deviceId,
            String sourcePlatform,
            String sourcePlatformId,
            String fileName,
            String fileFormat,
            String fileUploadDate,
            String mapFieldOfCgmDate,
            String mapFieldOfCgmValue,
            List<String> interactionHierarchy,
            String lastInteractionId) {
    }
}
// @formatter:on