package org.diabetestechnology.drh.service.http;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.oauth2.AuthorizationFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.oauth2.OrcidOAuth2UserService;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.diabetestechnology.drh.service.http.hub.prime.service.oauth2.DelegatingOAuth2UserService;
import org.diabetestechnology.drh.service.http.hub.prime.service.oauth2.GitHubOAuth2UserService;
import org.diabetestechnology.drh.service.http.hub.prime.service.DatabaseUserDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.session.HttpSessionEventPublisher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.filter.ForwardedHeaderFilter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@Configuration
@ConfigurationProperties(prefix = "spring.security.oauth2.client.registration")
@EnableWebSecurity
public class SecurityConfig {
    private static final int DEFAULT_TIMEOUT = 30 * 60; // 30 MINUTES
    private static final Logger LOG = LoggerFactory.getLogger(SecurityConfig.class);
    @Value("${server.servlet.session.timeout}")
    private static String sessionTimeout;
    @Value("${ORG_DRH_SERVICE_ALLOWED_ORIGINS}")
    private String allowedOrigins;

    @Autowired
    private AuthorizationFilter authzFilter;

    @Autowired
    private DelegatingOAuth2UserService delegatingOAuth2UserService;

    @Autowired
    private DatabaseUserDetailsService databaseUserDetailsService;
    private final DSLContext dsl;

    SecurityConfig(@Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(databaseUserDetailsService);
        provider.setPasswordEncoder(passwordEncoder());
        return new ProviderManager(provider);
    }

    @Bean
    SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/superadmin/login", "/login", "/public/**", "/oauth2/**", "/", "/css/**",
                                "/js/**", "/*.png", "/*.jpg", "/*.gif", "/*.svg", "/*.ico", "/**")
                        .permitAll()
                        .anyRequest().authenticated())
                .formLogin(form -> form
                        .loginPage("/")
                        .loginProcessingUrl("/superadmin/login")
                        .usernameParameter("username")
                        .passwordParameter("password")
                        .defaultSuccessUrl("/home", true)
                        .failureUrl("/superadmin/login?error=true")
                        .permitAll())
                .oauth2Login(oauth2Login -> oauth2Login
                        .userInfoEndpoint(userInfoEndpoint -> userInfoEndpoint.userService(delegatingOAuth2UserService))
                        .successHandler(gitHubLoginSuccessHandler())
                        .defaultSuccessUrl("/home", true)
                        .loginPage("/"))
                .logout(logout -> logout
                        .logoutUrl("/logout")
                        .deleteCookies("JSESSIONID")
                        .logoutSuccessUrl("/")
                        .invalidateHttpSession(true)
                        .permitAll())
                .csrf(AbstractHttpConfigurer::disable)
                .addFilterAfter(authzFilter, UsernamePasswordAuthenticationFilter.class);

        http.headers(headers -> headers.frameOptions(frameOptions -> frameOptions.sameOrigin()));
        http.sessionManagement(
                sessionManagement -> sessionManagement
                        .sessionCreationPolicy(SessionCreationPolicy.ALWAYS)
                        .maximumSessions(100)
                        .sessionRegistry(sessionRegistry()));

        return http.build();
    }

    @Bean
    CorsFilter corsFilter() {
        List<String> origins = Arrays.asList(allowedOrigins.split(","));
        // primarily setup for Swagger UI and OpenAPI integration
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        // config.addAllowedOriginPattern("*"); // Customize as needed
        config.setAllowedOrigins(origins);
        config.addAllowedMethod("*");
        config.addAllowedHeader("*");
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }

    @Bean
    AuthenticationSuccessHandler orcidLoginSuccessHandler() {
        return new AuthenticationSuccessHandler() {
            @Override
            public void onAuthenticationSuccess(HttpServletRequest request,
                    HttpServletResponse response,
                    Authentication authentication) throws IOException {
                // You can log the authenticated user's details if needed
                OAuth2User oAuth2User = (OAuth2User) authentication.getPrincipal();
                String userName = oAuth2User.getName();
                LOG.debug("User " + userName + " has successfully logged in with ORCID.");
                // Redirect to home page
                response.sendRedirect("/home");
            }
        };
    }

    @Bean
    AuthenticationSuccessHandler gitHubLoginSuccessHandler() {
        return new GitHubLoginSuccessHandler();
    }

    private static class GitHubLoginSuccessHandler implements AuthenticationSuccessHandler {

        private final RequestCache requestCache = new HttpSessionRequestCache();

        @Override
        public void onAuthenticationSuccess(HttpServletRequest request,
                HttpServletResponse response, Authentication authentication)
                throws IOException, jakarta.servlet.ServletException {
            OAuth2User oAuth2User = (OAuth2User) authentication.getPrincipal();
            String orcidId = (String) oAuth2User.getAttribute("orcid");
            System.out.println("onAuthenticationSuccess: orcidId: " + orcidId);
            String name = oAuth2User.getName();
            Map<String, Object> attributes = oAuth2User.getAttributes();

            String email = (String) attributes.get("email");
            System.out.println("onAuthenticationSuccess: Name: " + name);
            System.out.println("onAuthenticationSuccess: email: " + email);
            final var savedRequest = requestCache.getRequest(request, response);

            int timeoutInSeconds = parseTimeout(sessionTimeout);
            HttpSession session = request.getSession();
            session.setMaxInactiveInterval(timeoutInSeconds);

            if (savedRequest == null) {
                response.sendRedirect("/home");
                return;
            }

            final var targetUrl = savedRequest.getRedirectUrl();
            response.sendRedirect(targetUrl);

        }
    }

    public static int parseTimeout(String timeout) {
        if (timeout == null || timeout.isEmpty()) {
            return DEFAULT_TIMEOUT;
        }
        try {
            if (timeout.endsWith("s")) {
                return Integer.parseInt(timeout.replace("s", ""));
            } else if (timeout.endsWith("m")) {
                return Integer.parseInt(timeout.replace("m", "")) * 60;
            } else if (timeout.endsWith("h")) {
                return Integer.parseInt(timeout.replace("h", "")) * 3600;
            }
            // Default case: no unit specified, assume seconds
            return Integer.parseInt(timeout);
        } catch (NumberFormatException e) {
            return DEFAULT_TIMEOUT;
        }
    }

    // To ensures that the application correctly interprets the headers set by the
    // proxy.
    @Bean
    ForwardedHeaderFilter forwardedHeaderFilter() {
        return new ForwardedHeaderFilter();
    }

    @Bean
    DelegatingOAuth2UserService delegatingOAuth2UserService(
            OAuth2UserService<OAuth2UserRequest, OAuth2User> githubUserService,
            OAuth2UserService<OAuth2UserRequest, OAuth2User> orcidUserService) {
        return new DelegatingOAuth2UserService(githubUserService, orcidUserService);
    }

    @Bean
    OAuth2UserService<OAuth2UserRequest, OAuth2User> orcidUserService() {
        return new OrcidOAuth2UserService(); // Your ORCID user service implementation
    }

    @Bean
    OAuth2UserService<OAuth2UserRequest, OAuth2User> githubUserService() {
        return new GitHubOAuth2UserService(); // Your GitHub user service implementation
    }

    @Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }

    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher() {
        return new HttpSessionEventPublisher();
    }
}
