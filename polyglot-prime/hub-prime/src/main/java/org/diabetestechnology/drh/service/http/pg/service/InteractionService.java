package org.diabetestechnology.drh.service.http.pg.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.FileProcessingStatus;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.util.HttpRequestResponseUtil;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

@Service
public class InteractionService {
    private static final Logger LOG = LoggerFactory.getLogger(InteractionService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final DSLContext dsl;
    private final UserNameService userNameService;
    private final PartyService partyService;
    private final MasterService masterService;

    public InteractionService(@Qualifier("secondaryDsl") DSLContext dsl, UserNameService userNameService,
            PartyService partyService, MasterService masterService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.masterService = masterService;
    }

    @Transactional
    public String saveHubInteraction(String studyId) throws JsonProcessingException {
        final var userPartyId = partyService
                .getPartyIdByUserId(userNameService.getUserId());
        final var organizationPartyId = partyService
                .getOrganizationPartyIdByUser(userNameService.getUserId());

        ObjectNode jsonNode = objectMapper.createObjectNode();

        jsonNode.put("study_id", studyId);
        jsonNode.put("organization_party_id", organizationPartyId);
        jsonNode.put("created_by", userPartyId);
        jsonNode.put("updated_by", userPartyId);

        // Convert to JSON string
        String jsonString = objectMapper.writeValueAsString(jsonNode);

        final var query = dsl.select(DSL.field(
                "drh_stateless_activity_audit.insert_hub_interaction(?)", JSONB.class, DSL.cast(DSL.val(
                        jsonString),
                        JSONB.class)));

        LOG.info("Save Interaction Query: {}", query);
        JSONB saveHubInteractionResponse = query.fetchOneInto(JSONB.class);
        LOG.info("Save Interaction Response: {}", saveHubInteractionResponse);

        JsonNode jsonResponseNode = objectMapper.readTree(saveHubInteractionResponse.data());

        if (jsonResponseNode.has("hub_interaction_id")) {
            String hubInteractionId = jsonResponseNode.get("hub_interaction_id").asText();
            System.out.println("Hub Interaction ID: " + hubInteractionId);
            return hubInteractionId;
        } else {
            System.out.println("Key 'hub_interaction_id' does not exist.");
            return null;
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveStudyInteraction(String studyId, String hubInteractionId, String interactionType,
            String description, String fromState,
            String toState, String request, String response, String errorResponse, int responseCode, String status,
            String actionType, String actionStatus) {
        try {
            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
            final var uri = HttpRequestResponseUtil.getCurrentRequest().getRequestURI();

            ObjectNode jsonNode = objectMapper.createObjectNode();

            jsonNode.put("hub_interaction_id", hubInteractionId);
            jsonNode.put("study_id", studyId);
            jsonNode.put("organization_party_id", organizationPartyId);
            jsonNode.put("uri", uri);
            jsonNode.put("interaction_type", interactionType);
            jsonNode.put("description", description);
            if (request != null) {
                jsonNode.set("request", objectMapper.readTree(request));
            }
            if (response != null) {
                jsonNode.set("response", objectMapper.readTree(response));
            }
            jsonNode.put("from_state", fromState);
            jsonNode.put("to_state", toState);
            jsonNode.put("status", status);
            jsonNode.put("response_code", responseCode);
            if (errorResponse != null) {
                jsonNode.set("error_response", objectMapper.readTree(errorResponse));
            }
            jsonNode.put("created_by", userPartyId);
            jsonNode.put("updated_by", userPartyId);
            jsonNode.put("action_type", actionType);
            jsonNode.put("action_status", actionStatus);
            jsonNode.put("interaction_action_type_id", masterService.getActiontype(actionType));
            jsonNode.put("interaction_status_id", masterService.getInteractionStatus(actionStatus));
            jsonNode.set("interaction_hierarchy",
                    objectMapper.readTree(
                            objectMapper.writeValueAsString(getAndSetInteractionHierarchyofStudyInteraction(studyId))));

            // Convert to JSON string
            String jsonString = objectMapper.writeValueAsString(jsonNode);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_activity_audit.insert_study_interaction(?)", JSONB.class, DSL.cast(DSL.val(
                            jsonString),
                            JSONB.class)));

            LOG.info("Save Interaction Query: {}", query);
            JSONB saveStudyInteractionResponse = query.fetchOneInto(JSONB.class);
            LOG.info("Save Interaction Response: {}", saveStudyInteractionResponse);
        } catch (Exception e) {
            LOG.error("Error in saving study interaction", e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getHubIntercationIdOfStudy(String studyId) {
        final var query = dsl.selectDistinct(DSL.field(
                "hub_interaction_id"))
                .from("drh_stateless_activity_audit.hub_interaction_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)));
        LOG.info("Get Hub Interaction ID Query: {}", query);
        String hubInteractionId = query.fetchOneInto(String.class);
        LOG.info("Get Hub Interaction ID Response: {}", hubInteractionId);
        return hubInteractionId;
    }

    // TO DO : view to fetch Hub Interaction table Data
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JSONB saveFileInteraction(String hubInteractionId, String studyId, String participantId,
            String description, String request, String response, String dbFileId, String fileLocation, String fileName,
            String fileContentType, String fileContentJson, String fileCategory,
            String fileUploadStatus, String fileDescription, String fileProcessingInitiatedAt,
            String fileProcessingCompletedAt, String fileProcessingStatus, int responseCode, String errorResponse,
            List<String> interactionHierarchy, String actionType) {
        try {
            LOG.info("Save File Interaction");
            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
            final var uri = HttpRequestResponseUtil.getCurrentRequest().getRequestURI();

            ObjectNode jsonNode = objectMapper.createObjectNode();
            LOG.info("hub_interaction_id: {}", hubInteractionId);
            jsonNode.put("hub_interaction_id", hubInteractionId);
            LOG.info("study_id: {}", studyId);
            jsonNode.put("study_id", studyId);
            LOG.info("organization_party_id: {}", organizationPartyId);
            jsonNode.put("organization_party_id", organizationPartyId);
            LOG.info("participant_id: {}", participantId);
            jsonNode.put("participant_id", participantId);
            LOG.info("uri: {}", uri);
            jsonNode.put("uri", uri);
            LOG.info("description: {}", description);
            jsonNode.put("description", description);
            LOG.info("request: {}", request);
            jsonNode.put("request", request);
            LOG.info("response: {}", response);
            jsonNode.put("response", response);
            LOG.info("db_file_id: {}", dbFileId);
            jsonNode.put("db_file_id", dbFileId);
            LOG.info("file_location: {}", fileLocation);
            jsonNode.put("file_location", fileLocation);
            LOG.info("file_name: {}", fileName);
            jsonNode.put("file_name", fileName);
            LOG.info("file_content_type: {}", fileContentType);
            jsonNode.put("file_content_type", fileContentType);
            LOG.info("file_content_json: {}", fileContentJson);
            jsonNode.put("file_content_json", fileContentJson);
            LOG.info("file_category: {}", fileCategory);
            jsonNode.put("file_category", fileCategory);
            LOG.info("file_upload_status: {}", fileUploadStatus);
            jsonNode.put("file_upload_status", fileUploadStatus);
            LOG.info("file_processing_initiated_at: {}", fileProcessingInitiatedAt);
            jsonNode.put("file_processing_initiated_at", fileProcessingInitiatedAt);
            LOG.info("file_processing_completed_at: {}", fileProcessingCompletedAt);
            jsonNode.put("file_processing_completed_at", fileProcessingCompletedAt);
            LOG.info("file_processing_status: {}", fileProcessingStatus);
            jsonNode.put("file_processing_status", fileProcessingStatus);
            LOG.info("response_code: {}", responseCode);
            jsonNode.put("response_code", responseCode);
            LOG.info("error_response: {}", errorResponse);
            jsonNode.put("error_response", errorResponse);
            LOG.info("created_by: {}", userPartyId);
            jsonNode.put("created_by", userPartyId);
            LOG.info("updated_by: {}", userPartyId);
            jsonNode.put("updated_by", userPartyId);
            LOG.info("Intercation Hierarchy : {}", interactionHierarchy);
            jsonNode.set("interaction_hierarchy",
                    objectMapper.readTree(objectMapper.writeValueAsString(interactionHierarchy)));
            LOG.info("Action Type : {}", actionType);
            final var actionTypeId = masterService.getActiontype(actionType);
            LOG.info("Action Type ID: {}", actionTypeId);
            jsonNode.put("interaction_action_type_id", actionTypeId);
            final var interactionStatusId = masterService.getInteractionStatus(fileProcessingStatus);
            LOG.info("Interaction Status ID: {}", interactionStatusId);
            jsonNode.put("interaction_status_id", interactionStatusId);

            // Convert to JSON string
            String jsonString = objectMapper.writeValueAsString(jsonNode);

            final var query = dsl.select(DSL.field(
                    "drh_stateless_activity_audit.insert_file_interaction(?)", JSONB.class, DSL.cast(DSL.val(
                            jsonString),
                            JSONB.class)));

            LOG.info("Save File Interaction Query: {}", query);
            JSONB saveFileInteractionResponse = query.fetchOneInto(JSONB.class);
            LOG.info("Save File Interaction Response: {}", saveFileInteractionResponse);
            return saveFileInteractionResponse;
        } catch (Exception e) {
            LOG.error("Error in saving file interaction", e);
            return null;
        }
    }

    public Boolean isDbFileInteractionExistsForStudy(String studyId) {
        final var query = DSL.selectOne()
                .from("drh_stateless_activity_audit.file_interaction_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)))
                .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE)));
        LOG.info("Check entry for Study Database File . Query: {}", query);
        final var exists = dsl.fetchExists(query);
        LOG.info("Check if DB File Interaction Exists. Query Response: {}", exists);
        return exists;
    }

    public Boolean isDbFileInteractionFinishedForAction(String studyId, String actionType) {
        final var statusId = masterService.getInteractionStatus(FileProcessingStatus.SUCCESS);
        LOG.info("Status ID for Interaction Status SUCCESS is: {}", statusId);
        final var actionTypeId = masterService.getActiontype(actionType);
        LOG.info("Action Type ID for Action Type {} is: {}", actionType, actionTypeId);
        final var query = DSL.selectOne()
                .from("drh_stateless_activity_audit.file_interaction_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)))
                .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE)))
                .and(DSL.field("interaction_action_type_id")
                        .eq(DSL.value(
                                actionTypeId))
                        .and(DSL.field("interaction_status_id")
                                .eq(DSL.value(statusId))));
        LOG.info("Check Db File Interaction Exists For {} . Query: {}", actionType, query);
        final var exists = dsl.fetchExists(query);
        LOG.info("Check if DB File Interaction Exists For {}, Query Response: {}", actionType, exists);
        return exists;
    }

    public boolean isCompletedCgmRowData(String studyId) {
        LOG.info("Check CGM Row Data Copy status");
        final var query = DSL.selectOne()
                .from("drh_stateless_activity_audit.file_interaction_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)))
                .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE)))
                .and(DSL.field("interaction_action_type_id")
                        .eq(DSL.val(masterService.getActiontype(ActionType.SAVE_DB_CONTENT))))
                .and(DSL.field("interaction_status_id")
                        .eq(DSL.val(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS))));
        LOG.info("Check entry for Study Database File . Query: {}", query);
        final var exists = dsl.fetchExists(query);
        LOG.info("Check if DB File Interaction Exists Query: {}", exists);
        return exists;
    }

    public String getDbFileIdOfCompletedCgmRowData(String studyId) {
        LOG.info("Check DB File ID of CGM Row Data");
        final var query = dsl
                .select(DSL.field("db_file_id"))
                .from("drh_stateless_activity_audit.file_interaction_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)))
                .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE)))
                .and(DSL.field("interaction_action_type_id")
                        .eq(DSL.val(masterService.getActiontype(ActionType.SAVE_DB_CONTENT))))
                .and(DSL.field("interaction_status_id")
                        .eq(DSL.val(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS))));
        LOG.info("Check entry for Study Database File . Query: {}", query);
        final var dbFileId = query.fetchOneInto(String.class);
        LOG.info("DB file Id of Completed CGM Row Data : {}", dbFileId);
        return dbFileId;
    }

    public String getSuccessDbFileInteractionIdOfActionType(String actionType, String studyId) {
        LOG.info("Reading file_interaction_id of {}", actionType);
        LOG.info("InteractionService: DSLContext is {}", dsl.configuration().dialect());

        LOG.info("dsl: {}", dsl);
        LOG.info("dsl class: {}", dsl.getClass());
        LOG.info("Thread: {}", Thread.currentThread().getName());
        final var query = dsl
                .select(DSL.field("file_interaction_id"))
                .from("drh_stateless_activity_audit.file_interaction_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)))
                .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE)))
                .and(DSL.field("interaction_action_type_id")
                        .eq(DSL.val(masterService.getActiontype(actionType))))
                .and(DSL.field("interaction_status_id")
                        .eq(DSL.val(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS))));
        LOG.info("Query to read file_interaction_id of {}: {}", actionType, query);
        final var fileInteractionId = query.fetchOneInto(String.class);
        LOG.info("File Interaction Id of {} of the studyId {} : {}", actionType, studyId, fileInteractionId);
        return fileInteractionId;
    }

    public List<String> getInteractionHierarchy(JSONB interactionResponse, List<String> hierarchyJsonArray) {
        try {
            JsonNode fileInteracionResponseJson = objectMapper.readTree(interactionResponse.data());
            final var fileInteracionId = fileInteracionResponseJson.get("file_interaction_id").asText();
            if (hierarchyJsonArray == null) {
                hierarchyJsonArray = new ArrayList<>();
            }
            hierarchyJsonArray.add(fileInteracionId.toString());
            return hierarchyJsonArray;
        } catch (Exception e) {
            LOG.error("Error in reading interaction hierarchy", e);
            return null;
        }
    }

    public String getLastInteractionId(JSONB interactionResponse) {
        try {
            JsonNode lastFileInteracionResponseJson = objectMapper.readTree(interactionResponse.data());
            final var lastFileInteracionId = lastFileInteracionResponseJson.get("file_interaction_id").asText();

            return lastFileInteracionId;
        } catch (Exception e) {
            LOG.error("Error in reading last interaction id", e);
            return null;
        }
    }

    public JSONB getLastInteractionIdAndHierarchy(String firstInteractionId) {
        try {
            final var query = dsl
                    .select(DSL.field(
                            "jsonb_build_object(" +
                                    "'file_interaction_id', file_interaction_id, " +
                                    "'interaction_hierarchy', interaction_hierarchy" +
                                    ")",
                            JSONB.class))
                    .from("drh_stateless_activity_audit.file_interaction_view")
                    .where(DSL.field("jsonb_path_query_first(interaction_hierarchy::jsonb, '$[0]')::text")
                            .eq(DSL.val("\"" + firstInteractionId + "\"")))
                    .orderBy(DSL.field("created_at").desc())
                    .limit(1);
            LOG.info("Query to get last interaction id and hierarchy: {}", query);
            JSONB lastInteractionIdAndHierarchy = query.fetchOneInto(JSONB.class);
            LOG.info("Last Interaction Id and Hierarchy: {}", lastInteractionIdAndHierarchy);
            return lastInteractionIdAndHierarchy;
        } catch (Exception e) {
            LOG.error("Error in reading last interaction id", e);
            return null;
        }
    }

    public List<String> getLastInteractionHierarchy(String firstInteractionId)
            throws JsonMappingException, JsonProcessingException {
        LOG.info("Get Last Interaction Hierarchy");
        JSONB interactionHierarchy = getLastInteractionIdAndHierarchy(firstInteractionId);
        LOG.info("Interaction and Hierarchy response: {}", interactionHierarchy.data());

        // Parse the JSONB data into a JsonNode
        JsonNode lastFileInteractionResponseJson = objectMapper.readTree(interactionHierarchy.data());

        // Get the "interaction_hierarchy" field as a proper JsonNode
        JsonNode hierarchyNode = lastFileInteractionResponseJson.get("interaction_hierarchy");
        LOG.info("Interaction Hierarchy Node: {}", hierarchyNode);

        // If hierarchyNode is a string, parse it again as a JSON array
        if (hierarchyNode != null && hierarchyNode.isTextual()) {
            String jsonArrayString = hierarchyNode.asText(); // Extract the string representation
            List<String> hierarchyJsonArray = objectMapper.readValue(jsonArrayString,
                    new TypeReference<List<String>>() {
                    });

            LOG.info("Last Interaction Hierarchy: {}", hierarchyJsonArray);
            return hierarchyJsonArray;
        } else {
            LOG.error("Interaction hierarchy is not a valid JSON array: {}", hierarchyNode);
            return new ArrayList<>(); // Return an empty list to avoid errors
        }
    }

    public JSONB getLastInteractionLog(String lastInteractionId) {
        try {
            final var query = dsl
                    .select(DSL.field(
                            "jsonb_build_object(" +
                                    "'hub_interaction_id', hub_interaction_id, " +
                                    "'request', request, " +
                                    "'db_file_id', db_file_id, " +
                                    "'file_location', file_location," +
                                    "'file_name', file_name," +
                                    "'file_content_type', file_content_type," +
                                    "'file_upload_status', file_upload_status," +
                                    "'file_processing_initiated_at', file_processing_initiated_at," +
                                    "'interaction_hierarchy', interaction_hierarchy," +
                                    "'study_id', study_id" +
                                    ")",
                            JSONB.class))
                    .from("drh_stateless_activity_audit.file_interaction_view")
                    .where(DSL.field("file_interaction_id")
                            .eq(DSL.val(
                                    lastInteractionId)))
                    .orderBy(DSL.field("created_at").desc())
                    .limit(1);
            LOG.info("Query to get last interaction data: {}", query);
            JSONB lastInteractionIdAndHierarchy = query.fetchOneInto(JSONB.class);
            LOG.info("Last Interaction response: {}", lastInteractionIdAndHierarchy);
            return lastInteractionIdAndHierarchy;
        } catch (Exception e) {
            LOG.error("Error in reading last interaction id", e);
            return null;
        }
    }

    public List<String> getAndSetInteractionHierarchyFromInteractionLog(JSONB interactionHierarchy,
            String lastFileInteractionId) {
        try {
            LOG.info("Get Last Interaction Hierarchy");

            LOG.info("Interaction and Hierarchy response: {}", interactionHierarchy.data());

            // Parse the JSONB data into a JsonNode
            JsonNode lastFileInteractionResponseJson = objectMapper.readTree(interactionHierarchy.data());

            // Get the "interaction_hierarchy" field as a proper JsonNode
            JsonNode hierarchyNode = lastFileInteractionResponseJson.get("interaction_hierarchy");
            LOG.info("Interaction Hierarchy Node: {}", hierarchyNode);

            // If hierarchyNode is a string, parse it again as a JSON array
            if (hierarchyNode != null && hierarchyNode.isTextual()) {
                String jsonArrayString = hierarchyNode.asText(); // Extract the string representation
                List<String> hierarchyJsonArray = objectMapper.readValue(jsonArrayString,
                        new TypeReference<List<String>>() {
                        });

                LOG.info("Last Interaction Hierarchy: {}", hierarchyJsonArray);
                hierarchyJsonArray.add(lastFileInteractionId);
                LOG.info("Last Interaction Hierarchy with last interaction id: {}", hierarchyJsonArray);

                return hierarchyJsonArray;
            } else if (hierarchyNode != null && hierarchyNode.isArray() && hierarchyNode.size() == 0) {
                List<String> hierarchyJsonArray = objectMapper.convertValue(hierarchyNode,
                        new TypeReference<List<String>>() {
                        });

                LOG.info("Last Interaction Hierarchy: {}", hierarchyJsonArray);
                hierarchyJsonArray.add(lastFileInteractionId);
                LOG.info("Last Interaction Hierarchy with last interaction id: {}", hierarchyJsonArray);

                return hierarchyJsonArray;
            } else if (hierarchyNode != null && hierarchyNode.isArray()) {
                // If hierarchyNode is already a JSON array, directly convert it to a
                // List<String>
                List<String> hierarchyJsonArray = objectMapper.convertValue(hierarchyNode,
                        new TypeReference<List<String>>() {
                        });
                LOG.info("Last Interaction Hierarchy: {}", hierarchyJsonArray);
                hierarchyJsonArray.add(lastFileInteractionId);
                LOG.info("Last Interaction Hierarchy with last interaction id: {}", hierarchyJsonArray);
                return hierarchyJsonArray;

            }

            else {
                LOG.error("Interaction hierarchy is not a valid JSON array: {}", hierarchyNode);
                return new ArrayList<>(); // Return an empty list to avoid errors
            }
        } catch (Exception e) {
            LOG.error("Error in reading last interaction id", e);
            return new ArrayList<>();
        }
    }

    public List<String> getAndSetInteractionHierarchyofStudyInteraction(
            String studyId) {
        try {
            LOG.info("Get Last Interaction Hierarchy");

            LOG.info("Interaction and Hierarchy response of Study Id: {}", studyId);
            final var query = dsl
                    .select(DSL.field(
                            "jsonb_build_object(" +
                                    "'hub_interaction_id', hub_interaction_id, " +
                                    "'study_interaction_id', study_interaction_id, " +
                                    "'interaction_hierarchy', interaction_hierarchy," +
                                    "'study_id', study_id" +
                                    ")",
                            JSONB.class))
                    .from("drh_stateless_activity_audit.study_interaction_view")
                    .where(DSL.field("study_id")
                            .eq(DSL.val(
                                    studyId)))
                    .orderBy(DSL.field("created_at").desc())
                    .limit(1);
            LOG.info("Query to get last interaction data: {}", query);
            JSONB lastInteractionIdAndHierarchy = query.fetchOneInto(JSONB.class);
            if (lastInteractionIdAndHierarchy == null) {
                LOG.warn("No interaction found for studyId: {}", studyId);
                return new ArrayList<>();
            }
            JsonNode lastFileInteractionResponseJson = objectMapper.readTree(lastInteractionIdAndHierarchy.data());
            JsonNode interactionIdNode = lastFileInteractionResponseJson.get("study_interaction_id");
            if (interactionIdNode == null || interactionIdNode.isNull()) {
                LOG.warn(
                        "Missing 'study_interaction_id' in interaction data for studyId: {}",
                        studyId);
                return new ArrayList<>();
            }
            final var lastInteractionId = interactionIdNode.asText();
            return getAndSetInteractionHierarchyFromInteractionLog(lastInteractionIdAndHierarchy,
                    lastInteractionId);

        } catch (Exception e) {
            LOG.error("Error in reading last interaction id", e);
            return new ArrayList<>();
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveStudyParticipantInteraction(String studyId, String participantId, String hubInteractionId,
            String interactionType,
            String description, String fromState,
            String toState, String request, String response, String errorResponse, int responseCode, String status,
            String actionType, String actionStatus) {
        try {
            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
            final var uri = HttpRequestResponseUtil.getCurrentRequest().getRequestURI();

            ObjectNode jsonNode = objectMapper.createObjectNode();

            jsonNode.put("hub_interaction_id", hubInteractionId);
            jsonNode.put("study_id", studyId);
            jsonNode.put("participant_id", participantId);
            jsonNode.put("organization_party_id", organizationPartyId);
            jsonNode.put("uri", uri);
            jsonNode.put("interaction_type", interactionType);
            LOG.info("description: {}", description);
            jsonNode.put("description", description);
            LOG.info("request: {}", request);
            if (request != null) {
                jsonNode.set("request", objectMapper.readTree(request));
            }
            LOG.info("response: {}", response);
            if (response != null) {
                jsonNode.set("response", objectMapper.readTree(response));
            }
            LOG.info("from_state: {}", fromState);
            if (fromState != null) {
                LOG.info("from_state: {}", fromState);
                jsonNode.put("from_state", fromState);
            }
            if (toState != null) {
                jsonNode.put("to_state", toState);
            }
            if (status != null) {
                jsonNode.put("status", status);
            }
            LOG.info("response_code: {}", responseCode);
            jsonNode.put("response_code", responseCode);
            if (errorResponse != null) {
                LOG.info("error_response: {}", errorResponse);
                try {
                    JsonNode errorJsonNode = objectMapper.readTree(errorResponse);
                    jsonNode.set("error_response", errorJsonNode);
                } catch (IOException e) {
                    jsonNode.put("error_response", errorResponse);
                }
            }
            LOG.info("created_by: {}", userPartyId);
            jsonNode.put("created_by", userPartyId);
            jsonNode.put("updated_by", userPartyId);
            LOG.info("action_type: {}", actionType);
            jsonNode.put("action_type", actionType);
            jsonNode.put("action_status", actionStatus);
            LOG.info("action_type: {}", actionType);
            jsonNode.put("interaction_action_type_id", masterService.getActiontype(actionType));
            LOG.info("action_status: {}", actionStatus);
            jsonNode.put("interaction_status_id", masterService.getInteractionStatus(actionStatus));
            LOG.info("interaction_hierarchy: {}", getAndSetInteractionHierarchyofStudyParticipantInteraction(studyId,
                    participantId));
            jsonNode.set("interaction_hierarchy",
                    objectMapper.readTree(
                            objectMapper.writeValueAsString(
                                    getAndSetInteractionHierarchyofStudyParticipantInteraction(studyId,
                                            participantId))));

            // Convert to JSON string
            String jsonString = objectMapper.writeValueAsString(jsonNode);
            final var query = dsl.select(DSL.field(
                    "drh_stateless_activity_audit.insert_study_participant_interaction(?)", JSONB.class,
                    DSL.cast(DSL.val(
                            jsonString),
                            JSONB.class)));

            LOG.info("Save Interaction Query: {}", query);
            JSONB saveStudyInteractionResponse = query.fetchOneInto(JSONB.class);
            LOG.info("Save Interaction Response: {}", saveStudyInteractionResponse);
        } catch (Exception e) {
            LOG.error("Error in saving study participant interaction", e);
        }
    }

    public List<String> getAndSetInteractionHierarchyofStudyParticipantInteraction(
            String studyId, String participantId) {
        try {
            LOG.info("Get Last Interaction Hierarchy");

            LOG.info("Interaction and Hierarchy response of Study Id: {}", studyId);
            final var query = dsl
                    .select(DSL.field(
                            "jsonb_build_object(" +
                                    "'hub_interaction_id', hub_interaction_id, " +
                                    "'participant_interaction_id', participant_interaction_id, " +
                                    "'interaction_hierarchy', interaction_hierarchy," +
                                    "'participant_id', participant_id," +
                                    "'study_id', study_id" +
                                    ")",
                            JSONB.class))
                    .from("drh_stateless_activity_audit.study_participant_interaction_view")
                    .where(DSL.field("study_id")
                            .eq(DSL.val(
                                    studyId)))
                    .and(DSL.field("participant_id")
                            .eq(DSL.val(
                                    participantId)))
                    .orderBy(DSL.field("created_at").desc())
                    .limit(1);
            LOG.info("Query to get last interaction data of participant: {}", query);
            JSONB lastInteractionIdAndHierarchy = query.fetchOneInto(JSONB.class);
            if (lastInteractionIdAndHierarchy == null) {
                LOG.warn("No interaction found for studyId: {} and participantId: {}", studyId, participantId);
                return new ArrayList<>();
            }
            JsonNode lastFileInteractionResponseJson = objectMapper.readTree(lastInteractionIdAndHierarchy.data());
            JsonNode interactionIdNode = lastFileInteractionResponseJson.get("participant_interaction_id");
            if (interactionIdNode == null || interactionIdNode.isNull()) {
                LOG.warn(
                        "Missing 'participant_interaction_id' in interaction data for studyId: {} and participantId: {}",
                        studyId, participantId);
                return new ArrayList<>();
            }

            final var lastInteractionId = interactionIdNode.asText();
            return getAndSetInteractionHierarchyFromInteractionLog(lastInteractionIdAndHierarchy,
                    lastInteractionId);

        } catch (Exception e) {
            LOG.error("Error in reading last interaction id", e);
            return new ArrayList<>();
        }
    }

    public String getHubIntercationIdOfStudyParticipant(String participantId) {
        final var query = dsl.selectDistinct(DSL.field(
                "hub_interaction_id"))
                .from("drh_stateless_activity_audit.study_participant_interaction_view")
                .where(DSL.field("participant_id").eq(DSL.val(participantId)))
                .limit(1);
        LOG.info("Get Hub Interaction ID Query: {}", query);
        String hubInteractionId = query.fetchOneInto(String.class);
        LOG.info("Get Hub Interaction ID Response: {}", hubInteractionId);
        return hubInteractionId;
    }

    public Boolean isMealsOrFitnessInteractionExist(String studyId) {
        LOG.info("Check meals and fitness Data Copy status");
        final var query = DSL.selectOne()
                .from("drh_stateless_activity_audit.file_interaction_view")
                .where(DSL.field("study_id").eq(DSL.val(studyId)))
                .and(DSL.field("file_category").eq(DSL.value(FileType.DATABASE)))
                .and((((DSL.field("interaction_action_type_id")
                        .eq(DSL.val(masterService.getActiontype(ActionType.MEAL_MIGRATION))))).and(DSL
                                .field("interaction_status_id")
                                .eq(DSL.val(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)))))
                        .or(((DSL.field("interaction_action_type_id").eq(
                                DSL.val(masterService.getActiontype(ActionType.FITNESS_MIGRATION)))))
                                .and(DSL.field("interaction_status_id")
                                        .eq(DSL.val(masterService.getInteractionStatus(
                                                FileProcessingStatus.SUCCESS))))));
        LOG.info("Check entry for Study Database File . Query: {}", query);
        final var exists = dsl.fetchExists(query);
        LOG.info("Check if DB File Interaction Exists Query: {}", exists);
        return exists;
    }
}
