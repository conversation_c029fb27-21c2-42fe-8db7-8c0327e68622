package org.diabetestechnology.drh.service.http.hub.prime.service;

import org.diabetestechnology.drh.service.http.pg.service.AuthUserDetailsService;
import org.jooq.DSLContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DatabaseUserDetailsService implements UserDetailsService {
    private static final Logger LOG = LoggerFactory.getLogger(DatabaseUserDetailsService.class);
    private final DSLContext dsl;
    private final AuthUserDetailsService authUserDetailsService;

    public DatabaseUserDetailsService(@Qualifier("secondaryDsl") DSLContext dsl,
            AuthUserDetailsService authUserDetailsService) {
        this.dsl = dsl;
        this.authUserDetailsService = authUserDetailsService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // For testing purposes, return hardcoded user if username is "test"
        // if (username.equals("test")) {
        // return User.builder()
        // .username("test")
        // .password("$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG") //
        // This is a BCrypt hash
        // // of the word "password"
        // // using strength 10
        // .authorities(List.of(new SimpleGrantedAuthority("ROLE_USER")))
        // .build();
        // }

        // var result = dsl.fetch(
        // """
        // SELECT ua.user_id, ua.username, ua.email, uc.password_hash
        // FROM drh_stateful_authentication.user_account ua
        // JOIN drh_stateful_authentication.user_credentials uc ON ua.user_id =
        // uc.user_id
        // WHERE ua.username = ? AND ua.deleted_at IS NULL
        // """,
        // username);
        var result = dsl.fetch(
                """
                        SELECT ua.full_name, ua.email, ua.password ,ua.organization_party_id
                        FROM drh_stateless_authentication.super_admin_view ua
                        WHERE ua.email = ?
                        """,
                username);
        LOG.debug("Result: {}", result);
        if (result.isEmpty()) {
            throw new UsernameNotFoundException("User not found: " + username);
        }

        var record = result.get(0);
        String userId = record.get("email", String.class);
        String passwordHash = record.get("password", String.class);

        // Get user roles
        List<String> roles = authUserDetailsService.getRoles(userId, "DATABASE");

        // Convert roles to authorities
        var authorities = roles.stream()
                .map(role -> new SimpleGrantedAuthority("ROLE_" + role.toUpperCase()))
                .collect(Collectors.toList());

        return User.builder()
                .username(username)
                .password(passwordHash)
                .authorities(authorities)
                .build();
    }
}