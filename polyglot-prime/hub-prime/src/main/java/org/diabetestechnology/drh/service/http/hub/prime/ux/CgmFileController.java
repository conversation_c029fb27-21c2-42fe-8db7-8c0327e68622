package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.service.DataAccessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Hidden
@Tag(name = "DRH CGM File API")
public class CgmFileController {
    private final Presentation presentation;

    @Autowired
    private DataAccessService dataAccessService;

    public CgmFileController(Presentation presentation) {
        this.presentation = presentation;
    }

    private static final Logger LOG = LoggerFactory.getLogger(CgmFileController.class.getName());

    @GetMapping("/svm/files/list")
    public String totalfilesList(Model model, final HttpServletRequest request) {
        LOG.info("Getting files list: {}");
        model.addAttribute("allfiles", "true");
        model.addAttribute("studyId", "all");

        String[] pageDescription = {
                "This page displays all the files associated with the study list.",
                "We can see the content of each file by clicking on the individual filename in the row."
        };
        String[] notes = {
                "The loading time for the Files List may be longer due to data loading. The speed needs to be improved."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/files", model, request);
    }

    @GetMapping("/svm/files")
    public String totalfiles() {
        return "redirect:/svm/files/list";
    }

    @GetMapping("/svm/files/list/{studyId}")
    public String filesList(@PathVariable String studyId, Model model, final HttpServletRequest request) {
        LOG.info("Getting details for studyId: {}", studyId);
        model.addAttribute("studyId", studyId);

        String[] pageDescription = {
                "This page displays the files belongs to the study.",
                "We can see the content of each file by clicking on the individual filename in the row."
        };
        String[] notes = {
                "The loading time for the Files List may be longer due to data loading. The speed needs to be improved."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/files", model, request);
    }

    @GetMapping("/svm/files/{studyId}")
    public String files(@PathVariable String studyId) {
        return "redirect:/svm/files/list/{studyId}";
    }

    @GetMapping("/svm/file/view/{studyId}/{fileName}/{tableName}")
    public String fileView(@PathVariable String studyId, @PathVariable String fileName, @PathVariable String tableName,
            Model model,
            final HttpServletRequest request) {
        LOG.info("Getting details for studyId: {}", studyId);
        model.addAttribute("studyId", studyId);
        model.addAttribute("fileName", fileName);
        model.addAttribute("tableName", tableName);

        String[] pageDescription = {
                "This page displays the content of the file"
        };
        String[] notes = {
                "The loading time for the File content may be longer due to data loading. The speed needs to be improved."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/filecontent", model, request);
    }

    @GetMapping("/svm/file/{studyId}/{fileName}/{tableName}")
    public String file(@PathVariable String studyId, @PathVariable String fileName, @PathVariable String tableName) {
        return "redirect:/svm/file/view/{studyId}/{fileName}";
    }

    @GetMapping("/cgm/field-name/{studyId}/{cgmTable}")
    @ResponseBody
    public Object getFieldName(@PathVariable String studyId, @PathVariable String cgmTable) {
        return dataAccessService.getFieldName(studyId, cgmTable);
    }

    @GetMapping("/svm/cgm/files/list/{studyId}")
    public String cgmFilesList(@PathVariable String studyId, Model model, final HttpServletRequest request) {
        LOG.info("Getting details for studyId: {}", studyId);
        model.addAttribute("studyId", studyId);

        String[] pageDescription = {
                "This page displays the CGM files belongs to the study.",
                "We can see the content of each file by clicking on the individual filename in the row."
        };
        String[] notes = {
                "The loading time for the Files List may be longer due to data loading. The speed needs to be improved."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/studyFiles", model, request);
    }

    @GetMapping("/svm/cgm/files/{studyId}")
    public String cgmFiles(@PathVariable String studyId) {
        return "redirect:/svm/cgm/files/list/{studyId}";
    }

    @GetMapping("/svm/cgm/file/view/{studyId}/{fileName}/{tableName}")
    public String studyCgmFileView(@PathVariable String studyId, @PathVariable String fileName,
            @PathVariable String tableName,
            Model model,
            final HttpServletRequest request) {
        LOG.info("Getting details for studyId: {}", studyId);
        model.addAttribute("studyId", studyId);
        model.addAttribute("fileName", fileName);
        model.addAttribute("tableName", tableName);

        String[] pageDescription = {
                "This page displays the content of the file"
        };
        String[] notes = {
                "The loading time for the File content may be longer due to data loading. The speed needs to be improved."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/studyFilecontent", model, request);
    }

}
