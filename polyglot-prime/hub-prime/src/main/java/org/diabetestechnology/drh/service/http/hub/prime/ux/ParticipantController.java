package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.study.AgpStudyParticipant;
import org.diabetestechnology.drh.service.http.hub.prime.study.ComponentData;
import org.diabetestechnology.drh.service.http.hub.prime.study.Params;
import org.diabetestechnology.drh.service.http.hub.prime.study.StudyParticipant;
import org.diabetestechnology.drh.service.http.hub.prime.study.TimeInRange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.ResponseBody;
// import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
// import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
// import org.diabetestechnology.drh.service.http.hub.prime.service.DataAccessService;
// import java.sql.SQLException;

import io.swagger.v3.oas.annotations.Hidden;
// import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
// import java.util.concurrent.ExecutionException;
import java.text.DecimalFormat;
import java.time.LocalDate;

import java.time.temporal.ChronoUnit;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Hidden
@Tag(name = "DRH Study Participant UX API")
public class ParticipantController {

    // @Autowired
    // private DataAccessService dataAccessService;

    private final Presentation presentation;

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantController.class.getName());

    public ParticipantController(Presentation presentation) {
        this.presentation = presentation;
    }

    @Hidden
    @GetMapping("/participant/info/{studyId}/{participantId}")
    public String participantdetail(@PathVariable String studyId, @PathVariable String participantId, Model model,
            final HttpServletRequest request) {
        LOG.info("Read Participant  Details");

        model.addAttribute("dateRangeFilter", "'Last 14 days'");
        // Generate sample data (replace with actual data retrieval logic)
        StudyParticipant studyParticipants = new StudyParticipant("DCLP1-001-001", 41, 28.5, "Sample Study", 7.2,
                "Type 2");

        // Add data to the model
        model.addAttribute("study_participants", studyParticipants);
        model.addAttribute("studyId", studyId);
        model.addAttribute("participantId", participantId);
        // Create params object
        Params params = new Params("DCLP1-001-001");
        model.addAttribute("params", params);

        List<Map<String, LocalDate>> dateRangeValues = List.of(
                new HashMap<String, LocalDate>() {
                    {
                        put("start_date", LocalDate.of(2023, 6, 1));
                        put("end_date", LocalDate.of(2023, 6, 10));
                    }
                });
        LocalDate startDate = LocalDate.of(2023, 6, 1);
        LocalDate endDate = LocalDate.of(2023, 6, 10);

        // Calculate the difference in days
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);

        // Format the number of days as an integer
        String formattedDays = String.format("%d", daysBetween);

        model.addAttribute("daysBetween", formattedDays);

        // Sample data for percentage_of_time
        Map<String, String> percentageOfTime = Map.of("percentage_active", "75");

        // Sample data for no_of_days_worn
        Map<String, String> noOfDaysWorn = Map.of("Number_of_Days_CGM_Worn", "28");

        // Sample data for mean_glucose_for_each_patient
        Map<String, String> meanGlucoseForEachPatient = Map.of("MeanGlucose", "150");

        // Sample data for indicator
        Map<String, String> indicator = Map.of("GMI", "7.0");

        // Sample data for glycemic_variability_per_patient
        Map<String, String> glycemicVariabilityPerPatient = Map.of("coefficient_of_variation", "30");

        model.addAttribute("date_range_values", dateRangeValues);
        model.addAttribute("percentage_of_time", percentageOfTime);
        model.addAttribute("no_of_days_worn", noOfDaysWorn);
        model.addAttribute("mean_glucose_for_each_patient", meanGlucoseForEachPatient);
        model.addAttribute("indicator", indicator);
        model.addAttribute("glycemic_variability_per_patient", glycemicVariabilityPerPatient);

        // Create a DecimalFormat object with one decimal place
        DecimalFormat df = new DecimalFormat("#.0");

        // Format the number to one decimal place
        String formattedNumber1 = df.format(1.111);
        String formattedNumber2 = df.format(8.9455);
        String formattedNumber3 = df.format(88.7);
        String formattedNumber4 = df.format(1.2);
        String formattedNumber5 = df.format(0.2);

        // Example data structure for timeInRangesView
        List<TimeInRange> timeInRangesView = List.of(
                new TimeInRange("Participant1", 250, 12.3, "23h 20m", formattedNumber1),
                new TimeInRange("Participant2", 180, 50.0, "195h 40m", formattedNumber2),
                new TimeInRange("Participant3", 70, 50.0, "1947h 10m", formattedNumber3),
                new TimeInRange("Participant4", 54, 50.0, "25h 20m", formattedNumber4),
                new TimeInRange("Participant5", 22, 50.0, "03h 45m", formattedNumber5)
        // Add more entries as needed
        );

        model.addAttribute("timeInRangesView", timeInRangesView);
        model.addAttribute("isCalculationShown", false);

        List<AgpStudyParticipant> studyParticipantsWithRangeView = List.of(
                new AgpStudyParticipant("70.7", "3.71", "25.1", "0.476", "0.0322", "27.3"));

        model.addAttribute("study_participants_with_range_view", studyParticipantsWithRangeView);
        model.addAttribute("time_in_tight_range", 62.1);

        List<ComponentData> componentsPrimary = List.of(
                new ComponentData("Liability Index", 0.113, "Value 1", "mg/dL"),
                new ComponentData("Hypoglycemic Episodes", 349, "hypoglycemic_episodes", ""),
                new ComponentData("Euglycemic Episodes", 23366.0, "euglycemic_episodes", ""),
                new ComponentData("Hyperglycemic Episodes", 2629, "hyperglycemic_episodes", ""),
                new ComponentData("M Value", 0.00224, "m_value", "mg/dL"),
                new ComponentData("Mean Amplitude", 144, "mean_amplitude", ""),
                new ComponentData("Average Daily Risk Range", 144, "average_daily_risk", "mg/dL"),
                new ComponentData("J Index", 645, "j_index", "mg/dL"),
                new ComponentData("Low Blood Glucose Index", 391116186, "lbgi", ""),
                new ComponentData("High Blood Glucose Index", 24485149.1, "hbgi", "")

        );

        model.addAttribute("componentsPrimary", componentsPrimary);
        List<ComponentData> componentsSecondary = List.of(
                new ComponentData("Glycemic Risk Assessment Diabetes Equation (GRADE)", 7.79, "GRADE", ""),
                new ComponentData("Continuous Overall Net Glycemic Action (CONGA)", 5.64, "conga_hourly_mean", ""),
                new ComponentData("Mean of Daily Differences", 0.000835136468891167, "mean_daily_difference", "")

        );

        model.addAttribute("componentsSecondary", componentsSecondary);

        // Page description and notes to show on top of the page
        String[] pageDescription = {
                "The Participants Detail page is a comprehensive report that includes glucose statistics, such as the Ambulatory Glucose Profile (AGP), Glycemia Risk Index (GRI), Daily Glucose Profile, and all other metrics data."

        };
        String[] notes = {
                "The loading time for the AGP and GRI is currently a bit high due to data fetching and calculations. It needs optimization.",
                "Need to optimize the UI of the loader for individual metrics and charts."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);

        return presentation.populateModel("page/participant", model, request);
    }

    @Hidden
    @GetMapping("/participant/{studyId}/{participantId}")
    public String participantinfo(@PathVariable String studyId, @PathVariable String participantId) {
        return "redirect:/participant/info/{studyId}/{participantId}";
    }

    // @Operation(summary = "Get Participant cgm start and end dates")
    // @GetMapping("/participant-cgm-dates/{studyId}/{participantId}")
    // @Cacheable(value = "participantCgmDatesCache", key = "#studyId + '_' +
    // #participantId + '_' + #startDate + '_' + #endDate")
    // @ResponseBody
    // public Object getParticipantCGMDates(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId) {
    // try {
    // LOG.info("Fetching CGM dates for studyId: {} and participantId: {}", studyId,
    // participantId);
    // return dataAccessService.getParticipantCGMDates(studyId, participantId);
    // } catch (IllegalArgumentException e) {
    // LOG.error("Illegal argument: {}", e.getMessage(), e);
    // throw new IllegalArgumentException("Invalid studyId or participantId
    // provided");
    // } catch (Exception e) {
    // LOG.error("Error fetching CGM dates: {}", e.getMessage(), e);
    // throw new RuntimeException(
    // "Error fetching CGM dates for studyId: " + studyId + " and participantId: " +
    // participantId, e);
    // }
    // }

    // @Operation(summary = "Get Participant Metrics Coefficient of variation during
    // date range")
    // @GetMapping("/participant/glucose-variability/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object CoefficientOfVariation(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/glucose-variability, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.getCoefficientOfVariation(studyId, participantId,
    // startDate, endDate);
    // } catch (IllegalArgumentException e) {
    // LOG.error("Error in /participant/glucose-variability: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "coefficient_of_variation",
    // "Failed to fetch coefficient_of_variation.");
    // } catch (Exception e) {
    // LOG.error("Error in /participant/glucose-variability: Failed to calculate
    // CoefficientOfVariation", e);
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "coefficient_of_variation",
    // "Failed to fetch coefficient_of_variation.");
    // }
    // }

    // @Operation(summary = "Get Participant Metrics ambulatoryGlucoseProfile during
    // date range")
    // @GetMapping("/participant/ambulatory-glucose-profile/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // @Cacheable(value = "ambulatoryGlucoseProfileCache", key = "#studyId + '_' +
    // #participantId + '_' + #startDate + '_' + #endDate")
    // public Object AmbulatoryGlucoseProfile(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/ambulatory-glucose-profile, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.getAmbulatoryGlucoseProfile(studyId, participantId,
    // startDate, endDate);
    // } catch (IllegalArgumentException e) {
    // LOG.error("Error in /participant/ambulatory-glucose-profile: {}",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "ambulatory-glucose-profile",
    // "Failed to fetch coefficient_of_variation.");
    // } catch (Exception e) {
    // LOG.error("Error in /participant/ambulatory-glucose-profile: Failed to
    // calculate AmbulatoryGlucoseProfile",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "ambulatory-glucose-profile",
    // "Failed to fetch ambulatory-glucose-profile.");
    // }
    // }

    // @Operation(summary = "Get Participant Metrics TimeBelowRangelLow during date
    // range")
    // @GetMapping("/participant/time-below-range-low/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateTimeBelowRangeLow(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/time-below-range-low, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.calculateTimeBelowRangeLow(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/time-below-range-low: Failed to calculate
    // TimeBelowRangeLow",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "time-below-range-low",
    // "Failed to fetch time-below-range-low.");

    // }
    // }

    // @Operation(summary = "Get Participant Metrics TimeBelowRangeVerylLow during
    // date range")
    // @GetMapping("/participant/time-below-range-very-low/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateTimeBelowRangeVeryLow(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/time-below-range-very-low, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.calculateTimeBelowRangeVeryLow(studyId,
    // participantId, startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /time-below-range-very-low: Failed to calculate
    // TimeBelowRangeLow", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "time-below-range-very-low",
    // "Failed to fetch time-below-range-very-low.");
    // }
    // }

    // @Operation(summary = "Get Participant Metrics TimeInRange during date range")
    // @GetMapping("/participant/time-in-range/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateTimeInRange(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/time-in-range, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.calculateTimeInRange(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/time-in-range: Failed to calculate
    // TimeInRange", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error", "time-in-range",
    // "Failed to fetch time-in-range.");
    // }
    // }

    // @Operation(summary = "Get Participant Metrics TimeAboveRangeVeryHigh during
    // date range")
    // @GetMapping("/participant/time-above-range-very-high/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateTimeAboveRangeVeryHigh(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/time-above-range-very-high, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.calculateTimeAboveRangeVeryHigh(studyId,
    // participantId, startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/time-above-range-very-high: Failed to
    // calculate TimeAboveRangeVeryHigh",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "time-above-range-veryhigh",
    // "Failed to fetch time-above-range-veryhigh.");

    // }
    // }

    // @Operation(summary = "Get Participant Metrics Time In Tight range during date
    // range")
    // @GetMapping("/participant/time-in-tight-range/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateTimeInTightRange(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/time-in-tight-range, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.calculateTimeInTightRange(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/time-in-tight-range: Failed to calculate
    // TimeInTightRange",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "time-in-tight-range",
    // "Failed to fetch time-in-tight-range.");
    // }
    // }

    // @Operation(summary = "Get Participant Metrics time above range high during
    // date range")
    // @GetMapping("/participant/time-above-range-high/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateTimeAboveRangeHigh(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/time-above-range-high, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.calculateTimeAboveRangeHigh(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/time-above-range-high: Failed to calculate
    // Time above range high",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "time-above-range-high",
    // "Failed to fetch time-above-range-high.");
    // }
    // }

    // @Operation(summary = "Get Participant Metrics glycemicRiskIndicator during
    // date range")
    // @GetMapping("/participant/glycemicRiskIndicator/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object glycemicRiskIndicator(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/participant/glycemicRiskIndicator,
    // studyId: {}, participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.glycemicRiskIndicator(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/glycemicRiskIndicator: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "glycemicRiskIndicator",
    // "Failed to fetch glycemicRiskIndicator.");

    // }
    // }

    // @Operation(summary = "Get Participant Metrics glycemicRiskIndicator during
    // date range")
    // @GetMapping("/participant/liabilityIndex/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object liabilityIndex(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info("Request received for liabilityIndex with studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // LOG.info("liabilityIndex query executed successfully for participantId: {}
    // and studyId: {}", participantId,
    // studyId);
    // return dataAccessService.liabilityIndex(studyId, participantId, startDate,
    // endDate);
    // } catch (Exception e) {
    // LOG.error("Error in liabilityIndex for participantId: {} and studyId: {}:
    // {}", participantId, studyId,
    // e.getMessage(), e);
    // return dataAccessService.responseBuilder(Map.of(), "error", "liabilityIndex",
    // "Failed to fetch liabilityIndex.");
    // }
    // }

    // @Operation(summary = "Get Participant Mean Amplitude during date range")
    // @GetMapping("/participant/mean-amplitude/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getMeanAmplitude(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws InterruptedException,
    // ExecutionException {

    // LOG.info(
    // "Endpoint accessed: GET /participant/mean-amplitude, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.getMeanAmplitude(studyId, participantId, startDate,
    // endDate);
    // } catch (Exception e) {
    // LOG.error("Error retrieving mean amplitude: {}", e.getMessage(), e);
    // return dataAccessService.responseBuilder(Map.of(), "error", "meanAmplitude",
    // "Failed to fetch meanAmplitude.");
    // }
    // }

    // @Operation(summary = "Get Participant MValue during date range")
    // @GetMapping("/participant/m-value/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateMValue(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws InterruptedException,
    // ExecutionException {

    // LOG.info(
    // "Endpoint accessed: GET /participant/m-value, studyId: {}, participantId: {},
    // startDate: {}, endDate: {}",
    // studyId,
    // participantId, startDate, endDate);

    // try {
    // return dataAccessService.calculateMValue(studyId, participantId, startDate,
    // endDate);
    // } catch (Exception e) {
    // LOG.error("Error retrieving m-value: {}", e.getMessage(), e);
    // return dataAccessService.responseBuilder(Map.of(), "error", "m-value",
    // "Failed to fetch m-value.");
    // }
    // }

    // @Hidden
    // @Operation(summary = "Get Participant Metrics in the AGP tab for date range")
    // @GetMapping("/participant-metrics/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getParticipantMetrics(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException {
    // LOG.info(
    // "Endpoint accessed: GET /participant-metrics, studyId: {}, participantId: {},
    // startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.getParticipantMetrics(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant-metrics: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "participant-metrics",
    // "Failed to fetch participant-metrics.");
    // }
    // }

    // @Hidden
    // @Operation(summary = "Get TimeTangeStacked Data in the AGP tab for a date
    // range")
    // @GetMapping("/time-range-stacked-data/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getTimeRangeStackedData(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException {
    // LOG.info(
    // "Endpoint accessed: GET /time-range-stacked-data, studyId: {}, participantId:
    // {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.getTimeRangeStackedData(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /time-range-stacked-data: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "time-range-stacked-data",
    // "Failed to fetch time-range-stacked-data.");
    // }
    // }

    // @Operation(summary = "Get Participant Info ")
    // @GetMapping("/participant-info/{studyId}/{participantId}")
    // @ResponseBody
    // public Object getParticipantInfo(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId) {
    // LOG.info("Endpoint accessed: GET /participant-info, studyId: {},
    // participantId: {}", studyId, participantId);
    // try {
    // return dataAccessService.getParticipantInfo(studyId, participantId);
    // } catch (Exception e) {
    // LOG.error("Error in /participant-info: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "participant-info",
    // "Failed to fetch participant-info.");
    // }
    // }

    // @Operation(summary = "Get AverageDailyRisk for a date range")
    // @GetMapping("/participant/average-daily-risk/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateAverageDailyRisk(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException,
    // InterruptedException, ExecutionException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/average-daily-risk, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.calculateAverageDailyRisk(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/average-daily-risk: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "average-daily-risk",
    // "Failed to fetch average-daily-risk.");
    // }
    // }

    // @Operation(summary = "Get J Index for a date range")
    // @GetMapping("/participant/j-index/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateJIndex(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate)
    // throws SQLException, InterruptedException, ExecutionException,
    // InterruptedException, ExecutionException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/j-index, studyId: {}, participantId: {},
    // startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.calculateJIndex(studyId, participantId, startDate,
    // endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/j-index: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error", "j-index",
    // "Failed to fetch j-index.");
    // }
    // }

    // @Operation(summary = "Get LBGI and HBGI for a date range")
    // @GetMapping("/participant/lbgi-hbgi/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateLBGIandHBGI(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException,
    // InterruptedException, ExecutionException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/lbgi-hbgi, studyId: {}, participantId:
    // {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.calculateLBGIandHBGI(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/lbgi-hbgi: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error", "lbgi-hbgi",
    // "Failed to fetch lbgi-hbgi.");
    // }
    // }

    // @Operation(summary = "Get GRADE for a date range")
    // @GetMapping("/participant/grade/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateGRADE(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException,
    // InterruptedException, ExecutionException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/grade, studyId: {}, participantId: {},
    // startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.calculateGRADE(studyId, participantId, startDate,
    // endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/grade: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error", "grade", "Failed
    // to fetch grade.");
    // }
    // }

    // @Operation(summary = "Get Mean of Daily Differences for a date range")
    // @GetMapping("/participant/mean-of-daily-differences/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateMeanOfDailyDifferences(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException,
    // InterruptedException, ExecutionException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/mean-of-daily-differences, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.calculateMeanOfDailyDifferences(studyId,
    // participantId, startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/mean-of-daily-differences: {}",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "mean-of-daily-differences",
    // "Failed to fetch mean-of-daily-differences.");
    // }
    // }

    // @Operation(summary = "Get CONGA for a date range")
    // @GetMapping("/participant/conga/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object calculateCONGA(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException,
    // InterruptedException, ExecutionException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/conga, studyId: {}, participantId: {},
    // startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.calculateCONGA(studyId, participantId, startDate,
    // endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/conga: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error", "conga", "Failed
    // to fetch conga.");
    // }
    // }

    @Hidden
    // @Operation(summary = "Clear Ambulatory Cache")
    @GetMapping(value = "/ambulatoryGlucoseProfile/clear")
    @CacheEvict(value = "ambulatoryGlucoseProfileCache", allEntries = true)
    public ResponseEntity<?> emptyAmbulatoryGlucoseProfileCache() {
        LOG.info("emptying tenant-sftp-egress-content (on demand)");
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                .body("emptying ambulatoryGlucoseProfile Ccontent");
    }

    // @Hidden
    // @Operation(summary = "Retreive all the advanced metrics for a participant in
    // a date range")
    // @GetMapping("/compute-all-metrics/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object computeAllMetrics(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException {
    // LOG.info(
    // "Endpoint accessed: GET /compute-all-metrics, studyId: {}, participantId: {},
    // startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.computeAllMetrics(studyId, participantId, startDate,
    // endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /compute-all-metrics: {}", e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "compute-all-metrics",
    // "Failed to fetch compute-all-metrics.");
    // }
    // }

    // @Operation(summary = "Get the daily glucose profile for a participant in a
    // date range")
    // @GetMapping("/participant/get-daily-glcuose-profile/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getDailyGlucoseProfile(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) throws SQLException {
    // LOG.info(
    // "Endpoint accessed: GET /participant/get-daily-glcuose-profile, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // try {
    // return dataAccessService.getDailyGlucoseProfile(studyId, participantId,
    // startDate, endDate);
    // } catch (Exception e) {
    // LOG.error("Error in /participant/get-daily-glcuose-profile: {}",
    // e.getMessage());
    // return dataAccessService.responseBuilder(Map.of(), "error",
    // "get-daily-glcuose-profile",
    // "Failed to fetch get-daily-glcuose-profile.");
    // }
    // }

    @Hidden
    // @Operation(summary = "Clear Ambulatory Cache")
    @GetMapping(value = "/participant-cgm-dates/clear")
    @CacheEvict(value = "participantCgmDatesCache", allEntries = true)
    public ResponseEntity<?> emptyParticipantCgmDatesCache() {
        LOG.info("emptying tenant-sftp-egress-content (on demand)");
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                .body("emptying participantCgmDatesCache Ccontent");
    }

    // @Operation(summary = "Get Mean Glucose")
    // @GetMapping("/participant/mean-glucose-daterange/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getMeanGlucose(final @PathVariable String studyId, final
    // @PathVariable String participantId,
    // final @PathVariable String startDate, final @PathVariable String endDate) {
    // LOG.info(
    // "Endpoint accessed: GET /get-mean-glucose-daterange, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // return dataAccessService.getMeanGlucose(studyId, participantId, startDate,
    // endDate);
    // }

    // @Operation(summary = "Get CGM Worn Date Range")
    // @GetMapping("/participant/number-days-cgm-worn-daterange/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getNumberOfDaysCGMWorn(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {
    // LOG.info(
    // "Endpoint accessed: GET /get-number-days-cgm-worn-daterange, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);
    // return dataAccessService.getNumberOfDaysCGMWorn(studyId, participantId,
    // startDate, endDate);
    // }

    // @Operation(summary = "Get Percentage of CGM Active time")
    // @GetMapping("/participant/percentage-time-cgm-active/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getPercentageTimeCGMActive(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/percentage-time-cgm-active, studyId: {},
    // participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.getPercentageTimeCGMActive(studyId, participantId,
    // startDate,
    // endDate);
    // } catch (IllegalArgumentException e) {
    // LOG.error("Error in /participant/percentage-time-cgm-active: {}",
    // e.getMessage());
    // return "Error: " + e.getMessage();
    // } catch (Exception e) {
    // LOG.error(
    // "Error in /participant/percentage-time-cgm-active: Failed to calculate
    // percentage time CGM active",
    // e);
    // return "Error: Failed to calculate percentage time CGM active";
    // }
    // }

    // @Operation(summary = "Get Glucose Management Indicator")
    // @GetMapping("/participant/glucose-management-indicator/{studyId}/{participantId}/{startDate}/{endDate}")
    // @ResponseBody
    // public Object getGlucoseManagementIndicator(
    // final @PathVariable String studyId,
    // final @PathVariable String participantId,
    // final @PathVariable String startDate,
    // final @PathVariable String endDate) {

    // LOG.info(
    // "Endpoint accessed: GET /participant/glucose-management-indicator, studyId:
    // {}, participantId: {}, startDate: {}, endDate: {}",
    // studyId, participantId, startDate, endDate);

    // try {
    // return dataAccessService.getGlucoseManagementIndicator(studyId,
    // participantId, startDate,
    // endDate);
    // } catch (IllegalArgumentException e) {
    // LOG.error("Error in /participant/glucose-management-indicator: {}",
    // e.getMessage());
    // return "Error: " + e.getMessage();
    // } catch (Exception e) {
    // LOG.error(
    // "Error in /participant/glucose-management-indicator: Failed to calculate
    // Glucose Management Indicator",
    // e);
    // return "Error: Failed to calculate Glucose Management Indicator";
    // }
    // }

}