package org.diabetestechnology.drh.service.http.pg.service;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.VannaAI.VannaAIRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class VannaAiService {
    private static final Logger LOG = LoggerFactory.getLogger(InteractionService.class);

    private final DSLContext dsl;
    private final UserNameService userNameService;

    public VannaAiService(@Qualifier("secondaryDsl") DSLContext dsl, UserNameService userNameService) {
        this.dsl = dsl;
        this.userNameService = userNameService;
    }

    public Object saveVannaRequestAndResponse(VannaAIRequest request) {
        LOG.info("Received request: {}", request);
        final var query = dsl.select(DSL.field(
                "drh_stateless_vanna.log_vanna_ai_response{0}, {1}, {2}, {3}, {4}, {5}, {6} )",
                String.class,
                DSL.val(request.question()),
                DSL.val(request.sql()),
                DSL.val(userNameService.getCurrentuserPartyId()),
                DSL.val("Ask DRH"),
                DSL.val((String) null),
                DSL.val(request.result())));
        LOG.info("saveVannaRequestAndResponse: Query: {}", query);
        final var result = query
                .fetchOneInto(JSONB.class);
        LOG.info("saveVannaRequestAndResponse: Result: {}", result);
        return result;

    }

}
