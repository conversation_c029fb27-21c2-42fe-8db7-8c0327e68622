package org.diabetestechnology.drh.service.http.hub.prime.service.oauth2;

import org.diabetestechnology.drh.service.http.OauthUsersService;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.client.RestTemplate;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

public class GitHubOAuth2UserService implements OAuth2UserService<OAuth2UserRequest, OAuth2User> {
    private static final Logger LOG = LoggerFactory.getLogger(GitHubOAuth2UserService.class);
    @Autowired
    private OauthUsersService oauthUsersService;
    @Autowired
    private @Qualifier("secondaryDsl") DSLContext dsl;

    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        // Fetch user info from GitHub
        // GitHub user info endpoint is https://api.github.com/user
        final var accessToken = userRequest.getAccessToken().getTokenValue();
        LOG.info("GitHub AccessToken: {}", accessToken);

        // Use a RestTemplate or WebClient to fetch the user information from GitHub
        final var userInfoEndpointUri = "https://api.github.com/user";
        Map<String, Object> userAttributes = fetchUserAttributes(userInfoEndpointUri, accessToken);
        userAttributes.put("provider", "GitHub");
        LOG.info("GitHub UserAttributes: {}", userAttributes.values());
        userAttributes.put("provider", "GitHub");
        userAttributes.put("hasAdminMenu",
                oauthUsersService.hasAdminMenuDB(userAttributes.get("login").toString(), "GitHub"));

        // Extract user details from the GitHub response
        final var name = (String) userAttributes.get("name");
        final var username = (String) userAttributes.get("login");

        LOG.info("GitHub User, name: {}", name);
        LOG.info("GitHub User, username: {}", username);
        Boolean exists = dsl
                .select(DSL.value(true))
                .from("drh_stateless_authentication.user_profile_view")
                .where(DSL.field("provider_user_id").eq(DSL.val(
                        username)))
                .limit(1) // To ensure we only check if at least one row exists
                .fetchOptional() // Returns Optional<Boolean>
                .isPresent(); // Check if the result is present

        if (exists) {
            final var userName = dsl.select(DSL.field("first_name"))
                    .from(DSL.table("drh_stateless_authentication.user_profile_view"))
                    .where(DSL.field("provider_user_id").eq(
                            username))
                    .fetchOneInto(String.class);
            userAttributes.put("userFullName", userName);
            LOG.info("User Name is : {}", userName);

        } else {
            userAttributes.put("userFullName", name);
        }
        Set<SimpleGrantedAuthority> authorities = Collections.singleton(new SimpleGrantedAuthority("ROLE_USER"));

        // Create an OAuth2User object with the GitHub user details
        return new DefaultOAuth2User(authorities, userAttributes, "login");
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private Map<String, Object> fetchUserAttributes(String userInfoEndpointUri, String accessToken) {
        // Implement fetching user info from GitHub API
        // Example with RestTemplate
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<Map> response = restTemplate.exchange(userInfoEndpointUri, HttpMethod.GET, entity, Map.class);
        return response.getBody();
    }
}
