package org.diabetestechnology.drh.service.http.pg.request;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;

// @formatter:off
public record ResearchStudySettingsRequest

(String studyId,
        String studyTitle,
        String description,
        String locationId,
        String treatmentModalities,
        String fundingSource,
        String nctNumber,
        @JsonFormat(pattern = "MM-dd-yyyy") LocalDate startDate,
        @JsonFormat(pattern = "MM-dd-yyyy") LocalDate endDate,
        String userId) {
}
// @formatter:on