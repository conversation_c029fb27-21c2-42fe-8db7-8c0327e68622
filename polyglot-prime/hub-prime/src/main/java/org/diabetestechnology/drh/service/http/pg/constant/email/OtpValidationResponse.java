package org.diabetestechnology.drh.service.http.pg.constant.email;

public class OtpValidationResponse {
    public static final String OTP_VERIFICATION_SUCCESS = "OTP verified successfully";
    public static final String OTP_VERIFICATION_FAILURE = "Invalid OTP";
    public static final String OTP_EXPIRED = "OTP expired";
    public static final String OTP_NOT_FOUND = "OTP not found";
    public static final String OTP_MAX_ATTEMPTS_EXCEEDED = "Maximum attempts exceeded";
    public static final String OTP_VERIFICATION_PENDING = "OTP verification pending";

}
