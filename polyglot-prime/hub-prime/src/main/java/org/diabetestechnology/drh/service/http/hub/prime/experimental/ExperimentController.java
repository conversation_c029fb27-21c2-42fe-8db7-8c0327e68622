package org.diabetestechnology.drh.service.http.hub.prime.experimental;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Hidden
@RestController
@Tag(name = "Experiment Rest API")
public class ExperimentController {
    @Autowired
    private ExperimentService experimentService;
    private static final Logger LOG = LoggerFactory.getLogger(ExperimentController.class.getName());

    @Operation(summary = "Get from Duck DB")
    @GetMapping("/data")
    public List<String> getAllData() {
        LOG.info("Endpoint accessed: GET /getAllData");
        return experimentService.getAllData();
    }

}
