package org.diabetestechnology.drh.service.http.hub.prime.exception;

public class CgmFieldValidationException extends RuntimeException {

    private final String field;
    private final Object invalidValue;

    public CgmFieldValidationException(String field, Object invalidValue, String message) {
        super(message);
        this.field = field;
        this.invalidValue = invalidValue;
    }

    public String getField() {
        return field;
    }

    public Object getInvalidValue() {
        return invalidValue;
    }
}