package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.CgmMasterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH Hub CGM Master API Endpoints")
public class CgmMasterDataController {
    private static final Logger LOG = LoggerFactory.getLogger(CgmMasterDataController.class);
    private final CgmMasterService cgmMasterService;

    public CgmMasterDataController(CgmMasterService cgmMasterService) {
        this.cgmMasterService = cgmMasterService;
    }

    @GetMapping("/cgm/source-platform")
    @Operation(summary = "Read list of available source platforms")
    @ResponseBody
    public Response getSourcePlatformList() {
        LOG.info("Read list of available source platforms");
        return Response.builder()
                .data(new HashMap<>(Map.of("sourcePlatforms",
                        cgmMasterService.getSourcePlatformList())))
                .status("success")
                .message("Successfully read source platforms")
                .errors(null)
                .build();
    }

    @GetMapping("/cgm/cgm-devices")
    @Operation(summary = "Read list of available cgm devices corresopnds to manufacturer")
    @ResponseBody
    public Response getCgmDevicesList(@RequestParam String manufacturer) {
        LOG.info("Read list of available cgm devices, Manufacturer: {}", manufacturer);
        return Response.builder()
                .data(new HashMap<>(Map.of("cgmDevices",
                        cgmMasterService.getCgmDevicesList(manufacturer))))
                .status("success")
                .message("Successfully read cgm-devices")
                .errors(null)
                .build();
    }

}
