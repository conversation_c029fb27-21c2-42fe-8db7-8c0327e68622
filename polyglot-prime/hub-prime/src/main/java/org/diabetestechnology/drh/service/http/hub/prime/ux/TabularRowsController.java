package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.diabetestechnology.drh.udi.UdiSecondaryDbConfig;
import org.diabetestechnology.drh.pg.udi.auto.jooq.ingress.Tables;
import org.jetbrains.annotations.NotNull;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;
import lib.aide.tabular.JooqRowsSupplier;
import lib.aide.tabular.JooqRowsSupplier.TypableTable;
import lib.aide.tabular.TabularRowsRequest;
import lib.aide.tabular.TabularRowsRequest.FilterModel;
import lib.aide.tabular.TabularRowsResponse;

import static org.jooq.impl.DSL.*;
import org.jooq.*;
import org.jooq.Record;

@Controller
@Tag(name = "DRH Hub Tabular Row API Endpoints for AG Grid")
public class TabularRowsController {
    static private final Logger LOG = LoggerFactory.getLogger(TabularRowsController.class);

    private final UdiSecondaryDbConfig udiPrimeDbConfig;
    private final UserNameService userNameService;
    private final PartyService partyService;
    ObjectMapper objectMapper = new ObjectMapper();

    public TabularRowsController(final UdiSecondaryDbConfig udiPrimeDbConfig, UserNameService userNameService,
            PartyService partyService) {
        this.udiPrimeDbConfig = udiPrimeDbConfig;
        this.userNameService = userNameService;
        this.partyService = partyService;
    }

    @Operation(summary = "SQL rows from a master table or view")
    @PostMapping(value = { "/api/ux/tabular/jooq/{masterTableNameOrViewName}.json",
            "/api/ux/tabular/jooq/{schemaName}/{masterTableNameOrViewName}.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> tabularRows(@PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @RequestBody @Nonnull TabularRowsRequest payload,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        return new JooqRowsSupplier.Builder()
                .withRequest(payload)
                .withTable(Tables.class, schemaName, masterTableNameOrViewName)
                .withDSL(udiPrimeDbConfig.dsl())
                .withLogger(LOG)
                .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                .build()
                .response();
    }

    @PostMapping(value = {
            "/api/ux/tabular/file/jooq/{schemaName}/{viewName}.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> fetchTableData(
            @PathVariable String schemaName,
            @PathVariable String viewName,
            @RequestBody TabularRowsRequest payload,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        return new JooqRowsSupplier.Builder()
                .withRequest(payload)
                .withTable(Tables.class, schemaName, viewName)
                .withDSL(udiPrimeDbConfig.dsl())
                .withLogger(LOG)
                .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                .build()
                .response();
    }

    private Condition createCondition(Map<String, FilterModel> filter) {
        List<Condition> conditionsList = new ArrayList<>();

        // Process the filter model
        filter.forEach((field, filterModel) -> {
            // Extract the logical operator if present
            List<Condition> filterConditions = new ArrayList<>();
            if (filterModel.operator() == null) {
                String type = filterModel.type();
                Object filterValue = filterModel.filter();
                Object secondFilter = filterModel.secondFilter();
                // Add individual condition to the list of conditions
                filterConditions.add(individualCondition(field, type, filterValue, secondFilter));
                conditionsList.add(and(filterConditions));

            }

            else if (filterModel.conditions() != null) {
                // Iterate over each condition and create the respective condition
                for (TabularRowsRequest.ConditionsFilterModel conditionModel : filterModel.conditions()) {
                    String type = conditionModel.type();
                    Object filterValue = conditionModel.filter();
                    Object secondFilterValue = conditionModel.secondFilter();
                    // Add individual condition to the list of conditions
                    filterConditions.add(individualCondition(field, type, filterValue, secondFilterValue));
                }
                // Combine conditions using the specified operator (AND/OR)
                Condition combinedCondition;
                if (filterModel.operator().equalsIgnoreCase("AND")) {
                    combinedCondition = and(filterConditions);
                } else if (filterModel.operator().equalsIgnoreCase("OR")) {
                    combinedCondition = or(filterConditions); // Use OR here
                } else {
                    throw new IllegalArgumentException("Unknown operator '" + filterModel.operator() + "'");
                }
                // Add combined condition to conditions list
                conditionsList.add(combinedCondition);
            }

        });

        // Combine all filter conditions into the main query condition
        Condition finalCondition = and(conditionsList); // Use AND for combining all conditions

        return finalCondition;
    }

    private Condition individualCondition(final String field, String type, Object filter, Object secondFilter) {
        // Create individual condition based on filter type
        Condition individualCondition = switch (type) {
            case "like" -> field(field).likeIgnoreCase("%" + filter + "%");
            case "equals" -> field(field).eq(param(field, filter));
            case "notEqual" -> field(field).notEqual(param(field, filter));
            case "number" -> field(field).eq(param(field, filter));
            case "date" -> field(field).eq(param(field, filter));
            case "contains" -> field(field).likeIgnoreCase("%" + filter + "%");
            case "notContains" -> field(field).notLikeIgnoreCase("%" + filter + "%");
            case "startsWith" -> field(field).startsWith(filter);
            case "endsWith" -> field(field).endsWith(filter);
            case "lessOrEqual" -> field(field).lessOrEqual(filter);
            case "greaterOrEqual" -> field(field).greaterOrEqual(filter);
            case "greaterThan" -> field(field).greaterThan(filter);
            case "lessThan" -> field(field).lessThan(filter);
            case "between" -> field(field).cast(SQLDataType.REAL)
                    .between(Float.valueOf(filter.toString()),
                            Float.valueOf(secondFilter.toString()));
            case "blank" -> field(field).cast(SQLDataType.VARCHAR).eq(param(field, ""));
            case "notBlank" -> field(field).cast(SQLDataType.VARCHAR).notEqual(param(field, ""));
            default -> throw new IllegalArgumentException(
                    "Unknown filter type '" + type + "' for field '" + field + "'");
        };
        return individualCondition;

    }

    private @NotNull SortField<Object> createSortCondition(
            List<lib.aide.tabular.TabularRowsRequest.SortModel> sortModel,
            TypableTable participantDashboardTable) {
        for (final var sort : sortModel) {
            final var sortField = participantDashboardTable.column(sort.colId());
            if ((sort.sort()).equals("asc")) {
                return field(sortField).asc();
            } else if ((sort.sort()).equals("desc")) {
                return field(sortField).desc();
            } else {
                LOG.info("Not a Valid Sort Field");
            }
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    @Operation(summary = "SQL Custom Dashboard Summary ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/study/dashboard/dashboard_all_research_study_view.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> customDashboardTabularRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
            LOG.info("Get Study Details Corresponds to the userPartyId: {}, organizationPartyId: {}", userPartyId,
                    organizationPartyId);
            final var schemaName = "drh_stateless_research_study";
            final var tableName = "dashboard_all_research_study_view";
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    tableName);
            List<Field<?>> allFields = Arrays.asList(typableTable.table().fields());
            List<Field<?>> selectedFields = new ArrayList<>(allFields);
            var bindValues = new ArrayList<Object>();

            // Create the participant dashboard table
            var participantDashboardTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    tableName);

            // Create the final condition based on the filter model
            Condition finalCondition = payload.filterModel() != null ? createCondition(payload.filterModel())
                    : DSL
                            .field("deleted_at").isNull();
            Condition condition = DSL
                    .or(
                            DSL.field("visibility").eq(1),
                            DSL.field("organization_party_id").eq(organizationPartyId),
                            DSL.and(
                                    DSL.field("visibility").eq(2),
                                    DSL.field("created_by").eq(userPartyId)))
                    .and(DSL.field("deleted_at").isNull())
                    .and(DSL.field("rec_status_id").eq(1))
                    .and(
                            DSL.or(
                                    DSL.field("visibility").notEqual(2),
                                    DSL.field("created_by").eq(userPartyId)));
            finalCondition = finalCondition == null ? condition : finalCondition.and(condition);

            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .select(selectedFields) // Selecting the specified fields
                    .from(typableTable.table())
                    .where(finalCondition); // Optionally order by created_at or other
                                            // fields
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@NotNull SelectConditionStep<Record>) query.orderBy(createSortCondition(payload.sortModel(),
                        participantDashboardTable));
            }

            LOG.info("Get Study Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Details Corresponds to a Query {}:", query.getSQL());
            bindValues.add(1);
            bindValues.add(organizationPartyId);
            bindValues.add(2);
            bindValues.add(userPartyId);
            bindValues.add(1);
            bindValues.add(2);
            bindValues.add(userPartyId);
            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, tableName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for fetching dashboard", e);
        }
    }

    @Operation(summary = "SQL All Population Study Summary ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/research-study/population/dashboard.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> customAllStudyPopulationTabularRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp)
            throws JsonProcessingException {

        try {

            final var userId = userNameService.getUserId();
            LOG.info("Get Study Details Corresponds to the userId: {}", userId);
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            LOG.info("Get Study Details Corresponds to the userPartyId: {}", userPartyId);
            final List<Map<String, Object>> result = new ArrayList<>();
            Map<String, Object> request = extractFilters(payload.filterModel());
            final var jsonOutput = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request);
            final var sortModel = extractSortModelJson(payload);
            LOG.info("Get Study Details Corresponds to the userPartyId: {}, Query : {}", userPartyId,
                    udiPrimeDbConfig.dsl()
                            .select()
                            .from(DSL.table(
                                    "drh_stateless_research_study.get_filtered_study_metrics({0},{1},{2},{3},{4})",
                                    DSL.cast(DSL.val(
                                            jsonOutput),
                                            JSONB.class),
                                    payload.startRow(), payload.endRow(), userPartyId,
                                    DSL.cast(DSL.val(
                                            sortModel),
                                            JSONB.class)))
                            .fetchMaps());
            // Correct way to call a table-valued function in PostgreSQL via jOOQ
            udiPrimeDbConfig.dsl().transaction(configuration -> {
                DSLContext ctx = DSL.using(configuration);
                result.addAll(ctx
                        .select()
                        .from(DSL.table(
                                "drh_stateless_research_study.get_filtered_study_metrics({0},{1},{2},{3},{4})",
                                DSL.cast(DSL.val(
                                        jsonOutput),
                                        JSONB.class),
                                payload.startRow(), payload.endRow(), userPartyId,
                                DSL.cast(DSL.val(
                                        sortModel),
                                        JSONB.class)))
                        .fetchMaps());

            });

            // Log query execution (if required)
            if (includeGeneratedSqlInResp) {
                LOG.info(
                        "Executed query: SELECT * FROM drh_stateless_research_study.get_filtered_study_metrics(?,?,?,?,?)");
                LOG.info("Query parameters: {},{},{},{},{}", jsonOutput.toString(), payload.startRow(),
                        (payload.endRow() - payload
                                .startRow()),
                        userPartyId, sortModel);
            }

            // Construct and return response
            return new TabularRowsResponse<>(
                    includeGeneratedSqlInResp
                            ? "SELECT * FROM drh_stateless_research_study.get_filtered_study_metrics(?)"
                            : null,
                    result,
                    result.size(),
                    null);

        } catch (DataAccessException e) {
            LOG.error("Error executing SQL function: {}", e.getMessage(), e);

            return new TabularRowsResponse<>(
                    includeGeneratedSqlInErrorResp
                            ? "SELECT * FROM drh_stateless_research_study.get_filtered_study_metrics(?)"
                            : null,
                    List.of(),
                    0,
                    "Error fetching data from database");
        }
    }

    public Map<String, Object> extractFilters(Map<String, TabularRowsRequest.FilterModel> filterModel) {
        Map<String, Object> extractedFilters = new HashMap<>();

        for (Map.Entry<String, TabularRowsRequest.FilterModel> entry : filterModel.entrySet()) {
            String field = entry.getKey();
            TabularRowsRequest.FilterModel filter = entry.getValue();

            if (filter.type() != null) {
                String operatorKey = field + "_operator";
                String valueKey = field + "_value";
                extractedFilters.put(operatorKey, filter.type());
                extractedFilters.put(valueKey, filter.filter());

                if ("between".equals(filter.type()) && filter.secondFilter() != null) {
                    extractedFilters.put(field + "_min", filter.filter());
                    extractedFilters.put(field + "_max", filter.secondFilter());
                }
            }
        }
        return extractedFilters;
    }

    public String extractSortModelJson(TabularRowsRequest request) {
        Optional<TabularRowsRequest.SortModel> firstSortModel = request.sortModel().stream().findFirst();
        if (firstSortModel.isPresent()) {
            Map<String, String> sortJson = Map.of(
                    "column", firstSortModel.get().colId(),
                    "order", firstSortModel.get().sort().toUpperCase());
            try {
                return objectMapper.writeValueAsString(sortJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("Error serializing sort model to JSON", e);
            }
        }
        return "{}"; // Return empty JSON if no sort model is found
    }

}
