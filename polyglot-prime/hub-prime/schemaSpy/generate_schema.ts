#!/usr/bin/env -S deno run --allow-read --allow-write --allow-env --allow-run
const profile = Deno.env.get("SPRING_PROFILES_ACTIVE");

// Construct the environment variable name dynamically
const varName = `${profile}_DRH_UDI_DS_PRIME_DB_BASE_PATH`;

// Use indirect expansion to read the value of the constructed environment variable name
const basePath = Deno.env.get(varName) || "";

// Database and output configurations
const dbConfig = {
  "DRH.dclp1.sqlite.db": "../target/site/schemaSpy/study",
  "combined_cached.sqlite.db": "../target/site/schemaSpy/master",
};

const dbPath = {
  "DRH.dclp1.sqlite.db": "",
  "combined_cached.sqlite.db": "primary/",
};

// Path to SchemaSpy and JDBC driver
const schemaspyJar = "schemaspy-6.2.4.jar";
const jdbcDriver = "sqlite-jdbc-********.jar";

// Iterate over configurations
for (const db in dbConfig) {
  const outputDir = dbConfig[db];
  const path = dbPath[db] || "";

  // Create sqlite.properties content
  const sqlitePropertiesContent = `
description=SQLite
host=
db=DRH.dclp1.sqlite.db
driver=org.sqlite.JDBC
driverPath=sqlite-jdbc-********.jar

connectionSpec=jdbc:sqlite:${basePath}${path}${db}
db=combined_cached.sqlite.db
schemaspy.o=${outputDir}
  `;

  // Create schemaspy.properties content
  const schemaspyPropertiesContent = `
schemaspy.t=sqlite
schemaspy.dp=${jdbcDriver}
schemaspy.driver=org.sqlite.JDBC
schemaspy.u=user
schemaspy.p=password
schemaspy.o=${outputDir}
schemaspy.cat=%
schemaspy.s=.
schemaspy.all=
schemaspy.vizjs=
  `;

  // Write properties files
  await Deno.writeTextFile("sqlite.properties", sqlitePropertiesContent.trim());
  await Deno.writeTextFile("schemaspy.properties", schemaspyPropertiesContent.trim());

  // Run SchemaSpy
  const process = Deno.run({
    cmd: ["java", "-jar", schemaspyJar],
    stdout: "piped",
    stderr: "piped",
  });

  const { code } = await process.status();
  if (code === 0) {
    console.log(`SchemaSpy executed successfully for ${db}`);
  } else {
    console.error(`SchemaSpy execution failed for ${db}`);
  }

  process.close();
}

// Clean up temporary properties files
await Deno.remove("schemaspy.properties");
await Deno.remove("sqlite.properties");

