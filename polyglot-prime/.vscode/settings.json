{"java.configuration.updateBuildConfiguration": "automatic", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable", "java.format.settings.url": "hub-prime/eclipse-formatter.xml", "[java]": {"editor.detectIndentation": false, "files.trimTrailingWhitespace": true}, "[typescript]": {"files.trimTrailingWhitespace": true, "editor.defaultFormatter": "denoland.vscode-deno"}, "[javascript]": {"files.trimTrailingWhitespace": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"files.trimTrailingWhitespace": true, "editor.defaultFormatter": "denoland.vscode-deno"}, "[jsonc]": {"files.trimTrailingWhitespace": true, "editor.defaultFormatter": "denoland.vscode-deno"}, "[markdown]": {"files.trimTrailingWhitespace": false, "editor.defaultFormatter": "denoland.vscode-deno"}, "editor.formatOnSave": true, "java.compile.nullAnalysis.mode": "automatic", "deno.enable": true, "editor.formatOnPaste": true}