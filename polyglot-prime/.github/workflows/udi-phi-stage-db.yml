name: Synthetic Stage UDI schema generator

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Polyglot Prime Tag Version'
        required: true
jobs:
  schema-generation:
    runs-on: udi-phi-stage
    env:
      SQL_AIDE_TAG: v0.14.9
    steps:
      - name: Running schema generation
        run: echo "Running schema generation"

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Create .pgpass file
        run: |
          # Write the metadata comment to the .pgpass file
          echo "# { id: \"STAGE_DRH_UDI_DS\", description: \"UDI Stage database \", boundary: \"Stage\" }" > ~/.pgpass

          # Append the database connection details to the .pgpass file
          echo "${{ secrets.STAGE_DRH_UDI_DS_HOST }}:${{ secrets.STAGE_DRH_UDI_DS_PORT }}:${{ secrets.STAGE_DRH_UDI_DS_NAME }}:${{ secrets.STAGE_DRH_UDI_DS_USER }}:${{ secrets.STAGE_DRH_UDI_DS_PASSWORD }}" >> ~/.pgpass

          # Set the .pgpass file permissions to 600 (read and write for owner only)
          chmod 600 ~/.pgpass
      
      - name: Install Deno
        run: |
          curl -fsSL https://deno.land/x/install/install.sh | sh
          echo "$HOME/.deno/bin" >> $GITHUB_PATH

      - name: Clone SQL Aide repository
        run: |
          git clone --branch ${{ env.SQL_AIDE_TAG }} https://github.com/netspective-labs/sql-aide.git

      - name: Navigate to SQL Aide and Generate ISLM SQL
        run: |
          cd sql-aide/lib/postgres/islm
          chmod +x islmctl.ts
          ./islmctl.ts evolve up --conn-id STAGE_DRH_UDI_DS

      - name: Run ISLM test
        run: |
          cd sql-aide/lib/postgres/islm
          ./islmctl.ts evolve test --conn-id STAGE_DRH_UDI_DS

      - name: Display ISLM Test log
        run: |
          cd sql-aide/lib/postgres/islm
          log_file=$(ls -t ./islmctl-test-*.log | head -n 1)
          cat "$log_file"

      - name: Clone Polyglot Prime repository
        run: |
          git clone -b ${{ inputs.tag }} https://${{ secrets.ACTIONS_GITHUB_TOKEN }}@github.com/diabetes-research/polyglot-prime.git

      - name: Navigate to UDI Prime and Generate SQL
        run: |
          cd polyglot-prime/udi-prime
          ./udictl.ts ic generate sql

      - name: Load SQL into database
        run: |
          cd polyglot-prime/udi-prime
          ./udictl.ts ic load-sql --conn-id STAGE_DRH_UDI_DS

      - name: Display Load SQL log
        run: |
          cd polyglot-prime/udi-prime
          log_file=$(ls -t ./udictl-load-sql-*.log | head -n 1)
          cat "$log_file"


      - name: Run UDI Migrations
        run: |
          cd polyglot-prime/udi-prime
          ./udictl.ts ic migrate --conn-id STAGE_DRH_UDI_DS --is-linted false
