name: Polyglot Prime CI/CD Pipeline
on:
  # Runs on releases
  release:
    types: [published]
# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages 
permissions:
  contents: write
  pages: write
  id-token: write
# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false
jobs:
    synthetic-drh-devl-deployment:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v4

        - name: Configure Git
          run: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "GitHub Action"
  
        - name: Clone infrastructure repository
          run: |
            git clone https://${{ secrets.ACTIONS_GITHUB_TOKEN }}@github.com/diabetes-research/infrastructure-prime.git
            
        - name: Update APP environment file
          run: |
            cd infrastructure-prime
            sed -i 's/TAG=.*/TAG=${{ github.ref_name }}/g' docker/synthetic.devl.api.drh.org/.env
  
        - name: Commit and push changes
          run: |
            cd infrastructure-prime
            git add .
            git commit -m "ci: app development ${{ github.ref_name }} deployment"
            git push

    synthetic-drh-stage-deployment:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v4

        - name: Configure Git
          run: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "GitHub Action"
  
        - name: Clone infrastructure repository
          run: |
            git clone https://${{ secrets.ACTIONS_GITHUB_TOKEN }}@github.com/diabetes-research/infrastructure-prime.git

        - name: Create and switch to a new branch
          run: |
            cd infrastructure-prime
            git checkout -b synthetic-drh-app-stage-${{ github.ref_name}}-deployment

        - name: Update APP environment file
          run: |
            cd infrastructure-prime
            sed -i 's/TAG=.*/TAG=${{ github.ref_name }}/g' docker/synthetic.stage.api.drh.org_new/.env
  
        - name: Commit and push changes
          run: |
            cd infrastructure-prime
            git add .
            git commit -m "ci: app staging ${{ github.ref_name }} deployment"
            git push --set-upstream origin synthetic-drh-app-stage-${{ github.ref_name}}-deployment

        - name: Create Pull Request
          env:
            GH_TOKEN: ${{ secrets.ACTIONS_GITHUB_TOKEN }}
          run: |
            cd infrastructure-prime
            gh pr create --base main --head synthetic-drh-app-stage-${{ github.ref_name}}-deployment --title "APP stage drh.org ${{ github.ref_name }} update" --body "Automated drh app stage deployment for version ${{ github.ref_name }}"
