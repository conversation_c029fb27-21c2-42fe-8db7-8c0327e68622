# Polyglot Prime

About Primary monorepo for Diabetestechnology Research Hub polyglot bespoke code

# Diabetes Technology Research Hub Polyglot Monorepo

Welcome to the **Diabetes Technology Research Hub Polyglot Prime** repository! This repository is the
central hub for all bespoke code managed by **Diabetes Technology Research Hub Team**.
Our goal is to maintain a well-organized, scalable, and efficient monorepo that
supports our diverse range of projects and technologies.

## Primary Languages and Frameworks

At DTS, we focus on the following primary languages and frameworks for our
enterprise and utility projects:

- Modern Java 21 and above with Spring Boot 3.3 and above for all API and
  HTTP service-related use cases.
- <PERSON><PERSON> for project management.
- Thymeleaf for HTML templating and HTMX 2.0 for HATEOS interactions.
- OpenTelemetry (observability) and OpenFeature (feature flags).
- jOOQ with automatic code generation for type-safe SQL-first database
  interactions.
- PostgreSQL 16 for server-side persistence and SQLite for edge-side
  persistence.
- JUnit 5 with AssertJ assertions for testing the app server, <PERSON><PERSON> for
  testing the front end, and pgTAP for testing the database.
- Deployment via containers
- Deno and TypeScript for utilities and scripting where Java may be too heavy.

## Monorepo Strategy

Inspired by the practices at Microsoft, Google, and other large software
companies, we have designed our monorepo strategy to facilitate collaboration,
maintainability, and scalability. Here are the key aspects of our strategy:

1. **Modular Structure**: Each top-level directory represents a distinct project
   or service. This allows for clear separation of concerns and easy navigation.
2. **Consistent Naming Conventions**: Follow consistent naming conventions to
   make it easier to locate and manage code.
3. **Shared Libraries**: Common libraries and utilities will be placed in a
   shared directory to promote code reuse.
4. **Version Control**: Use Git submodules or subtree for managing third-party
   dependencies to keep the repository clean and manageable.
5. **CI/CD Integration**: Integrate Continuous Integration and Continuous
   Deployment (CI/CD) pipelines for automated testing and deployment.
6. **Documentation**: Each project will contain comprehensive documentation to
   assist developers in understanding and contributing to the codebase.

## Repository Structure

```
.
├── hub-prime
│   ├── lib
│   ├── src
│   │   ├── main
│   │   │   ├── java
│   │   │   │   └── org
│   │   │   │       └── diabetestechnology
│   │   │   │           ├── drh
│   │   │   │               ├── service
│   │   │   │           │   └── http
│   │   │   │           │       ├── hub
│   │   │   │           │           └── prime
│   │   │   │           ├── udi
│   │   │   │           └── util
│   │   │   └── resources
│   │   │       ├── META-INF
│   │   │       ├── public
│   │   │       └── templates
│   │   │           ├── fragments
│   │   │           ├── layout
│   │   │           ├── login
│   │   │           ├── mock
│   │   │           │   └── shinny-data-lake
│   │   │           │       └── 1115-validate
│   │   │           └── page
│   │   │               └── interactions
│   │   ├── site
│   │   │   └── markdown
│   │   └── test
│   │       └── java
│   │           └── org
│   │               └── diabetestechnology
│   │                   ├── drh
│   │                      ├── service
│   │                   │   └── http
│   │                   │       └── hub
│   │                   │       └── util
│   └── target
|
└── udi-prime
    ├── lib
    ├── src
    │   ├── main
    │   │   └── postgres
    │   │       └── ingestion-center
    │   └── test
    │       └── postgres
    │           └── ingestion-center
    ├── support
    │   └── jooq
    │       └── lib
    └── target
```

### Project: DRH Primary Hub

The `hub-prime` project is a Java Spring Boot application which serves application and API for research studies
endpoints.

#### Project Setup

To set up the `DRH Hub` project, follow these steps:

1. **Clone the Repository**:

   ```bash
   git clone https://github.com/diabetes-research/polyglot-prime.git
   cd polyglot-prime
   cp .envrc.example .envrc   # assume the use of direnv
   vi .envrc                  # make sure to store secrets in ENV or Vault, not in Git
   direnv allow               # apply the env vars
   cd udi-prime
   chmod +x databaseSupport.ts
   ./databaseSupport.ts
   cd ../hub-prime
   ```

2. **Build the Project**:

   ```bash
   mvn clean install
   ```

3. **Run the Application**:

   ```bash
   mvn spring-boot:run
   ```

4. **Access the Application**: Open your browser and navigate to
   `http://localhost:8080`.

### Shared Libraries

- `lib`: Contains reusable utility functions and classes that can be used across
  different projects.

### Supporting Work Products

- `support` contains all work products that _support_ the above but do not make
  their way into production


### Project: DRH Web Components

The `web-components` project is built using Vite and Lit, offering a fast and modern development experience for building web components.

1. **Prerequisites**:
Ensure you have the following installed:

Node.js (v22 or higher)
npm

2. **Installation**:
Clone the repository and install dependencies:

   ```bash
   git clone https://github.com/diabetes-research/polyglot-prime.git
   cd web-components
   npm install
   ```
3. **Development**:
Start the development server:

   ```bash
   npm run dev
   ```   
This will start the Vite server with HMR enabled. Open http://localhost:5173 in your browser to view the project.

4. **Build**:
Start the development server:

   ```bash
   npm run build
   ```  

   The build property customizes the production build output:

   outDir: Specifies the output directory as ../hub-prime/src/main/resources/static/js/wc. This directory is used for static assets in a web application.
   rollupOptions: Configures the Rollup bundler to handle multiple entry points and output settings:
   input: Defines entry files for various web components:
   stacked-bar-chart: src/components/stacked-bar-chart.ts
   agp-chart: src/components/agp-chart.ts
   dgp-chart: src/components/dgp-chart.js
   gri-chart: src/components/gri-chart.ts
   glucose-statistics-and-targets: src/components/glucose-statistics-and-targets.ts
   output.entryFileNames: '[name].js': Outputs each entry file with its name preserved for easier module management.


5. **test**:
Start the development server:

   ```bash
   npm run test
   ```  
   Executes all tests using Vitest:

   ```bash
   npm run test:ui
   ```  
   Launches the interactive Vitest UI for a visual representation of test results:
   
   ```bash
   npm run test:report
   ```  
   Generates an HTML test report for detailed analysis of test runs:

   ```bash
   npm run preview
   ```  
   Runs vite preview with a custom output directory (html):

6. **tailwind**:
 Start the tailwind:

   ```bash
   npx tailwindcss -i ./src/styles.css -o ./src/assets/styles.css --watch
   ```  

   