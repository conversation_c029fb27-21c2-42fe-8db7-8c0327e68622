/*
 * Test runner for save_research_study function tests
 * 
 * This file can be executed independently to run only the save_research_study tests
 * Usage: psql -d your_database -f run-save-research-study-tests.psql
 */

-- Set client encoding and suppress warnings during extension creation
SET client_encoding = 'UTF-8';
SET client_min_messages = warning;

-- Create the test schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS "drh_udi_assurance";

-- Create pgTAP extension in the test schema
CREATE EXTENSION IF NOT EXISTS pgtap SCHEMA drh_udi_assurance;

-- Reset client messages
RESET client_min_messages;

-- Set search path to include the test schema
SET search_path TO drh_udi_assurance, drh_stateless_research_study, drh_stateful_research_study, drh_stateful_party, drh_stateful_master, drh_stateless_util, public;

-- Load the test functions
\ir ./004-idempotent-save-research-study-unit-test.psql

-- Run comprehensive tests
\echo 'Running comprehensive tests...'
BEGIN;
SELECT * FROM drh_udi_assurance.test_save_research_study_comprehensive();
SELECT * FROM drh_udi_assurance.finish();
ROLLBACK;

-- Run error scenario tests
\echo 'Running error scenario tests...'
BEGIN;
SELECT * FROM drh_udi_assurance.test_save_research_study_error_scenarios();
SELECT * FROM drh_udi_assurance.finish();
ROLLBACK;

-- Run performance tests
\echo 'Running performance tests...'
BEGIN;
SELECT * FROM drh_udi_assurance.test_save_research_study_performance();
SELECT * FROM drh_udi_assurance.finish();
ROLLBACK;

-- Display test summary
\echo ''
\echo '========================================='
\echo 'save_research_study Function Test Summary'
\echo '========================================='
\echo 'Comprehensive Tests: Basic functionality, parameter validation, data integrity'
\echo 'Error Scenario Tests: Exception handling, edge cases, invalid inputs'
\echo 'Performance Tests: Timing, batch operations, activity logging performance'
\echo ''
\echo 'Test Coverage:'
\echo '- Function existence and signature validation'
\echo '- Successful study creation with all parameters'
\echo '- Duplicate study display ID prevention'
\echo '- Activity logging functionality'
\echo '- Parameter validation and default values'
\echo '- Foreign key constraint validation'
\echo '- Data integrity checks'
\echo '- Error handling for invalid inputs'
\echo '- Performance benchmarking'
\echo '- Concurrent operation handling'
\echo '========================================='
