/*pgTap function to test save_research_study function*/

DROP FUNCTION  IF EXISTS drh_udi_assurance.test_save_research_study(text);

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_save_research_study(schema_name text DEFAULT 'drh_stateful_research_study'::text)
 RETURNS SETOF text
 LANGUAGE plpgsql
AS $function$
DECLARE    
    v_study_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
    v_collab_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
    v_org_id TEXT;  -- Will store the inserted organization ID
    v_party_id TEXT; -- Will store the inserted party ID
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'save_research_study';
    current_query TEXT := pg_catalog.current_query();
    duplicate_display_id_count INTEGER;
    v_return TEXT;
    save_response TEXT;
BEGIN
    -- *Declare test plan*
--    PERFORM plan(6);  -- Adjust based on actual assertions

    -- Check if the table 'research_study' exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = schema_name 
        AND table_name = 'research_study') THEN 
        RETURN NEXT 'Table research_study does not exist';
    END IF;

    -- Insert test record into party table and capture the ID
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name, elaboration, created_at, created_by, 
        updated_at, updated_by, deleted_at, deleted_by, activity_log
    ) VALUES (
        drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N', 'James Joseph', NULL, 
        '2024-12-31 10:28:50.282312+05:30', 'UNKNOWN', NULL, NULL, NULL, NULL, NULL
    ) RETURNING party_id INTO v_party_id;

    RETURN NEXT ok(
        EXISTS(SELECT 1 WHERE v_party_id IS NOT NULL),
        'Successfully inserted into the party table'
    );

    -- Insert test record into organization table and capture the ID
    INSERT INTO drh_stateful_research_study.organization (
        id, party_id, identifier_system_value, "name", alias, type_code, type_display, 
        address_text, address_line, city, state, postal_code, country, phone, email, 
        website_url, parent_organization_id, rec_status_id, created_at, created_by, 
        updated_at, updated_by, deleted_at, deleted_by
    ) VALUES (
        drh_stateless_util.get_unique_id(), v_party_id, NULL, 'James Joseph', NULL, NULL, NULL, 
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 
        NULL, NULL, 1, '2024-12-31 10:25:30.94532+05:30', 'UNKNOWN', 
        NULL, NULL, NULL, NULL
    ) RETURNING id INTO v_org_id;

    RETURN NEXT ok(
        EXISTS(SELECT 1 WHERE v_org_id IS NOT NULL),
        'Successfully inserted into the organization table'
    );

    -- Check existence of required functions     
    RETURN NEXT has_function('drh_stateless_research_study', 'save_research_study',
        ARRAY['character varying', 'text', 'character varying','character varying','text','integer'],
        'Function save_research_study exists');
    
    -- Insert test record into research_study using the organization ID
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_study_id,
        p_org_party_id     => v_org_id,  -- Using the inserted organization ID here
        p_title            => 'Test Study',
        p_description      => 'Test Description',
        p_created_by       => v_party_id,
        p_visibility       => 1
    ) INTO v_return;
    
    RETURN NEXT ok(
        EXISTS(SELECT 1 WHERE v_return IS NOT NULL),
        'Successfully inserted into the table json_action_rule'
    );

    -- Test update operation
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_study_id,
        p_org_party_id     => v_org_id,  -- Using the inserted organization ID
        p_title            => 'Updated Test Study',
        p_description      => 'Updated Description',
        p_created_by       => v_party_id,
        p_visibility       => 1
    ) INTO save_response;

    RETURN NEXT ok(
        EXISTS(SELECT 1 WHERE save_response IS NOT NULL),
        'Successfully updated the table research_study'
    );

    -- Test exception handling
    BEGIN
        SELECT drh_stateless_research_study.save_research_study(
            p_study_display_id => NULL,
            p_org_party_id     => v_org_id,  -- Using the inserted organization ID
            p_title            => NULL,
            p_description      => NULL,
            p_created_by       => NULL,
            p_visibility       => NULL
        ) INTO save_response;
    EXCEPTION
        WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                     err_state = RETURNED_SQLSTATE,
                                     err_message = MESSAGE_TEXT,
                                     err_detail = PG_EXCEPTION_DETAIL,
                                     err_hint = PG_EXCEPTION_HINT;
            error_details_json := jsonb_build_object(
                'context', err_context,
                'state', err_state,
                'message', err_message,
                'detail', err_detail,
                'hint', err_hint
            );

            RETURN NEXT ok(
                EXISTS(SELECT 1 WHERE save_response IS NOT NULL),
                'Exception caught: ' || error_details_json::TEXT
            );
    END;

    -- Finish the test
  RETURN QUERY SELECT finish();

END;
$function$
;

--------Test function for create_organization function----------------
 
 DROP FUNCTION IF EXISTS drh_udi_assurance.test_create_organization(text);

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_create_organization()
 RETURNS SETOF text
 LANGUAGE plpgsql
AS $function$
DECLARE    
    v_org_id TEXT;
    v_party_id TEXT;
    v_result JSONB;
    v_duplicate_result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'create_organization';
    current_query TEXT := pg_catalog.current_query();
    v_table_schema TEXT := 'drh_stateful_research_study';
    v_table_name TEXT := 'organization';
    v_party_id_pk TEXT := drh_stateless_util.get_unique_id()::TEXT;
BEGIN
    -- Test Case 1: Create first organization (should succeed)
    SELECT drh_stateless_research_study.create_organization(
        'Test Organization_2',
        '[{"system":"http://example.com","value":"12345"}]'::jsonb,
        'Test Alias',
        'RESEARCH',
        'Research Organization',
        'Test City',
        'Test State',
        'Test Country',
        'http://test.org',
        'test_user',
        '12.3456',
        '78.9101'
    ) INTO v_result;

    -- First verify that we got a success response
    RETURN NEXT ok(
        v_result->>'status' = 'success',
        'Organization creation should return success status'
    );

    -- Extract the generated organization and party IDs only if creation was successful
    IF v_result->>'status' = 'success' THEN
        v_org_id := v_result->>'organization_id';
        v_party_id := v_result->>'organization_party_id';

        RETURN NEXT isnt(v_org_id, NULL, 'Organization ID should not be null');
        RETURN NEXT isnt(v_party_id, NULL, 'Party ID should not be null');

        -- Verify that the organization exists in the database
        RETURN NEXT ok(
            EXISTS (SELECT 1 FROM drh_stateful_research_study.organization WHERE id = v_org_id),
            'Organization should exist in the database'
        );

        -- Verify that the party exists in the database
        RETURN NEXT ok(
            EXISTS (SELECT 1 FROM drh_stateful_party.party WHERE party_id = v_party_id),
            'Party should exist in the database'
        );
    ELSE
        RETURN NEXT fail('Organization creation failed: ' || (v_result->>'message')::text);
    END IF;

    -- Test Case 2: Attempt to create organization with same name (should fail)
    SELECT drh_stateless_research_study.create_organization(
        'Test Organization_2',  -- Same name as before
        '[{"system":"http://example.com","value":"12346"}]'::jsonb,
        'Test Alias 2',
        'RESEARCH',
        'Research Organization',
        'Test City',
        'Test State',
        'Test Country',
        'http://test.org',
        'test_user',
        '12.3456',
        '78.9101'
    ) INTO v_duplicate_result;

    -- Verify duplicate check worked
    RETURN NEXT ok(
        v_duplicate_result->>'status' = 'failure',
        'Duplicate organization creation should fail'
    );

    RETURN NEXT ok(
        v_duplicate_result->>'message' = 'Organization already exists',
        'Correct error message for duplicate organization'
    );

    -- Test NULL parameters (exception handling)
    BEGIN

       SELECT drh_stateless_research_study.create_organization(
    'Test Org', '{}'::jsonb, 'Alias', 'TYPE_CODE', 'Type Display', 
    'New York', 'NY', 'USA', 'https://example.com', 'admin', 'invalid_lat', 'invalid_long'
);


        RETURN NEXT fail('Should have raised an exception for NULL parameters');
    EXCEPTION
        WHEN OTHERS THEN
            RETURN NEXT pass('Exception raised for NULL parameters as expected');
    END;
    
END;
$function$;
