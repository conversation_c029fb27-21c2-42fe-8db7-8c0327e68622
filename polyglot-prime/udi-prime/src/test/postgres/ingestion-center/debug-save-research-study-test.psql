/*
 * Debug script for save_research_study function
 * This script helps identify issues with the function call
 */

-- Set client encoding and suppress warnings
SET client_encoding = 'UTF-8';
SET client_min_messages = warning;

-- Create the test schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS "drh_udi_assurance";

-- Create pgTAP extension in the test schema
CREATE EXTENSION IF NOT EXISTS pgtap SCHEMA drh_udi_assurance;

-- Reset client messages
RESET client_min_messages;

-- Set search path
SET search_path TO drh_udi_assurance, drh_stateless_research_study, drh_stateful_research_study, drh_stateful_party, drh_stateful_master, drh_stateless_util, drh_stateless_activity_audit, public;

-- Begin transaction
BEGIN;

-- Test the function directly
DO $$
DECLARE
    v_test_org_id TEXT;
    v_test_party_id TEXT;
    v_test_user_party_id TEXT;
    v_result JSONB;
    v_status TEXT;
    v_message TEXT;
    v_study_id TEXT;
BEGIN
    RAISE NOTICE 'Starting debug test for save_research_study function...';
    
    -- Create test party for organization first (required for FK constraint)
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name, elaboration,
        created_at, created_by, updated_at, updated_by,
        deleted_at, deleted_by, activity_log
    ) VALUES (
        drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N',
        'Debug Test Research Organization', NULL,  -- Must match organization name
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'DEBUG_USER',
        NULL, NULL, NULL, NULL, NULL
    ) RETURNING party_id INTO v_test_party_id;

    RAISE NOTICE 'Created test party with ID: %', v_test_party_id;

    -- Create test organization using the party_id
    INSERT INTO drh_stateful_research_study.organization (
        id, party_id, identifier_system_value, "name", alias,
        type_code, type_display, address_text, address_line,
        city, state, postal_code, country, phone, email,
        website_url, parent_organization_id, rec_status_id,
        created_at, created_by, updated_at, updated_by,
        deleted_at, deleted_by
    ) VALUES (
        drh_stateless_util.get_unique_id(), v_test_party_id, NULL,
        'Debug Test Research Organization', 'DTRO', 'RESEARCH', 'Research Organization',
        'Debug Address', 'Debug Line', 'Debug City', 'Debug State',
        '12345', 'Debug Country', '+1234567890', '<EMAIL>',
        'https://debug.org', NULL, 1,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'DEBUG_USER',
        NULL, NULL, NULL, NULL
    ) RETURNING id INTO v_test_org_id;

    RAISE NOTICE 'Created test organization with ID: %', v_test_org_id;
    
    -- Create test user party
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name, elaboration, 
        created_at, created_by, updated_at, updated_by, 
        deleted_at, deleted_by, activity_log
    ) VALUES (
        drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N', 
        'Debug Test User', NULL, 
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'DEBUG_USER', 
        NULL, NULL, NULL, NULL, NULL
    ) RETURNING party_id INTO v_test_user_party_id;
    
    RAISE NOTICE 'Created test user party with ID: %', v_test_user_party_id;
    
    -- Check if the function exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_proc p 
        JOIN pg_namespace n ON p.pronamespace = n.oid 
        WHERE n.nspname = 'drh_stateless_research_study' 
        AND p.proname = 'save_research_study'
    ) THEN
        RAISE NOTICE 'ERROR: Function drh_stateless_research_study.save_research_study does not exist!';
        RETURN;
    END IF;
    
    RAISE NOTICE 'Function exists, proceeding with test...';
    
    -- Test the function call
    BEGIN
        SELECT drh_stateless_research_study.save_research_study(
            p_study_display_id => 'DEBUG_001',
            p_org_party_id     => v_test_party_id,
            p_title            => 'Debug Test Study',
            p_description      => 'Debug test description',
            p_created_by       => v_test_user_party_id,
            p_visibility       => 1,
            p_activity_json    => NULL
        ) INTO v_result;
        
        RAISE NOTICE 'Function call completed successfully';
        RAISE NOTICE 'Result: %', v_result;
        
        -- Extract values
        v_status := v_result->>'status';
        v_message := v_result->>'message';
        v_study_id := v_result->>'study_id';
        
        RAISE NOTICE 'Status: %', COALESCE(v_status, 'NULL');
        RAISE NOTICE 'Message: %', COALESCE(v_message, 'NULL');
        RAISE NOTICE 'Study ID: %', COALESCE(v_study_id, 'NULL');
        
        -- Check if study was created
        IF v_status = 'success' AND v_study_id IS NOT NULL THEN
            IF EXISTS (
                SELECT 1 FROM drh_stateful_research_study.research_study 
                WHERE study_id = v_study_id
            ) THEN
                RAISE NOTICE 'SUCCESS: Study record found in database';
            ELSE
                RAISE NOTICE 'WARNING: Study record not found in database despite success status';
            END IF;
        END IF;
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERROR during function call: %', SQLERRM;
        RAISE NOTICE 'SQLSTATE: %', SQLSTATE;
    END;
    
END;
$$;

-- Rollback to clean up
ROLLBACK;

\echo 'Debug test completed. Check the NOTICE messages above for results.'
