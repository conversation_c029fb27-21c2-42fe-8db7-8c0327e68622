/*
 * Simple pgTAP test for drh_stateless_research_study.save_research_study function
 * 
 * This test covers basic functionality:
 * 1. Function existence validation
 * 2. Successful study creation
 * 3. Duplicate study display ID validation
 * 4. Basic error handling
 */

-- Clean up all old test functions to avoid conflicts (including all possible signatures)
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study(text);
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_comprehensive();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_error_scenarios();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_performance();

-- Single test function for save_research_study (renamed to avoid conflict with existing function)

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_save_research_study_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE    
    -- Test data variables
    v_test_org_id TEXT;
    v_test_party_id TEXT;
    v_test_user_party_id TEXT := 'TEST_USER_001';
    v_test_study_display_id TEXT := 'TST001';  -- Max 10 chars
    v_test_study_display_id_2 TEXT := 'TST002';  -- Max 10 chars
    
    -- Function response variables
    v_result JSONB;
    v_duplicate_result JSONB;
    v_error_result JSONB;
    
    -- Extracted values from responses
    v_study_id TEXT;
    v_status TEXT;
    v_message TEXT;
    
BEGIN
    -- Note: No plan() call needed as the test runner handles planning
    
    -- ===========================================
    -- SETUP: Use existing organization or create simple one
    -- ===========================================
    
    -- Get an existing organization from the database
    SELECT 
        o.party_id,
        o.id
    INTO v_test_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;
    
    -- If no organization exists, create a simple one
    IF v_test_party_id IS NULL THEN
        -- Create test party for organization first
        INSERT INTO drh_stateful_party.party (
            party_id, party_type_id, party_name, 
            created_at, created_by
        ) VALUES (
            drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N', 
            'Simple Test Org', 
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
        ) RETURNING party_id INTO v_test_party_id;
        
        -- Create test organization
        INSERT INTO drh_stateful_research_study.organization (
            id, party_id, "name", rec_status_id, 
            created_at, created_by
        ) VALUES (
            drh_stateless_util.get_unique_id(), v_test_party_id, 
            'Simple Test Org', 1,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
        ) RETURNING id INTO v_test_org_id;
    END IF;
    
    -- Verify test data setup
    RETURN NEXT ok(
        v_test_party_id IS NOT NULL AND v_test_org_id IS NOT NULL,
        'Test data setup completed successfully'
    );
    
    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================
    
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p 
            JOIN pg_namespace n ON p.pronamespace = n.oid 
            WHERE n.nspname = 'drh_stateless_research_study' 
            AND p.proname = 'save_research_study'
        ),
        'Function save_research_study exists'
    );
    
    -- ===========================================
    -- TEST 2: Basic study creation
    -- ===========================================
    
    -- Test basic study creation
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,
        p_org_party_id     => v_test_party_id,
        p_title            => 'Simple Test Study',
        p_description      => 'Basic test description',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;
    
    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_study_id := v_result->>'study_id';
    
    RETURN NEXT ok(
        v_status = 'success',
        'Study creation returns success status'
    );
    
    RETURN NEXT ok(
        v_study_id IS NOT NULL,
        'Study creation returns valid study_id'
    );
    
    -- Verify study record exists
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.research_study 
            WHERE study_id = v_study_id 
            AND study_display_id = v_test_study_display_id
            AND deleted_at IS NULL
        ),
        'Study record exists in database'
    );
    
    -- ===========================================
    -- TEST 3: Duplicate study display ID validation
    -- ===========================================
    
    -- Attempt to create study with same display ID (should fail)
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,  -- Same as previous
        p_org_party_id     => v_test_party_id,
        p_title            => 'Duplicate Study Title',
        p_description      => 'This should fail due to duplicate display ID',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_duplicate_result;
    
    RETURN NEXT ok(
        (v_duplicate_result->>'status') = 'failure',
        'Duplicate study display ID returns failure status'
    );
    
    -- ===========================================
    -- TEST 4: Different study creation
    -- ===========================================
    
    -- Create another study with different ID
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id_2,
        p_org_party_id     => v_test_party_id,
        p_title            => 'Second Test Study',
        p_description      => 'Another test study',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;
    
    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Second study creation succeeds'
    );
    
    -- ===========================================
    -- TEST 5: Invalid organization test
    -- ===========================================
    
    -- Test with non-existent organization party ID
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'INV001',
        p_org_party_id     => 'invalid_org_id',
        p_title            => 'Invalid Org Study',
        p_description      => 'Testing invalid organization',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_error_result;
    
    RETURN NEXT ok(
        (v_error_result->>'status') = 'failure' OR v_error_result IS NULL,
        'Invalid organization handled appropriately'
    );
    
END;
$function$;
