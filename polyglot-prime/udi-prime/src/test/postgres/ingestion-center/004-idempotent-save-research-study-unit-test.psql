/*
 * Simple pgTAP test for drh_stateless_research_study.save_research_study function
 * 
 * This test covers basic functionality:
 * 1. Function existence validation
 * 2. Successful study creation
 * 3. Duplicate study display ID validation
 * 4. Basic error handling
 */

-- Clean up all old test functions to avoid conflicts (including all possible signatures)
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study(text);
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_comprehensive();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_error_scenarios();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_performance();

-- Single test function for save_research_study (renamed to avoid conflict with existing function)

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_save_research_study_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE    
    -- Test data variables
    v_test_org_id TEXT;
    v_test_party_id TEXT;
    v_test_user_party_id TEXT := 'TEST_USER_001';
    v_test_study_display_id TEXT := 'TST001';  -- Max 10 chars
    v_test_study_display_id_2 TEXT := 'TST002';  -- Max 10 chars
    
    -- Function response variables
    v_result JSONB;
    v_duplicate_result JSONB;
    v_error_result JSONB;
    
    -- Extracted values from responses
    v_study_id TEXT;
    v_status TEXT;
    v_message TEXT;
    
BEGIN
    -- Note: No plan() call needed as the test runner handles planning
    
    -- ===========================================
    -- SETUP: Use existing organization or create simple one
    -- ===========================================
    
    -- Get an existing organization from the database
    SELECT 
        o.party_id,
        o.id
    INTO v_test_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;
    
    -- If no organization exists, create a simple one
    IF v_test_party_id IS NULL THEN
        -- Create test party for organization first
        INSERT INTO drh_stateful_party.party (
            party_id, party_type_id, party_name, 
            created_at, created_by
        ) VALUES (
            drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N', 
            'Simple Test Org', 
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
        ) RETURNING party_id INTO v_test_party_id;
        
        -- Create test organization
        INSERT INTO drh_stateful_research_study.organization (
            id, party_id, "name", rec_status_id, 
            created_at, created_by
        ) VALUES (
            drh_stateless_util.get_unique_id(), v_test_party_id, 
            'Simple Test Org', 1,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
        ) RETURNING id INTO v_test_org_id;
    END IF;
    
    -- Verify test data setup
    RETURN NEXT ok(
        v_test_party_id IS NOT NULL AND v_test_org_id IS NOT NULL,
        'Test data setup completed successfully'
    );
    
    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================
    
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p 
            JOIN pg_namespace n ON p.pronamespace = n.oid 
            WHERE n.nspname = 'drh_stateless_research_study' 
            AND p.proname = 'save_research_study'
        ),
        'Function save_research_study exists'
    );
    
    -- ===========================================
    -- TEST 2: Basic study creation
    -- ===========================================
    
    -- Test basic study creation
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,
        p_org_party_id     => v_test_party_id,
        p_title            => 'Simple Test Study',
        p_description      => 'Basic test description',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;
    
    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_study_id := v_result->>'study_id';
    
    RETURN NEXT ok(
        v_status = 'success',
        'Study creation returns success status'
    );
    
    RETURN NEXT ok(
        v_study_id IS NOT NULL,
        'Study creation returns valid study_id'
    );
    
    -- Verify study record exists
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.research_study 
            WHERE study_id = v_study_id 
            AND study_display_id = v_test_study_display_id
            AND deleted_at IS NULL
        ),
        'Study record exists in database'
    );
    
    -- ===========================================
    -- TEST 3: Duplicate study display ID validation
    -- ===========================================
    
    -- Attempt to create study with same display ID (should fail)
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,  -- Same as previous
        p_org_party_id     => v_test_party_id,
        p_title            => 'Duplicate Study Title',
        p_description      => 'This should fail due to duplicate display ID',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_duplicate_result;
    
    RETURN NEXT ok(
        (v_duplicate_result->>'status') = 'failure',
        'Duplicate study display ID returns failure status'
    );
    
    -- ===========================================
    -- TEST 4: Different study creation
    -- ===========================================
    
    -- Create another study with different ID
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id_2,
        p_org_party_id     => v_test_party_id,
        p_title            => 'Second Test Study',
        p_description      => 'Another test study',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;
    
    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Second study creation succeeds'
    );
    
    -- ===========================================
    -- TEST 5: Invalid organization test
    -- ===========================================
    
    -- Test with non-existent organization party ID
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'INV001',
        p_org_party_id     => 'invalid_org_id',
        p_title            => 'Invalid Org Study',
        p_description      => 'Testing invalid organization',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_error_result;
    
    RETURN NEXT ok(
        (v_error_result->>'status') = 'failure' OR v_error_result IS NULL,
        'Invalid organization handled appropriately'
    );
    
END;
$function$;

-- ===========================================
-- Unit test for save_study_citation function
-- ===========================================

DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_study_citation_unit();

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_save_study_citation_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    -- Test data variables
    v_test_org_id TEXT;
    v_test_party_id TEXT;
    v_test_user_party_id TEXT := 'TEST_USER_CITATION_001';
    v_test_study_display_id TEXT := 'CIT001';  -- Max 10 chars
    v_test_study_id TEXT;

    -- Citation test data
    v_test_publication_title TEXT := 'Test Research Publication';
    v_test_publication_date DATE := '2024-01-15';
    v_test_publication_doi TEXT := '10.1234/test.doi.2024';
    v_test_pubmed_id TEXT := '12345678';
    v_test_source TEXT := 'manual';

    -- Function response variables
    v_study_result JSONB;
    v_citation_result JSONB;
    v_duplicate_citation_result JSONB;
    v_error_result JSONB;

    -- Extracted values from responses
    v_citation_id TEXT;
    v_status TEXT;
    v_message TEXT;

BEGIN
    -- Note: No plan() call needed as the test runner handles planning

    -- ===========================================
    -- SETUP: Create test study first
    -- ===========================================

    -- Get an existing organization from the database
    SELECT
        o.party_id,
        o.id
    INTO v_test_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;

    -- If no organization exists, create a simple one
    IF v_test_party_id IS NULL THEN
        -- Create test party for organization first
        INSERT INTO drh_stateful_party.party (
            party_id, party_type_id, party_name,
            created_at, created_by
        ) VALUES (
            drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N',
            'Citation Test Org',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
        ) RETURNING party_id INTO v_test_party_id;

        -- Create test organization
        INSERT INTO drh_stateful_research_study.organization (
            id, party_id, "name", rec_status_id,
            created_at, created_by
        ) VALUES (
            drh_stateless_util.get_unique_id(), v_test_party_id,
            'Citation Test Org', 1,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
        ) RETURNING id INTO v_test_org_id;
    END IF;

    -- Create a test study first
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,
        p_org_party_id     => v_test_party_id,
        p_title            => 'Citation Test Study',
        p_description      => 'Study for testing citations',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_study_result;

    v_test_study_id := v_study_result->>'study_id';

    -- Verify test study setup
    RETURN NEXT ok(
        v_test_study_id IS NOT NULL AND (v_study_result->>'status') = 'success',
        'Test study setup completed successfully'
    );

    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================

    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'drh_stateless_research_study'
            AND p.proname = 'save_study_citation'
        ),
        'Function save_study_citation exists'
    );

    -- ===========================================
    -- TEST 2: Basic citation creation
    -- ===========================================

    -- Test basic citation creation
    SELECT drh_stateless_research_study.save_study_citation(
        p_study_id => v_test_study_id,
        p_publication_title => v_test_publication_title,
        p_publication_date => v_test_publication_date,
        p_publication_doi => v_test_publication_doi,
        p_user_id => v_test_user_party_id,
        p_pubmed_id => v_test_pubmed_id,
        p_source => v_test_source,
        p_activity_json => NULL
    ) INTO v_citation_result;

    -- Extract response values
    v_status := v_citation_result->>'status';
    v_message := v_citation_result->>'message';
    v_citation_id := v_citation_result->>'citation_id';

    RETURN NEXT ok(
        v_status = 'success',
        'Citation creation returns success status'
    );

    RETURN NEXT ok(
        v_citation_id IS NOT NULL,
        'Citation creation returns valid citation_id'
    );

    -- Verify citation record exists in database
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.citation
            WHERE id = v_citation_id
            AND study_id = v_test_study_id
            AND title = v_test_publication_title
            AND deleted_at IS NULL
        ),
        'Citation record exists in database'
    );

    -- Verify DOI identifier was created if provided
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.citation_identifier
            WHERE citation_id = v_citation_id
            AND identifier_system = 'DOI'
            AND identifier_value = v_test_publication_doi
            AND deleted_at IS NULL
        ),
        'DOI identifier record exists in database'
    );

    -- ===========================================
    -- TEST 3: Citation without DOI
    -- ===========================================

    -- Test citation creation without DOI
    SELECT drh_stateless_research_study.save_study_citation(
        p_study_id => v_test_study_id,
        p_publication_title => 'Test Publication Without DOI',
        p_publication_date => '2024-02-01',
        p_publication_doi => NULL,  -- No DOI provided
        p_user_id => v_test_user_party_id,
        p_pubmed_id => '87654321',
        p_source => 'manual',
        p_activity_json => NULL
    ) INTO v_citation_result;

    RETURN NEXT ok(
        (v_citation_result->>'status') = 'success',
        'Citation creation without DOI succeeds'
    );

    -- ===========================================
    -- TEST 4: Citation with activity logging
    -- ===========================================

    -- Test citation creation with activity logging
    SELECT drh_stateless_research_study.save_study_citation(
        p_study_id => v_test_study_id,
        p_publication_title => 'Test Publication With Activity',
        p_publication_date => '2024-03-01',
        p_publication_doi => '10.5678/activity.test',
        p_user_id => v_test_user_party_id,
        p_pubmed_id => '11223344',
        p_source => 'pubmed',
        p_activity_json => jsonb_build_object(
            'session_id', 'citation_test_session',
            'activity_name', 'Citation Test',
            'activity_description', 'Testing citation with activity logging'
        )
    ) INTO v_citation_result;

    RETURN NEXT ok(
        (v_citation_result->>'status') = 'success',
        'Citation creation with activity logging succeeds'
    );

    -- ===========================================
    -- TEST 5: Invalid study ID test
    -- ===========================================

    -- Test with non-existent study ID
    SELECT drh_stateless_research_study.save_study_citation(
        p_study_id => 'invalid_study_id',
        p_publication_title => 'Invalid Study Citation',
        p_publication_date => '2024-04-01',
        p_publication_doi => '10.9999/invalid.test',
        p_user_id => v_test_user_party_id,
        p_pubmed_id => '99999999',
        p_source => 'manual',
        p_activity_json => NULL
    ) INTO v_error_result;

    RETURN NEXT ok(
        (v_error_result->>'status') = 'failure' OR v_error_result IS NULL,
        'Invalid study ID handled appropriately'
    );

    -- ===========================================
    -- TEST 6: Multiple citations for same study
    -- ===========================================

    -- Test creating multiple citations for the same study
    SELECT drh_stateless_research_study.save_study_citation(
        p_study_id => v_test_study_id,
        p_publication_title => 'Second Citation for Same Study',
        p_publication_date => '2024-05-01',
        p_publication_doi => '10.1111/second.citation',
        p_user_id => v_test_user_party_id,
        p_pubmed_id => '55667788',
        p_source => 'crossref',
        p_activity_json => NULL
    ) INTO v_citation_result;

    RETURN NEXT ok(
        (v_citation_result->>'status') = 'success',
        'Multiple citations for same study succeed'
    );

    -- Verify multiple citations exist for the study
    RETURN NEXT ok(
        (SELECT COUNT(*) FROM drh_stateful_research_study.citation
         WHERE study_id = v_test_study_id AND deleted_at IS NULL) >= 2,
        'Multiple citation records exist for the study'
    );

END;
$function$;
