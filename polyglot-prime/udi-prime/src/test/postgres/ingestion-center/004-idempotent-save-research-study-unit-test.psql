/*
 * Comprehensive pgTAP test suite for drh_stateless_research_study.save_research_study function
 * 
 * This test suite covers:
 * 1. Function existence and signature validation
 * 2. Successful study creation scenarios
 * 3. Duplicate study display ID validation
 * 4. Parameter validation and edge cases
 * 5. Error handling and exception scenarios
 * 6. Activity logging functionality
 * 7. Study collaboration record creation
 * 8. Data integrity and foreign key constraints
 */

-- Test function for save_research_study
DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_comprehensive();

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_save_research_study_comprehensive()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE    
    -- Test data variables
    v_test_org_id TEXT;
    v_test_party_id TEXT;
    v_test_user_party_id TEXT;
    v_test_study_display_id TEXT := 'TST001';  -- Max 10 chars
    v_test_study_display_id_2 TEXT := 'TST002';  -- Max 10 chars
    v_duplicate_study_display_id TEXT := 'DUP001';  -- Max 10 chars
    
    -- Function response variables
    v_result JSONB;
    v_duplicate_result JSONB;
    v_error_result JSONB;
    
    -- Extracted values from responses
    v_study_id TEXT;
    v_status TEXT;
    v_message TEXT;
    
    -- Test activity JSON
    v_activity_json JSONB;
    
    -- Error handling variables
    v_exception_caught BOOLEAN := FALSE;
    
BEGIN
    -- Test plan: Adjust number based on actual test count
    PERFORM plan(22);  -- Updated to match actual test count
    
    -- ===========================================
    -- SETUP: Create test data dependencies
    -- ===========================================
    
    -- Create test party for organization first (required for FK constraint)
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name, elaboration,
        created_at, created_by, updated_at, updated_by,
        deleted_at, deleted_by, activity_log
    ) VALUES (
        drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N',
        'Test Research Organization', NULL,  -- Must match organization name for view
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER',
        NULL, NULL, NULL, NULL, NULL
    ) RETURNING party_id INTO v_test_party_id;

    -- Create test organization using the party_id
    INSERT INTO drh_stateful_research_study.organization (
        id, party_id, identifier_system_value, "name", alias,
        type_code, type_display, address_text, address_line,
        city, state, postal_code, country, phone, email,
        website_url, parent_organization_id, rec_status_id,
        created_at, created_by, updated_at, updated_by,
        deleted_at, deleted_by
    ) VALUES (
        drh_stateless_util.get_unique_id(), v_test_party_id, NULL,
        'Test Research Organization', 'TRO', 'RESEARCH', 'Research Organization',
        'Test Address', 'Test Line', 'Test City', 'Test State',
        '12345', 'Test Country', '+1234567890', '<EMAIL>',
        'https://test.org', NULL, 1,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER',
        NULL, NULL, NULL, NULL
    ) RETURNING id INTO v_test_org_id;
    
    -- Create test user party
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name, elaboration, 
        created_at, created_by, updated_at, updated_by, 
        deleted_at, deleted_by, activity_log
    ) VALUES (
        drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N', 
        'Test User', NULL, 
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER', 
        NULL, NULL, NULL, NULL, NULL
    ) RETURNING party_id INTO v_test_user_party_id;
    
    -- Verify test data setup
    RETURN NEXT ok(
        v_test_party_id IS NOT NULL AND v_test_org_id IS NOT NULL AND v_test_user_party_id IS NOT NULL,
        'Test data setup completed successfully'
    );
    
    -- ===========================================
    -- TEST 1: Function existence and signature
    -- ===========================================
    
    RETURN NEXT has_function(
        'drh_stateless_research_study', 
        'save_research_study',
        ARRAY['character varying', 'text', 'character varying', 'character varying', 'text', 'integer', 'jsonb'],
        'Function save_research_study exists with correct signature'
    );
    
    RETURN NEXT function_returns(
        'drh_stateless_research_study', 
        'save_research_study',
        ARRAY['character varying', 'text', 'character varying', 'character varying', 'text', 'integer', 'jsonb'],
        'jsonb',
        'Function returns JSONB type'
    );
    
    -- ===========================================
    -- TEST 2: Successful study creation
    -- ===========================================
    
    -- Test successful study creation with all parameters
    -- Note: p_org_party_id should be the party_id of the organization, not the organization ID
    -- But we need to use the organization ID that was created, not the party ID
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,
        p_org_party_id     => v_test_party_id,  -- This is the organization's party_id
        p_title            => 'Test Diabetes Research Study',
        p_description      => 'A comprehensive study on diabetes management and treatment outcomes',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;
    
    -- Verify successful response structure
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_study_id := v_result->>'study_id';

    -- Debug: Output the actual result for troubleshooting
    RETURN NEXT ok(
        v_result IS NOT NULL,
        'Function returned a result. Result: ' || COALESCE(v_result::text, 'NULL')
    );

    RETURN NEXT ok(
        v_status = 'success',
        'Study creation returns success status. Got: ' || COALESCE(v_status, 'NULL') || ', Expected: success'
    );
    
    RETURN NEXT ok(
        v_message = 'Research study saved successfully',
        'Study creation returns correct success message'
    );
    
    RETURN NEXT ok(
        v_study_id IS NOT NULL AND length(v_study_id) > 0,
        'Study creation returns valid study_id'
    );
    
    -- Verify study record was actually created in database
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.research_study 
            WHERE study_id = v_study_id 
            AND study_display_id = v_test_study_display_id
            AND title = 'Test Diabetes Research Study'
            AND deleted_at IS NULL
        ),
        'Study record exists in database with correct data'
    );
    
    -- Verify study collaboration record was created
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.study_collaboration 
            WHERE study_id = v_study_id 
            AND user_id = v_test_user_party_id
            AND access_level = 'admin'
        ),
        'Study collaboration record created with admin access'
    );
    
    -- ===========================================
    -- TEST 3: Duplicate study display ID validation
    -- ===========================================
    
    -- Attempt to create study with same display ID (should fail)
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,  -- Same as previous
        p_org_party_id     => v_test_party_id,
        p_title            => 'Duplicate Study Title',
        p_description      => 'This should fail due to duplicate display ID',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_duplicate_result;
    
    RETURN NEXT ok(
        (v_duplicate_result->>'status') = 'failure',
        'Duplicate study display ID returns failure status'
    );
    
    RETURN NEXT ok(
        (v_duplicate_result->>'message') = 'Duplicate study display ID found',
        'Duplicate study display ID returns correct error message'
    );
    
    RETURN NEXT ok(
        (v_duplicate_result->>'study_display_id') = v_test_study_display_id,
        'Duplicate study response includes the conflicting display ID'
    );
    
    -- ===========================================
    -- TEST 4: Activity logging functionality
    -- ===========================================
    
    -- Prepare activity JSON for testing
    v_activity_json := jsonb_build_object(
        'session_id', 'test_session_123',
        'activity_name', 'Test Activity',
        'activity_description', 'Testing activity logging'
    );
    
    -- Create study with activity logging
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id_2,
        p_org_party_id     => v_test_party_id,
        p_title            => 'Study with Activity Logging',
        p_description      => 'Testing activity logging functionality',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 2,
        p_activity_json    => v_activity_json
    ) INTO v_result;
    
    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Study creation with activity logging succeeds'
    );
    
    -- ===========================================
    -- TEST 5: Parameter validation and edge cases
    -- ===========================================
    
    -- Test with minimal required parameters (using defaults)
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'MIN001',  -- Max 10 chars
        p_org_party_id     => v_test_party_id,
        p_title            => 'Minimal Study',
        p_description      => NULL,  -- Optional parameter
        p_created_by       => 'UNKNOWN',  -- Explicitly set to test value
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;
    
    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Study creation with minimal parameters succeeds'
    );
    
    -- Verify explicit values were applied
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.research_study
            WHERE study_display_id = 'MIN001'
            AND created_by = 'UNKNOWN'
        ),
        'Explicit created_by value applied correctly'
    );
    
    -- ===========================================
    -- TEST 6: Invalid organization party ID
    -- ===========================================
    
    -- Test with non-existent organization party ID
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'INV001',  -- Max 10 chars
        p_org_party_id     => 'non_existent_party_id',
        p_title            => 'Invalid Org Study',
        p_description      => 'Testing invalid organization',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_error_result;
    
    -- This should handle the error gracefully
    RETURN NEXT ok(
        (v_error_result->>'status') = 'failure' OR v_error_result IS NULL,
        'Invalid organization party ID handled appropriately'
    );
    
    -- ===========================================
    -- TEST 7: Verify foreign key constraints
    -- ===========================================
    
    -- Verify that created study has proper foreign key relationships
    v_study_id := v_result->>'study_id';
    
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.research_study rs
            JOIN drh_stateful_research_study.organization o ON rs.tenant_id = o.id
            JOIN drh_stateful_party.record_status rec ON rs.rec_status_id = rec.id
            WHERE rs.study_id = v_study_id
        ),
        'Study has valid foreign key relationships'
    );
    
    -- ===========================================
    -- TEST 8: Data integrity checks
    -- ===========================================
    
    -- Verify study has correct default values
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.research_study 
            WHERE study_id = v_study_id
            AND status_id = 1
            AND progress_status = '1'
            AND archive_status = false
            AND research_study_identifier = '{"value": ""}'
        ),
        'Study created with correct default values'
    );
    
    -- ===========================================
    -- TEST 9: Visibility parameter validation
    -- ===========================================
    
    -- Test with different visibility values
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'VIS001',  -- Max 10 chars
        p_org_party_id     => v_test_party_id,
        p_title            => 'Visibility Test Study',
        p_description      => 'Testing visibility parameter',
        p_created_by       => v_test_user_party_id,
        p_visibility       => 2,  -- Different visibility value
        p_activity_json    => NULL
    ) INTO v_result;
    
    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Study creation with different visibility succeeds'
    );
    
    v_study_id := v_result->>'study_id';
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.research_study 
            WHERE study_id = v_study_id AND visibility = 2
        ),
        'Visibility parameter stored correctly'
    );
    
    -- ===========================================
    -- TEST 10: Long text handling
    -- ===========================================
    
    -- Test with maximum length strings
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'LONG001',  -- Max 10 chars
        p_org_party_id     => v_test_party_id,
        p_title            => repeat('A', 255),  -- Maximum title length
        p_description      => repeat('B', 1000), -- Long description
        p_created_by       => v_test_user_party_id,
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;
    
    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Study creation with long text succeeds'
    );
    
    -- ===========================================
    -- CLEANUP AND FINISH
    -- ===========================================
    
    -- Note: In a real test environment, you might want to clean up test data
    -- For this test, we'll leave the data for potential debugging
    
    RETURN NEXT pass('All save_research_study tests completed successfully');
    
END;
$function$;

-- ===========================================
-- Additional test function for error scenarios
-- ===========================================

DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_error_scenarios();

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_save_research_study_error_scenarios()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    v_test_org_id TEXT;
    v_test_party_id TEXT;
    v_result JSONB;
    v_exception_caught BOOLEAN := FALSE;
    v_error_message TEXT;

BEGIN
    -- Test plan for error scenarios
    PERFORM plan(10);

    -- Setup test organization
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name,
        created_at, created_by
    ) VALUES (
        drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N',
        'Error Test Org Party',
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
    ) RETURNING party_id INTO v_test_party_id;

    INSERT INTO drh_stateful_research_study.organization (
        id, party_id, "name", rec_status_id,
        created_at, created_by
    ) VALUES (
        drh_stateless_util.get_unique_id(), v_test_party_id,
        'Error Test Organization', 1,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
    ) RETURNING id INTO v_test_org_id;

    -- ===========================================
    -- ERROR TEST 1: NULL study display ID
    -- ===========================================

    BEGIN
        SELECT drh_stateless_research_study.save_research_study(
            p_study_display_id => NULL,
            p_org_party_id     => v_test_party_id,
            p_title            => 'Test Study',
            p_description      => 'Test Description',
            p_created_by       => 'TEST_USER',
            p_visibility       => 1,
            p_activity_json    => NULL
        ) INTO v_result;

        -- If we get here, check if it's a graceful failure
        RETURN NEXT ok(
            (v_result->>'status') = 'failure',
            'NULL study display ID handled gracefully'
        );

    EXCEPTION WHEN OTHERS THEN
        v_exception_caught := TRUE;
        GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
        RETURN NEXT ok(
            v_exception_caught,
            'NULL study display ID raises exception as expected: ' || v_error_message
        );
    END;

    -- ===========================================
    -- ERROR TEST 2: NULL title (required field)
    -- ===========================================

    v_exception_caught := FALSE;
    BEGIN
        SELECT drh_stateless_research_study.save_research_study(
            p_study_display_id => 'ERROR_TEST_001',
            p_org_party_id     => v_test_party_id,
            p_title            => NULL,  -- Required field
            p_description      => 'Test Description',
            p_created_by       => 'TEST_USER',
            p_visibility       => 1,
            p_activity_json    => NULL
        ) INTO v_result;

        RETURN NEXT ok(
            (v_result->>'status') = 'failure',
            'NULL title handled gracefully'
        );

    EXCEPTION WHEN OTHERS THEN
        v_exception_caught := TRUE;
        GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
        RETURN NEXT ok(
            v_exception_caught,
            'NULL title raises exception as expected: ' || v_error_message
        );
    END;

    -- ===========================================
    -- ERROR TEST 3: Invalid visibility value
    -- ===========================================

    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'INVALID_VIS_001',
        p_org_party_id     => v_test_party_id,
        p_title            => 'Invalid Visibility Test',
        p_description      => 'Testing invalid visibility',
        p_created_by       => 'TEST_USER',
        p_visibility       => 999,  -- Invalid visibility ID
        p_activity_json    => NULL
    ) INTO v_result;

    -- This might succeed but with invalid FK, or fail gracefully
    RETURN NEXT ok(
        v_result IS NOT NULL,
        'Invalid visibility value handled (result returned)'
    );

    -- ===========================================
    -- ERROR TEST 4: Empty string parameters
    -- ===========================================

    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => '',  -- Empty string
        p_org_party_id     => v_test_party_id,
        p_title            => '',  -- Empty string
        p_description      => 'Test Description',
        p_created_by       => 'TEST_USER',
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;

    RETURN NEXT ok(
        v_result IS NOT NULL,
        'Empty string parameters handled'
    );

    -- ===========================================
    -- ERROR TEST 5: Very long study display ID
    -- ===========================================

    v_exception_caught := FALSE;
    BEGIN
        SELECT drh_stateless_research_study.save_research_study(
            p_study_display_id => repeat('X', 50),  -- Longer than VARCHAR(10) limit
            p_org_party_id     => v_test_party_id,
            p_title            => 'Long Display ID Test',
            p_description      => 'Testing long display ID',
            p_created_by       => 'TEST_USER',
            p_visibility       => 1,
            p_activity_json    => NULL
        ) INTO v_result;

        RETURN NEXT ok(
            (v_result->>'status') = 'failure',
            'Long study display ID handled gracefully'
        );

    EXCEPTION WHEN OTHERS THEN
        v_exception_caught := TRUE;
        GET STACKED DIAGNOSTICS v_error_message = MESSAGE_TEXT;
        RETURN NEXT ok(
            v_exception_caught,
            'Long study display ID raises exception: ' || v_error_message
        );
    END;

    -- ===========================================
    -- ERROR TEST 6: Invalid JSON in activity_json
    -- ===========================================

    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'JSON_TEST_001',
        p_org_party_id     => v_test_party_id,
        p_title            => 'JSON Test Study',
        p_description      => 'Testing JSON parameter',
        p_created_by       => 'TEST_USER',
        p_visibility       => 1,
        p_activity_json    => '{}'::jsonb  -- Empty but valid JSON
    ) INTO v_result;

    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Empty JSON activity parameter handled correctly'
    );

    -- ===========================================
    -- ERROR TEST 7: Non-existent organization
    -- ===========================================

    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'NO_ORG_001',
        p_org_party_id     => 'non_existent_org_id',
        p_title            => 'No Organization Test',
        p_description      => 'Testing non-existent organization',
        p_created_by       => 'TEST_USER',
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;

    -- Should fail due to organization lookup
    RETURN NEXT ok(
        v_result IS NOT NULL,
        'Non-existent organization handled'
    );

    -- ===========================================
    -- ERROR TEST 8: Concurrent duplicate creation
    -- ===========================================

    -- Create a study first
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'CONCURRENT_001',
        p_org_party_id     => v_test_party_id,
        p_title            => 'Concurrent Test Study',
        p_description      => 'First creation',
        p_created_by       => 'TEST_USER',
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;

    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'First concurrent study creation succeeds'
    );

    -- Try to create duplicate immediately
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'CONCURRENT_001',  -- Same ID
        p_org_party_id     => v_test_party_id,
        p_title            => 'Concurrent Duplicate Study',
        p_description      => 'Should fail',
        p_created_by       => 'TEST_USER',
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;

    RETURN NEXT ok(
        (v_result->>'status') = 'failure',
        'Concurrent duplicate creation fails as expected'
    );

END;
$function$;

-- ===========================================
-- Performance and stress test function
-- ===========================================

DROP FUNCTION IF EXISTS drh_udi_assurance.test_save_research_study_performance();

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_save_research_study_performance()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    v_test_org_id TEXT;
    v_test_party_id TEXT;
    v_result JSONB;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
    v_duration INTERVAL;
    i INTEGER;

BEGIN
    -- Test plan for performance tests
    PERFORM plan(5);

    -- Setup test organization
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name,
        created_at, created_by
    ) VALUES (
        drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N',
        'Performance Test Org Party',
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
    ) RETURNING party_id INTO v_test_party_id;

    INSERT INTO drh_stateful_research_study.organization (
        id, party_id, "name", rec_status_id,
        created_at, created_by
    ) VALUES (
        drh_stateless_util.get_unique_id(), v_test_party_id,
        'Performance Test Organization', 1,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'TEST_USER'
    ) RETURNING id INTO v_test_org_id;

    -- ===========================================
    -- PERFORMANCE TEST 1: Single study creation timing
    -- ===========================================

    v_start_time := clock_timestamp();

    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'PERF_001',
        p_org_party_id     => v_test_party_id,
        p_title            => 'Performance Test Study',
        p_description      => 'Testing single study creation performance',
        p_created_by       => 'TEST_USER',
        p_visibility       => 1,
        p_activity_json    => NULL
    ) INTO v_result;

    v_end_time := clock_timestamp();
    v_duration := v_end_time - v_start_time;

    RETURN NEXT ok(
        (v_result->>'status') = 'success',
        'Performance test study created successfully'
    );

    RETURN NEXT ok(
        v_duration < INTERVAL '5 seconds',
        'Single study creation completed within 5 seconds: ' || v_duration::text
    );

    -- ===========================================
    -- PERFORMANCE TEST 2: Multiple studies creation
    -- ===========================================

    v_start_time := clock_timestamp();

    -- Create 10 studies in sequence
    FOR i IN 1..10 LOOP
        SELECT drh_stateless_research_study.save_research_study(
            p_study_display_id => 'PERF_' || i::text,
            p_org_party_id     => v_test_party_id,
            p_title            => 'Performance Test Study ' || i::text,
            p_description      => 'Batch performance test study number ' || i::text,
            p_created_by       => 'TEST_USER',
            p_visibility       => 1,
            p_activity_json    => NULL
        ) INTO v_result;

        -- Verify each creation succeeded
        IF (v_result->>'status') != 'success' THEN
            RETURN NEXT fail('Batch study creation failed at iteration ' || i::text);
            EXIT;
        END IF;
    END LOOP;

    v_end_time := clock_timestamp();
    v_duration := v_end_time - v_start_time;

    RETURN NEXT ok(
        v_duration < INTERVAL '30 seconds',
        'Batch creation of 10 studies completed within 30 seconds: ' || v_duration::text
    );

    -- ===========================================
    -- PERFORMANCE TEST 3: Verify all studies exist
    -- ===========================================

    RETURN NEXT ok(
        (SELECT COUNT(*) FROM drh_stateful_research_study.research_study
         WHERE study_display_id LIKE 'PERF_%') >= 10,
        'All performance test studies exist in database'
    );

    -- ===========================================
    -- PERFORMANCE TEST 4: Activity logging performance
    -- ===========================================

    v_start_time := clock_timestamp();

    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => 'PERF_ACTIVITY',
        p_org_party_id     => v_test_party_id,
        p_title            => 'Activity Logging Performance Test',
        p_description      => 'Testing performance with activity logging',
        p_created_by       => 'TEST_USER',
        p_visibility       => 1,
        p_activity_json    => jsonb_build_object(
            'session_id', 'perf_test_session',
            'activity_name', 'Performance Test',
            'activity_description', 'Testing activity logging performance'
        )
    ) INTO v_result;

    v_end_time := clock_timestamp();
    v_duration := v_end_time - v_start_time;

    RETURN NEXT ok(
        (v_result->>'status') = 'success' AND v_duration < INTERVAL '10 seconds',
        'Study creation with activity logging completed within 10 seconds: ' || v_duration::text
    );

END;
$function$;
