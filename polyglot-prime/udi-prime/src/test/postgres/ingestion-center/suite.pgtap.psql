CREATE SCHEMA IF NOT EXISTS "drh_udi_assurance";


CREATE EXTENSION IF NOT EXISTS pgtap SCHEMA drh_udi_assurance;

-- TODO: figure out why this is required; when search_path is not set then seeing:
--       `ERROR:  42883: function findfuncs(name, unknown) does not exist`
SET search_path TO drh_udi_assurance;

\ir ./003-idempotent-function-unit-test.psql
\ir ./002-idempotent-migrate-unit-test.psql
\ir ./004-idempotent-save-research-study-unit-test.psql

SELECT * FROM drh_udi_assurance.runtests('drh_udi_assurance'::name);
SELECT * FROM drh_udi_assurance.runtests('info_schema_lifecycle'::name, 'test_all_migration_objects');
