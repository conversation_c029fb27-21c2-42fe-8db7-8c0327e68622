SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(113);

SELECT has_table(
    'drh_stateful_raw_data', 'cgm_raw_upload_data',
    'Should have table drh_stateful_raw_data.cgm_raw_upload_data'
);

SELECT has_pk(
    'drh_stateful_raw_data', 'cgm_raw_upload_data',
    'Table drh_stateful_raw_data.cgm_raw_upload_data should have a primary key'
);

SELECT columns_are('drh_stateful_raw_data'::name, 'cgm_raw_upload_data'::name, ARRAY[
    'cgm_raw_file_id'::name,
    'file_name'::name,
    'file_url'::name,
    'zip_file_id'::name,
    'database_id'::name,
    'cgm_raw_data_json'::name,
    'upload_timestamp'::name,
    'uploaded_by'::name,
    'file_size'::name,
    'is_processed'::name,
    'processed_at'::name,
    'status'::name,
    'file_metadata'::name,
    'file_type'::name,
    'study_id'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'cgm_raw_data_csv'::name,
    'cgm_raw_data_excel'::name,
    'cgm_raw_data_xml'::name,
    'cgm_raw_data_text'::name
]);

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_file_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_file_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_file_id', 'text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_file_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_file_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_file_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_file_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_file_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_name', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_name should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_name', 'character varying(255)', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_name', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_name', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_name should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_url', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_url should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_url', 'text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_url should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_url', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_url should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_url', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_url should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'zip_file_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.zip_file_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'zip_file_id', 'character varying(255)', 'Column drh_stateful_raw_data.cgm_raw_upload_data.zip_file_id should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'zip_file_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.zip_file_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'zip_file_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.zip_file_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'database_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.database_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'database_id', 'character varying(255)', 'Column drh_stateful_raw_data.cgm_raw_upload_data.database_id should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'database_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.database_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'database_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.database_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_json', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_json should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_json', 'jsonb', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_json should be type jsonb');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_json', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_json should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_json', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_json should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'upload_timestamp', 'Column drh_stateful_raw_data.cgm_raw_upload_data.upload_timestamp should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'upload_timestamp', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_upload_data.upload_timestamp should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'upload_timestamp', 'Column drh_stateful_raw_data.cgm_raw_upload_data.upload_timestamp should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'upload_timestamp', 'Column drh_stateful_raw_data.cgm_raw_upload_data.upload_timestamp should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'uploaded_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.uploaded_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'uploaded_by', 'character varying(255)', 'Column drh_stateful_raw_data.cgm_raw_upload_data.uploaded_by should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'uploaded_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.uploaded_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'uploaded_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.uploaded_by should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_size', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_size should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_size', 'character varying(50)', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_size should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_size', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_size should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_size', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_size should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'is_processed', 'Column drh_stateful_raw_data.cgm_raw_upload_data.is_processed should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'is_processed', 'boolean', 'Column drh_stateful_raw_data.cgm_raw_upload_data.is_processed should be type boolean');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'is_processed', 'Column drh_stateful_raw_data.cgm_raw_upload_data.is_processed should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'is_processed', 'Column drh_stateful_raw_data.cgm_raw_upload_data.is_processed should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'processed_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.processed_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'processed_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_upload_data.processed_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'processed_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.processed_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'processed_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.processed_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'status', 'Column drh_stateful_raw_data.cgm_raw_upload_data.status should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'status', 'character varying(50)', 'Column drh_stateful_raw_data.cgm_raw_upload_data.status should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'status', 'Column drh_stateful_raw_data.cgm_raw_upload_data.status should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'status', 'Column drh_stateful_raw_data.cgm_raw_upload_data.status should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_metadata', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_metadata should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_metadata', 'jsonb', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_metadata should be type jsonb');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_metadata', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_metadata should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_metadata', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_metadata should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_type', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_type should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_type', 'character varying(50)', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_type should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_type', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'file_type', 'Column drh_stateful_raw_data.cgm_raw_upload_data.file_type should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'study_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.study_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'study_id', 'text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.study_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'study_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'study_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.study_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'tenant_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'tenant_id', 'text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'tenant_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'tenant_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'rec_status_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'rec_status_id', 'integer', 'Column drh_stateful_raw_data.cgm_raw_upload_data.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_raw_data', 'cgm_raw_upload_data', 'rec_status_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'rec_status_id', 'Column drh_stateful_raw_data.cgm_raw_upload_data.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_at should have a default');
SELECT col_default_is(   'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_at default is');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_by', 'text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_by should have a default');
SELECT col_default_is(   'drh_stateful_raw_data', 'cgm_raw_upload_data', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_raw_data.cgm_raw_upload_data.created_by default is');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_by', 'text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'updated_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.updated_by should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_at', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_by', 'text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'deleted_by', 'Column drh_stateful_raw_data.cgm_raw_upload_data.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_csv', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_csv should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_csv', 'bytea', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_csv should be type bytea');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_csv', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_csv should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_csv', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_csv should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_excel', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_excel should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_excel', 'bytea', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_excel should be type bytea');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_excel', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_excel should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_excel', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_excel should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_xml', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_xml should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_xml', 'xml', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_xml should be type xml');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_xml', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_xml should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_xml', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_xml should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_text should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_text', 'bytea', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_text should be type bytea');
SELECT col_is_null(      'drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_text should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'cgm_raw_upload_data', 'cgm_raw_data_text', 'Column drh_stateful_raw_data.cgm_raw_upload_data.cgm_raw_data_text should not have a default');

SELECT * FROM finish();
ROLLBACK;
