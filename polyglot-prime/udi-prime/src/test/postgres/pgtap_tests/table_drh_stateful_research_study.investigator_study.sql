SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(36);

SELECT has_table(
    'drh_stateful_research_study', 'investigator_study',
    'Should have table drh_stateful_research_study.investigator_study'
);

SELECT has_pk(
    'drh_stateful_research_study', 'investigator_study',
    'Table drh_stateful_research_study.investigator_study should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'investigator_study'::name, ARRAY[
    'mapping_id'::name,
    'investigator_id'::name,
    'study_id'::name,
    'role'::name,
    'start_date'::name,
    'end_date'::name,
    'created_at'::name,
    'updated_at'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'mapping_id', 'Column drh_stateful_research_study.investigator_study.mapping_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'mapping_id', 'text', 'Column drh_stateful_research_study.investigator_study.mapping_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'investigator_study', 'mapping_id', 'Column drh_stateful_research_study.investigator_study.mapping_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'investigator_study', 'mapping_id', 'Column drh_stateful_research_study.investigator_study.mapping_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'investigator_id', 'Column drh_stateful_research_study.investigator_study.investigator_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'investigator_id', 'text', 'Column drh_stateful_research_study.investigator_study.investigator_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'investigator_study', 'investigator_id', 'Column drh_stateful_research_study.investigator_study.investigator_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'investigator_study', 'investigator_id', 'Column drh_stateful_research_study.investigator_study.investigator_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'study_id', 'Column drh_stateful_research_study.investigator_study.study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'study_id', 'text', 'Column drh_stateful_research_study.investigator_study.study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'investigator_study', 'study_id', 'Column drh_stateful_research_study.investigator_study.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'investigator_study', 'study_id', 'Column drh_stateful_research_study.investigator_study.study_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'role', 'Column drh_stateful_research_study.investigator_study.role should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'role', 'integer', 'Column drh_stateful_research_study.investigator_study.role should be type integer');
SELECT col_is_null(      'drh_stateful_research_study', 'investigator_study', 'role', 'Column drh_stateful_research_study.investigator_study.role should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'investigator_study', 'role', 'Column drh_stateful_research_study.investigator_study.role should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'start_date', 'Column drh_stateful_research_study.investigator_study.start_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'start_date', 'date', 'Column drh_stateful_research_study.investigator_study.start_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'investigator_study', 'start_date', 'Column drh_stateful_research_study.investigator_study.start_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'investigator_study', 'start_date', 'Column drh_stateful_research_study.investigator_study.start_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'end_date', 'Column drh_stateful_research_study.investigator_study.end_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'end_date', 'date', 'Column drh_stateful_research_study.investigator_study.end_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'investigator_study', 'end_date', 'Column drh_stateful_research_study.investigator_study.end_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'investigator_study', 'end_date', 'Column drh_stateful_research_study.investigator_study.end_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'created_at', 'Column drh_stateful_research_study.investigator_study.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.investigator_study.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'investigator_study', 'created_at', 'Column drh_stateful_research_study.investigator_study.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'investigator_study', 'created_at', 'Column drh_stateful_research_study.investigator_study.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'investigator_study', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.investigator_study.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'investigator_study', 'updated_at', 'Column drh_stateful_research_study.investigator_study.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'investigator_study', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.investigator_study.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'investigator_study', 'updated_at', 'Column drh_stateful_research_study.investigator_study.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'investigator_study', 'updated_at', 'Column drh_stateful_research_study.investigator_study.updated_at should not have a default');

SELECT * FROM finish();
ROLLBACK;
