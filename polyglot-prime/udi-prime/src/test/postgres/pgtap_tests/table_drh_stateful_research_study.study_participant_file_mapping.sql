SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(53);

SELECT has_table(
    'drh_stateful_research_study', 'study_participant_file_mapping',
    'Should have table drh_stateful_research_study.study_participant_file_mapping'
);

SELECT has_pk(
    'drh_stateful_research_study', 'study_participant_file_mapping',
    'Table drh_stateful_research_study.study_participant_file_mapping should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'study_participant_file_mapping'::name, ARRAY[
    'rs_file_map_id'::name,
    'study_id'::name,
    'file_url'::name,
    'file_content_json'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'rs_file_map_id', 'Column drh_stateful_research_study.study_participant_file_mapping.rs_file_map_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'rs_file_map_id', 'text', 'Column drh_stateful_research_study.study_participant_file_mapping.rs_file_map_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_participant_file_mapping', 'rs_file_map_id', 'Column drh_stateful_research_study.study_participant_file_mapping.rs_file_map_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'rs_file_map_id', 'Column drh_stateful_research_study.study_participant_file_mapping.rs_file_map_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'study_id', 'Column drh_stateful_research_study.study_participant_file_mapping.study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'study_id', 'text', 'Column drh_stateful_research_study.study_participant_file_mapping.study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_participant_file_mapping', 'study_id', 'Column drh_stateful_research_study.study_participant_file_mapping.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'study_id', 'Column drh_stateful_research_study.study_participant_file_mapping.study_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'file_url', 'Column drh_stateful_research_study.study_participant_file_mapping.file_url should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'file_url', 'text', 'Column drh_stateful_research_study.study_participant_file_mapping.file_url should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_participant_file_mapping', 'file_url', 'Column drh_stateful_research_study.study_participant_file_mapping.file_url should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'file_url', 'Column drh_stateful_research_study.study_participant_file_mapping.file_url should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'file_content_json', 'Column drh_stateful_research_study.study_participant_file_mapping.file_content_json should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'file_content_json', 'jsonb', 'Column drh_stateful_research_study.study_participant_file_mapping.file_content_json should be type jsonb');
SELECT col_is_null(      'drh_stateful_research_study', 'study_participant_file_mapping', 'file_content_json', 'Column drh_stateful_research_study.study_participant_file_mapping.file_content_json should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'file_content_json', 'Column drh_stateful_research_study.study_participant_file_mapping.file_content_json should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'tenant_id', 'Column drh_stateful_research_study.study_participant_file_mapping.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'tenant_id', 'text', 'Column drh_stateful_research_study.study_participant_file_mapping.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'study_participant_file_mapping', 'tenant_id', 'Column drh_stateful_research_study.study_participant_file_mapping.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'tenant_id', 'Column drh_stateful_research_study.study_participant_file_mapping.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'rec_status_id', 'Column drh_stateful_research_study.study_participant_file_mapping.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.study_participant_file_mapping.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'study_participant_file_mapping', 'rec_status_id', 'Column drh_stateful_research_study.study_participant_file_mapping.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'rec_status_id', 'Column drh_stateful_research_study.study_participant_file_mapping.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'created_at', 'Column drh_stateful_research_study.study_participant_file_mapping.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_participant_file_mapping.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_participant_file_mapping', 'created_at', 'Column drh_stateful_research_study.study_participant_file_mapping.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'study_participant_file_mapping', 'created_at', 'Column drh_stateful_research_study.study_participant_file_mapping.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'study_participant_file_mapping', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.study_participant_file_mapping.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'created_by', 'Column drh_stateful_research_study.study_participant_file_mapping.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'created_by', 'text', 'Column drh_stateful_research_study.study_participant_file_mapping.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_participant_file_mapping', 'created_by', 'Column drh_stateful_research_study.study_participant_file_mapping.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'study_participant_file_mapping', 'created_by', 'Column drh_stateful_research_study.study_participant_file_mapping.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'study_participant_file_mapping', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.study_participant_file_mapping.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'updated_at', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_participant_file_mapping', 'updated_at', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'updated_at', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'updated_by', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'updated_by', 'text', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_participant_file_mapping', 'updated_by', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'updated_by', 'Column drh_stateful_research_study.study_participant_file_mapping.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_at', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_at', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_at', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_by', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_by', 'text', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_by', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'study_participant_file_mapping', 'deleted_by', 'Column drh_stateful_research_study.study_participant_file_mapping.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
