SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(44);

SELECT has_table(
    'drh_stateful_authentication', 'external_auth_mappings',
    'Should have table drh_stateful_authentication.auth_mappings'
);

SELECT has_pk(
    'drh_stateful_authentication', 'external_auth_mappings',
    'Table drh_stateful_authentication.auth_mappings should have a primary key'
);

SELECT columns_are('drh_stateful_authentication'::name, 'external_auth_mappings'::name, ARRAY[
    'id'::name,
    'user_id'::name,
    'auth_provider'::name,
    'provider_user_id'::name,
    'access_token'::name,
    'refresh_token'::name,
    'status'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'updated_at'::name
]);

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'id', 'Column drh_stateful_authentication.auth_mappings.id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'id', 'text', 'Column drh_stateful_authentication.auth_mappings.id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'id', 'Column drh_stateful_authentication.auth_mappings.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'id', 'Column drh_stateful_authentication.auth_mappings.id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'user_id', 'Column drh_stateful_authentication.auth_mappings.user_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'user_id', 'text', 'Column drh_stateful_authentication.auth_mappings.user_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'user_id', 'Column drh_stateful_authentication.auth_mappings.user_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'user_id', 'Column drh_stateful_authentication.auth_mappings.user_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'auth_provider', 'Column drh_stateful_authentication.auth_mappings.auth_provider should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'auth_provider', 'character varying(50)', 'Column drh_stateful_authentication.auth_mappings.auth_provider should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'auth_provider', 'Column drh_stateful_authentication.auth_mappings.auth_provider should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'auth_provider', 'Column drh_stateful_authentication.auth_mappings.auth_provider should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'provider_user_id', 'Column drh_stateful_authentication.auth_mappings.provider_user_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'provider_user_id', 'character varying(255)', 'Column drh_stateful_authentication.auth_mappings.provider_user_id should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'provider_user_id', 'Column drh_stateful_authentication.auth_mappings.provider_user_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'provider_user_id', 'Column drh_stateful_authentication.auth_mappings.provider_user_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'access_token', 'Column drh_stateful_authentication.auth_mappings.access_token should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'access_token', 'character varying(255)', 'Column drh_stateful_authentication.auth_mappings.access_token should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'access_token', 'Column drh_stateful_authentication.auth_mappings.access_token should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'access_token', 'Column drh_stateful_authentication.auth_mappings.access_token should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'refresh_token', 'Column drh_stateful_authentication.auth_mappings.refresh_token should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'refresh_token', 'character varying(255)', 'Column drh_stateful_authentication.auth_mappings.refresh_token should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'refresh_token', 'Column drh_stateful_authentication.auth_mappings.refresh_token should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'refresh_token', 'Column drh_stateful_authentication.auth_mappings.refresh_token should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'status', 'Column drh_stateful_authentication.auth_mappings.status should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'status', 'character varying(50)', 'Column drh_stateful_authentication.auth_mappings.status should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'status', 'Column drh_stateful_authentication.auth_mappings.status should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'status', 'Column drh_stateful_authentication.auth_mappings.status should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'rec_status_id', 'Column drh_stateful_authentication.auth_mappings.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'rec_status_id', 'integer', 'Column drh_stateful_authentication.auth_mappings.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_authentication', 'external_auth_mappings', 'rec_status_id', 'Column drh_stateful_authentication.auth_mappings.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'rec_status_id', 'Column drh_stateful_authentication.auth_mappings.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'created_at', 'Column drh_stateful_authentication.auth_mappings.created_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'created_at', 'timestamp with time zone', 'Column drh_stateful_authentication.auth_mappings.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'external_auth_mappings', 'created_at', 'Column drh_stateful_authentication.auth_mappings.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'external_auth_mappings', 'created_at', 'Column drh_stateful_authentication.auth_mappings.created_at should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'external_auth_mappings', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_authentication.auth_mappings.created_at default is');

SELECT has_column(       'drh_stateful_authentication', 'external_auth_mappings', 'updated_at', 'Column drh_stateful_authentication.auth_mappings.updated_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'external_auth_mappings', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_authentication.auth_mappings.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'external_auth_mappings', 'updated_at', 'Column drh_stateful_authentication.auth_mappings.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'external_auth_mappings', 'updated_at', 'Column drh_stateful_authentication.auth_mappings.updated_at should not have a default');

SELECT * FROM finish();
ROLLBACK;
