SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(54);

SELECT has_table(
    'drh_stateful_master', 'loinc_codes',
    'Should have table drh_stateful_master.loinc_codes'
);

SELECT has_pk(
    'drh_stateful_master', 'loinc_codes',
    'Table drh_stateful_master.loinc_codes should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'loinc_codes'::name, ARRAY[
    'loinc_code_id'::name,
    'loinc_code'::name,
    'loinc_description'::name,
    'loinc_class'::name,
    'loinc_type'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'loinc_code_id', 'Column drh_stateful_master.loinc_codes.loinc_code_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'loinc_code_id', 'integer', 'Column drh_stateful_master.loinc_codes.loinc_code_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'loinc_codes', 'loinc_code_id', 'Column drh_stateful_master.loinc_codes.loinc_code_id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'loinc_codes', 'loinc_code_id', 'Column drh_stateful_master.loinc_codes.loinc_code_id should have a default');
SELECT col_default_is(   'drh_stateful_master', 'loinc_codes', 'loinc_code_id', 'nextval(''drh_stateful_master.loinc_codes_loinc_code_id_seq''::regclass)', 'Column drh_stateful_master.loinc_codes.loinc_code_id default is');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'loinc_code', 'Column drh_stateful_master.loinc_codes.loinc_code should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'loinc_code', 'text', 'Column drh_stateful_master.loinc_codes.loinc_code should be type text');
SELECT col_not_null(     'drh_stateful_master', 'loinc_codes', 'loinc_code', 'Column drh_stateful_master.loinc_codes.loinc_code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'loinc_code', 'Column drh_stateful_master.loinc_codes.loinc_code should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'loinc_description', 'Column drh_stateful_master.loinc_codes.loinc_description should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'loinc_description', 'text', 'Column drh_stateful_master.loinc_codes.loinc_description should be type text');
SELECT col_not_null(     'drh_stateful_master', 'loinc_codes', 'loinc_description', 'Column drh_stateful_master.loinc_codes.loinc_description should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'loinc_description', 'Column drh_stateful_master.loinc_codes.loinc_description should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'loinc_class', 'Column drh_stateful_master.loinc_codes.loinc_class should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'loinc_class', 'character varying(100)', 'Column drh_stateful_master.loinc_codes.loinc_class should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_master', 'loinc_codes', 'loinc_class', 'Column drh_stateful_master.loinc_codes.loinc_class should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'loinc_class', 'Column drh_stateful_master.loinc_codes.loinc_class should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'loinc_type', 'Column drh_stateful_master.loinc_codes.loinc_type should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'loinc_type', 'character varying(100)', 'Column drh_stateful_master.loinc_codes.loinc_type should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_master', 'loinc_codes', 'loinc_type', 'Column drh_stateful_master.loinc_codes.loinc_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'loinc_type', 'Column drh_stateful_master.loinc_codes.loinc_type should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'rec_status_id', 'Column drh_stateful_master.loinc_codes.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'rec_status_id', 'integer', 'Column drh_stateful_master.loinc_codes.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'loinc_codes', 'rec_status_id', 'Column drh_stateful_master.loinc_codes.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'rec_status_id', 'Column drh_stateful_master.loinc_codes.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'created_at', 'Column drh_stateful_master.loinc_codes.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.loinc_codes.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'loinc_codes', 'created_at', 'Column drh_stateful_master.loinc_codes.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'loinc_codes', 'created_at', 'Column drh_stateful_master.loinc_codes.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'loinc_codes', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.loinc_codes.created_at default is');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'created_by', 'Column drh_stateful_master.loinc_codes.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'created_by', 'text', 'Column drh_stateful_master.loinc_codes.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'loinc_codes', 'created_by', 'Column drh_stateful_master.loinc_codes.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'loinc_codes', 'created_by', 'Column drh_stateful_master.loinc_codes.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'loinc_codes', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.loinc_codes.created_by default is');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'updated_at', 'Column drh_stateful_master.loinc_codes.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.loinc_codes.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'loinc_codes', 'updated_at', 'Column drh_stateful_master.loinc_codes.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'updated_at', 'Column drh_stateful_master.loinc_codes.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'updated_by', 'Column drh_stateful_master.loinc_codes.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'updated_by', 'text', 'Column drh_stateful_master.loinc_codes.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'loinc_codes', 'updated_by', 'Column drh_stateful_master.loinc_codes.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'updated_by', 'Column drh_stateful_master.loinc_codes.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'deleted_at', 'Column drh_stateful_master.loinc_codes.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.loinc_codes.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'loinc_codes', 'deleted_at', 'Column drh_stateful_master.loinc_codes.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'deleted_at', 'Column drh_stateful_master.loinc_codes.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'loinc_codes', 'deleted_by', 'Column drh_stateful_master.loinc_codes.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'loinc_codes', 'deleted_by', 'text', 'Column drh_stateful_master.loinc_codes.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'loinc_codes', 'deleted_by', 'Column drh_stateful_master.loinc_codes.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'loinc_codes', 'deleted_by', 'Column drh_stateful_master.loinc_codes.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
