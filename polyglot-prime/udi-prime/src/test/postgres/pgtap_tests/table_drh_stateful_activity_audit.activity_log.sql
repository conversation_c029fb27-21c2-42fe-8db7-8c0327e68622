SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(103);

SELECT has_table(
    'drh_stateful_activity_audit', 'activity_log',
    'Should have table drh_stateful_activity_audit.activity_log'
);

SELECT has_pk(
    'drh_stateful_activity_audit', 'activity_log',
    'Table drh_stateful_activity_audit.activity_log should have a primary key'
);

SELECT columns_are('drh_stateful_activity_audit'::name, 'activity_log'::name, ARRAY[
    'activity_id'::name,
    'activity_name'::name,
    'activity_type'::name,
    'activity_description'::name,
    'root_id'::name,
    'parent_id'::name,
    'activity_hierarchy'::name,
    'hierarchy_path'::name,
    'request_url'::name,
    'tenant_id'::name,
    'platform'::name,
    'environment'::name,
    'created_by'::name,
    'user_name'::name,
    'created_at'::name,
    'app_version'::name,
    'test_case'::name,
    'session_id'::name,
    'linkage_id'::name,
    'ip_address'::name,
    'location_latitude'::name,
    'location_longitude'::name,
    'activity_data'::name,
    'activity_log_level'::name,
    'session_unique_id'::name
]);

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'activity_id', 'Column drh_stateful_activity_audit.activity_log.activity_id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'activity_id', 'text', 'Column drh_stateful_activity_audit.activity_log.activity_id should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'activity_id', 'Column drh_stateful_activity_audit.activity_log.activity_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'activity_id', 'Column drh_stateful_activity_audit.activity_log.activity_id should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'activity_name', 'Column drh_stateful_activity_audit.activity_log.activity_name should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'activity_name', 'text', 'Column drh_stateful_activity_audit.activity_log.activity_name should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'activity_name', 'Column drh_stateful_activity_audit.activity_log.activity_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'activity_name', 'Column drh_stateful_activity_audit.activity_log.activity_name should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'activity_type', 'Column drh_stateful_activity_audit.activity_log.activity_type should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'activity_type', 'text', 'Column drh_stateful_activity_audit.activity_log.activity_type should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'activity_type', 'Column drh_stateful_activity_audit.activity_log.activity_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'activity_type', 'Column drh_stateful_activity_audit.activity_log.activity_type should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'activity_description', 'Column drh_stateful_activity_audit.activity_log.activity_description should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'activity_description', 'text', 'Column drh_stateful_activity_audit.activity_log.activity_description should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'activity_description', 'Column drh_stateful_activity_audit.activity_log.activity_description should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'activity_description', 'Column drh_stateful_activity_audit.activity_log.activity_description should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'root_id', 'Column drh_stateful_activity_audit.activity_log.root_id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'root_id', 'integer', 'Column drh_stateful_activity_audit.activity_log.root_id should be type integer');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'root_id', 'Column drh_stateful_activity_audit.activity_log.root_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'root_id', 'Column drh_stateful_activity_audit.activity_log.root_id should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'parent_id', 'Column drh_stateful_activity_audit.activity_log.parent_id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'parent_id', 'integer', 'Column drh_stateful_activity_audit.activity_log.parent_id should be type integer');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'parent_id', 'Column drh_stateful_activity_audit.activity_log.parent_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'parent_id', 'Column drh_stateful_activity_audit.activity_log.parent_id should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'activity_hierarchy', 'Column drh_stateful_activity_audit.activity_log.activity_hierarchy should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'activity_hierarchy', 'text', 'Column drh_stateful_activity_audit.activity_log.activity_hierarchy should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'activity_hierarchy', 'Column drh_stateful_activity_audit.activity_log.activity_hierarchy should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'activity_hierarchy', 'Column drh_stateful_activity_audit.activity_log.activity_hierarchy should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'hierarchy_path', 'Column drh_stateful_activity_audit.activity_log.hierarchy_path should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'hierarchy_path', 'text', 'Column drh_stateful_activity_audit.activity_log.hierarchy_path should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'hierarchy_path', 'Column drh_stateful_activity_audit.activity_log.hierarchy_path should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'hierarchy_path', 'Column drh_stateful_activity_audit.activity_log.hierarchy_path should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'request_url', 'Column drh_stateful_activity_audit.activity_log.request_url should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'request_url', 'text', 'Column drh_stateful_activity_audit.activity_log.request_url should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'request_url', 'Column drh_stateful_activity_audit.activity_log.request_url should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'request_url', 'Column drh_stateful_activity_audit.activity_log.request_url should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'tenant_id', 'Column drh_stateful_activity_audit.activity_log.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'tenant_id', 'integer', 'Column drh_stateful_activity_audit.activity_log.tenant_id should be type integer');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'tenant_id', 'Column drh_stateful_activity_audit.activity_log.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'tenant_id', 'Column drh_stateful_activity_audit.activity_log.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'platform', 'Column drh_stateful_activity_audit.activity_log.platform should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'platform', 'text', 'Column drh_stateful_activity_audit.activity_log.platform should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'platform', 'Column drh_stateful_activity_audit.activity_log.platform should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'platform', 'Column drh_stateful_activity_audit.activity_log.platform should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'environment', 'Column drh_stateful_activity_audit.activity_log.environment should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'environment', 'text', 'Column drh_stateful_activity_audit.activity_log.environment should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'environment', 'Column drh_stateful_activity_audit.activity_log.environment should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'environment', 'Column drh_stateful_activity_audit.activity_log.environment should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'created_by', 'Column drh_stateful_activity_audit.activity_log.created_by should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'created_by', 'text', 'Column drh_stateful_activity_audit.activity_log.created_by should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'created_by', 'Column drh_stateful_activity_audit.activity_log.created_by should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'created_by', 'Column drh_stateful_activity_audit.activity_log.created_by should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'user_name', 'Column drh_stateful_activity_audit.activity_log.user_name should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'user_name', 'text', 'Column drh_stateful_activity_audit.activity_log.user_name should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'user_name', 'Column drh_stateful_activity_audit.activity_log.user_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'user_name', 'Column drh_stateful_activity_audit.activity_log.user_name should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'created_at', 'Column drh_stateful_activity_audit.activity_log.created_at should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'created_at', 'text', 'Column drh_stateful_activity_audit.activity_log.created_at should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'created_at', 'Column drh_stateful_activity_audit.activity_log.created_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'created_at', 'Column drh_stateful_activity_audit.activity_log.created_at should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'app_version', 'Column drh_stateful_activity_audit.activity_log.app_version should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'app_version', 'text', 'Column drh_stateful_activity_audit.activity_log.app_version should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'app_version', 'Column drh_stateful_activity_audit.activity_log.app_version should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'app_version', 'Column drh_stateful_activity_audit.activity_log.app_version should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'test_case', 'Column drh_stateful_activity_audit.activity_log.test_case should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'test_case', 'text', 'Column drh_stateful_activity_audit.activity_log.test_case should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'test_case', 'Column drh_stateful_activity_audit.activity_log.test_case should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'test_case', 'Column drh_stateful_activity_audit.activity_log.test_case should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'session_id', 'Column drh_stateful_activity_audit.activity_log.session_id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'session_id', 'text', 'Column drh_stateful_activity_audit.activity_log.session_id should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'session_id', 'Column drh_stateful_activity_audit.activity_log.session_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'session_id', 'Column drh_stateful_activity_audit.activity_log.session_id should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'linkage_id', 'Column drh_stateful_activity_audit.activity_log.linkage_id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'linkage_id', 'text', 'Column drh_stateful_activity_audit.activity_log.linkage_id should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'linkage_id', 'Column drh_stateful_activity_audit.activity_log.linkage_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'linkage_id', 'Column drh_stateful_activity_audit.activity_log.linkage_id should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'ip_address', 'Column drh_stateful_activity_audit.activity_log.ip_address should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'ip_address', 'text', 'Column drh_stateful_activity_audit.activity_log.ip_address should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'ip_address', 'Column drh_stateful_activity_audit.activity_log.ip_address should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'ip_address', 'Column drh_stateful_activity_audit.activity_log.ip_address should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'location_latitude', 'Column drh_stateful_activity_audit.activity_log.location_latitude should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'location_latitude', 'text', 'Column drh_stateful_activity_audit.activity_log.location_latitude should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'location_latitude', 'Column drh_stateful_activity_audit.activity_log.location_latitude should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'location_latitude', 'Column drh_stateful_activity_audit.activity_log.location_latitude should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'location_longitude', 'Column drh_stateful_activity_audit.activity_log.location_longitude should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'location_longitude', 'text', 'Column drh_stateful_activity_audit.activity_log.location_longitude should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'location_longitude', 'Column drh_stateful_activity_audit.activity_log.location_longitude should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'location_longitude', 'Column drh_stateful_activity_audit.activity_log.location_longitude should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'activity_data', 'Column drh_stateful_activity_audit.activity_log.activity_data should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'activity_data', 'text', 'Column drh_stateful_activity_audit.activity_log.activity_data should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'activity_data', 'Column drh_stateful_activity_audit.activity_log.activity_data should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'activity_data', 'Column drh_stateful_activity_audit.activity_log.activity_data should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'activity_log_level', 'Column drh_stateful_activity_audit.activity_log.activity_log_level should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'activity_log_level', 'text', 'Column drh_stateful_activity_audit.activity_log.activity_log_level should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'activity_log_level', 'Column drh_stateful_activity_audit.activity_log.activity_log_level should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'activity_log_level', 'Column drh_stateful_activity_audit.activity_log.activity_log_level should not have a default');

SELECT has_column(       'drh_stateful_activity_audit', 'activity_log', 'session_unique_id', 'Column drh_stateful_activity_audit.activity_log.session_unique_id should exist');
SELECT col_type_is(      'drh_stateful_activity_audit', 'activity_log', 'session_unique_id', 'text', 'Column drh_stateful_activity_audit.activity_log.session_unique_id should be type text');
SELECT col_not_null(     'drh_stateful_activity_audit', 'activity_log', 'session_unique_id', 'Column drh_stateful_activity_audit.activity_log.session_unique_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_activity_audit', 'activity_log', 'session_unique_id', 'Column drh_stateful_activity_audit.activity_log.session_unique_id should not have a default');

SELECT * FROM finish();
ROLLBACK;
