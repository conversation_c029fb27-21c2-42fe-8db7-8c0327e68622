SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(85);

SELECT has_table(
    'drh_stateful_raw_data', 'raw_cgm_extract_data',
    'Should have table drh_stateful_raw_data.raw_cgm_extract_data'
);

SELECT has_pk(
    'drh_stateful_raw_data', 'raw_cgm_extract_data',
    'Table drh_stateful_raw_data.raw_cgm_extract_data should have a primary key'
);

SELECT columns_are('drh_stateful_raw_data'::name, 'raw_cgm_extract_data'::name, ARRAY[
    'cgm_raw_data_id'::name,
    'raw_file_id'::name,
    'study_id'::name,
    'participant_sid'::name,
    'cgm_raw_data_json'::name,
    'file_url'::name,
    'file_meta_data'::name,
    'cgm_data'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'cgm_raw_data_csv'::name,
    'cgm_raw_data_excel'::name,
    'cgm_raw_data_text'::name,
    'cgm_raw_data_xml'::name
]);

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_id', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'raw_file_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.raw_file_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'raw_file_id', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.raw_file_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'raw_cgm_extract_data', 'raw_file_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.raw_file_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'raw_file_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.raw_file_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'study_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.study_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'study_id', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.study_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'raw_cgm_extract_data', 'study_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'study_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.study_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'participant_sid', 'Column drh_stateful_raw_data.raw_cgm_extract_data.participant_sid should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'participant_sid', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.participant_sid should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'raw_cgm_extract_data', 'participant_sid', 'Column drh_stateful_raw_data.raw_cgm_extract_data.participant_sid should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'participant_sid', 'Column drh_stateful_raw_data.raw_cgm_extract_data.participant_sid should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_json', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_json should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_json', 'jsonb', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_json should be type jsonb');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_json', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_json should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_json', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_json should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_url', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_url should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_url', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_url should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_url', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_url should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_url', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_url should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_meta_data', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_meta_data should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_meta_data', 'jsonb', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_meta_data should be type jsonb');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_meta_data', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_meta_data should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'file_meta_data', 'Column drh_stateful_raw_data.raw_cgm_extract_data.file_meta_data should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_data', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_data should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_data', 'jsonb', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_data should be type jsonb');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_data', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_data should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_data', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_data should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'tenant_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'tenant_id', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_raw_data', 'raw_cgm_extract_data', 'tenant_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'tenant_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'rec_status_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'rec_status_id', 'integer', 'Column drh_stateful_raw_data.raw_cgm_extract_data.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_raw_data', 'raw_cgm_extract_data', 'rec_status_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'rec_status_id', 'Column drh_stateful_raw_data.raw_cgm_extract_data.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_at should have a default');
SELECT col_default_is(   'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_at default is');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_by', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_by should have a default');
SELECT col_default_is(   'drh_stateful_raw_data', 'raw_cgm_extract_data', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_raw_data.raw_cgm_extract_data.created_by default is');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_by', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'updated_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.updated_by should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_at', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_by', 'text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'deleted_by', 'Column drh_stateful_raw_data.raw_cgm_extract_data.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_csv', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_csv should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_csv', 'bytea', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_csv should be type bytea');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_csv', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_csv should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_csv', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_csv should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_excel', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_excel should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_excel', 'bytea', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_excel should be type bytea');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_excel', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_excel should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_excel', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_excel should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_text should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_text', 'bytea', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_text should be type bytea');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_text should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_text', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_text should not have a default');

SELECT has_column(       'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_xml', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_xml should exist');
SELECT col_type_is(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_xml', 'xml', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_xml should be type xml');
SELECT col_is_null(      'drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_xml', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_xml should allow NULL');
SELECT col_hasnt_default('drh_stateful_raw_data', 'raw_cgm_extract_data', 'cgm_raw_data_xml', 'Column drh_stateful_raw_data.raw_cgm_extract_data.cgm_raw_data_xml should not have a default');

SELECT * FROM finish();
ROLLBACK;
