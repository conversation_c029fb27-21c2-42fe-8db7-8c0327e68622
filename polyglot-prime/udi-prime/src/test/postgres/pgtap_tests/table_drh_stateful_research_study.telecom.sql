SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(65);

SELECT has_table(
    'drh_stateful_research_study', 'telecom',
    'Should have table drh_stateful_research_study.telecom'
);

SELECT has_pk(
    'drh_stateful_research_study', 'telecom',
    'Table drh_stateful_research_study.telecom should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'telecom'::name, ARRAY[
    'id'::name,
    'party_id'::name,
    'telecom_type'::name,
    'telecom_value'::name,
    'telecom_use'::name,
    'contact_point_system_id'::name,
    'contact_point_use_type_id'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'id', 'Column drh_stateful_research_study.telecom.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'id', 'text', 'Column drh_stateful_research_study.telecom.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'id', 'Column drh_stateful_research_study.telecom.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'id', 'Column drh_stateful_research_study.telecom.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'party_id', 'Column drh_stateful_research_study.telecom.party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'party_id', 'text', 'Column drh_stateful_research_study.telecom.party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'party_id', 'Column drh_stateful_research_study.telecom.party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'party_id', 'Column drh_stateful_research_study.telecom.party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'telecom_type', 'Column drh_stateful_research_study.telecom.telecom_type should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'telecom_type', 'character varying(50)', 'Column drh_stateful_research_study.telecom.telecom_type should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'telecom_type', 'Column drh_stateful_research_study.telecom.telecom_type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'telecom_type', 'Column drh_stateful_research_study.telecom.telecom_type should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'telecom_value', 'Column drh_stateful_research_study.telecom.telecom_value should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'telecom_value', 'character varying(255)', 'Column drh_stateful_research_study.telecom.telecom_value should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'telecom_value', 'Column drh_stateful_research_study.telecom.telecom_value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'telecom_value', 'Column drh_stateful_research_study.telecom.telecom_value should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'telecom_use', 'Column drh_stateful_research_study.telecom.telecom_use should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'telecom_use', 'character varying(50)', 'Column drh_stateful_research_study.telecom.telecom_use should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'telecom', 'telecom_use', 'Column drh_stateful_research_study.telecom.telecom_use should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'telecom_use', 'Column drh_stateful_research_study.telecom.telecom_use should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'contact_point_system_id', 'Column drh_stateful_research_study.telecom.contact_point_system_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'contact_point_system_id', 'text', 'Column drh_stateful_research_study.telecom.contact_point_system_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'contact_point_system_id', 'Column drh_stateful_research_study.telecom.contact_point_system_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'contact_point_system_id', 'Column drh_stateful_research_study.telecom.contact_point_system_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'contact_point_use_type_id', 'Column drh_stateful_research_study.telecom.contact_point_use_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'contact_point_use_type_id', 'text', 'Column drh_stateful_research_study.telecom.contact_point_use_type_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'contact_point_use_type_id', 'Column drh_stateful_research_study.telecom.contact_point_use_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'contact_point_use_type_id', 'Column drh_stateful_research_study.telecom.contact_point_use_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'tenant_id', 'Column drh_stateful_research_study.telecom.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'tenant_id', 'text', 'Column drh_stateful_research_study.telecom.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'tenant_id', 'Column drh_stateful_research_study.telecom.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'tenant_id', 'Column drh_stateful_research_study.telecom.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'rec_status_id', 'Column drh_stateful_research_study.telecom.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.telecom.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'telecom', 'rec_status_id', 'Column drh_stateful_research_study.telecom.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'rec_status_id', 'Column drh_stateful_research_study.telecom.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'created_at', 'Column drh_stateful_research_study.telecom.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.telecom.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'telecom', 'created_at', 'Column drh_stateful_research_study.telecom.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'telecom', 'created_at', 'Column drh_stateful_research_study.telecom.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'telecom', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.telecom.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'created_by', 'Column drh_stateful_research_study.telecom.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'created_by', 'text', 'Column drh_stateful_research_study.telecom.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'telecom', 'created_by', 'Column drh_stateful_research_study.telecom.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'telecom', 'created_by', 'Column drh_stateful_research_study.telecom.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'telecom', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.telecom.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'updated_at', 'Column drh_stateful_research_study.telecom.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.telecom.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'telecom', 'updated_at', 'Column drh_stateful_research_study.telecom.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'updated_at', 'Column drh_stateful_research_study.telecom.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'updated_by', 'Column drh_stateful_research_study.telecom.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'updated_by', 'text', 'Column drh_stateful_research_study.telecom.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'telecom', 'updated_by', 'Column drh_stateful_research_study.telecom.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'updated_by', 'Column drh_stateful_research_study.telecom.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'deleted_at', 'Column drh_stateful_research_study.telecom.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.telecom.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'telecom', 'deleted_at', 'Column drh_stateful_research_study.telecom.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'deleted_at', 'Column drh_stateful_research_study.telecom.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'telecom', 'deleted_by', 'Column drh_stateful_research_study.telecom.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'telecom', 'deleted_by', 'text', 'Column drh_stateful_research_study.telecom.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'telecom', 'deleted_by', 'Column drh_stateful_research_study.telecom.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'telecom', 'deleted_by', 'Column drh_stateful_research_study.telecom.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
