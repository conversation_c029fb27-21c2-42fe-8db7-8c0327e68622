SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(61);

SELECT has_table(
    'drh_stateful_authentication', 'user_role',
    'Should have table drh_stateful_authentication.user_role'
);

SELECT has_pk(
    'drh_stateful_authentication', 'user_role',
    'Table drh_stateful_authentication.user_role should have a primary key'
);

SELECT columns_are('drh_stateful_authentication'::name, 'user_role'::name, ARRAY[
    'user_role_id'::name,
    'role_id'::name,
    'user_id'::name,
    'user_group_id'::name,
    'start_date'::name,
    'end_date'::name,
    'metadata'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'user_role_id', 'Column drh_stateful_authentication.user_role.user_role_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'user_role_id', 'text', 'Column drh_stateful_authentication.user_role.user_role_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'user_role', 'user_role_id', 'Column drh_stateful_authentication.user_role.user_role_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'user_role_id', 'Column drh_stateful_authentication.user_role.user_role_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'role_id', 'Column drh_stateful_authentication.user_role.role_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'role_id', 'text', 'Column drh_stateful_authentication.user_role.role_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'user_role', 'role_id', 'Column drh_stateful_authentication.user_role.role_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'role_id', 'Column drh_stateful_authentication.user_role.role_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'user_id', 'Column drh_stateful_authentication.user_role.user_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'user_id', 'text', 'Column drh_stateful_authentication.user_role.user_id should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'user_id', 'Column drh_stateful_authentication.user_role.user_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'user_id', 'Column drh_stateful_authentication.user_role.user_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'user_group_id', 'Column drh_stateful_authentication.user_role.user_group_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'user_group_id', 'text', 'Column drh_stateful_authentication.user_role.user_group_id should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'user_group_id', 'Column drh_stateful_authentication.user_role.user_group_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'user_group_id', 'Column drh_stateful_authentication.user_role.user_group_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'start_date', 'Column drh_stateful_authentication.user_role.start_date should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'start_date', 'date', 'Column drh_stateful_authentication.user_role.start_date should be type date');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'start_date', 'Column drh_stateful_authentication.user_role.start_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'start_date', 'Column drh_stateful_authentication.user_role.start_date should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'end_date', 'Column drh_stateful_authentication.user_role.end_date should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'end_date', 'date', 'Column drh_stateful_authentication.user_role.end_date should be type date');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'end_date', 'Column drh_stateful_authentication.user_role.end_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'end_date', 'Column drh_stateful_authentication.user_role.end_date should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'metadata', 'Column drh_stateful_authentication.user_role.metadata should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'metadata', 'jsonb', 'Column drh_stateful_authentication.user_role.metadata should be type jsonb');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'metadata', 'Column drh_stateful_authentication.user_role.metadata should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'metadata', 'Column drh_stateful_authentication.user_role.metadata should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'rec_status_id', 'Column drh_stateful_authentication.user_role.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'rec_status_id', 'integer', 'Column drh_stateful_authentication.user_role.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_authentication', 'user_role', 'rec_status_id', 'Column drh_stateful_authentication.user_role.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'rec_status_id', 'Column drh_stateful_authentication.user_role.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'created_at', 'Column drh_stateful_authentication.user_role.created_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'created_at', 'timestamp with time zone', 'Column drh_stateful_authentication.user_role.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'created_at', 'Column drh_stateful_authentication.user_role.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'user_role', 'created_at', 'Column drh_stateful_authentication.user_role.created_at should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'user_role', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_authentication.user_role.created_at default is');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'created_by', 'Column drh_stateful_authentication.user_role.created_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'created_by', 'text', 'Column drh_stateful_authentication.user_role.created_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'created_by', 'Column drh_stateful_authentication.user_role.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'user_role', 'created_by', 'Column drh_stateful_authentication.user_role.created_by should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'user_role', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_authentication.user_role.created_by default is');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'updated_at', 'Column drh_stateful_authentication.user_role.updated_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_authentication.user_role.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'updated_at', 'Column drh_stateful_authentication.user_role.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'updated_at', 'Column drh_stateful_authentication.user_role.updated_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'updated_by', 'Column drh_stateful_authentication.user_role.updated_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'updated_by', 'text', 'Column drh_stateful_authentication.user_role.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'updated_by', 'Column drh_stateful_authentication.user_role.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'updated_by', 'Column drh_stateful_authentication.user_role.updated_by should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'deleted_at', 'Column drh_stateful_authentication.user_role.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_authentication.user_role.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'deleted_at', 'Column drh_stateful_authentication.user_role.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'deleted_at', 'Column drh_stateful_authentication.user_role.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_role', 'deleted_by', 'Column drh_stateful_authentication.user_role.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_role', 'deleted_by', 'text', 'Column drh_stateful_authentication.user_role.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_role', 'deleted_by', 'Column drh_stateful_authentication.user_role.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_role', 'deleted_by', 'Column drh_stateful_authentication.user_role.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
