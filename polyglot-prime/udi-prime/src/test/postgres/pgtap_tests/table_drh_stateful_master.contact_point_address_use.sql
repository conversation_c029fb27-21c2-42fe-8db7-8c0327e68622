SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(49);

SELECT has_table(
    'drh_stateful_master', 'contact_point_address_use',
    'Should have table drh_stateful_master.contact_point_address_use'
);

SELECT has_pk(
    'drh_stateful_master', 'contact_point_address_use',
    'Table drh_stateful_master.contact_point_address_use should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'contact_point_address_use'::name, ARRAY[
    'id'::name,
    'code'::name,
    'system'::name,
    'value'::name,
    'description'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'id', 'Column drh_stateful_master.contact_point_address_use.id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'id', 'text', 'Column drh_stateful_master.contact_point_address_use.id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'contact_point_address_use', 'id', 'Column drh_stateful_master.contact_point_address_use.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'id', 'Column drh_stateful_master.contact_point_address_use.id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'code', 'Column drh_stateful_master.contact_point_address_use.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'code', 'character varying(50)', 'Column drh_stateful_master.contact_point_address_use.code should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'contact_point_address_use', 'code', 'Column drh_stateful_master.contact_point_address_use.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'code', 'Column drh_stateful_master.contact_point_address_use.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'system', 'Column drh_stateful_master.contact_point_address_use.system should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'system', 'character varying(100)', 'Column drh_stateful_master.contact_point_address_use.system should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'system', 'Column drh_stateful_master.contact_point_address_use.system should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'system', 'Column drh_stateful_master.contact_point_address_use.system should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'value', 'Column drh_stateful_master.contact_point_address_use.value should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'value', 'character varying(100)', 'Column drh_stateful_master.contact_point_address_use.value should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_master', 'contact_point_address_use', 'value', 'Column drh_stateful_master.contact_point_address_use.value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'value', 'Column drh_stateful_master.contact_point_address_use.value should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'description', 'Column drh_stateful_master.contact_point_address_use.description should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'description', 'text', 'Column drh_stateful_master.contact_point_address_use.description should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'description', 'Column drh_stateful_master.contact_point_address_use.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'description', 'Column drh_stateful_master.contact_point_address_use.description should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'created_at', 'Column drh_stateful_master.contact_point_address_use.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.contact_point_address_use.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'created_at', 'Column drh_stateful_master.contact_point_address_use.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contact_point_address_use', 'created_at', 'Column drh_stateful_master.contact_point_address_use.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contact_point_address_use', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.contact_point_address_use.created_at default is');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'created_by', 'Column drh_stateful_master.contact_point_address_use.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'created_by', 'text', 'Column drh_stateful_master.contact_point_address_use.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'created_by', 'Column drh_stateful_master.contact_point_address_use.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contact_point_address_use', 'created_by', 'Column drh_stateful_master.contact_point_address_use.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contact_point_address_use', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.contact_point_address_use.created_by default is');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'updated_at', 'Column drh_stateful_master.contact_point_address_use.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.contact_point_address_use.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'updated_at', 'Column drh_stateful_master.contact_point_address_use.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'updated_at', 'Column drh_stateful_master.contact_point_address_use.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'updated_by', 'Column drh_stateful_master.contact_point_address_use.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'updated_by', 'text', 'Column drh_stateful_master.contact_point_address_use.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'updated_by', 'Column drh_stateful_master.contact_point_address_use.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'updated_by', 'Column drh_stateful_master.contact_point_address_use.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'deleted_at', 'Column drh_stateful_master.contact_point_address_use.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.contact_point_address_use.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'deleted_at', 'Column drh_stateful_master.contact_point_address_use.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'deleted_at', 'Column drh_stateful_master.contact_point_address_use.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_address_use', 'deleted_by', 'Column drh_stateful_master.contact_point_address_use.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_address_use', 'deleted_by', 'text', 'Column drh_stateful_master.contact_point_address_use.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_address_use', 'deleted_by', 'Column drh_stateful_master.contact_point_address_use.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_address_use', 'deleted_by', 'Column drh_stateful_master.contact_point_address_use.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
