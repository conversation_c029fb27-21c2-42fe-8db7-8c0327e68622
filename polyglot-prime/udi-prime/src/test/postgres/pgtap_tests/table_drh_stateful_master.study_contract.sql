SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(109);

SELECT has_table(
    'drh_stateful_master', 'study_contract',
    'Should have table drh_stateful_master.study_contract'
);

SELECT has_pk(
    'drh_stateful_master', 'study_contract',
    'Table drh_stateful_master.study_contract should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'study_contract'::name, ARRAY[
    'contract_id'::name,
    'subject_reference_id'::name,
    'status_type_id'::name,
    'legal_state_code'::name,
    'issued_date'::name,
    'applies_start_date'::name,
    'applies_end_date'::name,
    'expiration_type_code'::name,
    'contract_name'::name,
    'title'::name,
    'subtitle'::name,
    'alias'::name,
    'authority_org_reference'::name,
    'location_id'::name,
    'site_id'::name,
    'contract_type_code'::name,
    'author_reference_id'::name,
    'scope_code'::name,
    'legally_binding_reference_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'study_contract', 'contract_id', 'Column drh_stateful_master.study_contract.contract_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'contract_id', 'text', 'Column drh_stateful_master.study_contract.contract_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'contract_id', 'Column drh_stateful_master.study_contract.contract_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'contract_id', 'Column drh_stateful_master.study_contract.contract_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'subject_reference_id', 'Column drh_stateful_master.study_contract.subject_reference_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'subject_reference_id', 'text', 'Column drh_stateful_master.study_contract.subject_reference_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'subject_reference_id', 'Column drh_stateful_master.study_contract.subject_reference_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'subject_reference_id', 'Column drh_stateful_master.study_contract.subject_reference_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'status_type_id', 'Column drh_stateful_master.study_contract.status_type_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'status_type_id', 'text', 'Column drh_stateful_master.study_contract.status_type_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'status_type_id', 'Column drh_stateful_master.study_contract.status_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'status_type_id', 'Column drh_stateful_master.study_contract.status_type_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'legal_state_code', 'Column drh_stateful_master.study_contract.legal_state_code should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'legal_state_code', 'text', 'Column drh_stateful_master.study_contract.legal_state_code should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'legal_state_code', 'Column drh_stateful_master.study_contract.legal_state_code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'legal_state_code', 'Column drh_stateful_master.study_contract.legal_state_code should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'issued_date', 'Column drh_stateful_master.study_contract.issued_date should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'issued_date', 'timestamp with time zone', 'Column drh_stateful_master.study_contract.issued_date should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'issued_date', 'Column drh_stateful_master.study_contract.issued_date should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'issued_date', 'Column drh_stateful_master.study_contract.issued_date should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'applies_start_date', 'Column drh_stateful_master.study_contract.applies_start_date should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'applies_start_date', 'timestamp with time zone', 'Column drh_stateful_master.study_contract.applies_start_date should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'applies_start_date', 'Column drh_stateful_master.study_contract.applies_start_date should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'applies_start_date', 'Column drh_stateful_master.study_contract.applies_start_date should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'applies_end_date', 'Column drh_stateful_master.study_contract.applies_end_date should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'applies_end_date', 'timestamp with time zone', 'Column drh_stateful_master.study_contract.applies_end_date should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'applies_end_date', 'Column drh_stateful_master.study_contract.applies_end_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'applies_end_date', 'Column drh_stateful_master.study_contract.applies_end_date should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'expiration_type_code', 'Column drh_stateful_master.study_contract.expiration_type_code should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'expiration_type_code', 'character varying(255)', 'Column drh_stateful_master.study_contract.expiration_type_code should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'expiration_type_code', 'Column drh_stateful_master.study_contract.expiration_type_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'expiration_type_code', 'Column drh_stateful_master.study_contract.expiration_type_code should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'contract_name', 'Column drh_stateful_master.study_contract.contract_name should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'contract_name', 'character varying(255)', 'Column drh_stateful_master.study_contract.contract_name should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'contract_name', 'Column drh_stateful_master.study_contract.contract_name should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'contract_name', 'Column drh_stateful_master.study_contract.contract_name should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'title', 'Column drh_stateful_master.study_contract.title should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'title', 'character varying(255)', 'Column drh_stateful_master.study_contract.title should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'title', 'Column drh_stateful_master.study_contract.title should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'title', 'Column drh_stateful_master.study_contract.title should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'subtitle', 'Column drh_stateful_master.study_contract.subtitle should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'subtitle', 'character varying(255)', 'Column drh_stateful_master.study_contract.subtitle should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'subtitle', 'Column drh_stateful_master.study_contract.subtitle should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'subtitle', 'Column drh_stateful_master.study_contract.subtitle should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'alias', 'Column drh_stateful_master.study_contract.alias should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'alias', 'character varying(255)', 'Column drh_stateful_master.study_contract.alias should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'alias', 'Column drh_stateful_master.study_contract.alias should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'alias', 'Column drh_stateful_master.study_contract.alias should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'authority_org_reference', 'Column drh_stateful_master.study_contract.authority_org_reference should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'authority_org_reference', 'text', 'Column drh_stateful_master.study_contract.authority_org_reference should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'authority_org_reference', 'Column drh_stateful_master.study_contract.authority_org_reference should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'authority_org_reference', 'Column drh_stateful_master.study_contract.authority_org_reference should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'location_id', 'Column drh_stateful_master.study_contract.location_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'location_id', 'text', 'Column drh_stateful_master.study_contract.location_id should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'location_id', 'Column drh_stateful_master.study_contract.location_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'location_id', 'Column drh_stateful_master.study_contract.location_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'site_id', 'Column drh_stateful_master.study_contract.site_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'site_id', 'text', 'Column drh_stateful_master.study_contract.site_id should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'site_id', 'Column drh_stateful_master.study_contract.site_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'site_id', 'Column drh_stateful_master.study_contract.site_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'contract_type_code', 'Column drh_stateful_master.study_contract.contract_type_code should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'contract_type_code', 'text', 'Column drh_stateful_master.study_contract.contract_type_code should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'contract_type_code', 'Column drh_stateful_master.study_contract.contract_type_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'contract_type_code', 'Column drh_stateful_master.study_contract.contract_type_code should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'author_reference_id', 'Column drh_stateful_master.study_contract.author_reference_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'author_reference_id', 'text', 'Column drh_stateful_master.study_contract.author_reference_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'author_reference_id', 'Column drh_stateful_master.study_contract.author_reference_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'author_reference_id', 'Column drh_stateful_master.study_contract.author_reference_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'scope_code', 'Column drh_stateful_master.study_contract.scope_code should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'scope_code', 'character varying(4096)', 'Column drh_stateful_master.study_contract.scope_code should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'scope_code', 'Column drh_stateful_master.study_contract.scope_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'scope_code', 'Column drh_stateful_master.study_contract.scope_code should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'legally_binding_reference_id', 'Column drh_stateful_master.study_contract.legally_binding_reference_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'legally_binding_reference_id', 'text', 'Column drh_stateful_master.study_contract.legally_binding_reference_id should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'legally_binding_reference_id', 'Column drh_stateful_master.study_contract.legally_binding_reference_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'legally_binding_reference_id', 'Column drh_stateful_master.study_contract.legally_binding_reference_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'rec_status_id', 'Column drh_stateful_master.study_contract.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'rec_status_id', 'integer', 'Column drh_stateful_master.study_contract.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'study_contract', 'rec_status_id', 'Column drh_stateful_master.study_contract.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'rec_status_id', 'Column drh_stateful_master.study_contract.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'created_at', 'Column drh_stateful_master.study_contract.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.study_contract.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'created_at', 'Column drh_stateful_master.study_contract.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'study_contract', 'created_at', 'Column drh_stateful_master.study_contract.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'study_contract', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.study_contract.created_at default is');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'created_by', 'Column drh_stateful_master.study_contract.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'created_by', 'text', 'Column drh_stateful_master.study_contract.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'created_by', 'Column drh_stateful_master.study_contract.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'study_contract', 'created_by', 'Column drh_stateful_master.study_contract.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'study_contract', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.study_contract.created_by default is');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'updated_at', 'Column drh_stateful_master.study_contract.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.study_contract.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'updated_at', 'Column drh_stateful_master.study_contract.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'updated_at', 'Column drh_stateful_master.study_contract.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'updated_by', 'Column drh_stateful_master.study_contract.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'updated_by', 'text', 'Column drh_stateful_master.study_contract.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'updated_by', 'Column drh_stateful_master.study_contract.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'updated_by', 'Column drh_stateful_master.study_contract.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'deleted_at', 'Column drh_stateful_master.study_contract.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.study_contract.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'deleted_at', 'Column drh_stateful_master.study_contract.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'deleted_at', 'Column drh_stateful_master.study_contract.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_contract', 'deleted_by', 'Column drh_stateful_master.study_contract.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_contract', 'deleted_by', 'text', 'Column drh_stateful_master.study_contract.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_contract', 'deleted_by', 'Column drh_stateful_master.study_contract.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_contract', 'deleted_by', 'Column drh_stateful_master.study_contract.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
