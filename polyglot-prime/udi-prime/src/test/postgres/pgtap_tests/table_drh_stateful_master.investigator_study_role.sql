SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(12);

SELECT has_table(
    'drh_stateful_master', 'investigator_study_role',
    'Should have table drh_stateful_master.investigator_study_role'
);

SELECT has_pk(
    'drh_stateful_master', 'investigator_study_role',
    'Table drh_stateful_master.investigator_study_role should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'investigator_study_role'::name, ARRAY[
    'code'::name,
    'role'::name
]);

SELECT has_column(       'drh_stateful_master', 'investigator_study_role', 'code', 'Column drh_stateful_master.investigator_study_role.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'investigator_study_role', 'code', 'integer', 'Column drh_stateful_master.investigator_study_role.code should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'investigator_study_role', 'code', 'Column drh_stateful_master.investigator_study_role.code should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'investigator_study_role', 'code', 'Column drh_stateful_master.investigator_study_role.code should have a default');
SELECT col_default_is(   'drh_stateful_master', 'investigator_study_role', 'code', 'nextval(''drh_stateful_master.investigator_study_role_code_seq''::regclass)', 'Column drh_stateful_master.investigator_study_role.code default is');

SELECT has_column(       'drh_stateful_master', 'investigator_study_role', 'role', 'Column drh_stateful_master.investigator_study_role.role should exist');
SELECT col_type_is(      'drh_stateful_master', 'investigator_study_role', 'role', 'text', 'Column drh_stateful_master.investigator_study_role.role should be type text');
SELECT col_not_null(     'drh_stateful_master', 'investigator_study_role', 'role', 'Column drh_stateful_master.investigator_study_role.role should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'investigator_study_role', 'role', 'Column drh_stateful_master.investigator_study_role.role should not have a default');

SELECT * FROM finish();
ROLLBACK;
