SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(50);

SELECT has_table(
    'drh_stateful_master', 'research_study_focus',
    'Should have table drh_stateful_master.research_study_focus'
);

SELECT has_pk(
    'drh_stateful_master', 'research_study_focus',
    'Table drh_stateful_master.research_study_focus should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'research_study_focus'::name, ARRAY[
    'id'::name,
    'coding_system'::name,
    'code'::name,
    'display'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'id', 'Column drh_stateful_master.research_study_focus.id should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'id', 'integer', 'Column drh_stateful_master.research_study_focus.id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'research_study_focus', 'id', 'Column drh_stateful_master.research_study_focus.id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'research_study_focus', 'id', 'Column drh_stateful_master.research_study_focus.id should have a default');
SELECT col_default_is(   'drh_stateful_master', 'research_study_focus', 'id', 'nextval(''drh_stateful_master.research_study_focus_id_seq''::regclass)', 'Column drh_stateful_master.research_study_focus.id default is');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'coding_system', 'Column drh_stateful_master.research_study_focus.coding_system should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'coding_system', 'character varying(255)', 'Column drh_stateful_master.research_study_focus.coding_system should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'research_study_focus', 'coding_system', 'Column drh_stateful_master.research_study_focus.coding_system should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'coding_system', 'Column drh_stateful_master.research_study_focus.coding_system should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'code', 'Column drh_stateful_master.research_study_focus.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'code', 'text', 'Column drh_stateful_master.research_study_focus.code should be type text');
SELECT col_not_null(     'drh_stateful_master', 'research_study_focus', 'code', 'Column drh_stateful_master.research_study_focus.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'code', 'Column drh_stateful_master.research_study_focus.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'display', 'Column drh_stateful_master.research_study_focus.display should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'display', 'character varying(255)', 'Column drh_stateful_master.research_study_focus.display should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'research_study_focus', 'display', 'Column drh_stateful_master.research_study_focus.display should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'display', 'Column drh_stateful_master.research_study_focus.display should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'rec_status_id', 'Column drh_stateful_master.research_study_focus.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'rec_status_id', 'integer', 'Column drh_stateful_master.research_study_focus.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'research_study_focus', 'rec_status_id', 'Column drh_stateful_master.research_study_focus.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'rec_status_id', 'Column drh_stateful_master.research_study_focus.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'created_at', 'Column drh_stateful_master.research_study_focus.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.research_study_focus.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'research_study_focus', 'created_at', 'Column drh_stateful_master.research_study_focus.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'research_study_focus', 'created_at', 'Column drh_stateful_master.research_study_focus.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'research_study_focus', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.research_study_focus.created_at default is');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'created_by', 'Column drh_stateful_master.research_study_focus.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'created_by', 'text', 'Column drh_stateful_master.research_study_focus.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'research_study_focus', 'created_by', 'Column drh_stateful_master.research_study_focus.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'research_study_focus', 'created_by', 'Column drh_stateful_master.research_study_focus.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'research_study_focus', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.research_study_focus.created_by default is');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'updated_at', 'Column drh_stateful_master.research_study_focus.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.research_study_focus.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'research_study_focus', 'updated_at', 'Column drh_stateful_master.research_study_focus.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'updated_at', 'Column drh_stateful_master.research_study_focus.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'updated_by', 'Column drh_stateful_master.research_study_focus.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'updated_by', 'text', 'Column drh_stateful_master.research_study_focus.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'research_study_focus', 'updated_by', 'Column drh_stateful_master.research_study_focus.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'updated_by', 'Column drh_stateful_master.research_study_focus.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'deleted_at', 'Column drh_stateful_master.research_study_focus.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.research_study_focus.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'research_study_focus', 'deleted_at', 'Column drh_stateful_master.research_study_focus.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'deleted_at', 'Column drh_stateful_master.research_study_focus.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_focus', 'deleted_by', 'Column drh_stateful_master.research_study_focus.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_focus', 'deleted_by', 'text', 'Column drh_stateful_master.research_study_focus.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'research_study_focus', 'deleted_by', 'Column drh_stateful_master.research_study_focus.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_focus', 'deleted_by', 'Column drh_stateful_master.research_study_focus.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
