SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(61);

SELECT has_table(
    'drh_stateful_authentication', 'group',
    'Should have table drh_stateful_authentication.group'
);

SELECT has_pk(
    'drh_stateful_authentication', 'group',
    'Table drh_stateful_authentication.group should have a primary key'
);

SELECT columns_are('drh_stateful_authentication'::name, 'group'::name, ARRAY[
    'group_id'::name,
    'grp_identifier'::name,
    'title'::name,
    'status_type_id'::name,
    'description'::name,
    'purpose'::name,
    'group_type_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_authentication', 'group', 'group_id', 'Column drh_stateful_authentication."group".group_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'group_id', 'text', 'Column drh_stateful_authentication."group".group_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'group', 'group_id', 'Column drh_stateful_authentication."group".group_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'group_id', 'Column drh_stateful_authentication."group".group_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'grp_identifier', 'Column drh_stateful_authentication."group".grp_identifier should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'grp_identifier', 'character varying(255)', 'Column drh_stateful_authentication."group".grp_identifier should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_authentication', 'group', 'grp_identifier', 'Column drh_stateful_authentication."group".grp_identifier should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'grp_identifier', 'Column drh_stateful_authentication."group".grp_identifier should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'title', 'Column drh_stateful_authentication."group".title should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'title', 'character varying(255)', 'Column drh_stateful_authentication."group".title should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_authentication', 'group', 'title', 'Column drh_stateful_authentication."group".title should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'title', 'Column drh_stateful_authentication."group".title should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'status_type_id', 'Column drh_stateful_authentication."group".status_type_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'status_type_id', 'integer', 'Column drh_stateful_authentication."group".status_type_id should be type integer');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'status_type_id', 'Column drh_stateful_authentication."group".status_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'status_type_id', 'Column drh_stateful_authentication."group".status_type_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'description', 'Column drh_stateful_authentication."group".description should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'description', 'text', 'Column drh_stateful_authentication."group".description should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'description', 'Column drh_stateful_authentication."group".description should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'description', 'Column drh_stateful_authentication."group".description should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'purpose', 'Column drh_stateful_authentication."group".purpose should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'purpose', 'text', 'Column drh_stateful_authentication."group".purpose should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'purpose', 'Column drh_stateful_authentication."group".purpose should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'purpose', 'Column drh_stateful_authentication."group".purpose should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'group_type_id', 'Column drh_stateful_authentication."group".group_type_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'group_type_id', 'text', 'Column drh_stateful_authentication."group".group_type_id should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'group_type_id', 'Column drh_stateful_authentication."group".group_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'group_type_id', 'Column drh_stateful_authentication."group".group_type_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'rec_status_id', 'Column drh_stateful_authentication."group".rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'rec_status_id', 'integer', 'Column drh_stateful_authentication."group".rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_authentication', 'group', 'rec_status_id', 'Column drh_stateful_authentication."group".rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'rec_status_id', 'Column drh_stateful_authentication."group".rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'created_at', 'Column drh_stateful_authentication."group".created_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'created_at', 'timestamp with time zone', 'Column drh_stateful_authentication."group".created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'created_at', 'Column drh_stateful_authentication."group".created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'group', 'created_at', 'Column drh_stateful_authentication."group".created_at should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'group', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_authentication."group".created_at default is');

SELECT has_column(       'drh_stateful_authentication', 'group', 'created_by', 'Column drh_stateful_authentication."group".created_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'created_by', 'text', 'Column drh_stateful_authentication."group".created_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'created_by', 'Column drh_stateful_authentication."group".created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'group', 'created_by', 'Column drh_stateful_authentication."group".created_by should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'group', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_authentication."group".created_by default is');

SELECT has_column(       'drh_stateful_authentication', 'group', 'updated_at', 'Column drh_stateful_authentication."group".updated_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_authentication."group".updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'updated_at', 'Column drh_stateful_authentication."group".updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'updated_at', 'Column drh_stateful_authentication."group".updated_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'updated_by', 'Column drh_stateful_authentication."group".updated_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'updated_by', 'text', 'Column drh_stateful_authentication."group".updated_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'updated_by', 'Column drh_stateful_authentication."group".updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'updated_by', 'Column drh_stateful_authentication."group".updated_by should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'deleted_at', 'Column drh_stateful_authentication."group".deleted_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_authentication."group".deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'deleted_at', 'Column drh_stateful_authentication."group".deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'deleted_at', 'Column drh_stateful_authentication."group".deleted_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group', 'deleted_by', 'Column drh_stateful_authentication."group".deleted_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group', 'deleted_by', 'text', 'Column drh_stateful_authentication."group".deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group', 'deleted_by', 'Column drh_stateful_authentication."group".deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group', 'deleted_by', 'Column drh_stateful_authentication."group".deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
