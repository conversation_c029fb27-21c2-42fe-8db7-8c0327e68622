SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(46);

SELECT has_table(
    'drh_stateful_master', 'study_visibility',
    'Should have table drh_stateful_master.study_visibility'
);

SELECT has_pk(
    'drh_stateful_master', 'study_visibility',
    'Table drh_stateful_master.study_visibility should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'study_visibility'::name, ARRAY[
    'visibility_id'::name,
    'visibility_name'::name,
    'visibility_description'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'visibility_id', 'Column drh_stateful_master.study_visibility.visibility_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'visibility_id', 'integer', 'Column drh_stateful_master.study_visibility.visibility_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'study_visibility', 'visibility_id', 'Column drh_stateful_master.study_visibility.visibility_id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'study_visibility', 'visibility_id', 'Column drh_stateful_master.study_visibility.visibility_id should have a default');
SELECT col_default_is(   'drh_stateful_master', 'study_visibility', 'visibility_id', 'nextval(''drh_stateful_master.study_visibility_visibility_id_seq''::regclass)', 'Column drh_stateful_master.study_visibility.visibility_id default is');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'visibility_name', 'Column drh_stateful_master.study_visibility.visibility_name should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'visibility_name', 'text', 'Column drh_stateful_master.study_visibility.visibility_name should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_visibility', 'visibility_name', 'Column drh_stateful_master.study_visibility.visibility_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_visibility', 'visibility_name', 'Column drh_stateful_master.study_visibility.visibility_name should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'visibility_description', 'Column drh_stateful_master.study_visibility.visibility_description should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'visibility_description', 'text', 'Column drh_stateful_master.study_visibility.visibility_description should be type text');
SELECT col_not_null(     'drh_stateful_master', 'study_visibility', 'visibility_description', 'Column drh_stateful_master.study_visibility.visibility_description should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_visibility', 'visibility_description', 'Column drh_stateful_master.study_visibility.visibility_description should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'rec_status_id', 'Column drh_stateful_master.study_visibility.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'rec_status_id', 'integer', 'Column drh_stateful_master.study_visibility.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'study_visibility', 'rec_status_id', 'Column drh_stateful_master.study_visibility.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_visibility', 'rec_status_id', 'Column drh_stateful_master.study_visibility.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'created_at', 'Column drh_stateful_master.study_visibility.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.study_visibility.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'study_visibility', 'created_at', 'Column drh_stateful_master.study_visibility.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'study_visibility', 'created_at', 'Column drh_stateful_master.study_visibility.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'study_visibility', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.study_visibility.created_at default is');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'created_by', 'Column drh_stateful_master.study_visibility.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'created_by', 'text', 'Column drh_stateful_master.study_visibility.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_visibility', 'created_by', 'Column drh_stateful_master.study_visibility.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'study_visibility', 'created_by', 'Column drh_stateful_master.study_visibility.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'study_visibility', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.study_visibility.created_by default is');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'updated_at', 'Column drh_stateful_master.study_visibility.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.study_visibility.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'study_visibility', 'updated_at', 'Column drh_stateful_master.study_visibility.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_visibility', 'updated_at', 'Column drh_stateful_master.study_visibility.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'updated_by', 'Column drh_stateful_master.study_visibility.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'updated_by', 'text', 'Column drh_stateful_master.study_visibility.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_visibility', 'updated_by', 'Column drh_stateful_master.study_visibility.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_visibility', 'updated_by', 'Column drh_stateful_master.study_visibility.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'deleted_at', 'Column drh_stateful_master.study_visibility.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.study_visibility.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'study_visibility', 'deleted_at', 'Column drh_stateful_master.study_visibility.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_visibility', 'deleted_at', 'Column drh_stateful_master.study_visibility.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'study_visibility', 'deleted_by', 'Column drh_stateful_master.study_visibility.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'study_visibility', 'deleted_by', 'text', 'Column drh_stateful_master.study_visibility.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'study_visibility', 'deleted_by', 'Column drh_stateful_master.study_visibility.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'study_visibility', 'deleted_by', 'Column drh_stateful_master.study_visibility.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
