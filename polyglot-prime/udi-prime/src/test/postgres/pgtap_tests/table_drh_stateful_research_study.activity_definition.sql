SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(89);

SELECT has_table(
    'drh_stateful_research_study', 'activity_definition',
    'Should have table drh_stateful_research_study.activity_definition'
);

SELECT has_pk(
    'drh_stateful_research_study', 'activity_definition',
    'Table drh_stateful_research_study.activity_definition should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'activity_definition'::name, ARRAY[
    'id'::name,
    'plan_definition_id'::name,
    'identifier'::name,
    'status_id'::name,
    'name'::name,
    'description'::name,
    'timing'::name,
    'location'::name,
    'participant'::name,
    'type'::name,
    'reason_code'::name,
    'goal_id'::name,
    'output'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'id', 'Column drh_stateful_research_study.activity_definition.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'id', 'text', 'Column drh_stateful_research_study.activity_definition.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'id', 'Column drh_stateful_research_study.activity_definition.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'id', 'Column drh_stateful_research_study.activity_definition.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'plan_definition_id', 'Column drh_stateful_research_study.activity_definition.plan_definition_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'plan_definition_id', 'text', 'Column drh_stateful_research_study.activity_definition.plan_definition_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'plan_definition_id', 'Column drh_stateful_research_study.activity_definition.plan_definition_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'plan_definition_id', 'Column drh_stateful_research_study.activity_definition.plan_definition_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'identifier', 'Column drh_stateful_research_study.activity_definition.identifier should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'identifier', 'character varying(255)', 'Column drh_stateful_research_study.activity_definition.identifier should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'identifier', 'Column drh_stateful_research_study.activity_definition.identifier should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'identifier', 'Column drh_stateful_research_study.activity_definition.identifier should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'status_id', 'Column drh_stateful_research_study.activity_definition.status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'status_id', 'character varying(50)', 'Column drh_stateful_research_study.activity_definition.status_id should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'status_id', 'Column drh_stateful_research_study.activity_definition.status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'status_id', 'Column drh_stateful_research_study.activity_definition.status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'name', 'Column drh_stateful_research_study.activity_definition.name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'name', 'character varying(255)', 'Column drh_stateful_research_study.activity_definition.name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'name', 'Column drh_stateful_research_study.activity_definition.name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'name', 'Column drh_stateful_research_study.activity_definition.name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'description', 'Column drh_stateful_research_study.activity_definition.description should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'description', 'character varying(255)', 'Column drh_stateful_research_study.activity_definition.description should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'description', 'Column drh_stateful_research_study.activity_definition.description should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'description', 'Column drh_stateful_research_study.activity_definition.description should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'timing', 'Column drh_stateful_research_study.activity_definition.timing should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'timing', 'character varying(255)', 'Column drh_stateful_research_study.activity_definition.timing should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'timing', 'Column drh_stateful_research_study.activity_definition.timing should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'timing', 'Column drh_stateful_research_study.activity_definition.timing should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'location', 'Column drh_stateful_research_study.activity_definition.location should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'location', 'text', 'Column drh_stateful_research_study.activity_definition.location should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'location', 'Column drh_stateful_research_study.activity_definition.location should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'location', 'Column drh_stateful_research_study.activity_definition.location should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'participant', 'Column drh_stateful_research_study.activity_definition.participant should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'participant', 'text', 'Column drh_stateful_research_study.activity_definition.participant should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'participant', 'Column drh_stateful_research_study.activity_definition.participant should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'participant', 'Column drh_stateful_research_study.activity_definition.participant should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'type', 'Column drh_stateful_research_study.activity_definition.type should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'type', 'character varying(100)', 'Column drh_stateful_research_study.activity_definition.type should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'type', 'Column drh_stateful_research_study.activity_definition.type should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'type', 'Column drh_stateful_research_study.activity_definition.type should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'reason_code', 'Column drh_stateful_research_study.activity_definition.reason_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'reason_code', 'character varying(255)', 'Column drh_stateful_research_study.activity_definition.reason_code should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'reason_code', 'Column drh_stateful_research_study.activity_definition.reason_code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'reason_code', 'Column drh_stateful_research_study.activity_definition.reason_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'goal_id', 'Column drh_stateful_research_study.activity_definition.goal_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'goal_id', 'text', 'Column drh_stateful_research_study.activity_definition.goal_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'activity_definition', 'goal_id', 'Column drh_stateful_research_study.activity_definition.goal_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'goal_id', 'Column drh_stateful_research_study.activity_definition.goal_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'output', 'Column drh_stateful_research_study.activity_definition.output should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'output', 'character varying(255)', 'Column drh_stateful_research_study.activity_definition.output should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'output', 'Column drh_stateful_research_study.activity_definition.output should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'output', 'Column drh_stateful_research_study.activity_definition.output should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'tenant_id', 'Column drh_stateful_research_study.activity_definition.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'tenant_id', 'text', 'Column drh_stateful_research_study.activity_definition.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'tenant_id', 'Column drh_stateful_research_study.activity_definition.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'tenant_id', 'Column drh_stateful_research_study.activity_definition.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'rec_status_id', 'Column drh_stateful_research_study.activity_definition.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.activity_definition.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'activity_definition', 'rec_status_id', 'Column drh_stateful_research_study.activity_definition.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'rec_status_id', 'Column drh_stateful_research_study.activity_definition.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'created_at', 'Column drh_stateful_research_study.activity_definition.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.activity_definition.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'activity_definition', 'created_at', 'Column drh_stateful_research_study.activity_definition.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'activity_definition', 'created_at', 'Column drh_stateful_research_study.activity_definition.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'activity_definition', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.activity_definition.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'created_by', 'Column drh_stateful_research_study.activity_definition.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'created_by', 'text', 'Column drh_stateful_research_study.activity_definition.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'activity_definition', 'created_by', 'Column drh_stateful_research_study.activity_definition.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'activity_definition', 'created_by', 'Column drh_stateful_research_study.activity_definition.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'activity_definition', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.activity_definition.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'updated_at', 'Column drh_stateful_research_study.activity_definition.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.activity_definition.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'activity_definition', 'updated_at', 'Column drh_stateful_research_study.activity_definition.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'updated_at', 'Column drh_stateful_research_study.activity_definition.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'updated_by', 'Column drh_stateful_research_study.activity_definition.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'updated_by', 'text', 'Column drh_stateful_research_study.activity_definition.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'activity_definition', 'updated_by', 'Column drh_stateful_research_study.activity_definition.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'updated_by', 'Column drh_stateful_research_study.activity_definition.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'deleted_at', 'Column drh_stateful_research_study.activity_definition.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.activity_definition.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'activity_definition', 'deleted_at', 'Column drh_stateful_research_study.activity_definition.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'deleted_at', 'Column drh_stateful_research_study.activity_definition.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'activity_definition', 'deleted_by', 'Column drh_stateful_research_study.activity_definition.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'activity_definition', 'deleted_by', 'text', 'Column drh_stateful_research_study.activity_definition.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'activity_definition', 'deleted_by', 'Column drh_stateful_research_study.activity_definition.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'activity_definition', 'deleted_by', 'Column drh_stateful_research_study.activity_definition.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
