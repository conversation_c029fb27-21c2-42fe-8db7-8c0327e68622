SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(101);

SELECT has_table(
    'drh_stateful_research_study', 'organization',
    'Should have table drh_stateful_research_study.organization'
);

SELECT has_pk(
    'drh_stateful_research_study', 'organization',
    'Table drh_stateful_research_study.organization should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'organization'::name, ARRAY[
    'id'::name,
    'party_id'::name,
    'identifier_system_value'::name,
    'name'::name,
    'alias'::name,
    'type_code'::name,
    'type_display'::name,
    'address_text'::name,
    'address_line'::name,
    'city'::name,
    'state'::name,
    'postal_code'::name,
    'country'::name,
    'phone'::name,
    'email'::name,
    'website_url'::name,
    'parent_organization_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'organization', 'id', 'Column drh_stateful_research_study.organization.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'id', 'text', 'Column drh_stateful_research_study.organization.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'organization', 'id', 'Column drh_stateful_research_study.organization.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'id', 'Column drh_stateful_research_study.organization.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'party_id', 'Column drh_stateful_research_study.organization.party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'party_id', 'text', 'Column drh_stateful_research_study.organization.party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'organization', 'party_id', 'Column drh_stateful_research_study.organization.party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'party_id', 'Column drh_stateful_research_study.organization.party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'identifier_system_value', 'Column drh_stateful_research_study.organization.identifier_system_value should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'identifier_system_value', 'jsonb', 'Column drh_stateful_research_study.organization.identifier_system_value should be type jsonb');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'identifier_system_value', 'Column drh_stateful_research_study.organization.identifier_system_value should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'identifier_system_value', 'Column drh_stateful_research_study.organization.identifier_system_value should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'name', 'Column drh_stateful_research_study.organization.name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'name', 'character varying(255)', 'Column drh_stateful_research_study.organization.name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'organization', 'name', 'Column drh_stateful_research_study.organization.name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'name', 'Column drh_stateful_research_study.organization.name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'alias', 'Column drh_stateful_research_study.organization.alias should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'alias', 'character varying(255)', 'Column drh_stateful_research_study.organization.alias should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'alias', 'Column drh_stateful_research_study.organization.alias should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'alias', 'Column drh_stateful_research_study.organization.alias should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'type_code', 'Column drh_stateful_research_study.organization.type_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'type_code', 'character varying(100)', 'Column drh_stateful_research_study.organization.type_code should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'type_code', 'Column drh_stateful_research_study.organization.type_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'type_code', 'Column drh_stateful_research_study.organization.type_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'type_display', 'Column drh_stateful_research_study.organization.type_display should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'type_display', 'character varying(255)', 'Column drh_stateful_research_study.organization.type_display should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'type_display', 'Column drh_stateful_research_study.organization.type_display should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'type_display', 'Column drh_stateful_research_study.organization.type_display should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'address_text', 'Column drh_stateful_research_study.organization.address_text should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'address_text', 'character varying(255)', 'Column drh_stateful_research_study.organization.address_text should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'address_text', 'Column drh_stateful_research_study.organization.address_text should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'address_text', 'Column drh_stateful_research_study.organization.address_text should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'address_line', 'Column drh_stateful_research_study.organization.address_line should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'address_line', 'character varying(255)', 'Column drh_stateful_research_study.organization.address_line should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'address_line', 'Column drh_stateful_research_study.organization.address_line should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'address_line', 'Column drh_stateful_research_study.organization.address_line should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'city', 'Column drh_stateful_research_study.organization.city should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'city', 'character varying(100)', 'Column drh_stateful_research_study.organization.city should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'city', 'Column drh_stateful_research_study.organization.city should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'city', 'Column drh_stateful_research_study.organization.city should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'state', 'Column drh_stateful_research_study.organization.state should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'state', 'character varying(100)', 'Column drh_stateful_research_study.organization.state should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'state', 'Column drh_stateful_research_study.organization.state should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'state', 'Column drh_stateful_research_study.organization.state should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'postal_code', 'Column drh_stateful_research_study.organization.postal_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'postal_code', 'character varying(20)', 'Column drh_stateful_research_study.organization.postal_code should be type character varying(20)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'postal_code', 'Column drh_stateful_research_study.organization.postal_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'postal_code', 'Column drh_stateful_research_study.organization.postal_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'country', 'Column drh_stateful_research_study.organization.country should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'country', 'character varying(100)', 'Column drh_stateful_research_study.organization.country should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'country', 'Column drh_stateful_research_study.organization.country should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'country', 'Column drh_stateful_research_study.organization.country should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'phone', 'Column drh_stateful_research_study.organization.phone should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'phone', 'character varying(20)', 'Column drh_stateful_research_study.organization.phone should be type character varying(20)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'phone', 'Column drh_stateful_research_study.organization.phone should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'phone', 'Column drh_stateful_research_study.organization.phone should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'email', 'Column drh_stateful_research_study.organization.email should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'email', 'character varying(255)', 'Column drh_stateful_research_study.organization.email should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'email', 'Column drh_stateful_research_study.organization.email should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'email', 'Column drh_stateful_research_study.organization.email should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'website_url', 'Column drh_stateful_research_study.organization.website_url should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'website_url', 'text', 'Column drh_stateful_research_study.organization.website_url should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'website_url', 'Column drh_stateful_research_study.organization.website_url should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'website_url', 'Column drh_stateful_research_study.organization.website_url should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'parent_organization_id', 'Column drh_stateful_research_study.organization.parent_organization_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'parent_organization_id', 'text', 'Column drh_stateful_research_study.organization.parent_organization_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'parent_organization_id', 'Column drh_stateful_research_study.organization.parent_organization_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'parent_organization_id', 'Column drh_stateful_research_study.organization.parent_organization_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'rec_status_id', 'Column drh_stateful_research_study.organization.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.organization.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'organization', 'rec_status_id', 'Column drh_stateful_research_study.organization.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'rec_status_id', 'Column drh_stateful_research_study.organization.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'created_at', 'Column drh_stateful_research_study.organization.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.organization.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'created_at', 'Column drh_stateful_research_study.organization.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'organization', 'created_at', 'Column drh_stateful_research_study.organization.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'organization', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.organization.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'created_by', 'Column drh_stateful_research_study.organization.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'created_by', 'text', 'Column drh_stateful_research_study.organization.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'created_by', 'Column drh_stateful_research_study.organization.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'organization', 'created_by', 'Column drh_stateful_research_study.organization.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'organization', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.organization.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'updated_at', 'Column drh_stateful_research_study.organization.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.organization.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'updated_at', 'Column drh_stateful_research_study.organization.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'updated_at', 'Column drh_stateful_research_study.organization.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'updated_by', 'Column drh_stateful_research_study.organization.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'updated_by', 'text', 'Column drh_stateful_research_study.organization.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'updated_by', 'Column drh_stateful_research_study.organization.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'updated_by', 'Column drh_stateful_research_study.organization.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'deleted_at', 'Column drh_stateful_research_study.organization.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.organization.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'deleted_at', 'Column drh_stateful_research_study.organization.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'deleted_at', 'Column drh_stateful_research_study.organization.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'organization', 'deleted_by', 'Column drh_stateful_research_study.organization.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'organization', 'deleted_by', 'text', 'Column drh_stateful_research_study.organization.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'organization', 'deleted_by', 'Column drh_stateful_research_study.organization.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'organization', 'deleted_by', 'Column drh_stateful_research_study.organization.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
