SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(65);

SELECT has_table(
    'drh_stateful_research_study', 'subject_observation',
    'Should have table drh_stateful_research_study.subject_observation'
);

SELECT has_pk(
    'drh_stateful_research_study', 'subject_observation',
    'Table drh_stateful_research_study.subject_observation should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'subject_observation'::name, ARRAY[
    'id'::name,
    'research_subject_id'::name,
    'code'::name,
    'category'::name,
    'value'::name,
    'unit'::name,
    'effective_datetime'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'id', 'Column drh_stateful_research_study.subject_observation.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'id', 'text', 'Column drh_stateful_research_study.subject_observation.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'subject_observation', 'id', 'Column drh_stateful_research_study.subject_observation.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'id', 'Column drh_stateful_research_study.subject_observation.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'research_subject_id', 'Column drh_stateful_research_study.subject_observation.research_subject_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'research_subject_id', 'text', 'Column drh_stateful_research_study.subject_observation.research_subject_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'subject_observation', 'research_subject_id', 'Column drh_stateful_research_study.subject_observation.research_subject_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'research_subject_id', 'Column drh_stateful_research_study.subject_observation.research_subject_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'code', 'Column drh_stateful_research_study.subject_observation.code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'code', 'character varying(50)', 'Column drh_stateful_research_study.subject_observation.code should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'subject_observation', 'code', 'Column drh_stateful_research_study.subject_observation.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'code', 'Column drh_stateful_research_study.subject_observation.code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'category', 'Column drh_stateful_research_study.subject_observation.category should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'category', 'character varying(50)', 'Column drh_stateful_research_study.subject_observation.category should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'subject_observation', 'category', 'Column drh_stateful_research_study.subject_observation.category should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'category', 'Column drh_stateful_research_study.subject_observation.category should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'value', 'Column drh_stateful_research_study.subject_observation.value should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'value', 'double precision', 'Column drh_stateful_research_study.subject_observation.value should be type double precision');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'value', 'Column drh_stateful_research_study.subject_observation.value should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'value', 'Column drh_stateful_research_study.subject_observation.value should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'unit', 'Column drh_stateful_research_study.subject_observation.unit should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'unit', 'character varying(50)', 'Column drh_stateful_research_study.subject_observation.unit should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'subject_observation', 'unit', 'Column drh_stateful_research_study.subject_observation.unit should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'unit', 'Column drh_stateful_research_study.subject_observation.unit should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'effective_datetime', 'Column drh_stateful_research_study.subject_observation.effective_datetime should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'effective_datetime', 'timestamp with time zone', 'Column drh_stateful_research_study.subject_observation.effective_datetime should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'effective_datetime', 'Column drh_stateful_research_study.subject_observation.effective_datetime should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'effective_datetime', 'Column drh_stateful_research_study.subject_observation.effective_datetime should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'tenant_id', 'Column drh_stateful_research_study.subject_observation.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'tenant_id', 'text', 'Column drh_stateful_research_study.subject_observation.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'subject_observation', 'tenant_id', 'Column drh_stateful_research_study.subject_observation.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'tenant_id', 'Column drh_stateful_research_study.subject_observation.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'rec_status_id', 'Column drh_stateful_research_study.subject_observation.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.subject_observation.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'subject_observation', 'rec_status_id', 'Column drh_stateful_research_study.subject_observation.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'rec_status_id', 'Column drh_stateful_research_study.subject_observation.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'created_at', 'Column drh_stateful_research_study.subject_observation.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.subject_observation.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'created_at', 'Column drh_stateful_research_study.subject_observation.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'subject_observation', 'created_at', 'Column drh_stateful_research_study.subject_observation.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'subject_observation', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.subject_observation.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'created_by', 'Column drh_stateful_research_study.subject_observation.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'created_by', 'text', 'Column drh_stateful_research_study.subject_observation.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'created_by', 'Column drh_stateful_research_study.subject_observation.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'subject_observation', 'created_by', 'Column drh_stateful_research_study.subject_observation.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'subject_observation', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.subject_observation.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'updated_at', 'Column drh_stateful_research_study.subject_observation.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.subject_observation.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'updated_at', 'Column drh_stateful_research_study.subject_observation.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'updated_at', 'Column drh_stateful_research_study.subject_observation.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'updated_by', 'Column drh_stateful_research_study.subject_observation.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'updated_by', 'text', 'Column drh_stateful_research_study.subject_observation.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'updated_by', 'Column drh_stateful_research_study.subject_observation.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'updated_by', 'Column drh_stateful_research_study.subject_observation.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'deleted_at', 'Column drh_stateful_research_study.subject_observation.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.subject_observation.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'deleted_at', 'Column drh_stateful_research_study.subject_observation.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'deleted_at', 'Column drh_stateful_research_study.subject_observation.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'subject_observation', 'deleted_by', 'Column drh_stateful_research_study.subject_observation.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'subject_observation', 'deleted_by', 'text', 'Column drh_stateful_research_study.subject_observation.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'subject_observation', 'deleted_by', 'Column drh_stateful_research_study.subject_observation.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'subject_observation', 'deleted_by', 'Column drh_stateful_research_study.subject_observation.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
