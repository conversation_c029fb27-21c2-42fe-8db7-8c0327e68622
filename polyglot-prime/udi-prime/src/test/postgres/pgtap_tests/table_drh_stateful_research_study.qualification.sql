SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(61);

SELECT has_table(
    'drh_stateful_research_study', 'qualification',
    'Should have table drh_stateful_research_study.qualification'
);

SELECT has_pk(
    'drh_stateful_research_study', 'qualification',
    'Table drh_stateful_research_study.qualification should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'qualification'::name, ARRAY[
    'id'::name,
    'org_party_id'::name,
    'qualification_code'::name,
    'issuer_name'::name,
    'start_date'::name,
    'end_date'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'id', 'Column drh_stateful_research_study.qualification.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'id', 'text', 'Column drh_stateful_research_study.qualification.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'qualification', 'id', 'Column drh_stateful_research_study.qualification.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'id', 'Column drh_stateful_research_study.qualification.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'org_party_id', 'Column drh_stateful_research_study.qualification.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'org_party_id', 'text', 'Column drh_stateful_research_study.qualification.org_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'qualification', 'org_party_id', 'Column drh_stateful_research_study.qualification.org_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'org_party_id', 'Column drh_stateful_research_study.qualification.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'qualification_code', 'Column drh_stateful_research_study.qualification.qualification_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'qualification_code', 'character varying(50)', 'Column drh_stateful_research_study.qualification.qualification_code should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'qualification_code', 'Column drh_stateful_research_study.qualification.qualification_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'qualification_code', 'Column drh_stateful_research_study.qualification.qualification_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'issuer_name', 'Column drh_stateful_research_study.qualification.issuer_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'issuer_name', 'character varying(255)', 'Column drh_stateful_research_study.qualification.issuer_name should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'issuer_name', 'Column drh_stateful_research_study.qualification.issuer_name should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'issuer_name', 'Column drh_stateful_research_study.qualification.issuer_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'start_date', 'Column drh_stateful_research_study.qualification.start_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'start_date', 'date', 'Column drh_stateful_research_study.qualification.start_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'start_date', 'Column drh_stateful_research_study.qualification.start_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'start_date', 'Column drh_stateful_research_study.qualification.start_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'end_date', 'Column drh_stateful_research_study.qualification.end_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'end_date', 'date', 'Column drh_stateful_research_study.qualification.end_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'end_date', 'Column drh_stateful_research_study.qualification.end_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'end_date', 'Column drh_stateful_research_study.qualification.end_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'tenant_id', 'Column drh_stateful_research_study.qualification.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'tenant_id', 'text', 'Column drh_stateful_research_study.qualification.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'qualification', 'tenant_id', 'Column drh_stateful_research_study.qualification.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'tenant_id', 'Column drh_stateful_research_study.qualification.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'rec_status_id', 'Column drh_stateful_research_study.qualification.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.qualification.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'qualification', 'rec_status_id', 'Column drh_stateful_research_study.qualification.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'rec_status_id', 'Column drh_stateful_research_study.qualification.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'created_at', 'Column drh_stateful_research_study.qualification.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.qualification.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'created_at', 'Column drh_stateful_research_study.qualification.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'qualification', 'created_at', 'Column drh_stateful_research_study.qualification.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'qualification', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.qualification.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'created_by', 'Column drh_stateful_research_study.qualification.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'created_by', 'text', 'Column drh_stateful_research_study.qualification.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'created_by', 'Column drh_stateful_research_study.qualification.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'qualification', 'created_by', 'Column drh_stateful_research_study.qualification.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'qualification', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.qualification.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'updated_at', 'Column drh_stateful_research_study.qualification.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.qualification.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'updated_at', 'Column drh_stateful_research_study.qualification.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'updated_at', 'Column drh_stateful_research_study.qualification.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'updated_by', 'Column drh_stateful_research_study.qualification.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'updated_by', 'text', 'Column drh_stateful_research_study.qualification.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'updated_by', 'Column drh_stateful_research_study.qualification.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'updated_by', 'Column drh_stateful_research_study.qualification.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'deleted_at', 'Column drh_stateful_research_study.qualification.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.qualification.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'deleted_at', 'Column drh_stateful_research_study.qualification.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'deleted_at', 'Column drh_stateful_research_study.qualification.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'qualification', 'deleted_by', 'Column drh_stateful_research_study.qualification.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'qualification', 'deleted_by', 'text', 'Column drh_stateful_research_study.qualification.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'qualification', 'deleted_by', 'Column drh_stateful_research_study.qualification.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'qualification', 'deleted_by', 'Column drh_stateful_research_study.qualification.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
