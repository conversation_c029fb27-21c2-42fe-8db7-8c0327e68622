SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(49);

SELECT has_table(
    'drh_stateful_master', 'contact_point_system',
    'Should have table drh_stateful_master.contact_point_system'
);

SELECT has_pk(
    'drh_stateful_master', 'contact_point_system',
    'Table drh_stateful_master.contact_point_system should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'contact_point_system'::name, ARRAY[
    'id'::name,
    'code'::name,
    'system'::name,
    'value'::name,
    'description'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'id', 'Column drh_stateful_master.contact_point_system.id should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'id', 'text', 'Column drh_stateful_master.contact_point_system.id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'contact_point_system', 'id', 'Column drh_stateful_master.contact_point_system.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'id', 'Column drh_stateful_master.contact_point_system.id should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'code', 'Column drh_stateful_master.contact_point_system.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'code', 'character varying(50)', 'Column drh_stateful_master.contact_point_system.code should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'contact_point_system', 'code', 'Column drh_stateful_master.contact_point_system.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'code', 'Column drh_stateful_master.contact_point_system.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'system', 'Column drh_stateful_master.contact_point_system.system should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'system', 'character varying(100)', 'Column drh_stateful_master.contact_point_system.system should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'system', 'Column drh_stateful_master.contact_point_system.system should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'system', 'Column drh_stateful_master.contact_point_system.system should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'value', 'Column drh_stateful_master.contact_point_system.value should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'value', 'character varying(100)', 'Column drh_stateful_master.contact_point_system.value should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_master', 'contact_point_system', 'value', 'Column drh_stateful_master.contact_point_system.value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'value', 'Column drh_stateful_master.contact_point_system.value should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'description', 'Column drh_stateful_master.contact_point_system.description should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'description', 'text', 'Column drh_stateful_master.contact_point_system.description should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'description', 'Column drh_stateful_master.contact_point_system.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'description', 'Column drh_stateful_master.contact_point_system.description should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'created_at', 'Column drh_stateful_master.contact_point_system.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.contact_point_system.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'created_at', 'Column drh_stateful_master.contact_point_system.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contact_point_system', 'created_at', 'Column drh_stateful_master.contact_point_system.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contact_point_system', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.contact_point_system.created_at default is');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'created_by', 'Column drh_stateful_master.contact_point_system.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'created_by', 'text', 'Column drh_stateful_master.contact_point_system.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'created_by', 'Column drh_stateful_master.contact_point_system.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'contact_point_system', 'created_by', 'Column drh_stateful_master.contact_point_system.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'contact_point_system', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.contact_point_system.created_by default is');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'updated_at', 'Column drh_stateful_master.contact_point_system.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.contact_point_system.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'updated_at', 'Column drh_stateful_master.contact_point_system.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'updated_at', 'Column drh_stateful_master.contact_point_system.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'updated_by', 'Column drh_stateful_master.contact_point_system.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'updated_by', 'text', 'Column drh_stateful_master.contact_point_system.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'updated_by', 'Column drh_stateful_master.contact_point_system.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'updated_by', 'Column drh_stateful_master.contact_point_system.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'deleted_at', 'Column drh_stateful_master.contact_point_system.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.contact_point_system.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'deleted_at', 'Column drh_stateful_master.contact_point_system.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'deleted_at', 'Column drh_stateful_master.contact_point_system.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'contact_point_system', 'deleted_by', 'Column drh_stateful_master.contact_point_system.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'contact_point_system', 'deleted_by', 'text', 'Column drh_stateful_master.contact_point_system.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'contact_point_system', 'deleted_by', 'Column drh_stateful_master.contact_point_system.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'contact_point_system', 'deleted_by', 'Column drh_stateful_master.contact_point_system.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
