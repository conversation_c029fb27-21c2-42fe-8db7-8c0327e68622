SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(101);

SELECT has_table(
    'drh_stateful_research_study', 'location',
    'Should have table drh_stateful_research_study.location'
);

SELECT has_pk(
    'drh_stateful_research_study', 'location',
    'Table drh_stateful_research_study.location should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'location'::name, ARRAY[
    'id'::name,
    'name'::name,
    'alias'::name,
    'description'::name,
    'type_code'::name,
    'type_display'::name,
    'address_line'::name,
    'city'::name,
    'state'::name,
    'postal_code'::name,
    'country'::name,
    'longitude'::name,
    'latitude'::name,
    'managing_org_id'::name,
    'part_of_id'::name,
    'characteristic_code'::name,
    'characteristic_display'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'location', 'id', 'Column drh_stateful_research_study.location.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'id', 'text', 'Column drh_stateful_research_study.location.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'location', 'id', 'Column drh_stateful_research_study.location.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'id', 'Column drh_stateful_research_study.location.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'name', 'Column drh_stateful_research_study.location.name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'name', 'character varying(255)', 'Column drh_stateful_research_study.location.name should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'name', 'Column drh_stateful_research_study.location.name should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'name', 'Column drh_stateful_research_study.location.name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'alias', 'Column drh_stateful_research_study.location.alias should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'alias', 'character varying(255)', 'Column drh_stateful_research_study.location.alias should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'alias', 'Column drh_stateful_research_study.location.alias should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'alias', 'Column drh_stateful_research_study.location.alias should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'description', 'Column drh_stateful_research_study.location.description should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'description', 'character varying(4096)', 'Column drh_stateful_research_study.location.description should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'description', 'Column drh_stateful_research_study.location.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'description', 'Column drh_stateful_research_study.location.description should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'type_code', 'Column drh_stateful_research_study.location.type_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'type_code', 'character varying(50)', 'Column drh_stateful_research_study.location.type_code should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'type_code', 'Column drh_stateful_research_study.location.type_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'type_code', 'Column drh_stateful_research_study.location.type_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'type_display', 'Column drh_stateful_research_study.location.type_display should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'type_display', 'character varying(255)', 'Column drh_stateful_research_study.location.type_display should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'type_display', 'Column drh_stateful_research_study.location.type_display should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'type_display', 'Column drh_stateful_research_study.location.type_display should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'address_line', 'Column drh_stateful_research_study.location.address_line should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'address_line', 'character varying(255)', 'Column drh_stateful_research_study.location.address_line should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'address_line', 'Column drh_stateful_research_study.location.address_line should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'address_line', 'Column drh_stateful_research_study.location.address_line should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'city', 'Column drh_stateful_research_study.location.city should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'city', 'character varying(100)', 'Column drh_stateful_research_study.location.city should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'city', 'Column drh_stateful_research_study.location.city should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'city', 'Column drh_stateful_research_study.location.city should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'state', 'Column drh_stateful_research_study.location.state should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'state', 'character varying(100)', 'Column drh_stateful_research_study.location.state should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'state', 'Column drh_stateful_research_study.location.state should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'state', 'Column drh_stateful_research_study.location.state should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'postal_code', 'Column drh_stateful_research_study.location.postal_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'postal_code', 'character varying(20)', 'Column drh_stateful_research_study.location.postal_code should be type character varying(20)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'postal_code', 'Column drh_stateful_research_study.location.postal_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'postal_code', 'Column drh_stateful_research_study.location.postal_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'country', 'Column drh_stateful_research_study.location.country should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'country', 'character varying(100)', 'Column drh_stateful_research_study.location.country should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'country', 'Column drh_stateful_research_study.location.country should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'country', 'Column drh_stateful_research_study.location.country should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'longitude', 'Column drh_stateful_research_study.location.longitude should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'longitude', 'double precision', 'Column drh_stateful_research_study.location.longitude should be type double precision');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'longitude', 'Column drh_stateful_research_study.location.longitude should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'longitude', 'Column drh_stateful_research_study.location.longitude should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'latitude', 'Column drh_stateful_research_study.location.latitude should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'latitude', 'double precision', 'Column drh_stateful_research_study.location.latitude should be type double precision');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'latitude', 'Column drh_stateful_research_study.location.latitude should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'latitude', 'Column drh_stateful_research_study.location.latitude should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'managing_org_id', 'Column drh_stateful_research_study.location.managing_org_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'managing_org_id', 'text', 'Column drh_stateful_research_study.location.managing_org_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'managing_org_id', 'Column drh_stateful_research_study.location.managing_org_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'managing_org_id', 'Column drh_stateful_research_study.location.managing_org_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'part_of_id', 'Column drh_stateful_research_study.location.part_of_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'part_of_id', 'text', 'Column drh_stateful_research_study.location.part_of_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'part_of_id', 'Column drh_stateful_research_study.location.part_of_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'part_of_id', 'Column drh_stateful_research_study.location.part_of_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'characteristic_code', 'Column drh_stateful_research_study.location.characteristic_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'characteristic_code', 'character varying(50)', 'Column drh_stateful_research_study.location.characteristic_code should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'characteristic_code', 'Column drh_stateful_research_study.location.characteristic_code should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'characteristic_code', 'Column drh_stateful_research_study.location.characteristic_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'characteristic_display', 'Column drh_stateful_research_study.location.characteristic_display should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'characteristic_display', 'character varying(255)', 'Column drh_stateful_research_study.location.characteristic_display should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'characteristic_display', 'Column drh_stateful_research_study.location.characteristic_display should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'characteristic_display', 'Column drh_stateful_research_study.location.characteristic_display should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'rec_status_id', 'Column drh_stateful_research_study.location.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.location.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'location', 'rec_status_id', 'Column drh_stateful_research_study.location.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'rec_status_id', 'Column drh_stateful_research_study.location.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'created_at', 'Column drh_stateful_research_study.location.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.location.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'created_at', 'Column drh_stateful_research_study.location.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'location', 'created_at', 'Column drh_stateful_research_study.location.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'location', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.location.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'location', 'created_by', 'Column drh_stateful_research_study.location.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'created_by', 'text', 'Column drh_stateful_research_study.location.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'created_by', 'Column drh_stateful_research_study.location.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'location', 'created_by', 'Column drh_stateful_research_study.location.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'location', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.location.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'location', 'updated_at', 'Column drh_stateful_research_study.location.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.location.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'updated_at', 'Column drh_stateful_research_study.location.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'updated_at', 'Column drh_stateful_research_study.location.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'updated_by', 'Column drh_stateful_research_study.location.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'updated_by', 'text', 'Column drh_stateful_research_study.location.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'updated_by', 'Column drh_stateful_research_study.location.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'updated_by', 'Column drh_stateful_research_study.location.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'deleted_at', 'Column drh_stateful_research_study.location.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.location.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'deleted_at', 'Column drh_stateful_research_study.location.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'deleted_at', 'Column drh_stateful_research_study.location.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'location', 'deleted_by', 'Column drh_stateful_research_study.location.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'location', 'deleted_by', 'text', 'Column drh_stateful_research_study.location.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'location', 'deleted_by', 'Column drh_stateful_research_study.location.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'location', 'deleted_by', 'Column drh_stateful_research_study.location.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
