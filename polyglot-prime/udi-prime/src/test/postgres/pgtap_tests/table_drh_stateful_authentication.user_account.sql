SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(69);

SELECT has_table(
    'drh_stateful_authentication', 'user_account',
    'Should have table drh_stateful_authentication.user_account'
);

SELECT has_pk(
    'drh_stateful_authentication', 'user_account',
    'Table drh_stateful_authentication.user_account should have a primary key'
);

SELECT columns_are('drh_stateful_authentication'::name, 'user_account'::name, ARRAY[
    'user_id'::name,
    'username'::name,
    'email'::name,
    'first_name'::name,
    'last_name'::name,
    'last_login_at'::name,
    'profile_status'::name,
    'metadata'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'party_id'::name
]);

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'user_id', 'Column drh_stateful_authentication.user_account.user_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'user_id', 'text', 'Column drh_stateful_authentication.user_account.user_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'user_account', 'user_id', 'Column drh_stateful_authentication.user_account.user_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'user_id', 'Column drh_stateful_authentication.user_account.user_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'username', 'Column drh_stateful_authentication.user_account.username should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'username', 'character varying(50)', 'Column drh_stateful_authentication.user_account.username should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_authentication', 'user_account', 'username', 'Column drh_stateful_authentication.user_account.username should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'username', 'Column drh_stateful_authentication.user_account.username should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'email', 'Column drh_stateful_authentication.user_account.email should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'email', 'character varying(255)', 'Column drh_stateful_authentication.user_account.email should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_authentication', 'user_account', 'email', 'Column drh_stateful_authentication.user_account.email should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'email', 'Column drh_stateful_authentication.user_account.email should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'first_name', 'Column drh_stateful_authentication.user_account.first_name should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'first_name', 'character varying(50)', 'Column drh_stateful_authentication.user_account.first_name should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_authentication', 'user_account', 'first_name', 'Column drh_stateful_authentication.user_account.first_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'first_name', 'Column drh_stateful_authentication.user_account.first_name should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'last_name', 'Column drh_stateful_authentication.user_account.last_name should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'last_name', 'character varying(50)', 'Column drh_stateful_authentication.user_account.last_name should be type character varying(50)');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'last_name', 'Column drh_stateful_authentication.user_account.last_name should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'last_name', 'Column drh_stateful_authentication.user_account.last_name should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'last_login_at', 'Column drh_stateful_authentication.user_account.last_login_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'last_login_at', 'timestamp with time zone', 'Column drh_stateful_authentication.user_account.last_login_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'last_login_at', 'Column drh_stateful_authentication.user_account.last_login_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'last_login_at', 'Column drh_stateful_authentication.user_account.last_login_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'profile_status', 'Column drh_stateful_authentication.user_account.profile_status should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'profile_status', 'integer', 'Column drh_stateful_authentication.user_account.profile_status should be type integer');
SELECT col_not_null(     'drh_stateful_authentication', 'user_account', 'profile_status', 'Column drh_stateful_authentication.user_account.profile_status should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'profile_status', 'Column drh_stateful_authentication.user_account.profile_status should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'metadata', 'Column drh_stateful_authentication.user_account.metadata should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'metadata', 'jsonb', 'Column drh_stateful_authentication.user_account.metadata should be type jsonb');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'metadata', 'Column drh_stateful_authentication.user_account.metadata should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'metadata', 'Column drh_stateful_authentication.user_account.metadata should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'rec_status_id', 'Column drh_stateful_authentication.user_account.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'rec_status_id', 'integer', 'Column drh_stateful_authentication.user_account.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_authentication', 'user_account', 'rec_status_id', 'Column drh_stateful_authentication.user_account.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'rec_status_id', 'Column drh_stateful_authentication.user_account.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'created_at', 'Column drh_stateful_authentication.user_account.created_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'created_at', 'timestamp with time zone', 'Column drh_stateful_authentication.user_account.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'created_at', 'Column drh_stateful_authentication.user_account.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'user_account', 'created_at', 'Column drh_stateful_authentication.user_account.created_at should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'user_account', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_authentication.user_account.created_at default is');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'created_by', 'Column drh_stateful_authentication.user_account.created_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'created_by', 'text', 'Column drh_stateful_authentication.user_account.created_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'created_by', 'Column drh_stateful_authentication.user_account.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'user_account', 'created_by', 'Column drh_stateful_authentication.user_account.created_by should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'user_account', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_authentication.user_account.created_by default is');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'updated_at', 'Column drh_stateful_authentication.user_account.updated_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_authentication.user_account.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'updated_at', 'Column drh_stateful_authentication.user_account.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'updated_at', 'Column drh_stateful_authentication.user_account.updated_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'updated_by', 'Column drh_stateful_authentication.user_account.updated_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'updated_by', 'text', 'Column drh_stateful_authentication.user_account.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'updated_by', 'Column drh_stateful_authentication.user_account.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'updated_by', 'Column drh_stateful_authentication.user_account.updated_by should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'deleted_at', 'Column drh_stateful_authentication.user_account.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_authentication.user_account.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'deleted_at', 'Column drh_stateful_authentication.user_account.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'deleted_at', 'Column drh_stateful_authentication.user_account.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'deleted_by', 'Column drh_stateful_authentication.user_account.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'deleted_by', 'text', 'Column drh_stateful_authentication.user_account.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'user_account', 'deleted_by', 'Column drh_stateful_authentication.user_account.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'deleted_by', 'Column drh_stateful_authentication.user_account.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'user_account', 'party_id', 'Column drh_stateful_authentication.user_account.party_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'user_account', 'party_id', 'text', 'Column drh_stateful_authentication.user_account.party_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'user_account', 'party_id', 'Column drh_stateful_authentication.user_account.party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'user_account', 'party_id', 'Column drh_stateful_authentication.user_account.party_id should not have a default');

SELECT * FROM finish();
ROLLBACK;
