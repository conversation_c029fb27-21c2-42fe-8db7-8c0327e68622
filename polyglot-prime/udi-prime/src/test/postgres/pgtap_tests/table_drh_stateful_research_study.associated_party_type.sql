SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(45);

SELECT has_table(
    'drh_stateful_research_study', 'associated_party_type',
    'Should have table drh_stateful_research_study.associated_party_type'
);

SELECT has_pk(
    'drh_stateful_research_study', 'associated_party_type',
    'Table drh_stateful_research_study.associated_party_type should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'associated_party_type'::name, ARRAY[
    'id'::name,
    'name'::name,
    'description'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'id', 'Column drh_stateful_research_study.associated_party_type.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'id', 'text', 'Column drh_stateful_research_study.associated_party_type.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'associated_party_type', 'id', 'Column drh_stateful_research_study.associated_party_type.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'id', 'Column drh_stateful_research_study.associated_party_type.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'name', 'Column drh_stateful_research_study.associated_party_type.name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'name', 'character varying(100)', 'Column drh_stateful_research_study.associated_party_type.name should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_research_study', 'associated_party_type', 'name', 'Column drh_stateful_research_study.associated_party_type.name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'name', 'Column drh_stateful_research_study.associated_party_type.name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'description', 'Column drh_stateful_research_study.associated_party_type.description should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'description', 'character varying(4096)', 'Column drh_stateful_research_study.associated_party_type.description should be type character varying(4096)');
SELECT col_is_null(      'drh_stateful_research_study', 'associated_party_type', 'description', 'Column drh_stateful_research_study.associated_party_type.description should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'description', 'Column drh_stateful_research_study.associated_party_type.description should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'rec_status_id', 'Column drh_stateful_research_study.associated_party_type.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.associated_party_type.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'associated_party_type', 'rec_status_id', 'Column drh_stateful_research_study.associated_party_type.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'rec_status_id', 'Column drh_stateful_research_study.associated_party_type.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'created_at', 'Column drh_stateful_research_study.associated_party_type.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.associated_party_type.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'associated_party_type', 'created_at', 'Column drh_stateful_research_study.associated_party_type.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'associated_party_type', 'created_at', 'Column drh_stateful_research_study.associated_party_type.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'associated_party_type', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.associated_party_type.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'created_by', 'Column drh_stateful_research_study.associated_party_type.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'created_by', 'text', 'Column drh_stateful_research_study.associated_party_type.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'associated_party_type', 'created_by', 'Column drh_stateful_research_study.associated_party_type.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'associated_party_type', 'created_by', 'Column drh_stateful_research_study.associated_party_type.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'associated_party_type', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.associated_party_type.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'updated_at', 'Column drh_stateful_research_study.associated_party_type.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.associated_party_type.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'associated_party_type', 'updated_at', 'Column drh_stateful_research_study.associated_party_type.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'updated_at', 'Column drh_stateful_research_study.associated_party_type.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'updated_by', 'Column drh_stateful_research_study.associated_party_type.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'updated_by', 'text', 'Column drh_stateful_research_study.associated_party_type.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'associated_party_type', 'updated_by', 'Column drh_stateful_research_study.associated_party_type.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'updated_by', 'Column drh_stateful_research_study.associated_party_type.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'deleted_at', 'Column drh_stateful_research_study.associated_party_type.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.associated_party_type.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'associated_party_type', 'deleted_at', 'Column drh_stateful_research_study.associated_party_type.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'deleted_at', 'Column drh_stateful_research_study.associated_party_type.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'associated_party_type', 'deleted_by', 'Column drh_stateful_research_study.associated_party_type.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'associated_party_type', 'deleted_by', 'text', 'Column drh_stateful_research_study.associated_party_type.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'associated_party_type', 'deleted_by', 'Column drh_stateful_research_study.associated_party_type.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'associated_party_type', 'deleted_by', 'Column drh_stateful_research_study.associated_party_type.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
