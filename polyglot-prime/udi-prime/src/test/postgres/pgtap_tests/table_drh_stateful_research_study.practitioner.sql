SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(69);

SELECT has_table(
    'drh_stateful_research_study', 'practitioner',
    'Should have table drh_stateful_research_study.practitioner'
);

SELECT has_pk(
    'drh_stateful_research_study', 'practitioner',
    'Table drh_stateful_research_study.practitioner should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'practitioner'::name, ARRAY[
    'id'::name,
    'system_identifier'::name,
    'name'::name,
    'gender_type_id'::name,
    'birth_date'::name,
    'photo_url'::name,
    'org_party_id'::name,
    'tenant_id'::name,
    'practitioner_party_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'id', 'Column drh_stateful_research_study.practitioner.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'id', 'text', 'Column drh_stateful_research_study.practitioner.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'practitioner', 'id', 'Column drh_stateful_research_study.practitioner.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'id', 'Column drh_stateful_research_study.practitioner.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'system_identifier', 'Column drh_stateful_research_study.practitioner.system_identifier should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'system_identifier', 'character varying(255)', 'Column drh_stateful_research_study.practitioner.system_identifier should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'system_identifier', 'Column drh_stateful_research_study.practitioner.system_identifier should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'system_identifier', 'Column drh_stateful_research_study.practitioner.system_identifier should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'name', 'Column drh_stateful_research_study.practitioner.name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'name', 'character varying(255)', 'Column drh_stateful_research_study.practitioner.name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'practitioner', 'name', 'Column drh_stateful_research_study.practitioner.name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'name', 'Column drh_stateful_research_study.practitioner.name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'gender_type_id', 'Column drh_stateful_research_study.practitioner.gender_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'gender_type_id', 'text', 'Column drh_stateful_research_study.practitioner.gender_type_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'gender_type_id', 'Column drh_stateful_research_study.practitioner.gender_type_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'gender_type_id', 'Column drh_stateful_research_study.practitioner.gender_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'birth_date', 'Column drh_stateful_research_study.practitioner.birth_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'birth_date', 'date', 'Column drh_stateful_research_study.practitioner.birth_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'birth_date', 'Column drh_stateful_research_study.practitioner.birth_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'birth_date', 'Column drh_stateful_research_study.practitioner.birth_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'photo_url', 'Column drh_stateful_research_study.practitioner.photo_url should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'photo_url', 'character varying(255)', 'Column drh_stateful_research_study.practitioner.photo_url should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'photo_url', 'Column drh_stateful_research_study.practitioner.photo_url should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'photo_url', 'Column drh_stateful_research_study.practitioner.photo_url should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'org_party_id', 'Column drh_stateful_research_study.practitioner.org_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'org_party_id', 'text', 'Column drh_stateful_research_study.practitioner.org_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'practitioner', 'org_party_id', 'Column drh_stateful_research_study.practitioner.org_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'org_party_id', 'Column drh_stateful_research_study.practitioner.org_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'tenant_id', 'Column drh_stateful_research_study.practitioner.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'tenant_id', 'text', 'Column drh_stateful_research_study.practitioner.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'practitioner', 'tenant_id', 'Column drh_stateful_research_study.practitioner.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'tenant_id', 'Column drh_stateful_research_study.practitioner.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'practitioner_party_id', 'Column drh_stateful_research_study.practitioner.practitioner_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'practitioner_party_id', 'text', 'Column drh_stateful_research_study.practitioner.practitioner_party_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'practitioner_party_id', 'Column drh_stateful_research_study.practitioner.practitioner_party_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'practitioner_party_id', 'Column drh_stateful_research_study.practitioner.practitioner_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'rec_status_id', 'Column drh_stateful_research_study.practitioner.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.practitioner.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'practitioner', 'rec_status_id', 'Column drh_stateful_research_study.practitioner.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'rec_status_id', 'Column drh_stateful_research_study.practitioner.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'created_at', 'Column drh_stateful_research_study.practitioner.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.practitioner.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'created_at', 'Column drh_stateful_research_study.practitioner.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'practitioner', 'created_at', 'Column drh_stateful_research_study.practitioner.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'practitioner', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.practitioner.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'created_by', 'Column drh_stateful_research_study.practitioner.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'created_by', 'text', 'Column drh_stateful_research_study.practitioner.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'created_by', 'Column drh_stateful_research_study.practitioner.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'practitioner', 'created_by', 'Column drh_stateful_research_study.practitioner.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'practitioner', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.practitioner.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'updated_at', 'Column drh_stateful_research_study.practitioner.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.practitioner.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'updated_at', 'Column drh_stateful_research_study.practitioner.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'updated_at', 'Column drh_stateful_research_study.practitioner.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'updated_by', 'Column drh_stateful_research_study.practitioner.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'updated_by', 'text', 'Column drh_stateful_research_study.practitioner.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'updated_by', 'Column drh_stateful_research_study.practitioner.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'updated_by', 'Column drh_stateful_research_study.practitioner.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'deleted_at', 'Column drh_stateful_research_study.practitioner.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.practitioner.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'deleted_at', 'Column drh_stateful_research_study.practitioner.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'deleted_at', 'Column drh_stateful_research_study.practitioner.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'practitioner', 'deleted_by', 'Column drh_stateful_research_study.practitioner.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'practitioner', 'deleted_by', 'text', 'Column drh_stateful_research_study.practitioner.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'practitioner', 'deleted_by', 'Column drh_stateful_research_study.practitioner.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'practitioner', 'deleted_by', 'Column drh_stateful_research_study.practitioner.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
