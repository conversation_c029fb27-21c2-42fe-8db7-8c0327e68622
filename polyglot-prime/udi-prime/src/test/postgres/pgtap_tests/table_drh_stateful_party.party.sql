SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(49);

SELECT has_table(
    'drh_stateful_party', 'party',
    'Should have table drh_stateful_party.party'
);

SELECT has_pk(
    'drh_stateful_party', 'party',
    'Table drh_stateful_party.party should have a primary key'
);

SELECT columns_are('drh_stateful_party'::name, 'party'::name, ARRAY[
    'party_id'::name,
    'party_type_id'::name,
    'party_name'::name,
    'elaboration'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name,
    'activity_log'::name
]);

SELECT has_column(       'drh_stateful_party', 'party', 'party_id', 'Column drh_stateful_party.party.party_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'party_id', 'text', 'Column drh_stateful_party.party.party_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party', 'party_id', 'Column drh_stateful_party.party.party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'party_id', 'Column drh_stateful_party.party.party_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'party_type_id', 'Column drh_stateful_party.party.party_type_id should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'party_type_id', 'text', 'Column drh_stateful_party.party.party_type_id should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party', 'party_type_id', 'Column drh_stateful_party.party.party_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'party_type_id', 'Column drh_stateful_party.party.party_type_id should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'party_name', 'Column drh_stateful_party.party.party_name should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'party_name', 'text', 'Column drh_stateful_party.party.party_name should be type text');
SELECT col_not_null(     'drh_stateful_party', 'party', 'party_name', 'Column drh_stateful_party.party.party_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'party_name', 'Column drh_stateful_party.party.party_name should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'elaboration', 'Column drh_stateful_party.party.elaboration should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'elaboration', 'jsonb', 'Column drh_stateful_party.party.elaboration should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'party', 'elaboration', 'Column drh_stateful_party.party.elaboration should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'elaboration', 'Column drh_stateful_party.party.elaboration should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'created_at', 'Column drh_stateful_party.party.created_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'created_at', 'timestamp with time zone', 'Column drh_stateful_party.party.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party', 'created_at', 'Column drh_stateful_party.party.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party', 'created_at', 'Column drh_stateful_party.party.created_at should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_party.party.created_at default is');

SELECT has_column(       'drh_stateful_party', 'party', 'created_by', 'Column drh_stateful_party.party.created_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'created_by', 'text', 'Column drh_stateful_party.party.created_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party', 'created_by', 'Column drh_stateful_party.party.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_party', 'party', 'created_by', 'Column drh_stateful_party.party.created_by should have a default');
SELECT col_default_is(   'drh_stateful_party', 'party', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_party.party.created_by default is');

SELECT has_column(       'drh_stateful_party', 'party', 'updated_at', 'Column drh_stateful_party.party.updated_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_party.party.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party', 'updated_at', 'Column drh_stateful_party.party.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'updated_at', 'Column drh_stateful_party.party.updated_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'updated_by', 'Column drh_stateful_party.party.updated_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'updated_by', 'text', 'Column drh_stateful_party.party.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party', 'updated_by', 'Column drh_stateful_party.party.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'updated_by', 'Column drh_stateful_party.party.updated_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'deleted_at', 'Column drh_stateful_party.party.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_party.party.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_party', 'party', 'deleted_at', 'Column drh_stateful_party.party.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'deleted_at', 'Column drh_stateful_party.party.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'deleted_by', 'Column drh_stateful_party.party.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'deleted_by', 'text', 'Column drh_stateful_party.party.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_party', 'party', 'deleted_by', 'Column drh_stateful_party.party.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'deleted_by', 'Column drh_stateful_party.party.deleted_by should not have a default');

SELECT has_column(       'drh_stateful_party', 'party', 'activity_log', 'Column drh_stateful_party.party.activity_log should exist');
SELECT col_type_is(      'drh_stateful_party', 'party', 'activity_log', 'jsonb', 'Column drh_stateful_party.party.activity_log should be type jsonb');
SELECT col_is_null(      'drh_stateful_party', 'party', 'activity_log', 'Column drh_stateful_party.party.activity_log should allow NULL');
SELECT col_hasnt_default('drh_stateful_party', 'party', 'activity_log', 'Column drh_stateful_party.party.activity_log should not have a default');

SELECT * FROM finish();
ROLLBACK;
