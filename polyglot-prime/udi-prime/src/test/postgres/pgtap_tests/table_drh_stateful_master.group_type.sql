SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(53);

SELECT has_table(
    'drh_stateful_master', 'group_type',
    'Should have table drh_stateful_master.group_type'
);

SELECT has_pk(
    'drh_stateful_master', 'group_type',
    'Table drh_stateful_master.group_type should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'group_type'::name, ARRAY[
    'group_type_id'::name,
    'code'::name,
    'system'::name,
    'display'::name,
    'definition'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'group_type', 'group_type_id', 'Column drh_stateful_master.group_type.group_type_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'group_type_id', 'text', 'Column drh_stateful_master.group_type.group_type_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'group_type', 'group_type_id', 'Column drh_stateful_master.group_type.group_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'group_type_id', 'Column drh_stateful_master.group_type.group_type_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'code', 'Column drh_stateful_master.group_type.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'code', 'character varying(50)', 'Column drh_stateful_master.group_type.code should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_master', 'group_type', 'code', 'Column drh_stateful_master.group_type.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'code', 'Column drh_stateful_master.group_type.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'system', 'Column drh_stateful_master.group_type.system should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'system', 'character varying(255)', 'Column drh_stateful_master.group_type.system should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'group_type', 'system', 'Column drh_stateful_master.group_type.system should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'system', 'Column drh_stateful_master.group_type.system should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'display', 'Column drh_stateful_master.group_type.display should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'display', 'character varying(255)', 'Column drh_stateful_master.group_type.display should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'group_type', 'display', 'Column drh_stateful_master.group_type.display should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'display', 'Column drh_stateful_master.group_type.display should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'definition', 'Column drh_stateful_master.group_type.definition should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'definition', 'text', 'Column drh_stateful_master.group_type.definition should be type text');
SELECT col_is_null(      'drh_stateful_master', 'group_type', 'definition', 'Column drh_stateful_master.group_type.definition should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'definition', 'Column drh_stateful_master.group_type.definition should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'rec_status_id', 'Column drh_stateful_master.group_type.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'rec_status_id', 'integer', 'Column drh_stateful_master.group_type.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'group_type', 'rec_status_id', 'Column drh_stateful_master.group_type.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'rec_status_id', 'Column drh_stateful_master.group_type.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'created_at', 'Column drh_stateful_master.group_type.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.group_type.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'group_type', 'created_at', 'Column drh_stateful_master.group_type.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'group_type', 'created_at', 'Column drh_stateful_master.group_type.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'group_type', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.group_type.created_at default is');

SELECT has_column(       'drh_stateful_master', 'group_type', 'created_by', 'Column drh_stateful_master.group_type.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'created_by', 'text', 'Column drh_stateful_master.group_type.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'group_type', 'created_by', 'Column drh_stateful_master.group_type.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'group_type', 'created_by', 'Column drh_stateful_master.group_type.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'group_type', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.group_type.created_by default is');

SELECT has_column(       'drh_stateful_master', 'group_type', 'updated_at', 'Column drh_stateful_master.group_type.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.group_type.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'group_type', 'updated_at', 'Column drh_stateful_master.group_type.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'updated_at', 'Column drh_stateful_master.group_type.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'updated_by', 'Column drh_stateful_master.group_type.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'updated_by', 'text', 'Column drh_stateful_master.group_type.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'group_type', 'updated_by', 'Column drh_stateful_master.group_type.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'updated_by', 'Column drh_stateful_master.group_type.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'deleted_at', 'Column drh_stateful_master.group_type.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.group_type.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'group_type', 'deleted_at', 'Column drh_stateful_master.group_type.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'deleted_at', 'Column drh_stateful_master.group_type.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'group_type', 'deleted_by', 'Column drh_stateful_master.group_type.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'group_type', 'deleted_by', 'text', 'Column drh_stateful_master.group_type.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'group_type', 'deleted_by', 'Column drh_stateful_master.group_type.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'group_type', 'deleted_by', 'Column drh_stateful_master.group_type.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
