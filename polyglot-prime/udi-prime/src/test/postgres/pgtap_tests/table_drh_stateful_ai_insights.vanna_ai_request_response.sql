SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(35);

SELECT has_table(
    'drh_stateful_ai_insights', 'vanna_ai_request_response',
    'Should have table drh_stateful_ai_insights.vanna_ai_request_response'
);

SELECT hasnt_pk(
    'drh_stateful_ai_insights', 'vanna_ai_request_response',
    'Table drh_stateful_ai_insights.vanna_ai_request_response should have a primary key'
);

SELECT columns_are('drh_stateful_ai_insights'::name, 'vanna_ai_request_response'::name, ARRAY[
    'id'::name,
    'question'::name,
    'sql_query'::name,
    'results'::name,
    'json_result'::name,
    'created_at'::name,
    'updated_at'::name,
    'created_by'::name
]);

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'id', 'Column drh_stateful_ai_insights.vanna_ai_request_response.id should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'id', 'text', 'Column drh_stateful_ai_insights.vanna_ai_request_response.id should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'id', 'Column drh_stateful_ai_insights.vanna_ai_request_response.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'id', 'Column drh_stateful_ai_insights.vanna_ai_request_response.id should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'question', 'Column drh_stateful_ai_insights.vanna_ai_request_response.question should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'question', 'text', 'Column drh_stateful_ai_insights.vanna_ai_request_response.question should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'question', 'Column drh_stateful_ai_insights.vanna_ai_request_response.question should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'question', 'Column drh_stateful_ai_insights.vanna_ai_request_response.question should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'sql_query', 'Column drh_stateful_ai_insights.vanna_ai_request_response.sql_query should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'sql_query', 'text', 'Column drh_stateful_ai_insights.vanna_ai_request_response.sql_query should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'sql_query', 'Column drh_stateful_ai_insights.vanna_ai_request_response.sql_query should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'sql_query', 'Column drh_stateful_ai_insights.vanna_ai_request_response.sql_query should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'results', 'Column drh_stateful_ai_insights.vanna_ai_request_response.results should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'results', 'text', 'Column drh_stateful_ai_insights.vanna_ai_request_response.results should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'results', 'Column drh_stateful_ai_insights.vanna_ai_request_response.results should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'results', 'Column drh_stateful_ai_insights.vanna_ai_request_response.results should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'json_result', 'Column drh_stateful_ai_insights.vanna_ai_request_response.json_result should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'json_result', 'text', 'Column drh_stateful_ai_insights.vanna_ai_request_response.json_result should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'json_result', 'Column drh_stateful_ai_insights.vanna_ai_request_response.json_result should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'json_result', 'Column drh_stateful_ai_insights.vanna_ai_request_response.json_result should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_at', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_at should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_at', 'timestamp with time zone', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_at should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_at', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_at', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_at should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'updated_at', 'Column drh_stateful_ai_insights.vanna_ai_request_response.updated_at should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_ai_insights.vanna_ai_request_response.updated_at should be type timestamp with time zone');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'updated_at', 'Column drh_stateful_ai_insights.vanna_ai_request_response.updated_at should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'updated_at', 'Column drh_stateful_ai_insights.vanna_ai_request_response.updated_at should not have a default');

SELECT has_column(       'drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_by', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_by should exist');
SELECT col_type_is(      'drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_by', 'text', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_by should be type text');
SELECT col_not_null(     'drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_by', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_by should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_ai_insights', 'vanna_ai_request_response', 'created_by', 'Column drh_stateful_ai_insights.vanna_ai_request_response.created_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
