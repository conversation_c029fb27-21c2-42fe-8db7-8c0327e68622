SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(61);

SELECT has_table(
    'drh_stateful_research_study', 'research_study_associated_party',
    'Should have table drh_stateful_research_study.research_study_associated_party'
);

SELECT has_pk(
    'drh_stateful_research_study', 'research_study_associated_party',
    'Table drh_stateful_research_study.research_study_associated_party should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'research_study_associated_party'::name, ARRAY[
    'associated_party_id'::name,
    'research_study_id'::name,
    'party_role_type_id'::name,
    'party_id'::name,
    'party_name'::name,
    'classifier_id'::name,
    'period_start'::name,
    'period_end'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'associated_party_id', 'Column drh_stateful_research_study.research_study_associated_party.associated_party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'associated_party_id', 'text', 'Column drh_stateful_research_study.research_study_associated_party.associated_party_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study_associated_party', 'associated_party_id', 'Column drh_stateful_research_study.research_study_associated_party.associated_party_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'associated_party_id', 'Column drh_stateful_research_study.research_study_associated_party.associated_party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'research_study_id', 'Column drh_stateful_research_study.research_study_associated_party.research_study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'research_study_id', 'text', 'Column drh_stateful_research_study.research_study_associated_party.research_study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study_associated_party', 'research_study_id', 'Column drh_stateful_research_study.research_study_associated_party.research_study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'research_study_id', 'Column drh_stateful_research_study.research_study_associated_party.research_study_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'party_role_type_id', 'Column drh_stateful_research_study.research_study_associated_party.party_role_type_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'party_role_type_id', 'text', 'Column drh_stateful_research_study.research_study_associated_party.party_role_type_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'research_study_associated_party', 'party_role_type_id', 'Column drh_stateful_research_study.research_study_associated_party.party_role_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'party_role_type_id', 'Column drh_stateful_research_study.research_study_associated_party.party_role_type_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'party_id', 'Column drh_stateful_research_study.research_study_associated_party.party_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'party_id', 'character varying(255)', 'Column drh_stateful_research_study.research_study_associated_party.party_id should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'party_id', 'Column drh_stateful_research_study.research_study_associated_party.party_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'party_id', 'Column drh_stateful_research_study.research_study_associated_party.party_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'party_name', 'Column drh_stateful_research_study.research_study_associated_party.party_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'party_name', 'character varying(255)', 'Column drh_stateful_research_study.research_study_associated_party.party_name should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'party_name', 'Column drh_stateful_research_study.research_study_associated_party.party_name should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'party_name', 'Column drh_stateful_research_study.research_study_associated_party.party_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'classifier_id', 'Column drh_stateful_research_study.research_study_associated_party.classifier_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'classifier_id', 'text', 'Column drh_stateful_research_study.research_study_associated_party.classifier_id should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'classifier_id', 'Column drh_stateful_research_study.research_study_associated_party.classifier_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'classifier_id', 'Column drh_stateful_research_study.research_study_associated_party.classifier_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'period_start', 'Column drh_stateful_research_study.research_study_associated_party.period_start should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'period_start', 'date', 'Column drh_stateful_research_study.research_study_associated_party.period_start should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'period_start', 'Column drh_stateful_research_study.research_study_associated_party.period_start should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'period_start', 'Column drh_stateful_research_study.research_study_associated_party.period_start should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'period_end', 'Column drh_stateful_research_study.research_study_associated_party.period_end should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'period_end', 'date', 'Column drh_stateful_research_study.research_study_associated_party.period_end should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'period_end', 'Column drh_stateful_research_study.research_study_associated_party.period_end should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'period_end', 'Column drh_stateful_research_study.research_study_associated_party.period_end should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'created_at', 'Column drh_stateful_research_study.research_study_associated_party.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_study_associated_party.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'created_at', 'Column drh_stateful_research_study.research_study_associated_party.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'research_study_associated_party', 'created_at', 'Column drh_stateful_research_study.research_study_associated_party.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'research_study_associated_party', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.research_study_associated_party.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'created_by', 'Column drh_stateful_research_study.research_study_associated_party.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'created_by', 'text', 'Column drh_stateful_research_study.research_study_associated_party.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'created_by', 'Column drh_stateful_research_study.research_study_associated_party.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'research_study_associated_party', 'created_by', 'Column drh_stateful_research_study.research_study_associated_party.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'research_study_associated_party', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.research_study_associated_party.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'updated_at', 'Column drh_stateful_research_study.research_study_associated_party.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_study_associated_party.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'updated_at', 'Column drh_stateful_research_study.research_study_associated_party.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'updated_at', 'Column drh_stateful_research_study.research_study_associated_party.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'updated_by', 'Column drh_stateful_research_study.research_study_associated_party.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'updated_by', 'text', 'Column drh_stateful_research_study.research_study_associated_party.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'updated_by', 'Column drh_stateful_research_study.research_study_associated_party.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'updated_by', 'Column drh_stateful_research_study.research_study_associated_party.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'deleted_at', 'Column drh_stateful_research_study.research_study_associated_party.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.research_study_associated_party.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'deleted_at', 'Column drh_stateful_research_study.research_study_associated_party.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'deleted_at', 'Column drh_stateful_research_study.research_study_associated_party.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'research_study_associated_party', 'deleted_by', 'Column drh_stateful_research_study.research_study_associated_party.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'research_study_associated_party', 'deleted_by', 'text', 'Column drh_stateful_research_study.research_study_associated_party.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'research_study_associated_party', 'deleted_by', 'Column drh_stateful_research_study.research_study_associated_party.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'research_study_associated_party', 'deleted_by', 'Column drh_stateful_research_study.research_study_associated_party.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
