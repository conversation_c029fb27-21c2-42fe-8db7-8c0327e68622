SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(49);

SELECT has_table(
    'drh_stateful_master', 'role_permission',
    'Should have table drh_stateful_authentication.role_permission'
);

SELECT has_pk(
    'drh_stateful_master', 'role_permission',
    'Table drh_stateful_authentication.role_permission should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'role_permission'::name, ARRAY[
    'role_permission_id'::name,
    'role_id'::name,
    'permission_id'::name,
    'metadata'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'role_permission', 'role_permission_id', 'Column drh_stateful_authentication.role_permission.role_permission_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'role_permission_id', 'text', 'Column drh_stateful_authentication.role_permission.role_permission_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'role_permission', 'role_permission_id', 'Column drh_stateful_authentication.role_permission.role_permission_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'role_permission_id', 'Column drh_stateful_authentication.role_permission.role_permission_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'role_id', 'Column drh_stateful_authentication.role_permission.role_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'role_id', 'text', 'Column drh_stateful_authentication.role_permission.role_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'role_permission', 'role_id', 'Column drh_stateful_authentication.role_permission.role_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'role_id', 'Column drh_stateful_authentication.role_permission.role_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'permission_id', 'Column drh_stateful_authentication.role_permission.permission_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'permission_id', 'text', 'Column drh_stateful_authentication.role_permission.permission_id should be type text');
SELECT col_not_null(     'drh_stateful_master', 'role_permission', 'permission_id', 'Column drh_stateful_authentication.role_permission.permission_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'permission_id', 'Column drh_stateful_authentication.role_permission.permission_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'metadata', 'Column drh_stateful_authentication.role_permission.metadata should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'metadata', 'jsonb', 'Column drh_stateful_authentication.role_permission.metadata should be type jsonb');
SELECT col_is_null(      'drh_stateful_master', 'role_permission', 'metadata', 'Column drh_stateful_authentication.role_permission.metadata should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'metadata', 'Column drh_stateful_authentication.role_permission.metadata should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'rec_status_id', 'Column drh_stateful_authentication.role_permission.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'rec_status_id', 'integer', 'Column drh_stateful_authentication.role_permission.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'role_permission', 'rec_status_id', 'Column drh_stateful_authentication.role_permission.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'rec_status_id', 'Column drh_stateful_authentication.role_permission.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'created_at', 'Column drh_stateful_authentication.role_permission.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'created_at', 'timestamp with time zone', 'Column drh_stateful_authentication.role_permission.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'role_permission', 'created_at', 'Column drh_stateful_authentication.role_permission.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'role_permission', 'created_at', 'Column drh_stateful_authentication.role_permission.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'role_permission', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_authentication.role_permission.created_at default is');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'created_by', 'Column drh_stateful_authentication.role_permission.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'created_by', 'text', 'Column drh_stateful_authentication.role_permission.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role_permission', 'created_by', 'Column drh_stateful_authentication.role_permission.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'role_permission', 'created_by', 'Column drh_stateful_authentication.role_permission.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'role_permission', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_authentication.role_permission.created_by default is');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'updated_at', 'Column drh_stateful_authentication.role_permission.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_authentication.role_permission.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'role_permission', 'updated_at', 'Column drh_stateful_authentication.role_permission.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'updated_at', 'Column drh_stateful_authentication.role_permission.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'updated_by', 'Column drh_stateful_authentication.role_permission.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'updated_by', 'text', 'Column drh_stateful_authentication.role_permission.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role_permission', 'updated_by', 'Column drh_stateful_authentication.role_permission.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'updated_by', 'Column drh_stateful_authentication.role_permission.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'deleted_at', 'Column drh_stateful_authentication.role_permission.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_authentication.role_permission.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'role_permission', 'deleted_at', 'Column drh_stateful_authentication.role_permission.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'deleted_at', 'Column drh_stateful_authentication.role_permission.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'role_permission', 'deleted_by', 'Column drh_stateful_authentication.role_permission.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'role_permission', 'deleted_by', 'text', 'Column drh_stateful_authentication.role_permission.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'role_permission', 'deleted_by', 'Column drh_stateful_authentication.role_permission.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'role_permission', 'deleted_by', 'Column drh_stateful_authentication.role_permission.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
