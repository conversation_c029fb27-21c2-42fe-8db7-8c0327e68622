SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(93);

SELECT has_table(
    'drh_stateful_research_study', 'citation',
    'Should have table drh_stateful_research_study.citation'
);

SELECT has_pk(
    'drh_stateful_research_study', 'citation',
    'Table drh_stateful_research_study.citation should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'citation'::name, ARRAY[
    'id'::name,
    'url'::name,
    'identifier_system'::name,
    'identifier_value'::name,
    'title'::name,
    'status'::name,
    'date'::name,
    'publisher'::name,
    'abstract'::name,
    'journal_title'::name,
    'journal_volume'::name,
    'journal_issue'::name,
    'journal_page'::name,
    'publication_date'::name,
    'rec_status_id'::name,
    'study_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'citation', 'id', 'Column drh_stateful_research_study.citation.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'id', 'text', 'Column drh_stateful_research_study.citation.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation', 'id', 'Column drh_stateful_research_study.citation.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'id', 'Column drh_stateful_research_study.citation.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'url', 'Column drh_stateful_research_study.citation.url should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'url', 'text', 'Column drh_stateful_research_study.citation.url should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'url', 'Column drh_stateful_research_study.citation.url should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'url', 'Column drh_stateful_research_study.citation.url should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'identifier_system', 'Column drh_stateful_research_study.citation.identifier_system should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'identifier_system', 'text', 'Column drh_stateful_research_study.citation.identifier_system should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation', 'identifier_system', 'Column drh_stateful_research_study.citation.identifier_system should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'identifier_system', 'Column drh_stateful_research_study.citation.identifier_system should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'identifier_value', 'Column drh_stateful_research_study.citation.identifier_value should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'identifier_value', 'text', 'Column drh_stateful_research_study.citation.identifier_value should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation', 'identifier_value', 'Column drh_stateful_research_study.citation.identifier_value should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'identifier_value', 'Column drh_stateful_research_study.citation.identifier_value should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'title', 'Column drh_stateful_research_study.citation.title should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'title', 'text', 'Column drh_stateful_research_study.citation.title should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation', 'title', 'Column drh_stateful_research_study.citation.title should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'title', 'Column drh_stateful_research_study.citation.title should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'status', 'Column drh_stateful_research_study.citation.status should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'status', 'text', 'Column drh_stateful_research_study.citation.status should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'status', 'Column drh_stateful_research_study.citation.status should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'status', 'Column drh_stateful_research_study.citation.status should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'date', 'Column drh_stateful_research_study.citation.date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'date', 'date', 'Column drh_stateful_research_study.citation.date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'date', 'Column drh_stateful_research_study.citation.date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'date', 'Column drh_stateful_research_study.citation.date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'publisher', 'Column drh_stateful_research_study.citation.publisher should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'publisher', 'text', 'Column drh_stateful_research_study.citation.publisher should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'publisher', 'Column drh_stateful_research_study.citation.publisher should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'publisher', 'Column drh_stateful_research_study.citation.publisher should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'abstract', 'Column drh_stateful_research_study.citation.abstract should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'abstract', 'text', 'Column drh_stateful_research_study.citation.abstract should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'abstract', 'Column drh_stateful_research_study.citation.abstract should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'abstract', 'Column drh_stateful_research_study.citation.abstract should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'journal_title', 'Column drh_stateful_research_study.citation.journal_title should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'journal_title', 'text', 'Column drh_stateful_research_study.citation.journal_title should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'journal_title', 'Column drh_stateful_research_study.citation.journal_title should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'journal_title', 'Column drh_stateful_research_study.citation.journal_title should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'journal_volume', 'Column drh_stateful_research_study.citation.journal_volume should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'journal_volume', 'text', 'Column drh_stateful_research_study.citation.journal_volume should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'journal_volume', 'Column drh_stateful_research_study.citation.journal_volume should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'journal_volume', 'Column drh_stateful_research_study.citation.journal_volume should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'journal_issue', 'Column drh_stateful_research_study.citation.journal_issue should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'journal_issue', 'text', 'Column drh_stateful_research_study.citation.journal_issue should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'journal_issue', 'Column drh_stateful_research_study.citation.journal_issue should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'journal_issue', 'Column drh_stateful_research_study.citation.journal_issue should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'journal_page', 'Column drh_stateful_research_study.citation.journal_page should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'journal_page', 'text', 'Column drh_stateful_research_study.citation.journal_page should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'journal_page', 'Column drh_stateful_research_study.citation.journal_page should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'journal_page', 'Column drh_stateful_research_study.citation.journal_page should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'publication_date', 'Column drh_stateful_research_study.citation.publication_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'publication_date', 'date', 'Column drh_stateful_research_study.citation.publication_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'publication_date', 'Column drh_stateful_research_study.citation.publication_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'publication_date', 'Column drh_stateful_research_study.citation.publication_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'rec_status_id', 'Column drh_stateful_research_study.citation.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.citation.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'citation', 'rec_status_id', 'Column drh_stateful_research_study.citation.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'rec_status_id', 'Column drh_stateful_research_study.citation.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'study_id', 'Column drh_stateful_research_study.citation.study_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'study_id', 'text', 'Column drh_stateful_research_study.citation.study_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'citation', 'study_id', 'Column drh_stateful_research_study.citation.study_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'study_id', 'Column drh_stateful_research_study.citation.study_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'created_at', 'Column drh_stateful_research_study.citation.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'created_at', 'Column drh_stateful_research_study.citation.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'citation', 'created_at', 'Column drh_stateful_research_study.citation.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'citation', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.citation.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'created_by', 'Column drh_stateful_research_study.citation.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'created_by', 'text', 'Column drh_stateful_research_study.citation.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'created_by', 'Column drh_stateful_research_study.citation.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'citation', 'created_by', 'Column drh_stateful_research_study.citation.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'citation', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.citation.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'updated_at', 'Column drh_stateful_research_study.citation.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'updated_at', 'Column drh_stateful_research_study.citation.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'updated_at', 'Column drh_stateful_research_study.citation.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'updated_by', 'Column drh_stateful_research_study.citation.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'updated_by', 'text', 'Column drh_stateful_research_study.citation.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'updated_by', 'Column drh_stateful_research_study.citation.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'updated_by', 'Column drh_stateful_research_study.citation.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'deleted_at', 'Column drh_stateful_research_study.citation.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.citation.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'deleted_at', 'Column drh_stateful_research_study.citation.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'deleted_at', 'Column drh_stateful_research_study.citation.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'citation', 'deleted_by', 'Column drh_stateful_research_study.citation.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'citation', 'deleted_by', 'text', 'Column drh_stateful_research_study.citation.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'citation', 'deleted_by', 'Column drh_stateful_research_study.citation.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'citation', 'deleted_by', 'Column drh_stateful_research_study.citation.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
