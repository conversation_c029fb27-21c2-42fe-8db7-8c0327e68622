SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(77);

SELECT has_table(
    'drh_stateful_research_study', 'device',
    'Should have table drh_stateful_research_study.device'
);

SELECT has_pk(
    'drh_stateful_research_study', 'device',
    'Table drh_stateful_research_study.device should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'device'::name, ARRAY[
    'id'::name,
    'identifier'::name,
    'manufacturer'::name,
    'serial_number'::name,
    'device_name'::name,
    'status'::name,
    'device_type'::name,
    'udi_carrier'::name,
    'manufacture_date'::name,
    'expiration_date'::name,
    'lot_number'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'device', 'id', 'Column drh_stateful_research_study.device.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'id', 'text', 'Column drh_stateful_research_study.device.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'device', 'id', 'Column drh_stateful_research_study.device.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'id', 'Column drh_stateful_research_study.device.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'identifier', 'Column drh_stateful_research_study.device.identifier should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'identifier', 'character varying(255)', 'Column drh_stateful_research_study.device.identifier should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'identifier', 'Column drh_stateful_research_study.device.identifier should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'identifier', 'Column drh_stateful_research_study.device.identifier should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'manufacturer', 'Column drh_stateful_research_study.device.manufacturer should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'manufacturer', 'character varying(255)', 'Column drh_stateful_research_study.device.manufacturer should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'device', 'manufacturer', 'Column drh_stateful_research_study.device.manufacturer should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'manufacturer', 'Column drh_stateful_research_study.device.manufacturer should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'serial_number', 'Column drh_stateful_research_study.device.serial_number should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'serial_number', 'character varying(255)', 'Column drh_stateful_research_study.device.serial_number should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'serial_number', 'Column drh_stateful_research_study.device.serial_number should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'serial_number', 'Column drh_stateful_research_study.device.serial_number should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'device_name', 'Column drh_stateful_research_study.device.device_name should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'device_name', 'character varying(255)', 'Column drh_stateful_research_study.device.device_name should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'device', 'device_name', 'Column drh_stateful_research_study.device.device_name should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'device_name', 'Column drh_stateful_research_study.device.device_name should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'status', 'Column drh_stateful_research_study.device.status should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'status', 'character varying(50)', 'Column drh_stateful_research_study.device.status should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'device', 'status', 'Column drh_stateful_research_study.device.status should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'status', 'Column drh_stateful_research_study.device.status should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'device_type', 'Column drh_stateful_research_study.device.device_type should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'device_type', 'character varying(100)', 'Column drh_stateful_research_study.device.device_type should be type character varying(100)');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'device_type', 'Column drh_stateful_research_study.device.device_type should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'device_type', 'Column drh_stateful_research_study.device.device_type should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'udi_carrier', 'Column drh_stateful_research_study.device.udi_carrier should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'udi_carrier', 'character varying(255)', 'Column drh_stateful_research_study.device.udi_carrier should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'udi_carrier', 'Column drh_stateful_research_study.device.udi_carrier should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'udi_carrier', 'Column drh_stateful_research_study.device.udi_carrier should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'manufacture_date', 'Column drh_stateful_research_study.device.manufacture_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'manufacture_date', 'date', 'Column drh_stateful_research_study.device.manufacture_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'manufacture_date', 'Column drh_stateful_research_study.device.manufacture_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'manufacture_date', 'Column drh_stateful_research_study.device.manufacture_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'expiration_date', 'Column drh_stateful_research_study.device.expiration_date should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'expiration_date', 'date', 'Column drh_stateful_research_study.device.expiration_date should be type date');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'expiration_date', 'Column drh_stateful_research_study.device.expiration_date should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'expiration_date', 'Column drh_stateful_research_study.device.expiration_date should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'lot_number', 'Column drh_stateful_research_study.device.lot_number should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'lot_number', 'character varying(255)', 'Column drh_stateful_research_study.device.lot_number should be type character varying(255)');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'lot_number', 'Column drh_stateful_research_study.device.lot_number should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'lot_number', 'Column drh_stateful_research_study.device.lot_number should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'rec_status_id', 'Column drh_stateful_research_study.device.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.device.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'device', 'rec_status_id', 'Column drh_stateful_research_study.device.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'rec_status_id', 'Column drh_stateful_research_study.device.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'created_at', 'Column drh_stateful_research_study.device.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.device.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'created_at', 'Column drh_stateful_research_study.device.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'device', 'created_at', 'Column drh_stateful_research_study.device.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'device', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.device.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'device', 'created_by', 'Column drh_stateful_research_study.device.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'created_by', 'text', 'Column drh_stateful_research_study.device.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'created_by', 'Column drh_stateful_research_study.device.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'device', 'created_by', 'Column drh_stateful_research_study.device.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'device', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.device.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'device', 'updated_at', 'Column drh_stateful_research_study.device.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.device.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'updated_at', 'Column drh_stateful_research_study.device.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'updated_at', 'Column drh_stateful_research_study.device.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'updated_by', 'Column drh_stateful_research_study.device.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'updated_by', 'text', 'Column drh_stateful_research_study.device.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'updated_by', 'Column drh_stateful_research_study.device.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'updated_by', 'Column drh_stateful_research_study.device.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'deleted_at', 'Column drh_stateful_research_study.device.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.device.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'deleted_at', 'Column drh_stateful_research_study.device.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'deleted_at', 'Column drh_stateful_research_study.device.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'device', 'deleted_by', 'Column drh_stateful_research_study.device.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'device', 'deleted_by', 'text', 'Column drh_stateful_research_study.device.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'device', 'deleted_by', 'Column drh_stateful_research_study.device.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'device', 'deleted_by', 'Column drh_stateful_research_study.device.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
