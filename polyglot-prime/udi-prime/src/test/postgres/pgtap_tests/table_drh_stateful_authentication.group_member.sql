SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(57);

SELECT has_table(
    'drh_stateful_authentication', 'group_member',
    'Should have table drh_stateful_authentication.group_member'
);

SELECT has_pk(
    'drh_stateful_authentication', 'group_member',
    'Table drh_stateful_authentication.group_member should have a primary key'
);

SELECT columns_are('drh_stateful_authentication'::name, 'group_member'::name, ARRAY[
    'group_member_id'::name,
    'group_type_id'::name,
    'user_id'::name,
    'member_party_id'::name,
    'period_start'::name,
    'period_end'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'group_member_id', 'Column drh_stateful_authentication.group_member.group_member_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'group_member_id', 'text', 'Column drh_stateful_authentication.group_member.group_member_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'group_member', 'group_member_id', 'Column drh_stateful_authentication.group_member.group_member_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'group_member_id', 'Column drh_stateful_authentication.group_member.group_member_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'group_type_id', 'Column drh_stateful_authentication.group_member.group_type_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'group_type_id', 'text', 'Column drh_stateful_authentication.group_member.group_type_id should be type text');
SELECT col_not_null(     'drh_stateful_authentication', 'group_member', 'group_type_id', 'Column drh_stateful_authentication.group_member.group_type_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'group_type_id', 'Column drh_stateful_authentication.group_member.group_type_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'user_id', 'Column drh_stateful_authentication.group_member.user_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'user_id', 'text', 'Column drh_stateful_authentication.group_member.user_id should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'user_id', 'Column drh_stateful_authentication.group_member.user_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'user_id', 'Column drh_stateful_authentication.group_member.user_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'member_party_id', 'Column drh_stateful_authentication.group_member.member_party_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'member_party_id', 'text', 'Column drh_stateful_authentication.group_member.member_party_id should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'member_party_id', 'Column drh_stateful_authentication.group_member.member_party_id should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'member_party_id', 'Column drh_stateful_authentication.group_member.member_party_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'period_start', 'Column drh_stateful_authentication.group_member.period_start should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'period_start', 'date', 'Column drh_stateful_authentication.group_member.period_start should be type date');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'period_start', 'Column drh_stateful_authentication.group_member.period_start should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'period_start', 'Column drh_stateful_authentication.group_member.period_start should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'period_end', 'Column drh_stateful_authentication.group_member.period_end should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'period_end', 'date', 'Column drh_stateful_authentication.group_member.period_end should be type date');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'period_end', 'Column drh_stateful_authentication.group_member.period_end should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'period_end', 'Column drh_stateful_authentication.group_member.period_end should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'rec_status_id', 'Column drh_stateful_authentication.group_member.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'rec_status_id', 'integer', 'Column drh_stateful_authentication.group_member.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_authentication', 'group_member', 'rec_status_id', 'Column drh_stateful_authentication.group_member.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'rec_status_id', 'Column drh_stateful_authentication.group_member.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'created_at', 'Column drh_stateful_authentication.group_member.created_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'created_at', 'timestamp with time zone', 'Column drh_stateful_authentication.group_member.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'created_at', 'Column drh_stateful_authentication.group_member.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'group_member', 'created_at', 'Column drh_stateful_authentication.group_member.created_at should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'group_member', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_authentication.group_member.created_at default is');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'created_by', 'Column drh_stateful_authentication.group_member.created_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'created_by', 'text', 'Column drh_stateful_authentication.group_member.created_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'created_by', 'Column drh_stateful_authentication.group_member.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_authentication', 'group_member', 'created_by', 'Column drh_stateful_authentication.group_member.created_by should have a default');
SELECT col_default_is(   'drh_stateful_authentication', 'group_member', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_authentication.group_member.created_by default is');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'updated_at', 'Column drh_stateful_authentication.group_member.updated_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_authentication.group_member.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'updated_at', 'Column drh_stateful_authentication.group_member.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'updated_at', 'Column drh_stateful_authentication.group_member.updated_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'updated_by', 'Column drh_stateful_authentication.group_member.updated_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'updated_by', 'text', 'Column drh_stateful_authentication.group_member.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'updated_by', 'Column drh_stateful_authentication.group_member.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'updated_by', 'Column drh_stateful_authentication.group_member.updated_by should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'deleted_at', 'Column drh_stateful_authentication.group_member.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_authentication.group_member.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'deleted_at', 'Column drh_stateful_authentication.group_member.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'deleted_at', 'Column drh_stateful_authentication.group_member.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_authentication', 'group_member', 'deleted_by', 'Column drh_stateful_authentication.group_member.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_authentication', 'group_member', 'deleted_by', 'text', 'Column drh_stateful_authentication.group_member.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_authentication', 'group_member', 'deleted_by', 'Column drh_stateful_authentication.group_member.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_authentication', 'group_member', 'deleted_by', 'Column drh_stateful_authentication.group_member.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
