SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(19);

SELECT has_table(
    'drh_stateful_db_import_migration', 'file_meta_ingest_data',
    'Should have table drh_stateful_db_import_migration.file_meta_ingest_data'
);

SELECT hasnt_pk(
    'drh_stateful_db_import_migration', 'file_meta_ingest_data',
    'Table drh_stateful_db_import_migration.file_meta_ingest_data should have a primary key'
);

SELECT columns_are('drh_stateful_db_import_migration'::name, 'file_meta_ingest_data'::name, ARRAY[
    'db_file_id'::name,
    'participant_display_id'::name,
    'file_meta_data'::name,
    'cgm_data'::name
]);

SELECT has_column(       'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'db_file_id', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.db_file_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'db_file_id', 'text', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.db_file_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'db_file_id', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.db_file_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'file_meta_ingest_data', 'db_file_id', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.db_file_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'participant_display_id', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.participant_display_id should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'participant_display_id', 'text', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.participant_display_id should be type text');
SELECT col_not_null(     'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'participant_display_id', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.participant_display_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'file_meta_ingest_data', 'participant_display_id', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.participant_display_id should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'file_meta_data', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.file_meta_data should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'file_meta_data', 'text', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.file_meta_data should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'file_meta_data', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.file_meta_data should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'file_meta_ingest_data', 'file_meta_data', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.file_meta_data should not have a default');

SELECT has_column(       'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'cgm_data', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.cgm_data should exist');
SELECT col_type_is(      'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'cgm_data', 'text', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.cgm_data should be type text');
SELECT col_is_null(      'drh_stateful_db_import_migration', 'file_meta_ingest_data', 'cgm_data', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.cgm_data should allow NULL');
SELECT col_hasnt_default('drh_stateful_db_import_migration', 'file_meta_ingest_data', 'cgm_data', 'Column drh_stateful_db_import_migration.file_meta_ingest_data.cgm_data should not have a default');

SELECT * FROM finish();
ROLLBACK;
