SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(81);

SELECT has_table(
    'drh_stateful_research_study', 'goal',
    'Should have table drh_stateful_research_study.goal'
);

SELECT has_pk(
    'drh_stateful_research_study', 'goal',
    'Table drh_stateful_research_study.goal should have a primary key'
);

SELECT columns_are('drh_stateful_research_study'::name, 'goal'::name, ARRAY[
    'id'::name,
    'plan_definition_id'::name,
    'identifier'::name,
    'category'::name,
    'description'::name,
    'priority'::name,
    'start'::name,
    'conditions_addressed'::name,
    'outcome_code'::name,
    'status_id'::name,
    'status_reason'::name,
    'tenant_id'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_research_study', 'goal', 'id', 'Column drh_stateful_research_study.goal.id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'id', 'text', 'Column drh_stateful_research_study.goal.id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'id', 'Column drh_stateful_research_study.goal.id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'id', 'Column drh_stateful_research_study.goal.id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'plan_definition_id', 'Column drh_stateful_research_study.goal.plan_definition_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'plan_definition_id', 'text', 'Column drh_stateful_research_study.goal.plan_definition_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'plan_definition_id', 'Column drh_stateful_research_study.goal.plan_definition_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'plan_definition_id', 'Column drh_stateful_research_study.goal.plan_definition_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'identifier', 'Column drh_stateful_research_study.goal.identifier should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'identifier', 'character varying(255)', 'Column drh_stateful_research_study.goal.identifier should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'identifier', 'Column drh_stateful_research_study.goal.identifier should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'identifier', 'Column drh_stateful_research_study.goal.identifier should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'category', 'Column drh_stateful_research_study.goal.category should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'category', 'character varying(100)', 'Column drh_stateful_research_study.goal.category should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'category', 'Column drh_stateful_research_study.goal.category should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'category', 'Column drh_stateful_research_study.goal.category should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'description', 'Column drh_stateful_research_study.goal.description should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'description', 'character varying(255)', 'Column drh_stateful_research_study.goal.description should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'description', 'Column drh_stateful_research_study.goal.description should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'description', 'Column drh_stateful_research_study.goal.description should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'priority', 'Column drh_stateful_research_study.goal.priority should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'priority', 'character varying(50)', 'Column drh_stateful_research_study.goal.priority should be type character varying(50)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'priority', 'Column drh_stateful_research_study.goal.priority should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'priority', 'Column drh_stateful_research_study.goal.priority should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'start', 'Column drh_stateful_research_study.goal.start should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'start', 'character varying(100)', 'Column drh_stateful_research_study.goal.start should be type character varying(100)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'start', 'Column drh_stateful_research_study.goal.start should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'start', 'Column drh_stateful_research_study.goal.start should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'conditions_addressed', 'Column drh_stateful_research_study.goal.conditions_addressed should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'conditions_addressed', 'character varying(255)', 'Column drh_stateful_research_study.goal.conditions_addressed should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'conditions_addressed', 'Column drh_stateful_research_study.goal.conditions_addressed should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'conditions_addressed', 'Column drh_stateful_research_study.goal.conditions_addressed should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'outcome_code', 'Column drh_stateful_research_study.goal.outcome_code should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'outcome_code', 'character varying(255)', 'Column drh_stateful_research_study.goal.outcome_code should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'outcome_code', 'Column drh_stateful_research_study.goal.outcome_code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'outcome_code', 'Column drh_stateful_research_study.goal.outcome_code should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'status_id', 'Column drh_stateful_research_study.goal.status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'status_id', 'integer', 'Column drh_stateful_research_study.goal.status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'status_id', 'Column drh_stateful_research_study.goal.status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'status_id', 'Column drh_stateful_research_study.goal.status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'status_reason', 'Column drh_stateful_research_study.goal.status_reason should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'status_reason', 'character varying(255)', 'Column drh_stateful_research_study.goal.status_reason should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'status_reason', 'Column drh_stateful_research_study.goal.status_reason should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'status_reason', 'Column drh_stateful_research_study.goal.status_reason should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'tenant_id', 'Column drh_stateful_research_study.goal.tenant_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'tenant_id', 'text', 'Column drh_stateful_research_study.goal.tenant_id should be type text');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'tenant_id', 'Column drh_stateful_research_study.goal.tenant_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'tenant_id', 'Column drh_stateful_research_study.goal.tenant_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'rec_status_id', 'Column drh_stateful_research_study.goal.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'rec_status_id', 'integer', 'Column drh_stateful_research_study.goal.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_research_study', 'goal', 'rec_status_id', 'Column drh_stateful_research_study.goal.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'rec_status_id', 'Column drh_stateful_research_study.goal.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'created_at', 'Column drh_stateful_research_study.goal.created_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'created_at', 'timestamp with time zone', 'Column drh_stateful_research_study.goal.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'goal', 'created_at', 'Column drh_stateful_research_study.goal.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'goal', 'created_at', 'Column drh_stateful_research_study.goal.created_at should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'goal', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_research_study.goal.created_at default is');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'created_by', 'Column drh_stateful_research_study.goal.created_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'created_by', 'text', 'Column drh_stateful_research_study.goal.created_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'goal', 'created_by', 'Column drh_stateful_research_study.goal.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_research_study', 'goal', 'created_by', 'Column drh_stateful_research_study.goal.created_by should have a default');
SELECT col_default_is(   'drh_stateful_research_study', 'goal', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_research_study.goal.created_by default is');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'updated_at', 'Column drh_stateful_research_study.goal.updated_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_research_study.goal.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'goal', 'updated_at', 'Column drh_stateful_research_study.goal.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'updated_at', 'Column drh_stateful_research_study.goal.updated_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'updated_by', 'Column drh_stateful_research_study.goal.updated_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'updated_by', 'text', 'Column drh_stateful_research_study.goal.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'goal', 'updated_by', 'Column drh_stateful_research_study.goal.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'updated_by', 'Column drh_stateful_research_study.goal.updated_by should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'deleted_at', 'Column drh_stateful_research_study.goal.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_research_study.goal.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_research_study', 'goal', 'deleted_at', 'Column drh_stateful_research_study.goal.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'deleted_at', 'Column drh_stateful_research_study.goal.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_research_study', 'goal', 'deleted_by', 'Column drh_stateful_research_study.goal.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_research_study', 'goal', 'deleted_by', 'text', 'Column drh_stateful_research_study.goal.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_research_study', 'goal', 'deleted_by', 'Column drh_stateful_research_study.goal.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_research_study', 'goal', 'deleted_by', 'Column drh_stateful_research_study.goal.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
