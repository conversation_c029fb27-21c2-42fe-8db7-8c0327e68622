SET client_encoding = 'UTF-8';
SET client_min_messages = warning;
CREATE EXTENSION IF NOT EXISTS pgtap;
RESET client_min_messages;

BEGIN;
SELECT plan(50);

SELECT has_table(
    'drh_stateful_master', 'research_study_condition',
    'Should have table drh_stateful_master.research_study_condition'
);

SELECT has_pk(
    'drh_stateful_master', 'research_study_condition',
    'Table drh_stateful_master.research_study_condition should have a primary key'
);

SELECT columns_are('drh_stateful_master'::name, 'research_study_condition'::name, ARRAY[
    'id'::name,
    'coding_system'::name,
    'code'::name,
    'display'::name,
    'rec_status_id'::name,
    'created_at'::name,
    'created_by'::name,
    'updated_at'::name,
    'updated_by'::name,
    'deleted_at'::name,
    'deleted_by'::name
]);

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'id', 'Column drh_stateful_master.research_study_condition.id should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'id', 'integer', 'Column drh_stateful_master.research_study_condition.id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'research_study_condition', 'id', 'Column drh_stateful_master.research_study_condition.id should be NOT NULL');
SELECT col_has_default(  'drh_stateful_master', 'research_study_condition', 'id', 'Column drh_stateful_master.research_study_condition.id should have a default');
SELECT col_default_is(   'drh_stateful_master', 'research_study_condition', 'id', 'nextval(''drh_stateful_master.research_study_condition_id_seq''::regclass)', 'Column drh_stateful_master.research_study_condition.id default is');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'coding_system', 'Column drh_stateful_master.research_study_condition.coding_system should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'coding_system', 'character varying(255)', 'Column drh_stateful_master.research_study_condition.coding_system should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'research_study_condition', 'coding_system', 'Column drh_stateful_master.research_study_condition.coding_system should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'coding_system', 'Column drh_stateful_master.research_study_condition.coding_system should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'code', 'Column drh_stateful_master.research_study_condition.code should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'code', 'text', 'Column drh_stateful_master.research_study_condition.code should be type text');
SELECT col_not_null(     'drh_stateful_master', 'research_study_condition', 'code', 'Column drh_stateful_master.research_study_condition.code should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'code', 'Column drh_stateful_master.research_study_condition.code should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'display', 'Column drh_stateful_master.research_study_condition.display should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'display', 'character varying(255)', 'Column drh_stateful_master.research_study_condition.display should be type character varying(255)');
SELECT col_not_null(     'drh_stateful_master', 'research_study_condition', 'display', 'Column drh_stateful_master.research_study_condition.display should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'display', 'Column drh_stateful_master.research_study_condition.display should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'rec_status_id', 'Column drh_stateful_master.research_study_condition.rec_status_id should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'rec_status_id', 'integer', 'Column drh_stateful_master.research_study_condition.rec_status_id should be type integer');
SELECT col_not_null(     'drh_stateful_master', 'research_study_condition', 'rec_status_id', 'Column drh_stateful_master.research_study_condition.rec_status_id should be NOT NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'rec_status_id', 'Column drh_stateful_master.research_study_condition.rec_status_id should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'created_at', 'Column drh_stateful_master.research_study_condition.created_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'created_at', 'timestamp with time zone', 'Column drh_stateful_master.research_study_condition.created_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'research_study_condition', 'created_at', 'Column drh_stateful_master.research_study_condition.created_at should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'research_study_condition', 'created_at', 'Column drh_stateful_master.research_study_condition.created_at should have a default');
SELECT col_default_is(   'drh_stateful_master', 'research_study_condition', 'created_at', 'CURRENT_TIMESTAMP', 'Column drh_stateful_master.research_study_condition.created_at default is');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'created_by', 'Column drh_stateful_master.research_study_condition.created_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'created_by', 'text', 'Column drh_stateful_master.research_study_condition.created_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'research_study_condition', 'created_by', 'Column drh_stateful_master.research_study_condition.created_by should allow NULL');
SELECT col_has_default(  'drh_stateful_master', 'research_study_condition', 'created_by', 'Column drh_stateful_master.research_study_condition.created_by should have a default');
SELECT col_default_is(   'drh_stateful_master', 'research_study_condition', 'created_by', 'UNKNOWN'::text, 'Column drh_stateful_master.research_study_condition.created_by default is');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'updated_at', 'Column drh_stateful_master.research_study_condition.updated_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'updated_at', 'timestamp with time zone', 'Column drh_stateful_master.research_study_condition.updated_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'research_study_condition', 'updated_at', 'Column drh_stateful_master.research_study_condition.updated_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'updated_at', 'Column drh_stateful_master.research_study_condition.updated_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'updated_by', 'Column drh_stateful_master.research_study_condition.updated_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'updated_by', 'text', 'Column drh_stateful_master.research_study_condition.updated_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'research_study_condition', 'updated_by', 'Column drh_stateful_master.research_study_condition.updated_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'updated_by', 'Column drh_stateful_master.research_study_condition.updated_by should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'deleted_at', 'Column drh_stateful_master.research_study_condition.deleted_at should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'deleted_at', 'timestamp with time zone', 'Column drh_stateful_master.research_study_condition.deleted_at should be type timestamp with time zone');
SELECT col_is_null(      'drh_stateful_master', 'research_study_condition', 'deleted_at', 'Column drh_stateful_master.research_study_condition.deleted_at should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'deleted_at', 'Column drh_stateful_master.research_study_condition.deleted_at should not have a default');

SELECT has_column(       'drh_stateful_master', 'research_study_condition', 'deleted_by', 'Column drh_stateful_master.research_study_condition.deleted_by should exist');
SELECT col_type_is(      'drh_stateful_master', 'research_study_condition', 'deleted_by', 'text', 'Column drh_stateful_master.research_study_condition.deleted_by should be type text');
SELECT col_is_null(      'drh_stateful_master', 'research_study_condition', 'deleted_by', 'Column drh_stateful_master.research_study_condition.deleted_by should allow NULL');
SELECT col_hasnt_default('drh_stateful_master', 'research_study_condition', 'deleted_by', 'Column drh_stateful_master.research_study_condition.deleted_by should not have a default');

SELECT * FROM finish();
ROLLBACK;
