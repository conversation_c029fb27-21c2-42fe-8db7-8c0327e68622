#!/usr/bin/env -S deno run --allow-all

/**
 * This TypeScript file implements a SQL migration feature for PostgreSQL databases using Deno.
 * It provides methods for defining and executing migrations.
 *
 * @module Information_Schema_Lifecycle_Management_Migration
 */
import * as ws from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/lib/universal/whitespace.ts";
import * as SQLa from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/mod.ts";
import * as udm from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/udm/mod.ts";
import * as mod from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/dialect/pg/routine.ts";
import * as tmpl from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/emit/mod.ts";
import * as typ from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/typical/mod.ts";
import { pgSQLa } from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.13.34/pattern/pgdcp/deps.ts";
import * as srch from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.9.2/render/dialect/pg/search_path.ts";

// deconstructed modules provide convenient access to internal imports
//const { typical: typ, typical: { SQLa, ws } } = typ;

type EmitContext = typ.typical.SQLa.SqlEmitContext;

interface MigrationVersion {
  readonly description: string;
  readonly dateTime: Date;
}

enum StateStatus {
  STATEFUL = "_stateful_",
  IDEMPOTENT = "_idempotent_",
}

const prependMigrateSPText = "migrate_";
const appendMigrateUndoSPText = "_undo";
const appendMigrateStatusFnText = "_status";

const ingressSchema = SQLa.sqlSchemaDefn("drh_udi_ingress", {
  isIdempotent: true,
});

const assuranceSchema = SQLa.sqlSchemaDefn("drh_udi_assurance", {
  isIdempotent: true,
});

const diagnosticsSchema = SQLa.sqlSchemaDefn("drh_udi_diagnostics", {
  isIdempotent: true,
});

export const researchSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_research_study",
  {
    isIdempotent: true,
  },
);

export const drhPartySchema = SQLa.sqlSchemaDefn("drh_stateful_party", {
  isIdempotent: true,
});

export const rawSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_raw_observation",
  {
    isIdempotent: true,
  },
);

export const rawDataSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_raw_data",
  {
    isIdempotent: true,
  },
);

export const activitySchema = SQLa.sqlSchemaDefn(
  "drh_stateful_activity_audit",
  {
    isIdempotent: true,
  },
);

export const aiSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_ai_insights",
  {
    isIdempotent: true,
  },
);

export const partySchema = SQLa.sqlSchemaDefn(
  "drh_stateful_party",
  {
    isIdempotent: true,
  },
);

export const authSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_authentication",
  {
    isIdempotent: true,
  },
);

export const masterSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_master",
  {
    isIdempotent: true,
  },
);

export const statelessStudySchema = SQLa.sqlSchemaDefn(
  "drh_stateless_research_study",
  {
    isIdempotent: true,
  },
);

export const statelessUtilSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_util",
  {
    isIdempotent: true,
  },
);

export const statelessMasterSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_master",
  {
    isIdempotent: true,
  },
);

export const statelessRawObservation = SQLa.sqlSchemaDefn(
  "drh_stateless_raw_observation",
  {
    isIdempotent: true,
  },
);

export const statelessAuthentication = SQLa.sqlSchemaDefn(
  "drh_stateless_authentication",
  {
    isIdempotent: true,
  },
);

export const dbImportMigrateSchema = SQLa.sqlSchemaDefn(
  "drh_stateful_db_import_migration",
  {
    isIdempotent: true,
  },
);

export const statelessRawDataSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_raw_data",
  {
    isIdempotent: true,
  },
);

export const statelessDBMigrateSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_db_import_migration",
  {
    isIdempotent: true,
  },
);

export const cronSchema = SQLa.sqlSchemaDefn(
  "drh_udi_cron",
  {
    isIdempotent: true,
  },
);

export const statelessactivitySchema = SQLa.sqlSchemaDefn(
  "drh_stateless_activity_audit",
  {
    isIdempotent: true,
  },
);

export const statelessVannaSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_vanna",
  {
    isIdempotent: true,
  },
);

export const statelessAiSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_ai_insights",
  {
    isIdempotent: true,
  },
);

export const udmts = typ.governedTemplateState<EmitContext>({
  defaultNS: ingressSchema,
});

const searchPath = srch.pgSearchPath<
  typeof drhPartySchema.sqlNamespace,
  typeof gm.ctx
>(drhPartySchema);

export const allReferenceTables: (
  & SQLa.TableDefinition<
    Any,
    udm.EmitContext,
    typ.TypicalDomainQS
  >
  & typ.EnumTableDefn<udm.EmitContext>
  & { searchPath }
)[] = [
  udm.execCtx,
  udm.partyType,
  udm.partyRelationType,
  udm.partyIdentifierType,
  udm.organizationRoleType,
  udm.contactType,
  //udm.personType,
  udm.genderType,
  udm.partyRoleType,
  udm.sexType,
  udm.party,
  udm.partyRole,
  udm.partyRelation,
  //udm.person,
  udm.partyIdentifier,
  //udm.contactElectronic,
  //udm.contactLand,
  //udm.organization,
  //udm.organizationRole,
  // rdm.recordStatus,
  // rdm.entitytype,
];

const gts = typ.governedTemplateState<
  typ.TypicalDomainQS,
  typ.TypicalDomainsQS,
  EmitContext
>();
const gm = typ.governedModel<
  typ.TypicalDomainQS,
  typ.TypicalDomainsQS,
  EmitContext
>(gts.ddlOptions);

const {
  text,
  textNullable,
  integer,
  date,
  varChar,
  varCharNullable,
  integerNullable,
  boolean,
  dateTimeNullable,
  dateNullable,
  ulid,
  Serial,
} = gm.domains;
const { autoIncPrimaryKey: autoIncPK, ulidPrimaryKey: primaryKey } = gm.keys;

const ctx = SQLa.typicalSqlEmitContext({
  sqlDialect: SQLa.postgreSqlDialect(),
});

const infoSchemaLifecycle = SQLa.sqlSchemaDefn("info_schema_lifecycle", {
  isIdempotent: true,
});

// Specify the file path for the er diagram plantUML...
const filePath = "er-diagram.puml";
//export const {ulidPrimaryKey: primaryKey } = typ.keys;

enum EnumFileExchangeProtocol {
  SFTP = "SFTP",
  S3 = "S3",
}

const fileExchangeProtocol = typ.textEnumTable(
  "file_exchange_protocol",
  EnumFileExchangeProtocol,
  { isIdempotent: true, sqlNS: ingressSchema },
);

const testMigrateDependencies = [
  "../../../../test/postgres/ingestion-center/003-idempotent-function-unit-test.psql",
  //"../../../../test/postgres/ingestion-center/fixtures.sql",
] as const;

// Read SQL queries from files
const testMigrateDependenciesWithPgtap = [
  ...testMigrateDependencies,
  "../../../../test/postgres/ingestion-center/suite.pgtap.psql",
] as const;

const partyRoleInsertion = udm.partyRole.insertDML(
  [
    {
      party_role_id: "01H8ZQG4T88FZXE5B1YXEPM2DQ",
      code: "CUSTOMER",
      value: "Customer",
    },
    {
      party_role_id: "01H8ZQG8TKHKZWQ6T3CW5Y7MZF",
      code: "VENDOR",
      value: "Vendor",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

const partyIdentifierTypeInsertion = udm.partyIdentifierType.insertDML(
  [
    {
      party_identifier_type_id: "01H8ZQGBXP7V6X1CJ6TZY1W5PE",
      code: "UUID",
      value: "UUID",
    },
    {
      party_identifier_type_id: "01H8ZQGDSKH9XZF2EPFCE3QWYQ",
      code: "DRIVING_LICENSE",
      value: "Driving License",
    },
    {
      party_identifier_type_id: "01H8ZQGFYR5H5B2N94TKXEJ0NQ",
      code: "PASSPORT",
      value: "Passport",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

const partyTypeInsertion = udm.partyType.insertDML(
  [
    {
      party_type_id: "01H8ZQKH3FZPY9X8W6CJ7BMQ0N",
      code: "PERSON",
      value: "person",
      //value: udm.partyType.seedEnum.PERSON,
    },
    {
      party_type_id: "01H8ZQKJ1XBYW6RM9C8F5ZQPTN",
      code: "ORGANIZATION",
      value: "organization",
      //value: udm.partyType.seedEnum.ORGANIZATION,
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

const contactTypeInsertion = udm.contactType.insertDML(
  [
    {
      contact_type_id: "01H8ZQGJW6R7P9YN7ZB6X0T2TF",
      code: "HOME_ADDRESS",
      value: "Home Address",
    },
    {
      contact_type_id: "01H8ZQGMPXZFWBK3T1JCQV1FQE",
      code: "OFFICIAL_ADDRESS",
      value: "Official Address",
    },
    {
      contact_type_id: "01H8ZQGPXHQFT4MRYC8YEZ0Q5H",
      code: "MOBILE_PHONE_NUMBER",
      value: "Mobile Phone Number",
    },
    {
      contact_type_id: "01H8ZQGT0J9FPF5NVW6TCXZEYN",
      code: "LAND_PHONE_NUMBER",
      value: "Land Phone Number",
    },
    {
      contact_type_id: "01H8ZQGX0C6QYVW4T8FRJBMKRT",
      code: "OFFICIAL_EMAIL",
      value: "Official Email",
    },
    {
      contact_type_id: "01H8ZQH04JTYV8R3X9WFZMKC5H",
      code: "PERSONAL_EMAIL",
      value: "Personal Email",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

const genderTypeInsertion = udm.genderType.insertDML(
  [
    {
      gender_type_id: "01H8ZQGJW6R7P9YN7ZB6X0T2TF",
      code: "MALE",
      value: "Male",
    },
    {
      gender_type_id: "01H8ZQGMPXZFWBK3T1JCQV1FQE",
      code: "FEMALE",
      value: "Female",
    },
    {
      gender_type_id: "01H8ZQGPXHQFT4MRYC8YEZ0Q5H",
      code: "OTHER",
      value: "Other",
    },
    {
      gender_type_id: "01H8ZQGT0J9FPF5NVW6TCXZEYN",
      code: "UNKNOWN",
      value: "Unknown",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

const organizationRoleTypeInsertion = udm.organizationRoleType.insertDML(
  [
    {
      organization_role_type_id: "01H8ZQH3PMQ5BY2T4XJ7ZVF61N",
      code: "PROJECT_MANAGER_TECHNOLOGY",
      value: "Project Manager Technology",
    },
    {
      organization_role_type_id: "01H8ZQH6WCQ3K9MBRZP8FYXT0E",
      code: "PROJECT_MANAGER_QUALITY",
      value: "Project Manager Quality",
    },
    {
      organization_role_type_id: "01H8ZQH9T9JQW6F2PYX1BZRMN4",
      code: "PROJECT_MANAGER_DEVOPS",
      value: "Project Manager DevOps",
    },
    {
      organization_role_type_id: "01H8ZQHD6FWRXP2TYZ0J8MB5KN",
      code: "ASSOCIATE_MANAGER_TECHNOLOGY",
      value: "Associated Manager Technology",
    },
    {
      organization_role_type_id: "01H8ZQHH06T3FXW9PBYZRJQ5KM",
      code: "ASSOCIATE_MANAGER_QUALITY",
      value: "Associate Manager Quality",
    },
    {
      organization_role_type_id: "01H8ZQHLTJ5WRX6PM2FYZN4CQ8",
      code: "ASSOCIATE_MANAGER_DEVOPS",
      value: "Associate Manager DevOps",
    },
    {
      organization_role_type_id: "01H8ZQHPX8MCJRW2BYZF5T0K91",
      code: "SENIOR_LEAD_SOFTWARE_ENGINEER_ARCHITECT",
      value: "Senior Lead Software Engineer Architect",
    },
    {
      organization_role_type_id: "01H8ZQHTY9FWRM3P2JCX0BZK7N",
      code: "LEAD_SOFTWARE_ENGINEER_ARCHITECT",
      value: "Lead Software Engineer Architect",
    },
    {
      organization_role_type_id: "01H8ZQHWQMTCFX9PR8BZ5YKJN2",
      code: "SENIOR_LEAD_SOFTWARE_QUALITY_ENGINEER",
      value: "Senior Lead Software DevOps Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJ10TYR9FXWZP8BK5MJNC",
      code: "LEAD_SOFTWARE_ENGINEER",
      value: "Lead Software Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJ4PM2FYC8TJ9XWR6BKZ0",
      code: "LEAD_SOFTWARE_QUALITY_ENGINEER",
      value: "Lead Software Quality Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJ7X6RWZBYFPM9C8KT1JN",
      code: "LEAD_SOFTWARE_DEVOPS_ENGINEER",
      value: "Lead Software DevOps Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJ9T8FRWPY6ZBKJMQC5XN",
      code: "LEAD_SYSTEM_NETWORK_ENGINEER",
      value: "Lead System Network Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJCJMZFWTPX6BK89RYCQ1",
      code: "SENIOR_SOFTWARE_ENGINEER",
      value: "Senior Software Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJFRPYC8ZWMKJTX5B69N4",
      code: "SENIOR_SOFTWARE_QUALITY_ENGINEER",
      value: "Senior Software Quality Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJHWZ8BT6PMXF5K9CYRJN",
      code: "SOFTWARE_QUALITY_ENGINEER",
      value: "Software Quality Engineer",
    },
    {
      organization_role_type_id: "01H8ZQJK6PBT9XJRWMCYFQ8KZN",
      code: "SECURITY_ENGINEER",
      value: "Security Engineer",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

export const partyRoleTypeInsertion = udm.partyRoleType.insertDML(
  [
    {
      party_role_type_id: "01H8ZQJNW9C5FYXTP8JR6MBZK1",
      code: "CLIENT",
      value: "Client",
    },
    {
      party_role_type_id: "01H8ZQJQZ8FYX5MBK6RWP9CJNT",
      code: "FINANCIAL_ADVISOR",
      value: "Financial Advisor",
    },
    {
      party_role_type_id: "01H8ZQJT5XRP8YMCZK9FW6BJQN",
      code: "BOARD_MEMBER",
      value: "Board member",
    },
    {
      party_role_type_id: "01H8ZQJW8KM5RYXZTF9P6CBQJN",
      code: "SUB_TENANT",
      value: "Sub Tenant",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

export const partyIdentifierInsertion = udm.partyIdentifierType.insertDML(
  [
    {
      party_identifier_type_id: "01H8ZQK10FZRP6XMWYCBK8TJQN",
      code: "PASSPORT",
      value: "Passport",
    },
    {
      party_identifier_type_id: "01H8ZQK4TZ9F6RYXPBKJWM8CQ5",
      code: "UUID",
      value: "UUID",
    },
    {
      party_identifier_type_id: "01H8ZQK7WMXRY5BTZP8F9CJQ6N",
      code: "DRIVING_LICENSE",
      value: "Driving License",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

export const partyRelationTypeInsertion = udm.partyRelationType.insertDML(
  [
    {
      party_relation_type_id: "01H8ZQKHW9FZRPX6YTMC8BJQ21",
      code: "ORGANIZATION_TO_ORGANIZATION",
      value: "ORGANIZATION_TO_ORGANIZATION",
    },
    {
      party_relation_type_id: "01H8ZQKF5XTYZ8RWPMC9BJ6QFN",
      code: "ORGANIZATION_TO_PERSON",
      value: "ORGANIZATION_TO_PERSON",
    },
  ],
  {
    onConflict: {
      SQL: () => `ON CONFLICT DO NOTHING`,
    },
  },
);

export const migrationInput: MigrationVersion = {
  description: "basic-infra",
  dateTime: new Date(2024, 6, 28, 13, 16),
};
function formatDateToCustomString(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth()).padStart(2, "0"); // Month is zero-based
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${year}_${month}_${day}_${hours}_${minutes}`;
}
// Function to read SQL from a list of .psql files
async function readSQLFiles(filePaths: readonly string[]): Promise<string[]> {
  const sqlContents = [];
  for (const filePath of filePaths) {
    try {
      const absolutePath = new URL(filePath, import.meta.url).pathname;
      const data = await Deno.readTextFile(absolutePath);
      sqlContents.push(data);
    } catch (err) {
      console.error(`Error reading file ${filePath}:`, err);
      throw err;
    }
  }
  return sqlContents;
}
export const migrateVersion = formatDateToCustomString(migrationInput.dateTime);

const testDependencies: string[] = [];

const testDependenciesSQL = await readSQLFiles(testMigrateDependencies);
for (const filePath of testMigrateDependenciesWithPgtap) {
  try {
    const absolutePath = import.meta.resolve(filePath);
    testDependencies.push(absolutePath);
  } catch (err) {
    console.error(`Error reading filepath ${filePath}:`, err);
    throw err;
  }
}

const migrateSP = pgSQLa.storedProcedure(
  prependMigrateSPText + "v" + migrateVersion + StateStatus.IDEMPOTENT +
    migrationInput.description,
  {},
  (name, args, _) =>
    pgSQLa.typedPlPgSqlBody(name, args, ctx, {
      autoBeginEnd: false,
    }),
  {
    embeddedStsOptions: SQLa.typicalSqlTextSupplierOptions(),
    autoBeginEnd: false,
    isIdempotent: true,
    sqlNS: infoSchemaLifecycle,
    headerBodySeparator: "$migrateVersionSP$",
  },
)`
    BEGIN

      ${researchSchema}
      ${drhPartySchema}
      ${searchPath}
      ${allReferenceTables}
      ${rawSchema}
      ${rawDataSchema}
      ${activitySchema}
      ${aiSchema}
      ${partySchema}
      ${authSchema}
      ${masterSchema}
      ${dbImportMigrateSchema}
      ${statelessStudySchema}
      ${statelessUtilSchema}
      ${statelessMasterSchema}
      ${assuranceSchema}

      ${organizationRoleTypeInsertion}
      ${partyRoleTypeInsertion}
      ${partyRoleInsertion}
      ${partyIdentifierTypeInsertion}
      ${partyIdentifierInsertion}
      ${contactTypeInsertion}
      ${partyRelationTypeInsertion}
      ${partyTypeInsertion}
      ${genderTypeInsertion}
      ${statelessRawObservation}
      ${statelessAuthentication}
      ${statelessRawDataSchema}
      ${statelessDBMigrateSchema}
      ${statelessactivitySchema}
      ${statelessVannaSchema}
      ${statelessAiSchema}

      CREATE EXTENSION IF NOT EXISTS pgtap SCHEMA ${assuranceSchema.sqlNamespace};


      ${testDependenciesSQL}



    END;

`;

const rollbackSP = pgSQLa.storedProcedure(
  prependMigrateSPText + "v" + migrateVersion + StateStatus.IDEMPOTENT +
    migrationInput.description + appendMigrateUndoSPText,
  {},
  (name, args, _) =>
    pgSQLa.typedPlPgSqlBody(name, args, ctx, {
      autoBeginEnd: false,
    }),
  {
    embeddedStsOptions: SQLa.typicalSqlTextSupplierOptions(),
    autoBeginEnd: false,
    isIdempotent: true,
    sqlNS: infoSchemaLifecycle,
    headerBodySeparator: "$migrateVersionUSP$",
  },
)`
    BEGIN
    -- Add any PostgreSQL you need either manually constructed or SQLa.
    -- Your code will be placed automatically into a ISLM rollback stored procedure.
    -- DROP table if exists "sample_schema".sample_table1;
    END;
  `;
const statusFn = pgSQLa.storedFunction(
  prependMigrateSPText + "v" + migrateVersion + StateStatus.IDEMPOTENT +
    migrationInput.description + appendMigrateStatusFnText,
  {},
  "integer",
  (name, args) =>
    pgSQLa.typedPlPgSqlBody(name, args, ctx, {
      autoBeginEnd: false,
    }),
  {
    embeddedStsOptions: SQLa.typicalSqlTextSupplierOptions(),
    autoBeginEnd: false,
    isIdempotent: true,
    sqlNS: infoSchemaLifecycle,
    headerBodySeparator: "$fnMigrateVersionStatus$",
  },
)`
    DECLARE
      status INTEGER := 0; -- Initialize status to 0 (not executed)
    BEGIN
      -- Add any PostgreSQL you need either manually constructed or SQLa.
      -- Your code will be placed automatically into a ISLM status stored function.
      -- All your checks must be idempotent and not have any side effects.
      -- Use information_schema and other introspection capabilities of PostgreSQL
      -- instead of manually checking. For example:

      -- IF EXISTS (
      --  SELECT FROM information_schema.columns
      --  WHERE table_name = 'sample_table1'
      -- ) THEN
      --  status := 1; -- Set status to 1 (already executed)
      -- END IF;
      RETURN status; -- Return the status

    END;
  `;

/**
 * Generates SQL Data Definition Language (DDL) for the migrations.
 *
 * @returns {string} The SQL DDL for migrations.
 */

function sqlDDLGenerateMigration() {
  return SQLa.SQL<EmitContext>(typ.ddlOptions)`
      \\set ON_ERROR_STOP on
    DO $$
      DECLARE
        table_count int;
        schema_count int;
      BEGIN

        -- Check if the required schemas exist
        SELECT count(*)
        INTO schema_count
        FROM information_schema.schemata
        WHERE schema_name IN ('info_schema_lifecycle', 'info_schema_lifecycle_assurance');

        -- If less than 3 schemas are found, raise an error
        IF schema_count < 2 THEN
            RAISE EXCEPTION 'One or more of the required schemas info_schema_lifecycle, info_schema_lifecycle_assurance are missing';
        END IF;
      END;
      $$;


    ${migrateSP}
    ${rollbackSP}
    ${statusFn}

    `;
}

export function generated() {
  const ctx = SQLa.typicalSqlEmitContext({
    sqlDialect: SQLa.postgreSqlDialect(),
  });
  const testDependencies: string[] = [];
  // for (const filePath of testMigrateDependenciesWithPgtap) {
  //   try {
  //     const absolutePath = import.meta.resolve(filePath);
  //     testDependencies.push(absolutePath);
  //   } catch (err) {
  //     console.error(`Error reading filepath ${filePath}:`, err);
  //     throw err;
  //   }
  // }

  // after this execution `ctx` will contain list of all tables which will be
  // passed into `dvts.pumlERD` below (ctx should only be used once)
  const driverGenerateMigrationSQL = ws.unindentWhitespace(
    sqlDDLGenerateMigration().SQL(ctx),
  );
  return {
    driverGenerateMigrationSQL,
    // pumlERD: dvts.pumlERD(ctx).content,
    destroySQL: ws.unindentWhitespace(`

      DROP SCHEMA IF EXISTS public CASCADE;

      DROP SCHEMA IF EXISTS ${ingressSchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${assuranceSchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${diagnosticsSchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessStudySchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessUtilSchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessMasterSchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessRawObservation.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessAuthentication.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessRawDataSchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessDBMigrateSchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessactivitySchema.sqlNamespace} cascade;
      DROP SCHEMA IF EXISTS ${statelessVannaSchema.sqlNamespace} cascade;

      DROP PROCEDURE IF EXISTS "${migrateSP.sqlNS?.sqlNamespace}"."${migrateSP.routineName}" CASCADE;
      DROP PROCEDURE IF EXISTS "${rollbackSP.sqlNS?.sqlNamespace}"."${rollbackSP.routineName}" CASCADE;
      DROP FUNCTION IF EXISTS "${statusFn.sqlNS?.sqlNamespace}"."${statusFn.routineName}" CASCADE;





      `),
    testDependencies,
  };
}
