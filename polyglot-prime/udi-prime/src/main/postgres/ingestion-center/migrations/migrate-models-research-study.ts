#!/usr/bin/env -S deno run --allow-all

/**
 * This TypeScript file implements a SQL migration feature for PostgreSQL databases using Deno.
 * It provides methods for defining and executing migrations.
 *
 * @module Information_Schema_Lifecycle_Management_Migration
 */

import * as ws from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/lib/universal/whitespace.ts";
import * as SQLa from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/mod.ts";
import * as typ from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/typical/mod.ts";
import * as udm from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/udm/mod.ts";
import { pgSQLa } from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.13.34/pattern/pgdcp/deps.ts";
import * as ddlTable from "./models-research-study.ts";
import * as diaPUML from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.13.34/render/diagram/plantuml-ie-notation.ts";
import {
    cronSchema,
    statelessAuthentication,
} from "./migrate-basic-infrastructure.ts";
// import * as migrate from "../../../../../../../../netspective-labs/sql-aide/pattern/postgres/migrate.ts";

//export const { gm, gts } = udm;

const gts = typ.governedTemplateState<
    typ.TypicalDomainQS,
    typ.TypicalDomainsQS,
    EmitContext
>();
const gm = typ.governedModel<
    typ.TypicalDomainQS,
    typ.TypicalDomainsQS,
    EmitContext
>(gts.ddlOptions);

type EmitContext = typ.typical.SQLa.SqlEmitContext;

interface MigrationVersion {
    readonly description: string;
    readonly dateTime: Date;
}

enum StateStatus {
    STATEFUL = "_stateful_",
    IDEMPOTENT = "_idempotent_",
}

const prependMigrateSPText = "migrate_";

const ingressSchema = SQLa.sqlSchemaDefn("drh_udi_ingress", {
    isIdempotent: true,
});

const assuranceSchema = SQLa.sqlSchemaDefn("drh_udi_assurance", {
    isIdempotent: true,
});

const researchSchema = SQLa.sqlSchemaDefn("drh_stateful_research_study", {
    isIdempotent: true,
});

const testMigrateDependenciesWithPgtap = [
    "../../../../test/postgres/ingestion-center/suite.pgtap.psql",
] as const;

const ctx = SQLa.typicalSqlEmitContext({
    sqlDialect: SQLa.postgreSqlDialect(),
});

const infoSchemaLifecycle = SQLa.sqlSchemaDefn("info_schema_lifecycle", {
    isIdempotent: true,
});

const rawSchema = SQLa.sqlSchemaDefn("drh_stateful_raw_observation", {
    isIdempotent: true,
});

export const rawDataSchema = SQLa.sqlSchemaDefn(
    "drh_stateful_raw_data",
    {
        isIdempotent: true,
    },
);

const statelessMasterSchema = SQLa.sqlSchemaDefn(
    "drh_stateless_master",
    {
        isIdempotent: true,
    },
);

const infoSchemaLifecycleAssurance = SQLa.sqlSchemaDefn(
    "info_schema_lifecycle_assurance",
    {
        isIdempotent: true,
    },
);

const activitySchema = SQLa.sqlSchemaDefn("drh_stateful_activity_audit", {
    isIdempotent: true,
});

export const aiSchema = SQLa.sqlSchemaDefn(
    "drh_stateful_ai_insights",
    {
        isIdempotent: true,
    },
);

export const partySchema = SQLa.sqlSchemaDefn(
    "drh_stateful_party",
    {
        isIdempotent: true,
    },
);

export const authSchema = SQLa.sqlSchemaDefn(
    "drh_stateful_authentication",
    {
        isIdempotent: true,
    },
);

export const masterSchema = SQLa.sqlSchemaDefn(
    "drh_stateful_master",
    {
        isIdempotent: true,
    },
);

export const statelessStudySchema = SQLa.sqlSchemaDefn(
    "drh_stateless_research_study",
    {
        isIdempotent: true,
    },
);

export const statelessUtilSchema = SQLa.sqlSchemaDefn(
    "drh_stateless_util",
    {
        isIdempotent: true,
    },
);

export const statelessrawObservation = SQLa.sqlSchemaDefn(
    "drh_stateless_raw_observation",
    {
        isIdempotent: true,
    },
);

export const dbImportMigrateSchema = SQLa.sqlSchemaDefn(
    "drh_stateful_db_import_migration",
    {
        isIdempotent: true,
    },
);

export const dbstatelessMigrateSchema = SQLa.sqlSchemaDefn(
    "drh_stateless_db_import_migration",
    {
        isIdempotent: true,
    },
);

export const statelessRawDataSchema = SQLa.sqlSchemaDefn(
    "drh_stateless_raw_data",
    {
        isIdempotent: true,
    },
);

export const statelessactivitySchema = SQLa.sqlSchemaDefn(
    "drh_stateless_activity_audit",
    {
        isIdempotent: true,
    },
);

export const statelessVannaSchema = SQLa.sqlSchemaDefn(
    "drh_stateless_vanna",
    {
        isIdempotent: true,
    },
);

export const statelessAiSchema = SQLa.sqlSchemaDefn(
  "drh_stateless_ai_insights",
  {
    isIdempotent: true,
  },
);

const searchPathAssurance = pgSQLa.pgSearchPath<
    typeof assuranceSchema.sqlNamespace,
    EmitContext
>(
    assuranceSchema,
);

export const migrationInput: MigrationVersion = {
    description: "ddl-table",
    dateTime: new Date(2024, 7, 30, 14, 52),
};
function formatDateToCustomString(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth()).padStart(2, "0"); // Month is zero-based
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${year}_${month}_${day}_${hours}_${minutes}`;
}

export const migrateVersion = formatDateToCustomString(migrationInput.dateTime);

const migrateSP = pgSQLa.storedProcedure(
    prependMigrateSPText + "v" + migrateVersion + StateStatus.IDEMPOTENT +
        migrationInput.description,
    {},
    (name, args, _) =>
        pgSQLa.typedPlPgSqlBody(name, args, ctx, {
            autoBeginEnd: false,
        }),
    {
        embeddedStsOptions: SQLa.typicalSqlTextSupplierOptions(),
        autoBeginEnd: false,
        isIdempotent: true,
        sqlNS: infoSchemaLifecycle,
        headerBodySeparator: "$migrateVersionSP$",
    },
)`
    BEGIN

      CREATE EXTENSION IF NOT EXISTS "pgcrypto" SCHEMA ${statelessUtilSchema.sqlNamespace};

      ${assuranceSchema}

      ${ddlTable.recordStatus}
      ${ddlTable.location}
      ${ddlTable.raceType}
      ${ddlTable.ethnicityType}
      ${ddlTable.organization}
      ${ddlTable.profileStatusType}
      ${ddlTable.userAccount}
      ${ddlTable.practitioner}
      ${ddlTable.patient}
      ${ddlTable.qualification}
      ${ddlTable.contactPointSystem}
      ${ddlTable.contactPointUse}
      ${ddlTable.contactPointAddressUse}
      ${ddlTable.contactPointAddressType}
      ${ddlTable.address}
      ${ddlTable.telecom}
      ${ddlTable.communication}
      ${ddlTable.associatedPartyType}
      ${ddlTable.laboratory}
      ${ddlTable.researchStudyFocus}
      ${ddlTable.researchStudyCondition}
      ${ddlTable.device}
      ${ddlTable.loincCodes}
      ${ddlTable.studyVisibility}
      ${ddlTable.citationIdentifier}
      ${ddlTable.metricDefinitions}
      ${ddlTable.studyStatusDefinitions}
      ${ddlTable.researchSubjectStatusDefinitions}
      ${ddlTable.citationStatusDefinitions}
      ${ddlTable.investigatorStudyRoleDefinitions}
      ${ddlTable.acceptedFileFormats}
      ${ddlTable.interactionStatus}
      ${ddlTable.interactionActionType}
      ${ddlTable.researchStudy}
      ${ddlTable.researchSubject}
      ${ddlTable.studyFileMapping}
      ${ddlTable.planDefinition}
      ${ddlTable.goal}
      ${ddlTable.activityDefinition}
      ${ddlTable.subjectObservation}
      ${ddlTable.citation}
      ${ddlTable.studyPartyRole}
      ${ddlTable.citationAuthor}
      ${ddlTable.investigatorStudy}

      ${ddlTable.externalAuthMappings}
      ${ddlTable.site}
      ${ddlTable.studyCollaboration}

      ${ddlTable.cgmData}

      ${ddlTable.activityLog}
      ${ddlTable.exceptionLog}
      ${ddlTable.filterInteraction}
      ${ddlTable.vannaAiRequestResponse}
      ${ddlTable.organizationType}
      ${ddlTable.fileContentType}
      ${ddlTable.researchStudyAssociatedParty}

      ${ddlTable.cgmRawDBData}
      ${ddlTable.cgmRawZipData}
      ${ddlTable.cgmRawUploadData}
      ${ddlTable.rawCgmExtractData}

      ${ddlTable.roleType}
      ${ddlTable.role}
      ${ddlTable.userRole}
      ${ddlTable.permission}
      ${ddlTable.rolePermission}
      ${ddlTable.groupType}
      ${ddlTable.groupMember}
      ${ddlTable.consentCategory}
      ${ddlTable.consentStatusCode}
      ${ddlTable.consentDecisionType}
      ${ddlTable.studyConsent}
      ${ddlTable.group}
      ${ddlTable.contractType}
      ${ddlTable.contractSubType}
      ${ddlTable.contractSignerType}
      ${ddlTable.legalStateCode}
      ${ddlTable.contractExpirationType}
      ${ddlTable.signatureType}
      ${ddlTable.contractTerm}
      ${ddlTable.decisionType}
      ${ddlTable.decisionMode}
      ${ddlTable.termOffer}
      ${ddlTable.termOfferParty}
      ${ddlTable.contractStatus}
      ${ddlTable.studyContract}
      ${ddlTable.migrationStatus}

      ${ddlTable.fileMetaIngestData}
      ${ddlTable.participant}
      ${ddlTable.cgmDataMigrationStatus}
      ${ddlTable.participantMigrationStatus}
      ${ddlTable.dbMigrationLog}

      ${ddlTable.cgmDeviceInfo}
      ${ddlTable.cgmMetrics}
      ${ddlTable.participantBase}
      ${ddlTable.hubInteraction}
      ${ddlTable.sessionUniqueIdMapping}
      ${ddlTable.fileInteraction}
      ${ddlTable.studyInteraction}
      ${ddlTable.studyParticipantInteraction}
      ${ddlTable.studyMetaData}
      ${ddlTable.participantMealFitnessData}
      ${ddlTable.studyTypeMapping}

      ${ddlTable.nutritionIntakeStatusCode}
      ${ddlTable.unitOfMeasurement}
      ${ddlTable.mealType}
      ${ddlTable.measurementCategory}
      ${ddlTable.unitCategoryMapping}
      ${ddlTable.nutritionIntakeBuilder}
      ${ddlTable.subjectObservationZipData}
      ${ddlTable.subjectObservationUploadData}
      ${ddlTable.subjectObservationExtractData}

      ${ddlTable.observationMethod}
      ${ddlTable.observationStatus}
      ${ddlTable.observationCategory}
      ${ddlTable.activityMaster}
      ${ddlTable.activityType}
      ${ddlTable.activityComponentType}
      ${ddlTable.observationFitnessBuilder}
      ${ddlTable.observationFitnessComponentBuilder}
      ${ddlTable.userCredentials}
      ${ddlTable.participantMigrationHistory}
      ${ddlTable.studyMetadataHistory}
      ${ddlTable.sessionAuditLog}
      ${ddlTable.activityLevel}
      ${ddlTable.activityLevelMapping}
      ${ddlTable.userVerificationStatus}
      ${ddlTable.userAccountVerificationLog}      
      ${ddlTable.participantFileMapping}
      ${ddlTable.nutritionIntakeMapping}
      ${ddlTable.fitnessMapping}
      ${ddlTable.aiConversationLog}



      CREATE EXTENSION IF NOT EXISTS pgtap SCHEMA ${assuranceSchema.sqlNamespace};

      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_csv BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_excel BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_xml XML;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_text BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName} ADD COLUMN IF NOT EXISTS database_id VARCHAR(255) NULL;

      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_csv BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_excel BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_text BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName} ADD COLUMN IF NOT EXISTS cgm_raw_data_xml XML;

      --add file contents of csv,xml,text,xml field into subject_observation_upload_data
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName} ADD COLUMN IF NOT EXISTS raw_data_csv BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName} ADD COLUMN IF NOT EXISTS raw_data_excel BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName} ADD COLUMN IF NOT EXISTS raw_data_xml XML;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName} ADD COLUMN IF NOT EXISTS raw_data_text BYTEA;
      --add file contents of csv,xml,text,xml into subject_observation_upload_data
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName} ADD COLUMN IF NOT EXISTS raw_data_csv BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName} ADD COLUMN IF NOT EXISTS raw_data_excel BYTEA;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName} ADD COLUMN IF NOT EXISTS raw_data_xml XML;
      ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName} ADD COLUMN IF NOT EXISTS raw_data_text BYTEA;


      ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.address.tableName} ALTER COLUMN org_party_id DROP NOT NULL;
      ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.address.tableName} ALTER COLUMN tenant_id DROP NOT NULL;

      ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName} ALTER COLUMN last_name DROP NOT NULL;

      --citation table identifier_system and identifier_value columns are not required to be not null
      ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName} ALTER COLUMN identifier_system DROP NOT NULL;
      ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName} ALTER COLUMN identifier_value DROP NOT NULL;

      -- telecom table adding unique constraint on party_id, contact_point_system_id, contact_point_use_type_id (used to use ON CONFLICT method on update_profile_details function)
      DO $$
      BEGIN
          -- Check if constraint doesn't exist before creating
          IF NOT EXISTS (
              SELECT 1
              FROM pg_constraint
              WHERE conname = 'uk_telecom_party_system_use'
          ) THEN
              ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}
              ADD CONSTRAINT uk_telecom_party_system_use
              UNIQUE (party_id, contact_point_system_id, contact_point_use_type_id);
          END IF;
      END $$;

      ALTER TABLE ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName} ADD COLUMN IF NOT EXISTS raw_cgm_extract_data_id text;
      DO $$
        BEGIN
            -- Add foreign key constraint if it doesn't exist
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.table_constraints
                WHERE table_schema = '${rawSchema.sqlNamespace}'
                AND table_name = '${ddlTable.cgmData.tableName}'
                AND constraint_name = 'fk_cgm_observation_raw_data'
            ) THEN
                ALTER TABLE ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}
                ADD CONSTRAINT fk_cgm_observation_raw_data
                FOREIGN KEY (raw_cgm_extract_data_id)
                REFERENCES ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}(cgm_raw_data_id);
            END IF;

            -- Create index if it doesn't exist
            IF NOT EXISTS (
                SELECT 1 FROM pg_indexes
                WHERE schemaname = '${rawSchema.sqlNamespace}'
                AND tablename = '${ddlTable.cgmData.tableName}'
                AND indexname = 'idx_cgm_observation_raw_data_id'
            ) THEN
                CREATE INDEX idx_cgm_observation_raw_data_id
                ON ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}(raw_cgm_extract_data_id);
            END IF;
        END $$;

        -- First drop the existing tenant_id column
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}
        DROP COLUMN IF EXISTS tenant_id CASCADE;

        DO $$
        BEGIN


            -- Add organization_party_id column if it doesn't exist
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_schema = '${activitySchema.sqlNamespace}'
                AND table_name = '${ddlTable.activityLog.tableName}'
                AND column_name = 'organization_party_id'
            ) THEN
                ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}
                ADD COLUMN organization_party_id TEXT;

                ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}
                ADD CONSTRAINT activity_log_organization_party_id_fkey
                FOREIGN KEY (organization_party_id)
                REFERENCES ${partySchema.sqlNamespace}.party(party_id);

                CREATE INDEX idx_activity_log_organization_party_id
                ON ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}(organization_party_id);
            END IF;
        END $$;

        --Modify Activitylog table nullable fields
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN activity_type DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN activity_name DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN activity_description DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN root_id DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN parent_id DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN activity_hierarchy DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN hierarchy_path DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN request_url DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN platform DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN environment DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN created_by DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN user_name DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN created_at DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN app_version DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN test_case DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN session_id DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN linkage_id DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN ip_address DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN location_latitude DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN location_longitude DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN activity_data DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN activity_log_level DROP NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN session_unique_id SET NOT NULL;
        ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} ALTER COLUMN organization_party_id DROP NOT NULL;

        DO $$
        BEGIN
            -- Add interaction_hierarchy to study_interaction table
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_schema = '${activitySchema.sqlNamespace}'
                AND table_name = '${ddlTable.studyInteraction.tableName}'
                AND column_name = 'interaction_hierarchy'
            ) THEN
                ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}
                ADD COLUMN interaction_hierarchy JSONB;

                COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.interaction_hierarchy
                IS 'JSON structure representing the hierarchical relationship of interactions';
            END IF;

            -- Add interaction_hierarchy to file_interaction table
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_schema = '${activitySchema.sqlNamespace}'
                AND table_name = '${ddlTable.fileInteraction.tableName}'
                AND column_name = 'interaction_hierarchy'
            ) THEN
                ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
                ADD COLUMN interaction_hierarchy JSONB;

                COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.interaction_hierarchy
                IS 'JSON structure representing the hierarchical relationship of file interactions';
            END IF;

            -- Add interaction_hierarchy to study_participant_interaction table
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_schema = '${activitySchema.sqlNamespace}'
                AND table_name = '${ddlTable.studyParticipantInteraction.tableName}'
                AND column_name = 'interaction_hierarchy'
            ) THEN
                ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}
                ADD COLUMN interaction_hierarchy JSONB;

                COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.interaction_hierarchy
                IS 'JSON str
                ucture representing the hierarchical relationship of participant interactions';
            END IF;
        END $$;

      --Add interaction_action_type_id and interaction_status_id columns to file_interaction table
      DO $$
      BEGIN
          -- Add interaction_action_type_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.fileInteraction.tableName}'
              AND column_name = 'interaction_action_type_id'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
              ADD COLUMN interaction_action_type_id INTEGER;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${activitySchema.sqlNamespace}'
                  AND table_name = '${ddlTable.fileInteraction.tableName}'
                  AND constraint_name = 'fk_file_interaction_action_type'
              ) THEN
                  ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
                  ADD CONSTRAINT fk_file_interaction_action_type
                  FOREIGN KEY (interaction_action_type_id)
                  REFERENCES ${masterSchema.sqlNamespace}.${ddlTable.interactionActionType.tableName}(id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${activitySchema.sqlNamespace}'
                  AND tablename = '${ddlTable.fileInteraction.tableName}'
                  AND indexname = 'idx_file_interaction_action_type_id'
              ) THEN
                  CREATE INDEX idx_file_interaction_action_type_id
                  ON ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(interaction_action_type_id);
              END IF;
          END IF;

          -- Add interaction_status_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.fileInteraction.tableName}'
              AND column_name = 'interaction_status_id'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
              ADD COLUMN interaction_status_id INTEGER;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${activitySchema.sqlNamespace}'
                  AND table_name = '${ddlTable.fileInteraction.tableName}'
                  AND constraint_name = 'fk_file_interaction_status'
              ) THEN
                  ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
                  ADD CONSTRAINT fk_file_interaction_status
                  FOREIGN KEY (interaction_status_id)
                  REFERENCES ${masterSchema.sqlNamespace}.${ddlTable.interactionStatus.tableName}(id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${activitySchema.sqlNamespace}'
                  AND tablename = '${ddlTable.fileInteraction.tableName}'
                  AND indexname = 'idx_file_interaction_status_id'
              ) THEN
                  CREATE INDEX idx_file_interaction_status_id
                  ON ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(interaction_status_id);
              END IF;
          END IF;
      END $$;

      ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
        DROP COLUMN IF EXISTS file_processing_status CASCADE;

      --SET PRIMARY KEY TO INTERACTION TABLES
      DO $$
      BEGIN

          -- Check if the primary key constraint doesn't already exist
          IF NOT EXISTS (
              SELECT 1
              FROM information_schema.table_constraints
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.fileInteraction.tableName}'
              AND constraint_type = 'PRIMARY KEY'
          ) THEN
              -- Add primary key constraint
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
              ADD CONSTRAINT pk_file_interaction PRIMARY KEY (file_interaction_id);
          END IF;
          -- Add primary key for hub_interaction table
          IF NOT EXISTS (
              SELECT 1
              FROM information_schema.table_constraints
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.hubInteraction.tableName}'
              AND constraint_type = 'PRIMARY KEY'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}
              ADD CONSTRAINT pk_hub_interaction PRIMARY KEY (hub_interaction_id);
          END IF;

          -- Add primary key for study_interaction table
          IF NOT EXISTS (
              SELECT 1
              FROM information_schema.table_constraints
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.studyInteraction.tableName}'
              AND constraint_type = 'PRIMARY KEY'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}
              ADD CONSTRAINT pk_study_interaction PRIMARY KEY (study_interaction_id);
          END IF;

          -- Add primary key for study_participant_interaction table
          IF NOT EXISTS (
              SELECT 1
              FROM information_schema.table_constraints
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.studyParticipantInteraction.tableName}'
              AND constraint_type = 'PRIMARY KEY'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}
              ADD CONSTRAINT pk_study_participant_interaction PRIMARY KEY (participant_interaction_id);
          END IF;
      END $$;

      --ADD FILE INTERACTION TABLE REFERENCE TO MULTIPLE TABLES
      DO $$
      BEGIN

          -- Add file_interaction_id column to cgm_raw_zip_data if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.cgmRawZipData.tableName}'
              AND column_name = 'file_interaction_id'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}
              ADD COLUMN file_interaction_id TEXT;

              -- Add foreign key constraint
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}
              ADD CONSTRAINT fk_cgm_raw_zip_file_interaction
              FOREIGN KEY (file_interaction_id)
              REFERENCES ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(file_interaction_id);

              -- Create index for the foreign key
              CREATE INDEX idx_cgm_raw_zip_file_interaction
              ON ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}(file_interaction_id);
          END IF;
          -- Add file_interaction_id column to cgm_raw_upload_data if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.cgmRawUploadData.tableName}'
              AND column_name = 'file_interaction_id'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}
              ADD COLUMN file_interaction_id TEXT;

              -- Add foreign key constraint
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}
              ADD CONSTRAINT fk_cgm_raw_upload_file_interaction
              FOREIGN KEY (file_interaction_id)
              REFERENCES ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(file_interaction_id);

              -- Create index for the foreign key
              CREATE INDEX idx_cgm_raw_upload_file_interaction
              ON ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}(file_interaction_id);
          END IF;

          -- Add file_interaction_id column to raw_cgm_extract_data if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.rawCgmExtractData.tableName}'
              AND column_name = 'file_interaction_id'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}
              ADD COLUMN file_interaction_id TEXT;

              -- Add foreign key constraint
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}
              ADD CONSTRAINT fk_raw_cgm_extract_file_interaction
              FOREIGN KEY (file_interaction_id)
              REFERENCES ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(file_interaction_id);

              -- Create index for the foreign key
              CREATE INDEX idx_raw_cgm_extract_file_interaction
              ON ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}(file_interaction_id);
          END IF;

          -- Add file_interaction_id column to cgm_raw_db if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = '${rawDataSchema.sqlNamespace}'
            AND table_name = '${ddlTable.cgmRawDBData.tableName}'
            AND column_name = 'file_interaction_id'
        ) THEN
            ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}
            ADD COLUMN file_interaction_id TEXT;

            -- Add foreign key constraint
            ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}
            ADD CONSTRAINT fk_cgm_raw_db_file_interaction
            FOREIGN KEY (file_interaction_id)
            REFERENCES ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(file_interaction_id);

            -- Create index for the foreign key
            CREATE INDEX idx_cgm_raw_db_file_interaction
            ON ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}(file_interaction_id);
        END IF;
      END $$;

      --Add citation table reference to citation_identifier table
      DO $$
      BEGIN
          -- First, alter the column type from integer to text
          IF EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${researchSchema.sqlNamespace}'
              AND table_name = '${ddlTable.citationIdentifier.tableName}'
              AND column_name = 'citation_id'
              AND data_type = 'integer'
          ) THEN
              ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}
              ALTER COLUMN citation_id TYPE text;
          END IF;

          -- Add foreign key constraint if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints
              WHERE table_schema = '${researchSchema.sqlNamespace}'
              AND table_name = '${ddlTable.citationIdentifier.tableName}'
              AND constraint_name = 'fk_citation_identifier_citation'
          ) THEN
              ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}
              ADD CONSTRAINT fk_citation_identifier_citation
              FOREIGN KEY (citation_id)
              REFERENCES ${researchSchema.sqlNamespace}.citation(id);
          END IF;

          -- Create index if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM pg_indexes
              WHERE schemaname = '${researchSchema.sqlNamespace}'
              AND tablename = '${ddlTable.citationIdentifier.tableName}'
              AND indexname = 'idx_citation_identifier_citation_id'
          ) THEN
              CREATE INDEX idx_citation_identifier_citation_id
              ON ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}(citation_id);
          END IF;
      END $$;

      --Add interaction_action_type_id and interaction_status_id columns to study_interaction table
      DO $$
      BEGIN
          -- Add interaction_action_type_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.studyInteraction.tableName}'
              AND column_name = 'interaction_action_type_id'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}
              ADD COLUMN interaction_action_type_id INTEGER;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${activitySchema.sqlNamespace}'
                  AND table_name = '${ddlTable.studyInteraction.tableName}'
                  AND constraint_name = 'fk_study_interaction_action_type'
              ) THEN
                  ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}
                  ADD CONSTRAINT fk_study_interaction_action_type
                  FOREIGN KEY (interaction_action_type_id)
                  REFERENCES ${masterSchema.sqlNamespace}.${ddlTable.interactionActionType.tableName}(id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${activitySchema.sqlNamespace}'
                  AND tablename = '${ddlTable.studyInteraction.tableName}'
                  AND indexname = 'idx_study_interaction_action_type_id'
              ) THEN
                  CREATE INDEX idx_study_interaction_action_type_id
                  ON ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}(interaction_action_type_id);
              END IF;
          END IF;

          -- Add interaction_status_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.studyInteraction.tableName}'
              AND column_name = 'interaction_status_id'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}
              ADD COLUMN interaction_status_id INTEGER;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${activitySchema.sqlNamespace}'
                  AND table_name = '${ddlTable.studyInteraction.tableName}'
                  AND constraint_name = 'fk_study_interaction_status'
              ) THEN
                  ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}
                  ADD CONSTRAINT fk_study_interaction_status
                  FOREIGN KEY (interaction_status_id)
                  REFERENCES ${masterSchema.sqlNamespace}.${ddlTable.interactionStatus.tableName}(id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${activitySchema.sqlNamespace}'
                  AND tablename = '${ddlTable.studyInteraction.tableName}'
                  AND indexname = 'idx_study_interaction_status_id'
              ) THEN
                  CREATE INDEX idx_study_interaction_status_id
                  ON ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}(interaction_status_id);
              END IF;
          END IF;
      END $$;

      --Add interaction_action_type_id and interaction_status_id columns to study_participant_interaction table
      DO $$
      BEGIN
          -- Add interaction_action_type_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.studyParticipantInteraction.tableName}'
              AND column_name = 'interaction_action_type_id'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}
              ADD COLUMN interaction_action_type_id INTEGER;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${activitySchema.sqlNamespace}'
                  AND table_name = '${ddlTable.studyParticipantInteraction.tableName}'
                  AND constraint_name = 'fk_participant_interaction_action_type'
              ) THEN
                  ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}
                  ADD CONSTRAINT fk_participant_interaction_action_type
                  FOREIGN KEY (interaction_action_type_id)
                  REFERENCES ${masterSchema.sqlNamespace}.${ddlTable.interactionActionType.tableName}(id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${activitySchema.sqlNamespace}'
                  AND tablename = '${ddlTable.studyParticipantInteraction.tableName}'
                  AND indexname = 'idx_participant_interaction_action_type_id'
              ) THEN
                  CREATE INDEX idx_participant_interaction_action_type_id
                  ON ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}(interaction_action_type_id);
              END IF;
          END IF;

          -- Add interaction_status_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.studyParticipantInteraction.tableName}'
              AND column_name = 'interaction_status_id'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}
              ADD COLUMN interaction_status_id INTEGER;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${activitySchema.sqlNamespace}'
                  AND table_name = '${ddlTable.studyParticipantInteraction.tableName}'
                  AND constraint_name = 'fk_participant_interaction_status'
              ) THEN
                  ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}
                  ADD CONSTRAINT fk_participant_interaction_status
                  FOREIGN KEY (interaction_status_id)
                  REFERENCES ${masterSchema.sqlNamespace}.${ddlTable.interactionStatus.tableName}(id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${activitySchema.sqlNamespace}'
                  AND tablename = '${ddlTable.studyParticipantInteraction.tableName}'
                  AND indexname = 'idx_participant_interaction_status_id'
              ) THEN
                  CREATE INDEX idx_participant_interaction_status_id
                  ON ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}(interaction_status_id);
              END IF;
          END IF;
      END $$;

      --Move role_permission table from master to auth schema
      DO $$
      BEGIN
          -- Check if source table exists and destination doesn't
          IF EXISTS (
              SELECT 1
              FROM information_schema.tables
              WHERE table_schema = '${masterSchema.sqlNamespace}'
              AND table_name = '${ddlTable.rolePermission.tableName}'
          ) AND NOT EXISTS (
              SELECT 1
              FROM information_schema.tables
              WHERE table_schema = '${authSchema.sqlNamespace}'
              AND table_name = '${ddlTable.rolePermission.tableName}'
          ) THEN
                    -- Move the table to the new schema
                    ALTER TABLE ${masterSchema.sqlNamespace}.${ddlTable.rolePermission.tableName}
                    SET SCHEMA ${authSchema.sqlNamespace};
          END IF;
      END $$;

      --Remove role_permission table if exist in master schema
      DO $$
      BEGIN
          -- First check if the table exists in drh_stateful_master schema
          IF EXISTS (
              SELECT 1
              FROM information_schema.tables
              WHERE table_schema = '${masterSchema.sqlNamespace}'
              AND table_name = '${ddlTable.rolePermission.tableName}'
          ) THEN
              -- Drop the table if it exists
              DROP TABLE ${masterSchema.sqlNamespace}.${ddlTable.rolePermission.tableName};
          END IF;
      END $$;

      --ADD FORIGN KEY CONSTRAINTS AND INDEXES TO FILE INTERACTION TABLE PARTICIPANT ID
      DO $$
      BEGIN
          -- Add foreign key constraint if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.fileInteraction.tableName}'
              AND constraint_name = 'fk_file_interaction_participant'
          ) THEN
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}
              ADD CONSTRAINT fk_file_interaction_participant
              FOREIGN KEY (participant_id)
              REFERENCES drh_stateful_research_study.research_subject(rsubject_id);
          END IF;

          -- Create index if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM pg_indexes
              WHERE schemaname = '${activitySchema.sqlNamespace}'
              AND tablename = '${ddlTable.fileInteraction.tableName}'
              AND indexname = 'idx_file_interaction_participant'
          ) THEN
              CREATE INDEX idx_file_interaction_participant
              ON ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(participant_id);
          END IF;
      END $$;

      --ADD file_interaction_id TO SUBJECT OBSERVATION TABLES
      DO $$
      BEGIN
          -- For subject_observation_zip_data table
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.subjectObservationZipData.tableName}'
              AND column_name = 'file_interaction_id'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}
              ADD COLUMN file_interaction_id TEXT;
          END IF;

          IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.subjectObservationZipData.tableName}'
              AND constraint_name = 'fk_subject_observation_zip_file_interaction'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}
              ADD CONSTRAINT fk_subject_observation_zip_file_interaction
              FOREIGN KEY (file_interaction_id)
              REFERENCES ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(file_interaction_id);
          END IF;

          -- For ${ddlTable.subjectObservationUploadData.tableName} table
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.subjectObservationUploadData.tableName}'
              AND column_name = 'file_interaction_id'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}
              ADD COLUMN file_interaction_id TEXT;
          END IF;

          IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.subjectObservationUploadData.tableName}'
              AND constraint_name = 'fk_subject_observation_upload_file_interaction'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}
              ADD CONSTRAINT fk_subject_observation_upload_file_interaction
              FOREIGN KEY (file_interaction_id)
              REFERENCES ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(file_interaction_id);
          END IF;

          -- For ${ddlTable.subjectObservationExtractData.tableName} table
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.subjectObservationExtractData.tableName}'
              AND column_name = 'file_interaction_id'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}
              ADD COLUMN file_interaction_id TEXT;
          END IF;

          IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints
              WHERE table_schema = '${rawDataSchema.sqlNamespace}'
              AND table_name = '${ddlTable.subjectObservationExtractData.tableName}'
              AND constraint_name = 'fk_subject_observation_extract_file_interaction'
          ) THEN
              ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}
              ADD CONSTRAINT fk_subject_observation_extract_file_interaction
              FOREIGN KEY (file_interaction_id)
              REFERENCES ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}(file_interaction_id);
          END IF;
      END $$;

      --ADD org_party_id TO USER ACCOUNT TABLE
      DO $$
      BEGIN
          -- Add org_party_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${authSchema.sqlNamespace}'
              AND table_name = '${ddlTable.userAccount.tableName}'
              AND column_name = 'org_party_id'
          ) THEN
              -- Add the column
              ALTER TABLE ${authSchema.sqlNamespace}.${ddlTable.userAccount.tableName}
              ADD COLUMN org_party_id TEXT;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${authSchema.sqlNamespace}'
                  AND table_name = '${ddlTable.userAccount.tableName}'
                  AND constraint_name = 'fk_user_account_org_party'
              ) THEN
                  ALTER TABLE ${authSchema.sqlNamespace}.${ddlTable.userAccount.tableName}
                  ADD CONSTRAINT fk_user_account_org_party
                  FOREIGN KEY (org_party_id)
                  REFERENCES ${partySchema.sqlNamespace}.party(party_id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${authSchema.sqlNamespace}'
                  AND tablename = '${ddlTable.userAccount.tableName}'
                  AND indexname = 'idx_user_account_org_party_id'
              ) THEN
                  CREATE INDEX idx_user_account_org_party_id
                  ON ${authSchema.sqlNamespace}.${ddlTable.userAccount.tableName}(org_party_id);
              END IF;

              -- Add comment for the new column
              COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userAccount.tableName}.org_party_id
              IS 'Foreign key referencing the organization party ID in the party table';
          END IF;
      END $$;

      --Add citation_data_source column to citation table
      ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName} ADD COLUMN IF NOT EXISTS citation_data_source text;

      DO $$
      BEGIN
          -- Add party_id column if it doesn't exist
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${researchSchema.sqlNamespace}'
              AND table_name = '${ddlTable.address.tableName}'
              AND column_name = 'party_id'
          ) THEN
              -- Add the column
              ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}
              ADD COLUMN party_id TEXT;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${researchSchema.sqlNamespace}'
                  AND table_name = '${ddlTable.address.tableName}'
                  AND constraint_name = 'fk_address_party'
              ) THEN
                  ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}
                  ADD CONSTRAINT fk_address_party
                  FOREIGN KEY (party_id)
                  REFERENCES ${partySchema.sqlNamespace}.party(party_id);
              END IF;

              -- Create index for the new column if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${researchSchema.sqlNamespace}'
                  AND tablename = '${ddlTable.address.tableName}'
                  AND indexname = 'idx_address_party_id'
              ) THEN
                  CREATE INDEX idx_address_party_id
                  ON ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}(party_id);
              END IF;

              -- Add comment for the new column
              COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.party_id
              IS 'Foreign key referencing the party ID in the party table';
          END IF;
      END $$;

      -- Remove unique constraint to research_study table on study_display_id
      DO $$
      BEGIN
          -- Check if the unique constraint exists before attempting to remove it
          IF EXISTS (
              SELECT 1
              FROM information_schema.table_constraints
              WHERE table_schema = '${researchSchema.sqlNamespace}'
              AND table_name = 'research_study'
              AND constraint_name = 'research_study_study_display_id_key'
          ) THEN
              -- Drop the unique constraint
              ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}
              DROP CONSTRAINT research_study_study_display_id_key;
          END IF;
      END $$;

      --Add activity_type_id column to activity_log table
      DO $$
      BEGIN
          -- First check if the column doesn't exist before adding it
          IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns
              WHERE table_schema = '${activitySchema.sqlNamespace}'
              AND table_name = '${ddlTable.activityLog.tableName}'
              AND column_name = 'activity_type_id'
          ) THEN
              -- Add the column
              ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}
              ADD COLUMN activity_type_id TEXT;

              -- Add foreign key constraint if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.table_constraints
                  WHERE table_schema = '${activitySchema.sqlNamespace}'
                  AND table_name = '${ddlTable.activityLog.tableName}'
                  AND constraint_name = 'fk_activity_log_activity_type'
              ) THEN
                  ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}
                  ADD CONSTRAINT fk_activity_log_activity_type
                  FOREIGN KEY (activity_type_id)
                  REFERENCES drh_stateful_master.activity_type(id);
              END IF;

              -- Create index if it doesn't exist
              IF NOT EXISTS (
                  SELECT 1 FROM pg_indexes
                  WHERE schemaname = '${activitySchema.sqlNamespace}'
                  AND tablename = '${ddlTable.activityLog.tableName}'
                  AND indexname = 'idx_activity_log_activity_type_id'
              ) THEN
                  CREATE INDEX idx_activity_log_activity_type_id
                  ON ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}(activity_type_id);
              END IF;

              -- Add comment for the new column
              COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_type_id
              IS 'Foreign key referencing the activity_type table, defining the type of activity being logged';
          END IF;
      END $$;

      --Add activity_level_id column to activity_log table
    DO $$
    BEGIN
        -- First check if the column doesn't exist before adding it
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = '${activitySchema.sqlNamespace}'
            AND table_name = '${ddlTable.activityLog.tableName}'
            AND column_name = 'activity_level_id'
        ) THEN
            -- Add the column
            ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}
            ADD COLUMN activity_level_id TEXT;

            -- Add foreign key constraint if it doesn't exist
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.table_constraints
                WHERE table_schema = '${activitySchema.sqlNamespace}'
                AND table_name = '${ddlTable.activityLog.tableName}'
                AND constraint_name = 'fk_activity_log_level'
            ) THEN
                ALTER TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}
                ADD CONSTRAINT fk_activity_log_level
                FOREIGN KEY (activity_level_id)
                REFERENCES drh_stateful_master.activity_level(id);
            END IF;

            -- Create index if it doesn't exist
            IF NOT EXISTS (
                SELECT 1 FROM pg_indexes
                WHERE schemaname = '${activitySchema.sqlNamespace}'
                AND tablename = '${ddlTable.activityLog.tableName}'
                AND indexname = 'idx_activity_log_level_id'
            ) THEN
                CREATE INDEX idx_activity_log_level_id
                ON ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}(activity_level_id);
            END IF;

            -- Add comment for the new column
            COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_level_id
            IS 'Foreign key referencing the activity_level table, defining the level of activity being logged';
        END IF;
    END $$;

    -- Add foreign key constraints to drh_stateful_authentication.user_role for role_id and user_id
    DO $$
    BEGIN
        -- Add foreign key for role_id if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE table_schema = 'drh_stateful_authentication'
            AND table_name = 'user_role'
            AND constraint_name = 'fk_user_role_role'
        ) THEN
            ALTER TABLE drh_stateful_authentication.user_role
            ADD CONSTRAINT fk_user_role_role
            FOREIGN KEY (role_id)
            REFERENCES drh_stateful_master.role(role_id);
        END IF;

        -- Add foreign key for user_id if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE table_schema = 'drh_stateful_authentication'
            AND table_name = 'user_role'
            AND constraint_name = 'fk_user_role_user'
        ) THEN
            ALTER TABLE drh_stateful_authentication.user_role
            ADD CONSTRAINT fk_user_role_user
            FOREIGN KEY (user_id)
            REFERENCES drh_stateful_authentication.user_account(user_id);
        END IF;
    END $$;

    -- Add foreign key constraint to drh_stateful_authentication.user_account for party_id
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE table_schema = 'drh_stateful_authentication'
            AND table_name = 'user_account'
            AND constraint_name = 'fk_user_account_party'
        ) THEN
            ALTER TABLE drh_stateful_authentication.user_account
            ADD CONSTRAINT fk_user_account_party
            FOREIGN KEY (party_id)
            REFERENCES drh_stateful_party.party(party_id);
        END IF;
    END $$;

    -- Add parent_id column to permission table if it does not exist, and add foreign key to self (permission_id)
    DO $$
    BEGIN
        -- Add parent_id column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'drh_stateful_master'
            AND table_name = 'permission'
            AND column_name = 'parent_id'
        ) THEN
            ALTER TABLE drh_stateful_master.permission
            ADD COLUMN parent_id VARCHAR(36);

            -- Add comment for the new column
            COMMENT ON COLUMN drh_stateful_master.permission.parent_id IS
                'Optional self-referencing foreign key to permission_id for hierarchical permissions';
        END IF;

        -- Add foreign key constraint if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE table_schema = 'drh_stateful_master'
            AND table_name = 'permission'
            AND constraint_name = 'fk_permission_parent'
        ) THEN
            ALTER TABLE drh_stateful_master.permission
            ADD CONSTRAINT fk_permission_parent
            FOREIGN KEY (parent_id)
            REFERENCES drh_stateful_master.permission(permission_id);
        END IF;

        -- Create index for the new column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes
            WHERE schemaname = 'drh_stateful_master'
            AND tablename = 'permission'
            AND indexname = 'idx_permission_parent_id'
        ) THEN
            CREATE INDEX idx_permission_parent_id
            ON drh_stateful_master.permission(parent_id);
        END IF;
    END $$;
    
    -- Add url column to permission table if it does not exist
    DO $$
    BEGIN
        -- Add url column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'drh_stateful_master'
            AND table_name = 'permission'
            AND column_name = 'url'
        ) THEN
            ALTER TABLE drh_stateful_master.permission
            ADD COLUMN url TEXT;

            -- Add comment for the new column
            COMMENT ON COLUMN drh_stateful_master.permission.url IS
                'Optional URL associated with the permission, e.g., documentation or resource link';
        END IF;
    END $$;

    DO $$ 
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = 'drh_stateful_master'
            AND table_name = 'permission'
            AND column_name = 'is_menu'
        ) THEN
            ALTER TABLE drh_stateful_master.permission
            ADD COLUMN is_menu BOOLEAN DEFAULT false;
        END IF;
    END $$;

      COMMENT ON COLUMN   ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.id IS 'Unique identifier for each record status entry (Primary Key)';
      COMMENT ON COLUMN   ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.code IS 'Mandatory,Individual code (alpha-numeric string) of the record_status like ACTIVE, PENDING, DELETED etc';
      COMMENT ON COLUMN   ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.value IS 'Mandatory,Individual values of the record_status eg: 1- ACTIVE, 2-PENDING etc';

      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName} IS 'Table to store race types with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.race_type_id IS 'Unique identifier for the race type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.code IS 'Code representing the race type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_uri IS 'The uri where the code is defined';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_oid IS 'Coding system (e.g., urn:oid:2.16.840.1.113883.6.238)';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.display IS 'Display value for the race type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.race_text IS 'Textual representation of the race';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.created_at IS 'Timestamp when the record was created';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.created_by IS 'Identifier of the user who created the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.updated_at IS 'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.updated_by IS 'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.deleted_at IS 'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.deleted_by IS 'Identifier of the user who marked the record as deleted';

      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName} IS 'Table to store ethnicity types with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.ethnicity_type_id IS 'Unique identifier for the ethnicity type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.code IS 'Code representing the ethnicity type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_uri IS 'The uri where the code is defined';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_oid IS 'Coding system (e.g., urn:oid:2.16.840.1.113883.6.238)';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.ethnicity_text IS 'Textual representation of the ethnicity';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.created_at IS 'Timestamp when the record was created';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.created_by IS 'Identifier of the user who created the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.updated_at IS 'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.updated_by IS 'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.deleted_at IS 'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.deleted_by IS 'Identifier of the user who marked the record as deleted';

      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.location.tableName} IS 'This table defines the various locations associated with research activities, such as study sites or laboratories.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.id IS 'Unique identifier for the location (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.name IS 'Name of the location as used by humans.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.alias IS 'Comma-separated alternate names for the location.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.description IS 'Additional details about the location.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.type_code IS 'Code representing the type of location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.type_display IS 'Display text for the type of location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.address_line IS 'Street address of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.city IS 'City of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.state IS 'State of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.postal_code IS 'Postal code of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.country IS 'Country of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.longitude IS 'Longitude coordinate of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.latitude IS 'Latitude coordinate of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.part_of_id IS 'Foreign key reference to the parent Location table';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.characteristic_code IS 'Code representing the characteristic of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.characteristic_display IS 'Display text for the characteristic of the location';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.location.tableName}.rec_status_id IS 'Status of the record (e.g., active) foreign key referencing record_status.';

      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}
            IS 'This master table categorizes different types of parties involved in research, such as sponsors or collaborators. ';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.id
            IS 'Primary Key, auto-incremented, uniquely identifies each associated party type.';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.name
            IS 'Human-readable name of the party type (e.g., Sponsor), mandatory field.';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.description
            IS 'Detailed description of the party type, optional field.';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.rec_status_id
            IS 'Record status ID (foreign key), indicates the current status of the record (active, inactive, etc.).';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.created_at
            IS 'Timestamp indicating when the record was created.';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.created_by
            IS 'User ID of the creator of the record.';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.updated_at
            IS 'Timestamp indicating the last time the record was updated.';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.updated_by
            IS 'User ID of the person who last updated the record.';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.deleted_at
            IS 'Timestamp indicating when the record was marked as deleted (soft delete).';

            COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.deleted_by
            IS 'User ID of the person who marked the record as deleted.';



      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName} IS 'The organization table serves as a master dataset for storing details of organizations or tenants involved in research studies.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.id IS 'Unique identifier for the organization, serving as the primary key.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.identifier_system_value IS 'System for the identifier, such as a URL or other unique system reference along with the value(ROR,GRID,ISNI, Crossref, Wikidata etc shall be maintained.)';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.name IS 'The official name of the organization.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.alias IS 'Alternative name(s) or aliases for the organization.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.type_code IS 'Code representing the type of organization (e.g., healthcare provider, research lab).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.type_display IS 'A human-readable display name for the type of organization.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.address_text IS 'Full address of the organization in free-text format.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.address_line IS 'Specific address line information, such as street or building details.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.city IS 'City where the organization is located.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.state IS 'State or province where the organization is located.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.postal_code IS 'Postal or ZIP code of the organization.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.country IS 'Country where the organization is located.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.phone IS 'Phone number to contact the organization.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.email IS 'Email address to contact the organization.';
       COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.website_url IS 'organization website url.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.parent_organization_id IS 'Reference to the parent organization (foreign key), if applicable.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.rec_status_id IS 'Status of the record, referencing the record_status table.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.created_at IS 'Timestamp of when the record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.created_by IS 'Identifier of the user who created the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.updated_at IS 'Timestamp of the last update to the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.updated_by IS 'Identifier of the user who last updated the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.deleted_at IS 'Timestamp of when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.organization.tableName}.deleted_by IS 'Identifier of the user who deleted the record, if applicable.';

      -- Comments for the profile_status_type table
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.profile_status_type IS 'Table to store profile status types, including codes and descriptions for user profiles (e.g., INCOMPLETE, COMPLETE, PENDING_VERIFICATION).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.profile_status_type.profile_status_type_id IS 'Unique identifier for the profile status type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.profile_status_type.code IS 'Unique code representing the profile status (e.g., INCOMPLETE, COMPLETE, PENDING_VERIFICATION)';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.profile_status_type.description IS 'Optional description of the profile status type';

      -- Comments for the user_account table
      COMMENT ON TABLE ${authSchema.sqlNamespace}.user_account IS 'Table to store user account information, including personal details, profile status, and audit tracking.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.user_id IS 'Unique identifier for the user (Primary Key)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.username IS 'User’s unique username (Mandatory)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.email IS 'User’s email address (Mandatory, Unique)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.first_name IS 'User’s first name (Mandatory)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.last_name IS 'User’s last name (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.last_login_at IS 'Timestamp for the last login of the user (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.profile_status IS 'Foreign key referencing the profile_status_type table (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.metadata IS 'Additional metadata in JSON format, if any (Default to empty JSON)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.rec_status_id IS 'Foreign key referencing the record status of the user account';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.created_at IS 'Timestamp for record creation (Mandatory)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.created_by IS 'ID of the user who created the record (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.updated_at IS 'Timestamp for last update (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.updated_by IS 'ID of the user who last updated the record (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.deleted_at IS 'Timestamp for when the record was soft deleted (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.deleted_by IS 'ID of the user who deleted the record (Optional)';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.user_account.party_id IS 'party ID of the user in party table(mandatory)';


      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName} IS 'The plan_definition table outlines the protocols and plans associated with clinical research studies.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.id IS 'Unique identifier for the PlanDefinition (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.url IS 'Canonical URL for the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.version IS 'Version of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.name IS 'Name of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.title IS 'Title of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.status_id IS 'Optional when using status other than record-status (e.g., active, draft).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.experimental IS 'Indicates if it is an experimental plan.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.description IS 'Description of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.purpose IS 'Purpose of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.usage IS 'Context of use or usage guidance.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.copyright IS 'Copyright information.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.publisher IS 'Publisher of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.jurisdiction_code IS 'Jurisdiction code.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.related_artifact IS 'JSON or serialized representation of related artifacts.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.tenant_id IS 'Optional tenant ID for multi-tenancy.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.rec_status_id IS 'Status of the record (e.g., active) foreign key referencing record_status.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.created_at IS 'Record creation timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.created_by IS 'Creator’s ID.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.updated_at IS 'Last update timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.updated_by IS 'User ID of the user who updated the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.deleted_at IS 'Deletion timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.deleted_by IS 'User ID of the user who deleted the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.study_id IS 'Reference to the study ID.';

      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName} IS 'This table holds essential details about investigators and medical practitioners associated with the research studies.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.id IS 'Unique identifier for the practitioner (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.system_identifier IS 'External identifier for the practitioner (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.name IS 'Full name of the practitioner.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.gender_type_id IS 'Gender id of the practitioner (e.g., male, female).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.org_party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.birth_date IS 'Birth date of the practitioner (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.photo_url IS 'URL for the practitioner’s photo (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.tenant_id IS 'Reference to the organization ID (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.practitioner_party_id IS 'Identifier for the associated practitioner party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.rec_status_id IS 'Status of the record (foreign key referencing record_status).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.created_at IS 'Timestamp of when the record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.created_by IS 'User ID of the creator of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.updated_at IS 'Timestamp of when the record was last updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.updated_by  IS 'User ID of the updater of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.deleted_at IS 'Timestamp of when the record was deleted (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.practitioner.tableName}.deleted_by IS 'User ID of the deleter of the record (optional).';

      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName} IS 'This table stores information about patients, including personal details, contact information, and associated organization.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.id IS 'Unique identifier for the patient (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.identifier_system IS 'System used for the patient’s identifier (e.g., URL).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.identifier_value IS 'Unique identifier value for the patient within the identifier system (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.name_use IS 'Indicates the usage of the name (e.g., official, nickname).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.name_family IS 'Family name (last name) of the patient (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.name_given IS 'Given name (first name) of the patient .';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.gender_type_id IS 'Gender id of the patient (e.g., male, female, ).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.birth_date IS 'Date of birth of the patient .';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.age IS 'The participant''s age is stored here. Since participant data is anonymized, only age is retained in this column, with all other identifiable information excluded.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.address_use
      IS 'Type of address (e.g., home, work) (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.org_party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.address_line1 IS 'First line of the address (street) (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.address_city IS 'City of the patient’s address (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.address_state IS 'State of the patient’s address (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.address_postal_code IS 'Postal code of the patient’s address (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.address_country IS 'Country of the patient’s address (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.contact_relationship IS 'Type of contact relationship (e.g., emergency contact) (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.contact_name_family IS 'Family name of the contact person (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.contact_name_given IS 'Given name of the contact person (mandatory).';COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.contact_telecom_system IS 'Telecom system used to contact (e.g., phone) (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.contact_telecom_value IS 'Phone number or other telecom details for the contact (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.contact_telecom_use IS 'Use of telecom contact (e.g., mobile, home) (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.tenant_id IS 'Foreign key reference to the organization ID (references organization table).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.rec_status_id IS 'Foreign key to the record status (references record_status table).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.created_at IS 'Timestamp of when the record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.created_by IS 'User ID of the creator of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.updated_at IS 'Timestamp of when the record was last updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.updated_by IS 'User ID of the updater of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.deleted_at IS 'Timestamp of when the record was deleted (optional).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.patient.tableName}.deleted_by IS 'User ID of the deleter of the record (optional).';



      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName} IS 'This table records the qualifications of practitioners, investigators, or organizations.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.id IS 'Unique identifier for the qualification (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.issuer_name IS 'Name of the organization that issued the qualification. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.org_party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.start_date IS 'Start date indicating when the qualification became effective. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.end_date IS 'End date indicating when the qualification expired or ceased to be effective. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.tenant_id IS 'Foreign key referencing the organization table to associate the qualification with a specific tenant.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.rec_status_id IS 'Foreign key referencing the record_status table to indicate the current status of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.created_at IS 'Timestamp indicating when the record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.created_by IS 'User ID of the creator of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.updated_at IS 'Timestamp indicating when the record was last updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.updated_by IS 'User ID of the user who last updated the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.deleted_at IS 'Timestamp indicating when the record was deleted. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.qualification.tableName}.deleted_by IS 'User ID of the user who deleted the record. Optional.';


      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName} IS 'The telecom table manages contact details such as phone numbers and email addresses for various entities.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.id IS 'Primary key for the telecom table. Unique identifier for each telecom entry.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.party_id IS 'Identifier for the associated party organization or practitioner(foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.telecom_type IS 'Specifies the type of telecom (e.g., phone, email, fax). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.telecom_value IS 'The value of the telecom (e.g., phone number, email address). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.telecom_use IS 'Context or purpose of the telecom (e.g., work, personal, home). Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.tenant_id  IS 'Foreign key referencing the tenant or organization owning this telecom record. Mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.rec_status_id IS 'Foreign key referencing the record_status table to indicate the current status of the telecom record. Mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.created_at IS 'Timestamp indicating when the telecom record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.created_by IS 'Identifier of the user who created the telecom record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.updated_at IS 'Timestamp indicating the last update to the telecom record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.updated_by IS 'Identifier of the user who last updated the telecom record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.deleted_at IS 'Timestamp indicating when the telecom record was deleted (soft delete).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.telecom.tableName}.deleted_by IS 'Identifier of the user who deleted the telecom record.';



      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.address.tableName} IS 'This table stores address information for entities like patients, practitioners, and organizations.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.id IS 'Unique identifier for the address entry (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.address_type IS 'Type of address, such as work or home. This field is mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.org_party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.line1 IS 'First line of the address, such as street address or apartment number. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.line2 IS 'Second line of the address, such as suite or unit number. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.city IS 'City or locality of the address. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.state IS 'State or region of the address. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.postal_code IS 'Postal or ZIP code for the address. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.country IS 'Country of the address. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.tenant_id IS 'Foreign key referencing the organization table, associating the address with a specific tenant.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.rec_status_id IS 'Foreign key referencing the record_status table to indicate the current status of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.created_at IS 'Timestamp indicating when the record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.created_by IS 'User ID of the creator of the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.updated_at IS 'Timestamp indicating when the record was last updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.updated_by IS 'User ID of the user who last updated the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.deleted_at IS 'Timestamp indicating when the record was deleted. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.address.tableName}.deleted_by IS 'User ID of the user who deleted the record. Optional.';

      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName} IS 'The communication table captures preferences for communication, including languages and contact methods.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.id IS 'Unique identifier for the communication entry (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.language_code IS 'Language code representing the preferred language of the entity (e.g., "en" for English). Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.org_party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.preferred IS 'Indicates whether this language is the preferred language for the entity (true/false). Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.tenant_id
      IS 'Foreign key referencing the organization table, associating the communication entry with a specific tenant.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table to indicate the current status of the communication entry.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.created_at
      IS 'Timestamp indicating when the communication record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.created_by
      IS 'User ID of the creator of the communication record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.updated_at
      IS 'Timestamp indicating when the communication record was last updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.updated_by
      IS 'User ID of the user who last updated the communication record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.deleted_at
      IS 'Timestamp indicating when the communication record was deleted. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.communication.tableName}.deleted_by
      IS 'User ID of the user who deleted the communication record. Optional.';



      --  COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}
      --  IS 'This master table categorizes different types of parties involved in research, such as sponsors or collaborators. ';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.id
      --  IS 'Primary Key, auto-incremented, uniquely identifies each associated party type.';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.name
      --  IS 'Human-readable name of the party type (e.g., Sponsor), mandatory field.';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.description
      --  IS 'Detailed description of the party type, optional field.';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.rec_status_id
      --  IS 'Record status ID (foreign key), indicates the current status of the record (active, inactive, etc.).';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.created_at
      --  IS 'Timestamp indicating when the record was created.';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.created_by
      --  IS 'User ID of the creator of the record.';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.updated_at
      --  IS 'Timestamp indicating the last time the record was updated.';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.updated_by
      --  IS 'User ID of the person who last updated the record.';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.deleted_at
      --  IS 'Timestamp indicating when the record was marked as deleted (soft delete).';
      --  COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.associatedPartyType.tableName}.deleted_by
      --  IS 'User ID of the person who marked the record as deleted.';


      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName} IS 'The laboratory table documents details of labs involved in research activities, including their locations, contact information, and parent organizations.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.id IS 'Unique identifier for the laboratory entry (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.system IS 'System identifier for the laboratory (e.g., URL or standardized identifier). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.name IS 'Name of the laboratory. Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.type IS 'Type of laboratory or organization (e.g., diagnostic lab). Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.address_line IS 'Address line of the laboratory, such as street address or suite number. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.org_party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.city IS 'City where the laboratory is located. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.state IS 'State or region where the laboratory is located. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.postal_code IS 'Postal or ZIP code for the laboratory. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.country IS 'Country where the laboratory is located. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.phone IS 'Primary contact number of the laboratory. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.email IS 'Email address for the laboratory. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.contact_name IS 'Name of the primary contact person at the laboratory. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.contact_phone IS 'Phone number of the primary contact person. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.parent_org_id IS 'Identifier of the parent organization, if the laboratory is part of a larger organization. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.tenant_id IS 'Foreign key referencing the organization table, associating the laboratory with a specific tenant. Mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.rec_status_id IS 'Foreign key referencing the record_status table to indicate the current status of the laboratory record. Mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.created_at IS 'Timestamp indicating when the laboratory record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.created_by IS 'User ID of the creator of the laboratory record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.updated_at IS 'Timestamp indicating when the laboratory record was last updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.updated_by IS 'User ID of the user who last updated the laboratory record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.deleted_at IS 'Timestamp indicating when the laboratory record was deleted. Optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.laboratory.tableName}.deleted_by IS 'User ID of the user who deleted the laboratory record. Optional.';

      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName} IS 'This is another master table to store the lists of medical conditions associated with research studies. .';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.id
      IS 'Primary key for the research_study_condition table. Unique identifier for each condition entry.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.coding_system IS 'System URL for coding the condition (e.g., "http://snomed.info/sct"). Optional field.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.code IS 'Code representing the condition (e.g., "44054006"). Mandatory field.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.display
      IS 'Human-readable description of the condition (e.g., "Type 1 Diabetes Mellitus"). Optional field.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table to indicate the current status of the condition record. Mandatory';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.created_at IS 'Timestamp indicating when the condition record was created.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.created_by IS 'Identifier of the user who created the condition record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.updated_at
      IS 'Timestamp indicating the last update to the condition record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.updated_by
      IS 'Identifier of the user who last updated the condition record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.deleted_at
      IS 'Timestamp indicating when the condition record was deleted (soft delete).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyCondition.tableName}.deleted_by
      IS 'Identifier of the user who deleted the condition record.';


      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.device.tableName} IS 'This is a master table to store information about various medical devices, including their identifiers, manufacturers, types, and statuses .';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.id IS 'Primary key for the device table. Unique identifier for each device entry.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.identifier IS 'Unique identifier for the device (e.g., device ID). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.manufacturer IS 'Name of the manufacturer of the device (e.g., "Medtronic"). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.serial_number IS 'Serial number assigned to the device (e.g., "SN123456789"). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.device_name  IS 'Name or model of the device (e.g., "Insulin Pump"). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.status IS 'Current status of the device (e.g., "active", "inactive"). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.device_type  IS 'Type of the device (e.g., "Medical", "Monitoring"). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.udi_carrier IS 'Unique Device Identifier (UDI) Carrier (e.g., barcode, RFID, etc.). Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.manufacture_date IS 'Date of manufacture of the device. Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.expiration_date IS 'Expiration date of the device. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.lot_number IS 'Lot number assigned by the manufacturer to the device. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.rec_status_id IS 'Foreign key referencing the record_status table to indicate the current status of the device record. Mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.created_at IS 'Timestamp indicating when the device record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.created_by IS 'Identifier of the user who created the device record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.updated_at IS 'Timestamp indicating the last update to the device record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.updated_by IS 'Identifier of the user who last updated the device record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.deleted_at IS 'Timestamp indicating when the device record was deleted (soft delete).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}.deleted_by IS 'Identifier of the user who deleted the device record.';

      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName} IS 'This table stores the list of IONIC codes with their descriptions and classifications, used for identifying laboratory and clinical codes.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.loinc_code_id IS 'Unique identifier for the LOINC code, serves as the primary key.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.loinc_code IS 'LOINC code itself (e.g., "12345-6"), used to identify specific tests or observations.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.loinc_description IS 'Description of the LOINC code (e.g., "Glucose [Mass/volume] in Blood"), provides a human-readable explanation of the code.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.loinc_class IS 'Class of the LOINC code (e.g., "Laboratory", "Clinical"), used to categorize the LOINC code.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.loinc_type IS 'Type of the LOINC code (e.g., "Observation", "Measurement"), defines the purpose of the code.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.rec_status_id IS 'Status of the LOINC code (e.g., "active", "retired"), indicates whether the code is currently in use or retired.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.created_at IS 'Timestamp of when the record was created.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.created_by IS 'ID of the user who created the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.updated_at IS 'Timestamp of when the record was last updated.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.updated_by IS 'ID of the user who last updated the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.loincCodes.tableName}.deleted_by IS 'ID of the user who deleted the record.';


      -- Comment on the study_visibility table
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName} IS 'This table stores visibility types for studies, such as "public" or "private", determining how a study can be accessed.';
      -- Comment on each column
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.visibility_id IS 'Unique identifier for each visibility type, serves as the primary key of the table.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.visibility_name IS 'Name of the visibility (e.g., "public", "private"), defines the level of visibility for a study.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.visibility_description IS 'Description of the visibility type, provides additional details about what the visibility level entails (optional).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.rec_status_id IS 'Foreign key referencing the record_status table, indicates the current status (e.g., active, inactive) of the visibility record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.created_at IS 'Timestamp of when the record was created';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.created_by IS 'ID of the user who created the record (drh user ID).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.updated_at IS 'Timestamp of the last update to the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.updated_by IS 'User ID of the user who last updated the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyVisibility.tableName}.deleted_by IS 'User ID of the user who deleted the record, if applicable.';

      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.researchSubjectStatusDefinitions.tableName} IS 'This table defines various research subject statuses, including codes, display names, and system URLs for standardized statuses related to research subjects, as per FHIR guidelines.';

      -- Column comments

      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchSubjectStatusDefinitions.tableName}.code IS 'Unique integer code representing the research subject status (e.g., 1 = Candidate, 2 = Eligible, etc.). Mandatory for identifying the status.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchSubjectStatusDefinitions.tableName}.display_name IS 'Human-readable name for the research subject status (e.g., "Candidate", "Eligible"). This is displayed in reports or user interfaces. Mandatory.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchSubjectStatusDefinitions.tableName}.definition IS 'Detailed definition or description of what the research subject status means (e.g., "Candidate" means the person has been identified as a potential participant). Mandatory.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchSubjectStatusDefinitions.tableName}.system_url IS 'URL to the system or standard that defines the research subject status (e.g., FHIR URL for research subject statuses). Mandatory for consistency and reference.';

    --TABLE COMMENTS ai_conversation_log 
    COMMENT ON TABLE ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName} IS 'Table to store logs of AI conversations, including user questions, answers, context, and metadata for auditing and analytics.';

    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.id IS 'Primary key: Unique identifier for each AI conversation log entry.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.message_json IS 'JSON object storing the question and answer exchanged in the conversation.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.auth_provider_id IS 'Authentication provider identifier (e.g., ORCID, GitHub) associated with the user.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.user_party_id IS 'Foreign key referencing the party table; identifies the user (nullable).';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.context_section IS 'Section, tab, or category where the question was asked (context of the conversation).';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.rec_status_id IS 'Foreign key referencing record_status; indicates the current status of the log entry.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.created_at IS 'Timestamp when the conversation log entry was created.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.created_by IS 'Identifier of the user or system that created the log entry.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.updated_at IS 'Timestamp when the conversation log entry was last updated.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.updated_by IS 'Identifier of the user or system that last updated the log entry.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.deleted_at IS 'Timestamp when the conversation log entry was deleted, if applicable.';
    COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.aiConversationLog.tableName}.deleted_by IS 'Identifier of the user or system that deleted the log entry, if applicable.';

      -- Comment on the citation_identifier table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName} IS 'This table stores citation identifiers, such as DOI or PubMed ID, associated with citations.';
      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.id
      IS 'Unique identifier for each citation identifier, serves as the primary key of the table.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.citation_id
      IS 'Foreign key referencing the citation table (id), links the citation identifier to the corresponding citation record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.identifier_system
      IS 'Identifier system (e.g., DOI, PubMed), specifies the type of identifier used for the citation.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.identifier_value
      IS 'The actual identifier value (e.g., DOI number, PubMed ID), uniquely identifies the citation within the specified system.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicates the current status (e.g., active, inactive) of the citation identifier record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.created_at
      IS 'Timestamp of when the citation identifier record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.created_by
      IS 'ID of the user who created the citation identifier record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.updated_at
      IS 'Timestamp of the last update to the citation identifier record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.updated_by
      IS 'User ID of the user who last updated the citation identifier record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.deleted_at
      IS 'Timestamp when the citation identifier record was deleted, if applicable.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationIdentifier.tableName}.deleted_by
      IS 'User ID of the user who deleted the citation identifier record, if applicable.';

      -- Comment on the metric_definitions table
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.metricDefinitions.tableName} IS 'This table stores definitions for various metrics used in research studies, including metric names and descriptions.';
      -- Comment on each column
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.metricDefinitions.tableName}.metric_id IS 'Unique identifier for each metric, serves as the primary key for the table.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.metricDefinitions.tableName}.metric_name IS 'Name of the metric, must be unique across all records. This field is mandatory and represents the name or label of the metric.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.metricDefinitions.tableName}.metric_info
      IS 'Description or additional information about the metric, providing context or details about its purpose and usage. This field is mandatory.';

      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName} IS 'This is a master table stores focus areas for research studies, such as specific conditions or outcomes being analyzed.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.id IS 'Unique identifier for each research focus entry (Primary Key).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.coding_system IS 'System URL for coding, e.g., "http://snomed.info/sct".';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.code IS 'Code representing the focus, e.g., "73211009".';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.display IS 'Human-readable description, e.g., "Diabetes mellitus".';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.rec_status_id IS 'Record status ID (foreign key).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.created_at IS 'The timestamp when the focus record was created.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.created_by IS 'The ID of the user or system that created the focus record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.updated_at IS 'The timestamp of the last update made to the focus record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.updated_by IS 'The ID of the user who made the last update to the focus record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.deleted_at IS 'The timestamp when the focus record was deleted, if applicable.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.researchStudyFocus.tableName}.deleted_by IS 'The ID of the user who deleted the focus record, if applicable.';


      -- Comment on the research_study table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName} IS 'This table captures the core details of each research study conducted under the DRH project. It includes metadata like study title, description, timeline, and associated parties, along with references to study conditions and protocols.';
      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.study_id
      IS 'Unique identifier for the research study, serves as the primary key for the table.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.${ddlTable.researchStudy.tableName}_identifier
      IS 'Business identifier for the research study, typically used for referencing the study in business processes or external systems. storing NCT numbers.This field is mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.study_display_id
      IS 'Display ID for the study, primarily used for representing the study in user interfaces or reports. This field is not mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.title
      IS 'Title of the research study, providing a concise description of the study. This field is mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.description
      IS 'Detailed description of the study, explaining the scope, goals, and methodologies.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.status_id
      IS 'Foreign key referencing the status table, indicating the current status of the study (e.g., active, completed, halted). This field is mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.start_date
      IS 'Start date of the study, marking when the research study officially begins. This field is mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.end_date
      IS 'End date of the study, marking when the research study concludes. This field is mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.focus_id
      IS 'Reference to the research focus being studied, such as a disease, condition, or scientific topic. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.condition_code
      IS 'Standardized code representing the condition being studied in the research, using a system such as ICD-10 or SNOMED. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.region_code
      IS 'Geographic region code where the study is being conducted, such as country or region identifier. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.site_id
      IS 'Reference to the location where the study is conducted, such as the hospital or research facility. This field is mandatory.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.treatment_modality
      IS 'Optional field containing details about the treatment modalities, typically used to describe the different groups or interventions in the study.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.protocol_reference_id
      IS 'Reference to the protocol document for the study, outlining the methodology, procedures, and design. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.phase
      IS 'Phase of the study (e.g., Phase I, II, III, IV), indicating the stage of the research. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.primary_purpose_type
      IS 'Primary purpose of the study, such as treatment, diagnostic, prevention, etc. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.keywords
      IS 'Keywords associated with the study, used to identify important topics or focus areas. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.progress_status
      IS 'Current progress status of the study, such as recruiting, completed, or suspended. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.why_stopped
      IS 'Reason for stopping the study, if applicable (e.g., recruitment issues, safety concerns). Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.eligibility_criteria
      IS 'Inclusion and exclusion criteria for participants in the study, outlining who can participate based on specific characteristics. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.target_enrollment
      IS 'Targeted number of participants for the study, representing the estimated sample size. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.actual_enrollment
      IS 'Actual number of participants enrolled in the study, showing the current participant count. Optional field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.tenant_id
      IS 'Reference to the organization or tenant conducting the research study, mandatory for multi-tenant systems.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicating the status of the research record (e.g., active, archived). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.created_at
      IS 'Timestamp indicating when the study record was created in the database.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.created_by
      IS 'ID of the user who created the study record, used for auditing purposes.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.updated_at
      IS 'Timestamp indicating the last time the study record was updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.updated_by
      IS 'ID of the user who last updated the study record, used for auditing purposes.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.deleted_at
      IS 'Timestamp indicating when the study record was deleted, if applicable.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.deleted_by
      IS 'ID of the user who deleted the study record, used for auditing purposes.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.visibility
      IS 'Foreign key referencing the study visibility table, indicating the visibility of the study (e.g., public, private). Mandatory field.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudy.tableName}.archive_status
      IS 'indicating the study is archived or not using boolean value. Mandatory field.';



      -- Comment on the research_subject table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName} IS 'This table stores information about participants enrolled in each research study. It links individuals to their respective studies, capturing details such as participant identifiers, status, and assigned study arms.';

      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.rsubject_id
      IS 'Unique identifier for the research subject, serves as the primary key for the table.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.participant_identifier
      IS 'Study-specific identifier for the participant, used to reference the subject in the context of a specific research study.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.study_reference
      IS 'Foreign key referencing the research_study table, indicating the study that the subject is part of.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.individual_reference
      IS 'Foreign key referencing the patient table, linking the subject to the corresponding patient record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.status_id
      IS 'Current status of the research subject in the study (e.g., enrolled, completed, dropped out).';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.group
      IS 'Study arm or group assigned to the subject within the study (e.g., treatment group, control group).';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.diabetes_type
      IS 'Type of diabetes diagnosed for the participant, if applicable (e.g., Type 1, Type 2).';

      -- COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.site_id
      -- IS 'Identifier for the study location where the subject is participating, can be null if not assigned.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.diagnosis_icd
      IS 'ICD (International Classification of Diseases) code for the subject’s diagnosis, used for medical classification and coding.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.med_rxnorm
      IS 'RxNorm code for the subject’s medication, used for standardizing drug names and identifiers.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.treatment_modality
      IS 'Structured JSON object containing details of the treatment modality assigned to the subject, such as drug regimen, therapy type, etc.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.race_type_id
      IS 'Race type id of the research subject';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.ethnicity_type_id
      IS 'Ethnicity type id of the research subject';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.tenant_id
      IS 'Foreign key referencing the organization table, indicating which organization owns or is responsible for the study and the subject.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicating the current status of the research subject record (e.g., active, inactive).';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.created_at
      IS 'Timestamp indicating when the research subject record was created.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.created_by
      IS 'User ID of the person who created the research subject record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.updated_at
      IS 'Timestamp of the last update made to the research subject record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.updated_by
      IS 'User ID of the person who last updated the research subject record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.deleted_at
      IS 'Timestamp indicating when the research subject record was deleted, if applicable.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchSubject.tableName}.deleted_by
      IS 'User ID of the person who deleted the research subject record, if applicable.';



      --- Comment on the investigator_study table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName} IS 'This table establishes a mapping between investigators and the research studies they are involved in. It tracks the role of the investigator, their start and end dates of involvement, and the relationships between the investigator and the research study.';

      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.mapping_id
      IS 'Primary key for the investigator_study table, uniquely identifying each mapping record between an investigator and a study.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.investigator_id
      IS 'Foreign key referencing the practitioner (investigator) table, linking the investigator to the specific study they are associated with.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.study_id
      IS 'Foreign key referencing the research study table, linking the study to the specific investigator.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.role
      IS 'Role of the investigator in the research study (e.g., Principal Investigator, Co-Investigator). This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.start_date
      IS 'Start date of the investigators involvement in the study, optional field for tracking the period of their participation.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.end_date
      IS 'End date of the investigators involvement in the study, optional field to indicate when the investigator is no longer part of the study.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.created_at
      IS 'Timestamp indicating when the investigator_study mapping record was created.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.updated_at
      IS 'Timestamp of the last update made to the investigator_study mapping record.';

      --

      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName} IS 'The plan_definition table outlines the protocols and plans associated with clinical research studies.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.id IS 'Unique identifier for the PlanDefinition (Primary Key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.url IS 'Canonical URL for the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.version IS 'Version of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.name IS 'Name of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.title IS 'Title of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.org_party_id IS 'Identifier for the associated organization party (foreign key).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.status_id IS 'Optional when using status other than record-status (e.g., active, draft).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.experimental IS 'Indicates if it is an experimental plan.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.description IS 'Description of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.purpose IS 'Purpose of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.usage IS 'Context of use or usage guidance.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.copyright IS 'Copyright information.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.publisher IS 'Publisher of the PlanDefinition.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.jurisdiction_code IS 'Jurisdiction code.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.related_artifact IS 'JSON or serialized representation of related artifacts.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.tenant_id IS 'Optional tenant ID for multi-tenancy.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.rec_status_id IS 'Status of the record (e.g., active) foreign key referencing record_status.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.created_at IS 'Record creation timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.created_by IS 'Creator’s ID.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.updated_at IS 'Last update timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.updated_by IS 'User ID of the user who updated the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.deleted_at IS 'Deletion timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.deleted_by IS 'User ID of the user who deleted the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.planDefinition.tableName}.study_id IS 'Reference to the study ID.';


      -- Comment on the goal table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName} IS 'The goal table defines objectives for research studies, including their priorities, outcomes, and associated conditions.';

      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.id
      IS 'Primary key for the goal table, auto-generated to uniquely identify each goal.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.plan_definition_id
      IS 'Foreign key referencing the PlanDefinition table, establishing the connection between the goal and a specific plan definition. This is a mandatory field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.identifier
      IS 'Business-specific identifier for the goal, used to reference the goal in external systems. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.category
      IS 'Category of the goal (e.g., treatment, diagnostic), helping to classify the goal. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.description
      IS 'Description providing details about the goal, including its purpose and scope. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.priority
      IS 'Priority level of the goal (e.g., high, medium, low), indicating its importance in the overall plan. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.start
      IS 'Starting point for the goal (e.g., date or condition), marking the initiation of the goal. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.conditions_addressed
      IS 'Issues or conditions that the goal aims to address, providing context for its focus. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.outcome_code
      IS 'Coded value representing the outcome associated with the goal, providing standardization for reporting. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.status_id
      IS 'Foreign key referencing the status table, indicating the current status of the goal (e.g., active, completed, deferred). Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.status_reason
      IS 'Explanation for the current status of the goal, providing additional context. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.tenant_id
      IS 'Foreign key referencing the organization table, linking the goal to a specific organization. This is a mandatory field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicating the current record status of the goal (e.g., active, inactive). This is a mandatory field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.created_at
      IS 'Timestamp indicating when the goal record was created.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.created_by
      IS 'User ID of the person who created the goal record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.updated_at
      IS 'Timestamp of the last update made to the goal record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.updated_by
      IS 'User ID of the person who last updated the goal record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.deleted_at
      IS 'Timestamp indicating when the goal record was deleted, if applicable.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.goal.tableName}.deleted_by
      IS 'User ID of the person who deleted the goal record, if applicable.';


      -- Comment on the activity_definition table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName} IS 'This table describes specific activities associated with research studies, including timing, location, and participants..';

      -- Comment on each column of the activity_definition table
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.id
      IS 'Primary key for the activity_definition table, auto-generated to uniquely identify each activity definition record. This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.plan_definition_id
      IS 'Foreign key referencing the plan_definition table, associating the activity definition with a specific plan. This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.identifier
      IS 'Optional business identifier for the activity definition, used for additional tracking or identification purposes.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.status_id
      IS 'Status identifier for the activity . This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.name
      IS 'Name of the activity . This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.description
      IS 'Detailed description of the activity, providing further context about its purpose or execution. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.timing
      IS 'Timing or schedule information for the activity, such as specific dates or timeframes when the activity is to occur. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.location
      IS 'Foreign key referencing the location table, specifying where the activity will take place. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.participant
      IS 'Foreign key referencing the patient table, identifying the participant in the activity. This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.type
      IS 'Type of activity (e.g., "procedure", "observation"). This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.reason_code
      IS 'Justification or reason for the activity, explaining why the activity is being carried out. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.goal_id
      IS 'Foreign key referencing the goal table, associating the activity with a specific goal. This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.output
      IS 'Outputs or deliverables expected from the activity (e.g., "test results", "medical report"). This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.tenant_id
      IS 'Foreign key referencing the organization table, indicating the organization responsible for the activity definition. This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicating the current status of the activity record (e.g., "active", "inactive"). This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.created_at
      IS 'Timestamp indicating when the activity definition record was created. This field is set to the current timestamp by default.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.created_by
      IS 'ID of the user who created the activity definition record. This field is set by default to the ID of the creator.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.updated_at
      IS 'Timestamp indicating when the activity definition record was last updated. This field is set to the current timestamp by default.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.updated_by
      IS 'ID of the user who last updated the activity definition record. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.deleted_at
      IS 'Timestamp indicating when the activity definition record was deleted, if applicable. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.activityDefinition.tableName}.deleted_by
      IS 'ID of the user who deleted the activity definition record, if applicable. This field is optional.';




      -- Comment on the subject_observation table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName} IS 'This table is used to store the various observations in the research participant like BMI,baseline_hba1c etc.';

      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.id
      IS 'Primary key for the subject_observation table, auto-generated to uniquely identify each observation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.${ddlTable.researchSubject.tableName}_id
      IS 'Foreign key referencing the ResearchSubject table, linking the observation to a specific research subject. This is a mandatory field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.code
      IS 'Code representing the type of observation (e.g., lab test, vital sign, etc.), mandatory to define the observation.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.category
      IS 'Category of the observation (e.g., clinical, lab, etc.), providing additional context. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.value
      IS 'The value of the observation, such as a measurement or result. This is a mandatory field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.unit
      IS 'The unit of measurement for the observed value (e.g., mg/dL, mmHg). Mandatory field for defining the observation.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.effective_datetime
      IS 'Date and time when the observation was made, providing temporal context. Optional field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.tenant_id
      IS 'Foreign key referencing the organization table, linking the observation to a specific organization. This is a mandatory field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicating the current record status of the observation (e.g., active, inactive). This is a mandatory field.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.created_at
      IS 'Timestamp indicating when the observation record was created.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.created_by
      IS 'User ID of the person who created the observation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.updated_at
      IS 'Timestamp of the last update made to the observation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.updated_by
      IS 'User ID of the person who last updated the observation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.deleted_at
      IS 'Timestamp indicating when the observation record was deleted, if applicable.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.subjectObservation.tableName}.deleted_by
      IS 'User ID of the person who deleted the observation record, if applicable.';



      -- Comment on the citation table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName} IS 'This table stores citation information related to research studies, including references to published papers, articles, or other academic resources. The citation includes metadata like identifier, title, publisher, journal details, and status.';

      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.id
      IS 'Primary key for the citation table, auto-incremented to uniquely identify each citation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.url
      IS 'URL for the citation, if available. This field is optional and may contain a link to the full text or publisher page.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.identifier_system
      IS 'The identifier system used for the citation (e.g., DOI, PubMed), mandatory to categorize the citation by its type.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.identifier_value
      IS 'The actual identifier value (e.g., DOI number, PubMed ID), mandatory to reference the specific citation.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.title
      IS 'Title of the citation, mandatory to provide a descriptive reference for the research study.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.status
      IS 'Status of the citation (e.g., active, withdrawn), providing information on the current validity of the citation. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.date
      IS 'Date of publication for the citation, providing temporal context to the reference. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.publisher
      IS 'Publisher of the citation, indicating who published the resource. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.abstract
      IS 'Abstract of the citation, providing a summary of the referenced work. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.journal_title
      IS 'Title of the journal or publication where the citation appears, if applicable. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.journal_volume
      IS 'Volume number of the journal, if applicable. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.journal_issue
      IS 'Issue number of the journal, if applicable. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.journal_page
      IS 'Page numbers for the article or citation in the journal, if applicable. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.publication_date
      IS 'Date when the journal issue or article was published. This field is optional.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicating the current record status of the citation (e.g., active, inactive). This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.created_at
      IS 'Timestamp indicating when the citation record was created.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.created_by
      IS 'User ID of the person who created the citation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.updated_at
      IS 'Timestamp of the last update made to the citation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.updated_by
      IS 'User ID of the person who last updated the citation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.deleted_at
      IS 'Timestamp indicating when the citation record was deleted, if applicable.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.deleted_by
      IS 'User ID of the person who deleted the citation record, if applicable.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citation.tableName}.citation_data_source
      IS 'Source system or database from which the citation was imported or retrieved (e.g., PubMed, DOI, etc.).';





      -- Comment on the citation_author table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName} IS 'This table stores information about authors of citations referenced in research studies. Each author is associated with a specific citation and includes their name, institutional affiliation, ORCID identifier, email, and record status.';

      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.id
      IS 'Primary key for the citation_author table, auto-incremented to uniquely identify each author record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.citation_id
      IS 'Foreign key referencing the citation table, linking the author to a specific citation record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.first_name
      IS 'Authors first name, mandatory field for identifying the author.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.last_name
      IS 'Authors last name, mandatory field for identifying the author.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.middle_name
      IS 'Authors middle name, optional field for providing additional name information.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.affiliation
      IS 'Authors institutional affiliation, optional field to indicate the institution the author is associated with.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.orcid
      IS 'ORCID identifier for the author, optional field for tracking academic authorship uniquely across research platforms.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.email
      IS 'Authors email address, optional field for contacting the author.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.role_id
      IS 'Unique identifier for the  role record referencing to research_study_party_role table';


      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.party_id
      IS 'party ID of the user in party table';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, indicating the current record status of the citation author (e.g., active, inactive). This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.created_at
      IS 'Timestamp indicating when the citation author record was created.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.created_by
      IS 'User ID of the person who created the citation author record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.updated_at
      IS 'Timestamp of the last update made to the citation author record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.updated_by
      IS 'User ID of the person who last updated the citation author record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.deleted_at
      IS 'Timestamp indicating when the citation author record was deleted, if applicable.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.citationAuthor.tableName}.deleted_by
      IS 'User ID of the person who deleted the citation author record, if applicable.';


      -- Comment on the investigator_study table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName} IS 'This table establishes a mapping between investigators and the research studies they are involved in. It tracks the role of the investigator, their start and end dates of involvement, and the relationships between the investigator and the research study.';
      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.mapping_id
      IS 'Primary key for the investigator_study table, uniquely identifying each mapping record between an investigator and a study.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.investigator_id
      IS 'Foreign key referencing the practitioner (investigator) table, linking the investigator to the specific study they are associated with.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.study_id
      IS 'Foreign key referencing the research study table, linking the study to the specific investigator.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.role
      IS 'Role of the investigator in the research study (e.g., Principal Investigator, Co-Investigator). This field is optional.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.start_date
      IS 'Start date of the investigators involvement in the study, optional field for tracking the period of their participation.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.end_date
      IS 'End date of the investigators involvement in the study, optional field to indicate when the investigator is no longer part of the study.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.created_at
      IS 'Timestamp indicating when the investigator_study mapping record was created.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.investigatorStudy.tableName}.updated_at
      IS 'Timestamp of the last update made to the investigator_study mapping record.';





      -- Comment on the external_auth_mappings table
      COMMENT ON TABLE ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName} IS 'This table stores the mappings between the internal user system and external authentication providers (e.g., ORCID, GitHub, Google). It includes details such as the user ID, authentication provider, external user ID, OAuth tokens, and the status of the authentication method.';

      -- Comment on each column
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.id
      IS 'Primary key for the external_auth_mappings table, uniquely identifying each authentication mapping record.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.user_id
      IS 'Foreign key referencing the user account table (user id), linking the external authentication provider with an internal user. This field is mandatory.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.auth_provider
      IS 'The authentication provider (e.g., "ORCID", "GitHub", "Google") used for the external authentication. This field is mandatory.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.provider_user_id
      IS 'The unique identifier for the user on the external platform, which is mandatory for linking the external authentication.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.access_token
      IS 'OAuth access token used for authentication with the external provider. This field is optional.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.refresh_token
      IS 'OAuth refresh token used to obtain a new access token. This field is optional.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.status
      IS 'The status of the authentication method, indicating whether it is active or revoked. This field is mandatory.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.rec_status_id
      IS 'Foreign key referencing the record_status table, used to track the current status of the record (e.g., Active, Deleted). This field is mandatory.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.created_at
      IS 'Timestamp indicating when the external authentication mapping was created in the system.';

      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.externalAuthMappings.tableName}.updated_at
      IS 'Timestamp indicating the last update made to the external authentication mapping record.';


         -- Comment on the study_collaboration table
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName} IS 'This table records the collaboration details for a specific study. It associates a study with users who have access to the study, along with their access level and timestamps for when the collaboration was shared and when the record was created, updated, or deleted.';

      -- Comment on each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.collab_id
      IS 'Primary key for the study_collaboration table, uniquely identifying each collaboration record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.study_id
      IS 'Foreign key referencing the study table, linking the collaboration to the specific study. This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.user_id
      IS 'Foreign key referencing the user table, linking the collaboration to the specific user involved in the study. This field is mandatory.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.access_level
      IS 'The level of access granted to the user for the study (e.g., read, write, admin).';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.shared_at
      IS 'Timestamp indicating when the study collaboration was shared with the user.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.rec_status_id
      IS 'Foreign key referencing the record status table, used to track the current status of the collaboration record (e.g., Active, Deleted).';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.created_at
      IS 'Timestamp indicating when the study collaboration record was created in the system.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.created_by
      IS 'ID of the user who created the collaboration record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.updated_at
      IS 'Timestamp of the last update made to the study collaboration record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.updated_by
      IS 'ID of the user who last updated the collaboration record.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.deleted_at
      IS 'Timestamp indicating when the study collaboration record was deleted, if applicable.';

      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyCollaboration.tableName}.deleted_by
      IS 'ID of the user who deleted the collaboration record, if applicable.';


      -- Comment on the activity_log table
      COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName} IS 'This table stores log records for various activities performed in the system. It tracks events such as user actions, system events, or other significant activities, and includes metadata such as session, IP address, platform, and additional custom data.';

      -- Comment on each column
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_id
      IS 'Primary key for the activity_log table, uniquely identifying each activity record. This field is auto-incremented and mandatory.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_name
      IS 'Name of the activity performed (e.g., "User Login", "Data Update"). This field is mandatory.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_type
      IS 'Type of activity (e.g., "user_action", "system_event"). This field is mandatory.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_description
      IS 'An optional description of the activity providing more details about what occurred.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.root_id
      IS 'Root activity ID, if this activity is part of a hierarchical activity structure. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.parent_id
      IS 'Parent activity ID, if this activity is a child or related to another activity in a hierarchy. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_hierarchy
      IS 'Information about the activity hierarchy, such as levels or stages, if applicable. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.hierarchy_path
      IS 'Path in the activity hierarchy, representing the sequence or relationship of the activity within the structure. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.request_url
      IS 'Associated request URL for the activity, if relevant (e.g., URL of the API or page the activity was triggered from). This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.organization_party_id
      IS 'Reference to the organization party ID in the party table';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.platform
      IS 'The platform where the activity occurred (e.g., "web", "mobile"). This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.environment
      IS 'The environment in which the activity took place (e.g., "production", "staging"). This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.created_by
      IS 'ID of the user who created the activity log entry. This field is optional, but typically filled with the ID of the system user.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.user_name
      IS 'Username of the individual who triggered the activity. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.created_at
      IS 'Timestamp indicating when the activity was logged. This field is set to the current timestamp by default.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.app_version
      IS 'Version of the application where the activity was recorded. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.test_case
      IS 'Associated test case if the activity is related to a specific test. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.session_id
      IS 'Session ID where the activity was recorded, which could link the activity to a specific user session. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.linkage_id
      IS 'Identifier linking the activity to related or dependent events, if any. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.ip_address
      IS 'IP address where the activity was logged from. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.location_latitude
      IS 'Latitude of the location where the activity was logged. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.location_longitude
      IS 'Longitude of the location where the activity was logged. This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_data
      IS 'Additional custom data related to the activity, in a flexible format (e.g., JSON). This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.activity_log_level
      IS 'Log level indicating the severity or type of the activity (e.g., "info", "debug", "error"). This field is optional.';

      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.activityLog.tableName}.session_unique_id
      IS 'Unique session identifier for the activity, linking related activities or events within the same session. This field is mandatory.';



      -- Table comment
      COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName} IS 'This table stores logs of exceptions, including details about the error, its context, resolution status, and associated query parameters.';

      -- Column comments
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.id IS 'Unique identifier for the exception, primary key';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.function_name IS 'Name of the function where the exception occurred';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.error_code IS 'PostgreSQL error code associated with the exception';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.error_message IS 'Detailed error message describing the exception';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.error_detail IS 'Additional details about the error, if available';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.error_hint IS 'Suggested hint for resolving the error, if available';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.error_context IS 'Context in which the error occurred, helping to understand its source';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.query IS 'SQL query that caused the exception, if applicable';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.parameters IS 'Parameters involved in the query or function call that caused the error';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.occurred_at IS 'Timestamp of when the exception occurred';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.resolved IS 'Indicates whether the issue has been resolved (e.g., "Yes", "No")';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.resolved_at IS 'Timestamp when the issue was resolved, if applicable';
      COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.exceptionLog.tableName}.resolver_comments IS 'Comments about the resolution or actions taken to resolve the exception';



      -- Comment on the filter_interaction table
      COMMENT ON TABLE ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName} IS 'This table stores interactions with filters, such as filter configurations and their metadata. It tracks information about filter types, view modes, and the users who created or updated the filter settings.';

      -- Comment on each column of the filter_interaction table
      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.filter_interaction_id
      IS 'Primary key for the filter_interaction table, uniquely identifying each filter interaction record. This field is auto-incremented and mandatory.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.filter_name
      IS 'Name of the filter (e.g., "Date Range", "Category"). This field is mandatory.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.filter_type
      IS 'Type of the filter (e.g., "date", "category"). This field is mandatory.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.filter_description
      IS 'An optional description of the filter interaction, providing more details about its purpose or usage.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.view_mode
      IS 'Optional view mode, indicating how the filter is visualized or presented in the interface (e.g., "list", "grid").';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.created_by
      IS 'ID of the user who created the filter interaction record. This field is optional.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.updated_by
      IS 'ID of the user who last updated the filter interaction record. This field is optional.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.created_at
      IS 'Timestamp indicating when the filter interaction record was created. This field is set to the current timestamp by default.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.updated_at
      IS 'Timestamp indicating when the filter interaction record was last updated. This field is set to the current timestamp by default.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.filterInteraction.tableName}.filter
      IS 'Filter criteria or configuration, which defines the parameters or settings for the filter. This field must be provided.';

      -- Comment on the vanna_ai_request_response table
      COMMENT ON TABLE ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName} IS 'This table stores responses to AI-based requests, including the question asked, related SQL queries, and any results or JSON data returned from the AI processing.';

      -- Comment on each column of the vanna_ai_request_response table
      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.id
      IS 'Primary key for the vanna_ai_request_response table, uniquely identifying each request response record. This field is mandatory.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.question
      IS 'Question asked in the AI request, such as an inquiry or query related to the system. This field is mandatory.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.sql_query
      IS 'Optional SQL query that is associated with the AI request, used to fetch data or trigger the AI process. This field is optional.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.results
      IS 'Optional field for storing the results returned from an AI query, such as processed data or responses from the AI.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.json_result
      IS 'Optional field for storing the JSON response returned from the AI, representing the structured data returned in JSON format.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.created_at
      IS 'Timestamp indicating when the request response was created. This field is set to the current timestamp by default.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.updated_at
      IS 'Timestamp indicating when the request response was last updated. This field is set to the current timestamp by default.';

      COMMENT ON COLUMN ${aiSchema.sqlNamespace}.${ddlTable.vannaAiRequestResponse.tableName}.created_by
      IS 'ID of the user who created the AI request response record. This field is optional.';


      -- Comment on the study_status_master table
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.studyStatusDefinitions.tableName} IS 'This table defines various study statuses, including codes, display names, and system URLs for standardized statuses related to study progress, as per FHIR guidelines.';

      -- Comment on each column of the study_status_master table
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyStatusDefinitions.tableName}.code IS 'Unique integer code representing the study status (e.g., 1 = Active, 2 = Completed, etc.). Mandatory for identifying the status.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyStatusDefinitions.tableName}.display_name IS 'Human-readable name for the study status (e.g., "Active", "Completed"). This is displayed in reports or user interfaces. Mandatory.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyStatusDefinitions.tableName}.definition IS 'Detailed definition or description of what the study status means (e.g., "Active" could mean the study is actively recruiting participants). Mandatory.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyStatusDefinitions.tableName}.system_url IS 'URL to the system or standard that defines the study status (e.g., FHIR URL for study statuses). Mandatory for consistency and reference.';


      -- Comment on the citation_status_master table
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.citationStatusDefinitions.tableName} IS 'This table defines various citation statuses, including codes, display names, and system URLs for citation statuses as per the standard or FHIR guidelines.';

      -- Comment on each column of the citation_status_master table
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.citationStatusDefinitions.tableName}.code IS 'Unique integer code representing the citation status (e.g., 1 = Published, 2 = In Review, etc.). Mandatory for identifying the citation status.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.citationStatusDefinitions.tableName}.display_name IS 'Human-readable name for the citation status (e.g., "Published", "In Review"). This is displayed in reports or user interfaces. Mandatory.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.citationStatusDefinitions.tableName}.definition IS 'Detailed definition or description of what the citation status means (e.g., "Published" could mean the citation is publicly available). Mandatory.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.citationStatusDefinitions.tableName}.system_url IS 'URL to the system or standard that defines the citation status . Mandatory for consistency and reference.';


      -- Comment on the investigator_study_role table
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.investigatorStudyRoleDefinitions.tableName} IS 'This table is useful for maintaining a list of unique roles related to an investigators study';

      -- Comment on each column of the investigator_study_role table
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.investigatorStudyRoleDefinitions.tableName}.code IS 'A serial type column, which automatically generates unique sequential integers';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.investigatorStudyRoleDefinitions.tableName}.role IS 'column that stores textual data';


      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName} IS 'This table maps study participants to files, including their content and metadata.';

      -- Add comments for each column
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.rs_file_map_id IS 'Unique identifier for the file mapping record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.study_id IS 'Foreign key referencing the study table, mandatory for associating the file with a specific study.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.file_url IS 'URL or file path where the file is stored, mandatory for locating the file.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.file_content_json IS 'JSON content of the uploaded file, mandatory for storing the file''s content in JSON format.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.tenant_id IS 'Reference to the organization ID, mandatory for identifying the organization associated with the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.rec_status_id IS 'Status ID for the record, mandatory for tracking the record status.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.created_at IS 'Timestamp of when the record was created, automatically set to the current timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.created_by IS 'User ID of the person who created the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.updated_at IS 'Timestamp of when the record was last updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.updated_by IS 'User ID of the person who last updated the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyFileMapping.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName} IS 'Table to store race types with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.race_type_id IS 'Unique identifier for the race type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.code IS 'Code representing the race type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_uri IS 'The uri where the code is defined';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_oid IS 'Coding system (e.g., urn:oid:2.16.840.1.113883.6.238)';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.display IS 'Display value for the race type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.race_text IS 'Textual representation of the race';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.created_at IS 'Timestamp when the record was created';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.created_by IS 'Identifier of the user who created the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.updated_at IS 'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.updated_by IS 'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.deleted_at IS 'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.deleted_by IS 'Identifier of the user who marked the record as deleted';


      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName} IS 'Table to store ethnicity types with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.ethnicity_type_id IS 'Unique identifier for the ethnicity type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.code IS 'Code representing the ethnicity type';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_uri IS 'The uri where the code is defined';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.raceType.tableName}.system_oid IS 'Coding system (e.g., urn:oid:2.16.840.1.113883.6.238)';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.ethnicity_text IS 'Textual representation of the ethnicity';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.created_at IS 'Timestamp when the record was created';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.created_by IS 'Identifier of the user who created the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.updated_at IS 'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.updated_by IS 'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.deleted_at IS 'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.ethnicityType.tableName}.deleted_by IS 'Identifier of the user who marked the record as deleted';

      -- Table-level comment
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName} IS 'Stores roles of parties associated with a research study, such as sponsor, investigator, etc.';

      -- Column-level comments
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.study_party_role_id IS 'Primary key, unique identifier for the study party role (e.g., sponsor, investigator).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.code IS 'Unique code representing the role, derived from a coding system (e.g., sponsor, lead-sponsor, etc.).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.system IS 'URI of the coding system defining the role (e.g., http://hl7.org/fhir/research-study-party-role).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.display IS 'Human-readable display name for the role (e.g., Doctor, Nurse).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.description IS 'Detailed description or additional information about the role.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.rec_status_id IS 'Foreign key linking to record status, indicating the status of the role (e.g., active, inactive).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.created_at IS 'Timestamp indicating when the record was created. Defaults to the current timestamp.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.created_by IS 'Identifier of the user or system that created the record. Defaults to "UNKNOWN".';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.updated_at IS 'Timestamp indicating the last time the record was updated.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.updated_by IS 'Identifier of the user or system that last updated the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.deleted_at IS 'Timestamp indicating when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyPartyRole.tableName}.deleted_by IS 'Identifier of the user or system that deleted the record, if applicable.';

      -- Table-level comment
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName} IS 'Stores classification types for organizations associated with research studies, such as NIH, FDA, Government, Nonprofit, Academic, or Industry.';

      -- Column-level comments
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.organization_type_id IS 'Primary key, unique identifier for the organization type (e.g., NIH, FDA).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.code IS 'Unique code representing the organization type (e.g., nih, fda, government, etc.).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.system_uri IS 'URI of the coding system defining the organization type (e.g., http://hl7.org/fhir/research-study-party-organization-type).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.display IS 'Human-readable display name for the organization type (e.g., NIH, FDA, Government).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.description IS 'Detailed description or additional information about the organization type.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.rec_status_id IS 'Foreign key linking to record status, indicating the status of the organization type (e.g., active, inactive).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.created_at IS 'Timestamp indicating when the record was created. Defaults to the current timestamp.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.created_by IS 'Identifier of the user or system that created the record. Defaults to "UNKNOWN".';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.updated_at IS 'Timestamp indicating the last time the record was updated.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.updated_by IS 'Identifier of the user or system that last updated the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.deleted_at IS 'Timestamp indicating when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.organizationType.tableName}.deleted_by IS 'Identifier of the user or system that deleted the record, if applicable.';

      -- Comment on the activity_type table
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName} IS 'This table defines various activity (log) types, including codes, titles etc.';

      -- Comment on each column of the activity_type table
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.id IS 'Unique identity for activity_type table, primary key';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.code IS 'Unique code representing the activity type code. Mandatory for identifying the activity type.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.title IS 'Unique title for activity type table. Title is Mandatory.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.description IS 'Detailed description or additional information about the activity type.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.rec_status_id IS 'Foreign key linking to record status, indicating the status of the activity type (e.g., active, inactive).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.created_at IS 'Timestamp indicating when the record was created. Defaults to the current timestamp.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.created_by IS 'Identifier of the user or system that created the record. Defaults to "UNKNOWN".';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.updated_at IS 'Timestamp indicating the last time the record was updated.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.updated_by IS 'Identifier of the user or system that last updated the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.deleted_at IS 'Timestamp indicating when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityType.tableName}.deleted_by IS 'Identifier of the user or system that deleted the record, if applicable.';

      -- Table-level comment
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName} IS 'Stores information about parties associated with a research study, such as sponsors, investigators, and collaborators, along with their roles and classifications.';

      -- Column-level comments
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.associated_party_id IS 'Primary key, unique identifier for the associated party record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.research_study_id IS 'Foreign key linking to the research study table. Represents the study to which the associated party is linked.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.party_role_type_id IS 'Foreign key linking to the research study party role table. Represents the role of the associated party (e.g., sponsor, investigator).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.party_id IS 'Foreign key linking to a party table, if details about the party (e.g., Practitioner) are available.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.party_name IS 'Name of the associated party (if no linked party record exists).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.classifier_id IS 'Foreign key linking to the organization type table. Represents the classification of the associated party (e.g., NIH, FDA).';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.period_start IS 'Start date of the party’s association with the research study.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.period_end IS 'End date of the party’s association with the research study.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.created_at IS 'Timestamp indicating when the record was created. Defaults to the current timestamp.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.created_by IS 'Identifier of the user or system that created the record. Defaults to "UNKNOWN".';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.updated_at IS 'Timestamp indicating the last time the record was updated.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.updated_by IS 'Identifier of the user or system that last updated the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.deleted_at IS 'Timestamp indicating when the record was deleted, if applicable.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.researchStudyAssociatedParty.tableName}.deleted_by IS 'Identifier of the user or system that deleted the record, if applicable.';

      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.contactType.tableName} IS
      'Table to store contact types with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.contact_type_id IS
      'Unique identifier for the contact type';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.code IS
      'Code representing the contact type (e.g., EMAIL, PHONE)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.value IS
      'Display value for the contact type (e.g., Email Address, Phone Number)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.updated_at IS
      'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.updated_by IS
      'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.contactType.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the contact type';

      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.execCtx.tableName} IS
      'Table to store execution contexts with associated metadata, including codes and display values.';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.execCtx.tableName}.code IS
      'Code representing the execution context (e.g., BATCH_PROCESS, REAL_TIME).';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.execCtx.tableName}.value IS
      'Human-readable value for the execution context (e.g., Batch Processing, Real-Time Processing).';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.execCtx.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp.';


        -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.genderType.tableName} IS
      'Table to store gender types with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';
      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.gender_type_id IS
      'Unique identifier for the gender type';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.code IS
      'Code representing the gender type (e.g., MALE, FEMALE, NON_BINARY)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.value IS
      'Display value for the gender type (e.g., Male, Female, Non-Binary)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.updated_at IS
      'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.updated_by IS
      'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.genderType.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the gender type';


              -- Table Comment
        COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName} IS
        'Table to store organization role types with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';

        -- Column Comments
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.organization_role_type_id IS
        'Unique identifier for the organization role type';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.code IS
        'Code representing the organization role type (e.g., ADMIN, MANAGER, MEMBER)';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.value IS
        'Display value for the organization role type (e.g., Administrator, Manager, Member)';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.created_at IS
        'Timestamp when the record was created. Defaults to the current timestamp';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.created_by IS
        'Identifier of the user who created the record. Defaults to UNKNOWN';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.updated_at IS
        'Timestamp when the record was last updated';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.updated_by IS
        'Identifier of the user who last updated the record';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.deleted_at IS
        'Timestamp when the record was marked as deleted';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.deleted_by IS
        'Identifier of the user who marked the record as deleted';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.organizationRoleType.tableName}.activity_log IS
        'JSONB field for tracking additional activity details or logs related to the organization role type';


                -- Table Comment
        COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.party.tableName} IS
        'Table to store party information, including the party ID, type, name, elaboration details, and tracking information for creation, updates, and deletion.';
        -- Column Comments
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.party_id IS
        'Unique identifier for the party';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.party_type_id IS
        'Identifier for the party type. References the party_type table to categorize the party (e.g., individual, organization).';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.party_name IS
        'Name of the party (e.g., individual name, organization name)';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.elaboration IS
        'Optional JSONB field for storing additional details or elaboration related to the party';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.created_at IS
        'Timestamp when the record was created. Defaults to the current timestamp';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.created_by IS
        'Identifier of the user who created the record. Defaults to UNKNOWN';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.updated_at IS
        'Timestamp when the record was last updated';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.updated_by IS
        'Identifier of the user who last updated the record';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.deleted_at IS
        'Timestamp when the record was marked as deleted';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.deleted_by IS
        'Identifier of the user who marked the record as deleted';
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.party.tableName}.activity_log IS
        'JSONB field for tracking additional activity details or logs related to the party';

     -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName} IS
      'Table to store party identifiers, such as social security numbers, email addresses, or other unique identifiers associated with parties. Includes tracking information for creation, updates, and deletion.';
      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.party_identifier_id IS
      'Unique identifier for the party identifier record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.identifier_name IS
      'Name of the identifier (e.g., Social Security Number, Email Address, etc.)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.identifier_value IS
      'Value of the identifier (e.g., the actual SSN, email address, etc.)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.party_identifier_type_id IS
      'Identifier for the type of identifier (e.g., SSN, Email). References the party_identifier_type table.';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.party_id IS
      'Identifier for the party associated with this identifier. References the party table.';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.elaboration IS
      'Optional JSONB field for storing additional details or elaboration related to the identifier';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.updated_at IS
      'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.updated_by IS
      'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifier.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the party identifier';


     -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName} IS
      'Table to store types of party identifiers, such as Social Security Number, Email, Phone Number, etc., with associated metadata and tracking information for creation, updates, and deletion.';
      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.party_identifier_type_id IS
      'Unique identifier for the party identifier type record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.code IS
      'Code representing the type of identifier (e.g., SSN, EMAIL, PHONE)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.value IS
      'Display value for the identifier type (e.g., Social Security Number, Email Address, Phone Number)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.updated_at IS
      'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.updated_by IS
      'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyIdentifierType.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the party identifier type';


      -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.partyRelation.tableName} IS
      'Table to store the relationships between parties, including details on how parties are related (e.g., family, colleagues) with associated metadata and tracking information for creation, updates, and deletion.';
      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.party_relation_id IS
      'Unique identifier for the party relation record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.party_id IS
      'Identifier for the primary party in the relationship. References the party table';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.related_party_id IS
      'Identifier for the related party in the relationship. References the party table';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.relation_type_id IS
      'Identifier for the type of relationship (e.g., family, colleague, business partner). References the party_relation_type table';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.party_role_id IS
      'Identifier for the role of the party in the relationship (optional). References the party_role table';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.elaboration IS
      'Optional JSONB field for storing additional details or elaboration related to the relationship';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.updated_at IS
     'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.updated_by IS
      'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelation.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the party relation';



      -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName} IS
      'Table to store the types of relationships between parties (e.g., family, colleagues, business) with associated metadata and tracking information for creation, updates, and deletion.';

      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.party_relation_type_id IS
      'Unique identifier for the party relation type record';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.code IS
      'Code representing the type of relationship (e.g., FAMILY, COLLEAGUE, PARTNER)';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.value IS
      'Display value for the relationship type (e.g., Family, Colleague, Business Partner)';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.updated_at IS
      'Timestamp when the record was last updated';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.updated_by IS
      'Identifier of the user who last updated the record';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRelationType.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the party relation type';

            -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.partyRole.tableName} IS
      'Table to store the roles of parties in various relationships (e.g., Manager, Employee, Parent, etc.) with associated metadata and tracking information for creation, updates, and deletion.';
      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.party_role_id IS
      'Unique identifier for the party role record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.code IS
      'Code representing the role of a party in a relationship (e.g., MANAGER, EMPLOYEE, PARENT)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.value IS
      'Display value for the party role (e.g., Manager, Employee, Parent)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.updated_at IS
      'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.updated_by IS
      'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRole.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the party role';


            -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName} IS
      'Table to store the types of roles that a party can have within an organization or relationship (e.g., Admin, User, Supervisor) with associated metadata and tracking information for creation, updates, and deletion.';
      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.party_role_type_id IS
      'Unique identifier for the party role type record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.code IS
      'Code representing the type of role (e.g., ADMIN, USER, SUPERVISOR)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.value IS
      'Display value for the role type (e.g., Admin, User, Supervisor)';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.created_at IS
      'Timestamp when the record was created. Defaults to the current timestamp';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.created_by IS
      'Identifier of the user who created the record. Defaults to UNKNOWN';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.updated_at IS
      'Timestamp when the record was last updated';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.updated_by IS
      'Identifier of the user who last updated the record';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.deleted_at IS
      'Timestamp when the record was marked as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.deleted_by IS
      'Identifier of the user who marked the record as deleted';
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyRoleType.tableName}.activity_log IS
      'JSONB field for tracking additional activity details or logs related to the party role type';

    -- Table Comment
    COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.partyType.tableName} IS
    'Table to store party type information with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';

    -- Column Comments
    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.party_type_id IS
    'Unique identifier for the party type (Primary Key)';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.code IS
    'Alpha-numeric code representing the party type (e.g., INDIVIDUAL, ORGANIZATION). This field is mandatory and must be unique.';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.value IS
    'Display value for the party type (e.g., Individual, Organization). This field is mandatory.';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.created_at IS
    'Timestamp indicating when the party type record was created. Defaults to the current timestamp if not specified.';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.created_by IS
    'Identifier of the user who created the party type record. Defaults to "UNKNOWN" if not specified.';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.updated_at IS
    'Timestamp indicating when the party type record was last updated.';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.updated_by IS
    'Identifier of the user who last updated the party type record.';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.deleted_at IS
    'Timestamp indicating when the party type record was marked as deleted.';

    COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.partyType.tableName}.deleted_by IS
    'Identifier of the user who marked the party type record as deleted.';

      -- Table Comment
      COMMENT ON TABLE ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName} IS
      'Table to store record status information with unique codes, values, and tracking metadata. Includes details on the creation, update, and deletion of status records.';

      -- Column Comments
      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.id IS
      'Unique identifier for each record status entry (Primary Key)';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.code IS
      'Alpha-numeric code representing the record status (e.g., ACTIVE, PENDING, DELETED). This field is mandatory and must be unique.';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.value IS
      'Integer value associated with the record status (e.g., 1 for ACTIVE, 2 for PENDING). This field is mandatory.';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.created_at IS
      'Timestamp indicating when the record status was created. Defaults to the current timestamp if not specified.';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.created_by IS
      'Identifier of the user who created the record status entry. Defaults to "UNKNOWN" if not specified.';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.updated_at IS
      'Timestamp indicating when the record status was last updated.';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.updated_by IS
      'Identifier of the user who last updated the record status entry.';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.deleted_at IS
      'Timestamp indicating when the record status was marked as deleted.';

      COMMENT ON COLUMN ${partySchema.sqlNamespace}.${ddlTable.recordStatus.tableName}.deleted_by IS
      'Identifier of the user who marked the record status as deleted.';


              -- Table Comment
        COMMENT ON TABLE ${partySchema.sqlNamespace}.${udm.sexType.tableName} IS
        'Table to store sex type information with associated metadata, including codes, display values, and tracking information for creation, updates, and deletion.';

        -- Column Comments
        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.sex_type_id IS
        'Unique identifier for the sex type (Primary Key)';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.code IS
        'Alpha-numeric code representing the sex type (e.g., MALE, FEMALE). This field is mandatory and must be unique.';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.value IS
        'Display value for the sex type (e.g., Male, Female). This field is mandatory.';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.created_at IS
        'Timestamp indicating when the sex type record was created. Defaults to the current timestamp if not specified.';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.created_by IS
        'Identifier of the user who created the sex type record. Defaults to "UNKNOWN" if not specified.';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.updated_at IS
        'Timestamp indicating when the sex type record was last updated.';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.updated_by IS
        'Identifier of the user who last updated the sex type record.';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.deleted_at IS
        'Timestamp indicating when the sex type record was marked as deleted.';

        COMMENT ON COLUMN ${partySchema.sqlNamespace}.${udm.sexType.tableName}.deleted_by IS
        'Identifier of the user who marked the sex type record as deleted.';


        -- Table-level comment
      COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.site.tableName} IS 'Stores information about research sites, including location, status, and associated research studies and participants.';

      -- Column-level comments
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.site_id IS 'Primary key, unique identifier for the site record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.site_name IS 'Name of the research site.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.site_description IS 'Optional description providing additional details about the site.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.organization_id IS 'Foreign key referencing organization.id. Indicates the organization managing the site.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.address IS 'Optional address of the research site.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.telecom IS 'Optional contact information for the research site.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.latitude IS 'Optional latitude coordinate for the site geographical location.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.longitude IS 'Optional longitude coordinate for the site geographical location.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.status IS 'Status of the site (e.g., active, inactive). Default is "active".';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.study_id IS 'Foreign key referencing research_study.study_id. Links the site to a research study.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.participant_id IS 'Foreign key referencing research_subject.rsubject_id. Links the site to a participant.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.rec_status_id IS 'Foreign key referencing record_status.id. Indicates the record status of the site.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.created_at IS 'Timestamp of when the record was created. Defaults to CURRENT_TIMESTAMP.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.updated_at IS 'Timestamp of when the record was last updated. Defaults to CURRENT_TIMESTAMP.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.created_by IS 'Optional identifier for the user who created the record.';
      COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.site.tableName}.updated_by IS 'Optional identifier for the user who last updated the record.';



        COMMENT ON SCHEMA ${activitySchema.sqlNamespace} IS
       'Persistent schema storing logs and audit trails for user activities and system events.';

        COMMENT ON SCHEMA ${aiSchema.sqlNamespace} IS
        'Persistent schema containing AI-related data, such as user chats, questions, and preferences, to provide insights.';

        COMMENT ON SCHEMA ${authSchema.sqlNamespace} IS
        'Persistent schema holding authentication-related data, including user accounts and authentication provider configurations.';

        COMMENT ON SCHEMA ${masterSchema.sqlNamespace} IS
        'Persistent schema containing master tables used as reference data sources for the application.';

        COMMENT ON SCHEMA ${partySchema.sqlNamespace} IS
        'Persistent schema implementing a party model to extend FHIR-based designs, capturing details like parties, persons, and roles.';

        COMMENT ON SCHEMA ${rawSchema.sqlNamespace} IS
        'Persistent schema storing FHIR-compliant measurements and simple observations about patients, devices, or subjects.';

        COMMENT ON SCHEMA ${rawDataSchema.sqlNamespace} IS
        'Schema designed to store raw files along with their extracted data, maintaining both the original formats and any potentially converted versions for further processing and analysis.';

        COMMENT ON SCHEMA ${researchSchema.sqlNamespace} IS
        'Persistent schema containing tables for managing and representing research studies and their associated data';

        COMMENT ON SCHEMA ${statelessMasterSchema.sqlNamespace} IS
        'Non-persistent schema containing views, functions, and stored procedures related to master data.';

        COMMENT ON SCHEMA ${statelessStudySchema.sqlNamespace} IS
        'Non-persistent schema providing views, functions, and stored procedures related to research studies.';

        COMMENT ON SCHEMA ${statelessAuthentication.sqlNamespace} IS
        'Non-persistent schema providing views, functions, and stored procedures related to user accounts and authentication provider configurations.';

        COMMENT ON SCHEMA drh_stateless_ai_insights IS
        'Non-persistent schema containing stateless AI-related data, such as temporary insights, analytics, or AI-generated results that do not require long-term storage. Used for transient computations, caching, or ephemeral AI outputs.';

        COMMENT ON SCHEMA ${statelessUtilSchema.sqlNamespace} IS
        'Non-persistent schema including utility views, functions, and stored procedures to support application functionalities.';

        COMMENT ON SCHEMA ${assuranceSchema.sqlNamespace} IS 'Stores test results and artifacts generated by unit or integration testing tools.';

        COMMENT ON SCHEMA ${infoSchemaLifecycle.sqlNamespace} IS 'Supports database schema versioning and migration scripting processes.';

        COMMENT ON SCHEMA ${infoSchemaLifecycleAssurance.sqlNamespace} IS 'Stores test files and results to validate database migration scripts.';

        COMMENT ON SCHEMA ${statelessrawObservation.sqlNamespace} IS 'Schema for managing CGM observation data, integrating raw CGM data with metadata for study participants';

        -- Comment on drh_stateful_db_import_migration schema
       COMMENT ON SCHEMA ${dbImportMigrateSchema.sqlNamespace} IS 'Schema for storing imported stateful database files, participant data, and associated research study metadata. Used for processing, integration, and analysis of clinical and CGM data.';



       COMMENT ON SCHEMA ${dbstatelessMigrateSchema.sqlNamespace} IS 'The schema handles database migrations related to the import process in the DRH system. It supports the stateless migration of data, ensuring that the migration can be repeated or rolled back without affecting the underlying data consistency.';

       COMMENT ON SCHEMA ${statelessRawDataSchema.sqlNamespace}  IS 'Schema for holding views and functions related to the handling of raw cgm data in various  formats.';

       COMMENT ON SCHEMA ${statelessactivitySchema.sqlNamespace} IS 'Schema for logging stateless activity audits, capturing interactions that do not maintain session or state across transactions.';

       -- Adding table comment
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName} IS 'The role_type table stores predefined types of roles (e.g., doctor, nurse, administrator, patient) based on FHIR standards.';

      -- Adding column comments
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.role_type_id IS 'Unique identifier for the role type (ULID).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.code IS 'FHIR code value representing the role (e.g., doctor, nurse).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.system IS 'URI of the coding system (e.g., http://terminology.hl7.org/CodeSystem/practitioner-role).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.display IS 'Human-readable name of the role (e.g., Doctor, Nurse).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.definition IS 'Detailed description of the role from the FHIR standard.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.rec_status_id IS 'Foreign key referencing record_status to track status (e.g., active, inactive).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.created_at IS 'Timestamp when the record was created.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.created_by IS 'ULID of the user who created the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.updated_at IS 'Timestamp when the record was last updated.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.updated_by IS 'ULID of the user who last updated the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.deleted_at IS 'Timestamp when the record was deleted.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.roleType.tableName}.deleted_by IS 'ULID of the user who deleted the record.';

     -- Adding table comment
      COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.role.tableName} IS 'This table stores various roles (master data) defined in the system, referring to the FHIR PractitionerRole. It links to role types and organizations if applicable.';

      -- Adding column comments
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.role_id IS 'Primary key: Unique identifier for the role (ULID).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.role_type_id IS 'Foreign key linking to the role_type table, defining the type of role.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.org_party_id IS 'Optional foreign key referencing an organization or group.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.role_name IS 'Name of the role (e.g., Administrator, Clinician).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.description IS 'Detailed description of the role.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.is_system_role IS 'Boolean flag indicating whether this is a system role (e.g., admin roles).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.metadata IS 'Additional metadata stored as JSONB.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.rec_status_id IS 'Foreign key referencing record_status to track role status (e.g., active, inactive).';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.created_at IS 'Timestamp when the record was created.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.created_by IS 'ULID of the user who created the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.updated_at IS 'Timestamp when the record was last updated.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.updated_by IS 'ULID of the user who last updated the record.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.deleted_at IS 'Timestamp when the record was deleted.';
      COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.role.tableName}.deleted_by IS 'ULID of the user who deleted the record.';


      -- Adding table comment
      COMMENT ON TABLE ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName} IS 'Mapping table that stores various roles linked to users or user groups. Roles can be assigned at an individual or group level.';

      -- Adding column comments
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.user_role_id IS 'Primary key: Unique identifier for the user role (ULID).';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.role_id IS 'Foreign key reference to the role table, defining the assigned role.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.user_id IS 'Foreign key reference to the users table. Can be NULL if assigned at the group level.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.user_group_id IS 'Foreign key reference to the user groups table, used for group-based role assignments.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.start_date IS 'Optional start date indicating when the role assignment became active.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.end_date IS 'Optional end date indicating when the role assignment ended. NULL if the role is still active.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.metadata IS 'Additional metadata stored as JSONB.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.rec_status_id IS 'Foreign key reference to record_status, tracking the status of the role assignment (e.g., active, inactive).';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.created_at IS 'Timestamp when the record was created.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.created_by IS 'ULID of the user who created the record.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.updated_at IS 'Timestamp when the record was last updated.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.updated_by IS 'ULID of the user who last updated the record.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.deleted_at IS 'Timestamp when the record was deleted.';
      COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userRole.tableName}.deleted_by IS 'ULID of the user who deleted the record.';


      -- Adding table comment
    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}  IS 'Table storing permissions that define allowed actions on system resources.';

    -- Adding column comments
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.permission_id IS 'Primary key: Unique identifier for the permission (ULID).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.code IS 'Unique permission code used to identify the permission. Must be unique.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.permission_name IS 'Name of the permission, describing the action it allows.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.description IS 'Detailed description of the permission and its purpose.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.resource_type IS 'Type of resource the permission applies to (e.g., User, Document).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.action_type IS 'Type of action the permission allows (e.g., READ, WRITE, DELETE).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.rec_status_id IS 'Foreign key reference to record_status, tracking the status of the permission (e.g., active, inactive).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.created_at IS 'Timestamp when the permission record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.created_by IS 'ULID of the user who created the permission record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.updated_at IS 'Timestamp when the permission record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.updated_by IS 'ULID of the user who last updated the permission record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.deleted_at IS 'Timestamp when the permission record was deleted.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.permission.tableName}.deleted_by IS 'ULID of the user who deleted the permission record.';


    COMMENT ON TABLE ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName}  IS 'Mapping table defining permissions assigned to roles in the system.';

    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .role_permission_id IS 'Primary key: Unique identifier for the role-permission mapping (ULID).';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .role_id IS 'Foreign key: Links to the role table.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .permission_id IS 'Foreign key: Links to the permission table.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .metadata  IS 'Additional metadata stored in JSONB format.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .rec_status_id IS 'Foreign key: References record_status to indicate role-permission status.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .created_at IS 'Timestamp of when the record was created.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .created_by IS 'ULID of the user who created the record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .updated_at IS 'Timestamp of the last update.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .updated_by IS 'ULID of the user who last updated the record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .deleted_at IS 'Timestamp of when the record was deleted (soft delete).';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.rolePermission.tableName} .deleted_by IS 'ULID of the user who deleted the record.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName} IS 'Master table used to define various types of group objects in FHIR (e.g., doctor, nurse).';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.group_type_id IS 'Primary key: Unique identifier for the group type (ULID).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.code IS 'Code value from the coding system (e.g., doctor, nurse).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.system IS 'URI of the coding system (e.g., http://hl7.org/fhir/group-type).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.display IS 'Display name of the role (e.g., Doctor, Nurse).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.definition IS 'Description of the group type from the standard or specification.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.rec_status_id IS 'Foreign key: References record_status to indicate group type status (e.g., active).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.created_at IS 'Timestamp of when the record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.created_by IS 'ULID of the user who created the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.updated_at IS 'Timestamp of the last update to the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.updated_by IS 'ULID of the user who last updated the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.deleted_at IS 'Timestamp of when the record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.groupType.tableName}.deleted_by IS 'ULID of the user who deleted the record.';

    COMMENT ON TABLE ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName} IS 'Table representing members of different groups, linking users and parties to a group type with start and end periods for their membership.';

    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.group_member_id IS 'Primary key: Unique identifier for the group membership (ULID).';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.group_type_id IS 'Foreign key: References the group type (e.g., doctor, nurse) from the group_type table.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.user_id IS 'Foreign key (nullable): References the user_account table, representing the user in the group. Either this or member_party_id must be populated.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.member_party_id IS 'Foreign key (nullable): References the party table, representing the party (e.g., organization) in the group. Either this or user_id must be populated.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.period_start IS 'Start date of the membership in the group.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.period_end IS 'End date of the membership in the group. NULL indicates ongoing membership.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.rec_status_id IS 'Foreign key: References the record_status table, indicating the record status (e.g., active, deleted).';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.created_at IS 'Timestamp of when the record was created.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.created_by IS 'ULID of the user who created the record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.updated_at IS 'Timestamp of the last update to the record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.updated_by IS 'ULID of the user who last updated the record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.deleted_at IS 'Timestamp of when the record was deleted (soft delete).';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.groupMember.tableName}.deleted_by IS 'ULID of the user who deleted the record.';


    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName} IS 'Master repository for storing various consent category codes defined across different systems (e.g., FHIR, LOINC).';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.category_id IS 'Primary key: Unique identifier for the consent category (ULID).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.code IS 'Code value from the coding system, used to represent the consent category (e.g., healthcare-related consent).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.system IS 'URI of the coding system that defines the consent category (e.g., FHIR, LOINC).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.display IS 'Display name for the consent category (e.g., Doctor, Nurse).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.definition IS 'Description or definition of the consent category as per the relevant standard.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.rec_status_id IS 'Foreign key: References the record_status table to indicate the record’s current status (e.g., active, deleted).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.created_at IS 'Timestamp when the consent category record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.created_by IS 'ULID of the user who created the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.updated_at IS 'Timestamp when the consent category record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.updated_by IS 'ULID of the user who last updated the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.deleted_at IS 'Timestamp when the consent category record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentCategory.tableName}.deleted_by IS 'ULID of the user who deleted the record.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName} IS 'Stores various status codes of a consent, for cases where consent status differs from the record_status.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.status_code_id IS 'Primary key: Unique identifier for the consent status code (ULID).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.code IS 'Code value from the coding system, used to represent the consent status (e.g., active, revoked).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.system IS 'URI of the coding system that defines the consent status (e.g., FHIR, custom codes).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.display IS 'Display name for the consent status (e.g., Approved, Denied).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.definition IS 'Description or definition of the consent status as per the relevant standard or system.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.rec_status_id IS 'Foreign key: References the record_status table to indicate the record’s current status (e.g., active, deleted).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.created_at IS 'Timestamp when the consent status code record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.created_by IS 'ULID of the user who created the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.updated_at IS 'Timestamp when the consent status code record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.updated_by IS 'ULID of the user who last updated the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.deleted_at IS 'Timestamp when the consent status code record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentStatusCode.tableName}.deleted_by IS 'ULID of the user who deleted the record.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName} IS 'Stores various decision codes associated with consent, particularly for workflows requiring approval or provision, with default decision types such as Deny and Permit (as per HL7 FHIR).';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.decision_type_id IS 'Primary key: Unique identifier for the decision type (Integer).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.code IS 'Code value representing the decision type (e.g., Deny, Permit).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.system IS 'URI of the coding system that defines the decision type (e.g., http://hl7.org/fhir/consent-provision-type).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.display IS 'Display name for the decision type (e.g., Deny, Permit).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.definition IS 'Description or definition of the decision type according to the standard (e.g., the meaning of Deny or Permit in the context of consent).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.rec_status_id IS 'Foreign key: References the record_status table to indicate the record’s current status (e.g., active, deleted).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.created_at IS 'Timestamp when the consent decision type record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.created_by IS 'ULID of the user who created the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.updated_at IS 'Timestamp when the consent decision type record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.updated_by IS 'ULID of the user who last updated the record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.deleted_at IS 'Timestamp when the consent decision type record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.consentDecisionType.tableName}.deleted_by IS 'ULID of the user who deleted the record.';


    COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName} IS 'Stores sharing and access information related to a research study. Used when granting study access to other researchers, managing consent workflows.';

    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.consent_id IS 'Primary key: Unique auto-incrementing identifier for each consent.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.status_type_id IS 'Foreign key: References the current status of the consent (e.g., draft, active, inactive). Nullable, used for approval-based consent workflows.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.subject_reference_id IS 'Foreign key: References the research study to which consent applies. Required for each consent record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.grantor_reference_id IS 'Foreign key: References the person (party ID) who granted consent to others.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.start_date IS 'Start date of the effective period for the consent.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.end_date IS 'End date of the effective period for the consent.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.decision_type_id IS 'Foreign key: Represents the decision for consent allowance (e.g., deny or permit). Nullable.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.category_code IS 'Foreign key: Category code that classifies the consent.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.rec_status_id IS 'Foreign key: References the record status (e.g., active), indicating the current status of the consent record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.created_at IS 'Timestamp when the consent record was created.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.created_by IS 'ULID of the user who created the consent record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.updated_at IS 'Timestamp when the consent record was last updated.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.updated_by IS 'ULID of the user who last updated the consent record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.deleted_at IS 'Timestamp when the consent record was deleted (soft delete).';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyConsent.tableName}.deleted_by IS 'ULID of the user who deleted the consent record.';

    COMMENT ON TABLE ${authSchema.sqlNamespace}.${ddlTable.group.tableName} IS 'Represents the main group entity, the definitions of a group object in FHIR. It stores details about different groups such as person, device, organization, etc.';

    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.group_id IS 'Primary key: Unique identifier for the group. Matches the id for the group.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.grp_identifier IS 'Short form or identifier for the group. A label or unique reference for the group.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.title IS 'Human-friendly title of the group. Used for identification and display purposes.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.status_type_id IS 'Foreign key: Represents the status of the group (e.g., draft, active, retired, unknown). Nullable.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.description IS 'Description of the group. Provides additional information about the group.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.purpose IS 'Purpose of the group. Explains why the group exists or its intended use.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.group_type_id IS 'Foreign key: References the type of the group (e.g., person, device, organization). Specifies the kind of entity the group represents.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.rec_status_id IS 'Foreign key: References the record status (e.g., active), indicating the current status of the group record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.created_at IS 'Timestamp when the group record was created.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.created_by IS 'ULID of the user who created the group record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.updated_at IS 'Timestamp when the group record was last updated.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.updated_by IS 'ULID of the user who last updated the group record.';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.deleted_at IS 'Timestamp when the group record was deleted (soft delete).';
    COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.group.tableName}.deleted_by IS 'ULID of the user who deleted the group record.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName} IS 'Master table that stores various contract type codes defined in different systems, such as the HL7 FHIR standard.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.contract_type_id IS 'Primary key: Auto-generated unique identifier for each contract type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.code IS 'Code value from the coding system (e.g., privacy, consent). This identifies the contract type in the system.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.system IS 'URI of the coding system that defines the contract type (e.g., http://hl7.org/fhir/ValueSet/contract-type). Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.display IS 'Display name for the contract type. Provides a human-readable representation of the contract type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.definition IS 'Description of the contract type. Explains the meaning or purpose of the contract type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.rec_status_id IS 'Foreign key: References the status of the record (e.g., active) by linking to the record_status table.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.created_at IS 'Timestamp when the contract type record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.created_by IS 'ULID of the user who created the contract type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.updated_at IS 'Timestamp when the contract type record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.updated_by IS 'ULID of the user who last updated the contract type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.deleted_at IS 'Timestamp when the contract type record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractType.tableName}.deleted_by IS 'ULID of the user who deleted the contract type record.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName} IS 'Master table that stores various contract sub-type codes defined in different systems, such as HL7 Terminology for contract subtypes.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.contract_sub_type_id IS 'Primary key: Auto-generated unique identifier for each contract sub-type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.code IS 'Code value from the coding system (e.g., disclosure-ca, disclosure-us). Identifies the contract sub-type in the system.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.system IS 'URI of the coding system that defines the contract sub-type (e.g., http://terminology.hl7.org/CodeSystem/contractsubtypecodes). Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.display IS 'Display name for the contract sub-type. Provides a human-readable representation of the contract sub-type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.definition IS 'Description of the contract sub-type. Explains the meaning or purpose of the contract sub-type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.rec_status_id IS 'Foreign key: References the status of the record (e.g., active) by linking to the record_status table.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.created_at IS 'Timestamp when the contract sub-type record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.created_by IS 'ULID of the user who created the contract sub-type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.updated_at IS 'Timestamp when the contract sub-type record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.updated_by IS 'ULID of the user who last updated the contract sub-type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.deleted_at IS 'Timestamp when the contract sub-type record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSubType.tableName}.deleted_by IS 'ULID of the user who deleted the contract sub-type record.';


    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName} IS 'Stores the various codes for contract signing parties defined in the contract signer type code system, such as those defined by HL7 Terminology for contract signer types.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.signer_type_id IS 'Primary key: Unique identifier for the contract signer type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.code IS 'Code value from the coding system (e.g., grantee, grantor). Identifies the role of the signing party in the contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.system IS 'URI of the coding system that defines the contract signer type (e.g., http://terminology.hl7.org/CodeSystem/contractsignertypecodes). Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.display IS 'Display name for the contract signer type. Provides a human-readable representation of the signer role.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.definition IS 'Description of the contract signer type. Explains the meaning or purpose of the signing party role in the contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.rec_status_id IS 'Foreign key: References the status of the record (e.g., active) by linking to the record_status table.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.created_at IS 'Timestamp when the contract signer type record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.created_by IS 'ULID of the user who created the contract signer type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.updated_at IS 'Timestamp when the contract signer type record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.updated_by IS 'ULID of the user who last updated the contract signer type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.deleted_at IS 'Timestamp when the contract signer type record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractSignerType.tableName}.deleted_by IS 'ULID of the user who deleted the contract signer type record.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName} IS 'Stores various codes representing the legal state of a contract, as defined in the coding system for contract legal states, such as those from HL7 FHIR (http://hl7.org/fhir/ValueSet/contract-legalstate).';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.state_code_id IS 'Primary key: Unique identifier for the legal state code.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.code IS 'Code value representing the legal state of the contract. This is the actual legal state code.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.system IS 'URI of the coding system that defines the legal state codes (e.g., http://hl7.org/fhir/ValueSet/contract-legalstate). Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.display IS 'Display name for the legal state code. This is a human-readable name representing the legal state.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.definition IS 'Description of the legal state code as per the standard definition from the coding system.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.rec_status_id IS 'Foreign key: References the status of the record (e.g., active) by linking to the record_status table.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.created_at IS 'Timestamp when the legal state code record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.created_by IS 'ULID of the user who created the legal state code record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.updated_at IS 'Timestamp when the legal state code record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.updated_by IS 'ULID of the user who last updated the legal state code record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.deleted_at IS 'Timestamp when the legal state code record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.legalStateCode.tableName}.deleted_by IS 'ULID of the user who deleted the legal state code record.';


    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName} IS 'Stores various reasons or causes for the cessation (expiration) of a contract, as defined in the system for contract expiration types, such as those from HL7 FHIR (http://hl7.org/fhir/ValueSet/contract-expiration-type). It provides clarity on why a contract is no longer active or has ended.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.expiration_type_id IS 'Primary key: Unique identifier for the contract expiration type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.code IS 'Code value representing the reason or cause for the contract expiration. Must be from the coding system.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.system IS 'URI of the coding system that defines the contract expiration types (e.g., http://hl7.org/fhir/ValueSet/contract-expiration-type). Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.display IS 'Display name for the contract expiration type. A human-readable representation.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.definition IS 'Description of the contract expiration type, explaining the cause or reason for the cessation of the contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.rec_status_id IS 'Foreign key: References the status of the record (e.g., active) by linking to the record_status table.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.created_at IS 'Timestamp when the contract expiration type record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.created_by IS 'ULID of the user who created the contract expiration type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.updated_at IS 'Timestamp when the contract expiration type record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.updated_by IS 'ULID of the user who last updated the contract expiration type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.deleted_at IS 'Timestamp when the contract expiration type record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractExpirationType.tableName}.deleted_by IS 'ULID of the user who deleted the contract expiration type record.';


    COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName} IS 'Stores various codes representing signature types used when a contract is signed by associated parties. These codes are defined in the coding system, such as the HL7 FHIR ValueSet (http://hl7.org/fhir/ValueSet/signature-type).';

    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.signature_type_id IS 'Primary key: Unique identifier for the signature type.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.code IS 'Code value representing the signature type (e.g., handwritten, electronic). Must be from the coding system.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.system IS 'URI of the coding system that defines the signature type (e.g., http://hl7.org/fhir/ValueSet/signature-type). Nullable.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.display IS 'Display name for the signature type. A human-readable representation.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.definition IS 'Description of the signature type, explaining its meaning and use.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.rec_status_id IS 'Foreign key: References the status of the record (e.g., active) by linking to the record_status table.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.created_at IS 'Timestamp when the signature type record was created.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.created_by IS 'ULID of the user who created the signature type record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.updated_at IS 'Timestamp when the signature type record was last updated.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.updated_by IS 'ULID of the user who last updated the signature type record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.deleted_at IS 'Timestamp when the signature type record was deleted (soft delete).';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.signatureType.tableName}.deleted_by IS 'ULID of the user who deleted the signature type record.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName} IS 'Represents individual terms within a contract, allowing detailed tracking of their specifics. Includes fields for term identification, association with the contract, time periods, classifications, and descriptions.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.term_id IS 'Primary key: Unique identifier for the contract term.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.term_reference_id IS 'Publicly exposed ID for reference, which can be used externally to refer to the contract term.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.contract_id IS 'Foreign key: References the contract to which this term belongs.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.issued_at IS 'The date and time when the contract term was issued.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.term_period_start IS 'The start date of the contract term’s effective period, defining when the term becomes active.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.term_period_end IS 'The end date of the contract term’s effective period, defining when the term becomes inactive.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.type_code IS 'Type classification code for the term, used for categorization.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.sub_type_code IS 'Sub-type classification code for the term, used for further categorization or granularity.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.term_statement IS 'Detailed statement or description of the contract term, providing more context or explanation.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.rec_status_id IS 'Foreign key: Status of the record (e.g., active, deleted) from the record_status table.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.created_at IS 'Timestamp when the contract term record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.created_by IS 'ULID of the user who created the contract term record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.updated_at IS 'Timestamp when the contract term record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.updated_by IS 'ULID of the user who last updated the contract term record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.deleted_at IS 'Timestamp when the contract term record was deleted (soft delete).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractTerm.tableName}.deleted_by IS 'ULID of the user who deleted the contract term record.';

    COMMENT ON TABLE  ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName} IS 'Stores various decision types that are used to classify the decisions made within the system. Includes metadata about the decision type, such as its code, definition, and status.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.decision_type_id IS 'Primary key: Unique identifier for the decision type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.code IS 'Code value representing the decision type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.system IS 'URI or system identifier used to define the decision type system.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.display IS 'Human-readable display name for the decision type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.definition IS 'Description or definition of the decision type, providing more context about its usage.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.rec_status_id IS 'Foreign key: Status of the record (e.g., active), referencing the record_status table for tracking status.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.created_at IS 'Timestamp when the decision type record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.created_by IS 'User ID (ulid) of the creator of the decision type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.updated_at IS 'Timestamp when the decision type record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.updated_by IS 'User ID (ulid) of the person who last updated the decision type record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.deleted_at IS 'Timestamp when the decision type record was soft-deleted.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionType.tableName}.deleted_by IS 'User ID (ulid) of the person who performed the soft delete operation.';


    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName} IS 'Stores various decision modes used to classify how decisions are made within the system. Includes metadata about each mode, such as its code, definition, and status.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.decision_mode_id IS 'Primary key: Unique identifier for the decision mode.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.code IS 'Code value representing the decision mode.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.system IS 'URI or system identifier used to define the decision mode system.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.display IS 'Human-readable display name for the decision mode.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.definition IS 'Description or definition of the decision mode, providing more context about its usage.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.rec_status_id IS 'Foreign key: Status of the record (e.g., active), referencing the record_status table for tracking the status of the decision mode.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.created_at IS 'Timestamp when the decision mode record was created.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.created_by IS 'User ID (ulid) of the creator of the decision mode record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.updated_at IS 'Timestamp when the decision mode record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.updated_by IS 'User ID (ulid) of the person who last updated the decision mode record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.deleted_at IS 'Timestamp when the decision mode record was soft-deleted.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.decisionMode.tableName}.deleted_by IS 'User ID (ulid) of the person who performed the soft delete operation.';

    COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName} IS 'Stores detailed information about offers associated with contract terms. It links offers to specific contract terms, decision-making processes, and classifications, and helps manage the specifics of each offer.';

    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.offer_id IS 'Primary key: Unique identifier for the offer.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.term_id IS 'Foreign key: References the contract_term table, linking the offer to a specific contract term.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.term_offer_desc IS 'Human-readable description of the offer, providing more details about its nature.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.decision_mode IS 'Mode or method used to make decisions about the offer (e.g., automated, manual).';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.topic_reference IS 'Reference to the subject or topic that the offer is related to, providing context to the offer.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.type_system IS 'URI or coding system used to classify the offer type (e.g., HL7 standard).';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.type_code IS 'Specific offer type code used for classification within the system.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.decision_type_id IS 'Foreign key: References the decision_type table, linking the offer to a specific decision type.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.decision_mode_id IS 'Foreign key: References the decision_mode table, linking the offer to a specific decision mode.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.rec_status_id IS 'Foreign key: Status of the record (e.g., active), referencing the record_status table for tracking the status of the offer.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.created_at IS 'Timestamp when the term offer record was created.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.created_by IS 'User ID (ulid) of the creator of the term offer record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.updated_at IS 'Timestamp when the term offer record was last updated.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.updated_by IS 'User ID (ulid) of the person who last updated the term offer record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.deleted_at IS 'Timestamp when the term offer record was soft-deleted.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOffer.tableName}.deleted_by IS 'User ID (ulid) of the person who performed the soft delete operation.';

    COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName} IS 'Stores information about the parties involved in a particular offer, identifying the offeror and offeree, and tracking their roles in the context of a contract term offer.';

    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.id IS 'Primary key: Unique identifier for the entry in the term_offer_party table.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.party_id IS 'Foreign key: References the party table, linking the party to a specific offer.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.offer_id IS 'Foreign key: References the term_offer table, linking the party to a specific offer.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.role_code IS 'Code representing the role of the party (e.g., offeror, offeree), indicating the party’s involvement in the offer.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.rec_status_id IS 'Foreign key: Status of the record (e.g., active), referencing the record_status table for tracking the status of the entry.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.created_at IS 'Timestamp when the record was created in the term_offer_party table.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.created_by IS 'User ID (ulid) of the creator of the term_offer_party record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.updated_at IS 'Timestamp when the record was last updated.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.updated_by IS 'User ID (ulid) of the person who last updated the term_offer_party record.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.deleted_at IS 'Timestamp when the record was soft-deleted.';
    COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.termOfferParty.tableName}.deleted_by IS 'User ID (ulid) of the person who performed the soft delete operation.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName} IS 'Stores the status of a contract, including its type, system, display information, and definition. It helps track the contract’s progress or state within its lifecycle.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.contract_status_type_id IS 'Primary key: Unique identifier for the contract status type.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.code IS 'Code representing the status type, used for referencing and categorizing the status.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.system IS 'The system or classification framework to which the contract status belongs (e.g., internal or external classification system).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.display IS 'Display name for the contract status, used in user interfaces for better understanding.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.definition IS 'Detailed description or definition of the contract status type, explaining its meaning and context.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.rec_status_id IS 'Foreign key: Status of the record (e.g., active or deleted), referencing the record_status table for tracking the status of the entry.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.created_at IS 'Timestamp when the record was created in the contract_status table.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.created_by IS 'User ID (ulid) of the creator of the contract_status record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.updated_at IS 'Timestamp when the record was last updated.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.updated_by IS 'User ID (ulid) of the person who last updated the contract_status record.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.deleted_at IS 'Timestamp when the record was soft-deleted.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.contractStatus.tableName}.deleted_by IS 'User ID (ulid) of the person who performed the soft delete operation.';

    COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName} IS 'Stores the base details of study contracts, including information like contract reference IDs, status, legal state, issue dates, and scope. It connects to various entities such as studies, organizations, and sites to represent a complete contract in the context of a research study.';

    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.contract_id IS 'Primary key: Unique identifier for the contract. Auto-incremented for each contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.subject_reference_id IS 'Foreign key: References the associated research study (study_id).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.status_type_id IS 'Foreign key: References the contract status type (e.g., amended, cancelled, executable). Optional for tracking the current contract status.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.legal_state_code IS 'Foreign key: References the legal state code, representing the jurisdiction or legal context for the contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.issued_date IS 'Timestamp: Date and time when the contract was officially issued.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.applies_start_date IS 'Timestamp: The start date when the contract applies and becomes active.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.applies_end_date IS 'Timestamp: The end date when the contract expires or stops being applicable. Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.expiration_type_code IS 'Foreign key: References the expiration type code, indicating how and when the contract expires (e.g., date, event-based).';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.contract_name IS 'String: The computer-friendly name of the contract used for system references.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.title IS 'String: The official title of the contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.subtitle IS 'String: A subtitle providing additional context to the contract title. Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.alias IS 'String: Alias name or shorthand reference for the contract. Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.authority_org_reference IS 'Foreign key: References the organization (org_id) responsible for the contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.location_id IS 'Foreign key: References the location (location_id) where the contract applies. Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.site_id IS 'Foreign key: References the site (site_id) where the contract is relevant. Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.contract_type_code IS 'Foreign key: References the contract type code (e.g., privacy, health insurance, supply). Nullable.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.author_reference_id IS 'Foreign key: References the party (party_id) who created or authorized the contract.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.scope_code IS 'String: The scope of the contract as defined by FHIR (e.g., policy). Nullable, with only one value defined currently.';
    COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.studyContract.tableName}.legally_binding_reference_id IS 'Foreign key: References any legally binding documentation related to the contract, to be used in the future if needed. Nullable.';

    -- Comment on drh_stateful_raw_data.cgm_raw_db table
COMMENT ON TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName} IS 'Stores metadata about CGM raw data files, including file details, upload status, processing status, and associations with study and tenant entities.';

-- Comments on columns of drh_stateful_raw_data.cgm_raw_db
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.db_file_id IS 'Unique identifier for each CGM raw database file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.file_name IS ' The original name of the uploaded file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.file_url IS 'URL location of the uploaded file, if applicable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.upload_timestamp IS 'The date and time when the file was uploaded.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.uploaded_by IS 'References the party (party_id) who uploaded the file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.file_size IS 'Size of the file in a human-readable format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.is_processed IS ' Indicates whether the file has been processed.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.processed_at IS ' The date and time when the file was processed. Nullable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.process_status IS 'The current status of the file processing (e.g., pending, completed, failed).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.db_file_metadata IS 'Stores additional metadata related to the file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.db_type IS 'Specifies the type of database file (e.g., SQLite, PostgreSQL).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.study_id IS 'References the research study (study_id) associated with the file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.tenant_id IS 'References the tenant organization (organization.id) associated with the file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.rec_status_id IS ' References the record status (record_status.id), indicating the state of the record.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.created_at IS ' Record creation timestamp. Defaults to current timestamp.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.created_by IS 'Identifier of the user who created the record. Defaults to UNKNOWN.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.updated_at IS 'Last update timestamp of the record. Nullable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.updated_by IS ' Identifier of the user who last updated the record. Nullable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.deleted_at IS 'Timestamp when the record was deleted. Nullable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.deleted_by IS 'Identifier of the user who deleted the record. Nullable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}.file_interaction_id IS 'Reference to file_interaction table, which stores the interaction details of the file.';

-- Comment on drh_stateful_db_import_migration.file_meta_ingest_data table
COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName} IS 'Stores ingested file metadata and CGM data for further processing and integration into research studies.';

-- Comments on columns of drh_stateful_db_import_migration.file_meta_ingest_data
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName}.file_meta_id IS 'unique identifier.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName}.db_file_id IS 'References the CGM raw database file (cgm_raw_db.db_file_id).';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName}.file_meta_data IS 'String: Metadata related to the uploaded file. Nullable.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName}.cgm_data IS 'String: Raw CGM data extracted from the file. Nullable.';

-- Comment on drh_stateful_db_import_migration.participant table
COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName} IS 'Stores participant demographic, medical, and study-related information for research studies.';


-- Comments on columns of drh_stateful_db_import_migration.participant
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.db_file_id IS 'References the source database file that contains the participant data.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.tenant_id IS 'Identifier for the tenant associated with the participant.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.site_id IS 'Identifier for the site where the participant is enrolled.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.diagnosis_icd IS 'ICD code representing the participant medical diagnosis.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.med_rxnorm IS 'RxNorm code for medications prescribed to the participant.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.treatment_modality IS 'Description of the participant treatment method .';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.gender IS 'Gender of the participant.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.race_ethnicity IS 'Participant race or ethnicity classification.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.age IS 'Participant age at the time of study enrollment. Stored as TEXT.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.bmi IS 'Body Mass Index of the participant. Stored as TEXT.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.baseline_hba1c IS 'Baseline HbA1c level at study entry. Stored as TEXT.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.diabetes_type IS 'Type of diabetes diagnosed in the participant.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.study_arm IS 'Study arm to which the participant is assigned .';



COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName} IS 'Stores captured snapshots of participant data during migration for auditing and recovery purposes';

COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName}.log_id IS 'Unique identifier for the migration log record';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName}.db_file_id IS 'Identifier of the database file the participant came from';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName}.study_display_id IS 'Study identifier as displayed in the source file optional';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName}.captured_at IS 'Timestamp when the participant data was captured';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName}.participant_json IS 'JSON-encoded full participant details at capture time';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName}.captured_by IS 'Name or system that captured the data';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationHistory.tableName}.study_id IS 'Study ID after the migration; set later once resolved';


COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName} IS 'Stores captured snapshots of study metadata during migration for auditing and recovery purposes';

COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName}.log_id IS 'Unique identifier for the migration log record';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName}.db_file_id IS 'Identifier of the database file the study metadata came from';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName}.study_meta_id IS 'Study metadata ID as defined in the source';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName}.captured_at IS 'Timestamp when the metadata was captured';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName}.study_metadata IS 'JSON-encoded full study metadata at capture time';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName}.captured_by IS 'Name or system that captured the metadata';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetadataHistory.tableName}.study_id IS 'Study ID after migration; resolved and populated later';



-- Comments for cgm_raw_zip_data table
COMMENT ON TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName} IS 'Table storing uploaded CGM ZIP files with metadata, processing status, and related details.';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.zip_file_id IS 'Unique identifier for the ZIP file (primary key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.tenant_id IS 'Reference to the organization Id (foreign key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.study_id IS 'Study identifier associated with the ZIP file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.file_name IS 'Name of the uploaded ZIP file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.file_url IS 'URL where the uploaded file is stored.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.file_format IS 'Format of the ZIP file (e.g., zip).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.upload_timestamp IS 'Timestamp when the ZIP file was uploaded.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.uploaded_by IS 'Reference to the user who uploaded the file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.file_size IS 'Size of the ZIP file in bytes.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.is_processed IS 'Indicates whether the file has been processed (true/false).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.processed_at IS 'Timestamp when the file was processed; nullable if not processed.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.status IS 'Status of the file (e.g., uploaded, processing, complete).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.file_metadata IS 'Metadata about the file stored in JSON format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.file_content IS 'Binary content of the ZIP file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.rec_status_id IS 'Reference to the record status (foreign key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.deleted_by IS 'User ID of the person who deleted the record.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawZipData.tableName}.file_interaction_id IS 'Reference to file_interaction table, which stores the interaction details of the file.';

-- Comments for cgm_raw_upload_data table
COMMENT ON TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName} IS 'Table storing uploaded CGM raw data files, linking them to ZIP files and tracking metadata.';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.cgm_raw_file_id IS 'Unique identifier for the raw CGM file (primary key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.file_name IS 'Name of the uploaded CGM data file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.file_url IS 'URL where the uploaded file is stored.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.zip_file_id IS 'Reference to the ZIP file containing this raw data file; nullable if not associated with a ZIP.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.cgm_raw_data_json IS 'Stores CGM raw file content in JSON format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.upload_timestamp IS 'Timestamp when the file was uploaded.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.uploaded_by IS 'Reference to the user who uploaded the file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.file_size IS 'Size of the uploaded file in bytes.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.is_processed IS 'Indicates whether the file has been processed (true/false).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.processed_at IS 'Timestamp when the file was processed; nullable if not processed.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.status IS 'Status of the file (e.g., uploaded, processing, complete).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.file_metadata IS 'Stores metadata about the file in JSON format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.file_type IS 'Type of the file (e.g., csv, text, xls, xlsx, json, xml).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.study_id IS 'Study identifier associated with the raw data file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.tenant_id IS 'Reference to the organization Id (foreign key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.rec_status_id IS 'Reference to the record status (foreign key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.deleted_by IS 'User ID of the person who deleted the record.';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.cgm_raw_data_csv IS 'Binary data for CGM raw data in CSV format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.cgm_raw_data_excel IS 'Binary data for CGM raw data in Excel format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.cgm_raw_data_xml IS 'XML representation of the CGM raw data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.cgm_raw_data_text IS 'Text representation of the CGM raw data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.database_id IS 'Identifier linking to the associated database, nullable if no database details are available.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}.file_interaction_id IS 'Reference to file_interaction table, which stores the interaction details of the file.';


COMMENT ON TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName} IS 'Table to store raw CGM data extracts, including references to the study, participant, and associated files, along with metadata and timestamps for record tracking.';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.cgm_raw_data_id IS 'Unique identifier for each CGM data set (primary key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.raw_file_id IS 'Foreign key referencing the master table, linking raw file data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.study_id IS 'Foreign key linking to the research study table.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.participant_sid IS 'Foreign key linking to the participant in the research_subject table.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.cgm_raw_data_json IS 'Raw CGM data in JSON file format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.file_url IS 'Uploaded file path URL where the raw CGM data is stored.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.file_meta_data IS 'Metadata about the file, such as device ID and device name.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.cgm_data IS 'Converted CGM data in JSON format for uniform representation (optional).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.tenant_id IS 'Foreign key linking to the tenant/organization table.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.rec_status_id IS 'Status ID for the record, indicating its current state (mandatory).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.created_at IS 'Timestamp of when the record was created.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.created_by IS 'ID of the creator of the record.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.updated_at IS 'Timestamp of when the record was last updated.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.updated_by IS 'ID of the person who last updated the record.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.deleted_at IS 'Timestamp of when the record was deleted, if applicable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.deleted_by IS 'ID of the person who deleted the record, if applicable.';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.cgm_raw_data_csv IS 'Binary data for CGM raw data in CSV format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.cgm_raw_data_excel IS 'Binary data for CGM raw data in Excel format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.cgm_raw_data_text IS 'Text representation of the CGM raw data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.cgm_raw_data_xml IS 'XML representation of the CGM raw data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.rawCgmExtractData.tableName}.file_interaction_id IS 'Reference to file_interaction table, which stores the interaction details of the file.';


COMMENT ON TABLE ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName} IS 'Table to store Continuous Glucose Monitoring (CGM) observations, including study details, participant data, and the recorded CGM value with relevant metadata.';

COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.id IS 'Primary key, unique identifier for the CGM record.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.study_id IS 'Foreign key referencing the research study, mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.research_subject_id IS 'Foreign key referencing the participant (ResearchSubject), mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.raw_cgm_extract_data_id IS 'Foreign key referencing the CGM row data (raw_cgm_extract_data).';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.period IS 'Study period (e.g., "Baseline", "Post Randomization"), mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.date_time IS 'Date and time when the CGM reading was taken, mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.cgm_value IS 'CGM value , mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.unit IS 'Unit of CGM value , mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.tenant_id IS 'Foreign key referencing the organization (tenant), mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.rec_status_id IS 'Record status, mandatory.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.created_at IS 'Record creation timestamp.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.created_by IS 'Creator ID.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.updated_at IS 'Last update timestamp.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.updated_by IS 'Updater ID.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.deleted_at IS 'Deletion timestamp.';
COMMENT ON COLUMN ${rawSchema.sqlNamespace}.${ddlTable.cgmData.tableName}.deleted_by IS 'Deleter ID.';


-- Table Comment
COMMENT ON TABLE  ${masterSchema.sqlNamespace}.${ddlTable.migrationStatus.tableName} IS 'Tracks the different stages of data migration.';

-- Column Comments
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.migrationStatus.tableName}.stage_id IS 'Auto-incrementing primary key representing a unique stage.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.migrationStatus.tableName}.stage_name IS 'Unique name identifying the migration stage.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.migrationStatus.tableName}.stage_description IS 'Optional description providing details about the migration stage.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.migrationStatus.tableName}.created_at IS 'Timestamp indicating when the stage was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.migrationStatus.tableName}.updated_at IS 'Timestamp of the last update to the stage.';

-- Table Comment
COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName} IS 'Tracks the migration status of individual study participants.';

-- Column Comments
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.db_file_id IS 'Identifier of the database file containing the participant data.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.study_id IS 'Identifier of the study to which the participant belongs.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.participant_display_id IS 'Human-readable participant ID used for display.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.participant_id IS 'System-assigned unique identifier for the participant.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.migration_status IS 'Reference to migration_status table indicating the current migration stage.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.migration_start_time IS 'Timestamp marking the start of the participant migration process.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.migration_end_time IS 'Timestamp marking the completion of the participant migration process.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMigrationStatus.tableName}.last_updated_at IS 'Timestamp indicating the last update to the migration record.';

-- Table Comment
COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName} IS 'Tracks the migration status of CGM data for each participant in a study.';

-- Column Comments
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.cgm_migrate_id IS 'Unique identifier for each CGM data migration entry.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.cgm_raw_data_id IS 'Reference to the raw CGM data record being migrated.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.db_file_id IS 'Identifier of the database file containing the CGM data.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.study_id IS 'Identifier of the study to which the CGM data belongs.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.participant_id IS 'System-assigned unique identifier for the participant.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.cgm_migration_status IS 'Reference to migration_status table indicating the migration stage.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.migration_start_time IS 'Timestamp marking the start of CGM data migration.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.migration_end_time IS 'Timestamp marking the completion of CGM data migration.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.last_updated_at IS 'Timestamp of the last update to the migration record.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.participant_display_id IS 'Human-readable participant ID used for display.';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.cgmDataMigrationStatus.tableName}.file_meta_id IS 'Identifier for file metadata associated with the CGM data migration.';


-- Add a comment on the table
COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName} IS 'Stores interactions related to file processing within the system, tracking uploads, processing statuses, and responses.';

-- Add comments on columns
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_interaction_id IS 'Unique identifier for the file interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.hub_interaction_id IS 'Reference to the associated hub interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.study_id IS 'Identifier for the study related to this file interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.organization_party_id IS 'Identifier for the organization associated with the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.participant_id IS 'Identifier for the participant involved in this file interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.uri IS 'URI associated with the file interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.description IS 'Description of the file interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.request IS 'JSONB object containing the request details for the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.response IS 'JSONB object containing the response details for the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.db_file_id IS 'Identifier for the file stored in the database.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_location IS 'Physical or logical location where the file is stored.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_name IS 'Name of the file involved in the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_content_type IS 'MIME type of the file, such as application/json, image/png, etc.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_content_json IS 'JSONB object containing structured content of the file if applicable.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_category IS 'Category of the file (e.g., raw data, processed data, metadata).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_upload_status IS 'Current upload status of the file (e.g., pending, completed, failed).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_processing_initiated_at IS 'Timestamp indicating when file processing started.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.file_processing_completed_at IS 'Timestamp indicating when file processing was completed.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.interaction_status_id IS 'Status reference to interaction_status table(e.g., success, error, in progress).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.interaction_action_type_id IS 'Action type reference to interaction_action_type table(e.g., DB FILE UPLOAD,S3 BUCKET UPLOAD.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.response_code IS 'HTTP or system response code indicating the outcome of the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.error_response IS 'Details of any errors encountered during processing.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.created_by IS 'User or system that created this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.updated_by IS 'User or system that last updated this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.created_at IS 'Timestamp indicating when the record was created.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.fileInteraction.tableName}.updated_at IS 'Timestamp indicating when the record was last updated.';

-- Add a comment on the table
COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName} IS 'Stores interactions occurring at the hub level, tracking study-related and organizational activities.';

-- Add comments on columns
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}.hub_interaction_id IS 'Unique identifier for the hub interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}.study_id IS 'Identifier for the study associated with this hub interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}.organization_party_id IS 'Identifier for the organization involved in the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}.created_by IS 'User or system that created this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}.updated_by IS 'User or system that last updated this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}.created_at IS 'Timestamp indicating when the record was created.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.hubInteraction.tableName}.updated_at IS 'Timestamp indicating when the record was last updated.';

-- Add a comment on the table session_unique_id_mapping
COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.sessionUniqueIdMapping.tableName} IS 'Stores unique session id.';

-- Add comments on columns
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionUniqueIdMapping.tableName}.id IS 'Unique identifier for the session id mapping.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionUniqueIdMapping.tableName}.session_unique_id IS 'Identifier for the session unique id mapping.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionUniqueIdMapping.tableName}.session_id IS 'Identifier for the session id mapping.';

-- Add a comment on the table
COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName} IS 'Records interactions related to studies, tracking changes, transitions, and responses within the study lifecycle.';

-- Add comments on columns
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.study_interaction_id IS 'Unique identifier for the study interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.hub_interaction_id IS 'Reference to the associated hub interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.study_id IS 'Identifier for the study associated with this interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.organization_party_id IS 'Identifier for the organization involved in the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.uri IS 'The resource URI associated with this interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.interaction_type IS 'Type of interaction (e.g., study creation, update, approval).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.description IS 'Detailed description of the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.request IS 'JSON object storing request payload details.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.response IS 'JSON object storing response payload details.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.from_state IS 'The previous state before the interaction occurred.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.to_state IS 'The resulting state after the interaction occurred.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.status IS 'Status of the interaction (e.g., success, failure, pending).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.response_code IS 'HTTP or application response code for the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.error_response IS 'Error details in case of interaction failure.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.created_by IS 'User or system that created this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.updated_by IS 'User or system that last updated this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.created_at IS 'Timestamp indicating when the record was created.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyInteraction.tableName}.updated_at IS 'Timestamp indicating when the record was last updated.';

-- Add a comment on the table
COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName} IS 'Tracks interactions related to participants within studies, capturing state transitions, requests, and responses.';

-- Add comments on columns
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.participant_interaction_id IS 'Unique identifier for the participant interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.hub_interaction_id IS 'Reference to the associated hub interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.study_id IS 'Identifier for the study associated with this interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.organization_party_id IS 'Identifier for the organization involved in the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.participant_id IS 'Identifier for the participant associated with this interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.uri IS 'The resource URI associated with this interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.interaction_type IS 'Type of interaction (e.g., participant enrollment, update, withdrawal).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.description IS 'Detailed description of the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.request IS 'JSON object storing request payload details.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.response IS 'JSON object storing response payload details.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.from_state IS 'The previous state before the interaction occurred.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.to_state IS 'The resulting state after the interaction occurred.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.status IS 'Status of the interaction (e.g., success, failure, pending).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.response_code IS 'HTTP or application response code for the interaction.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.error_response IS 'Error details in case of interaction failure.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.created_by IS 'User or system that created this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.updated_by IS 'User or system that last updated this record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.created_at IS 'Timestamp indicating when the record was created.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.studyParticipantInteraction.tableName}.updated_at IS 'Timestamp indicating when the record was last updated.';



-- Comment on table
COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName} IS
'Stores participant-level details for research studies, including demographics and baseline health metrics.';

-- Comments on columns
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.organization_party_id IS
'Unique identifier for the organization associated with the participant.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.organization_id IS
'Identifier for the organization conducting the study.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.study_id IS
'Unique identifier for the research study in which the participant is enrolled.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.study_display_id IS
'Short identifier used to display the study information in reports and dashboards.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.participant_id IS
'Unique identifier assigned to the participant in the study.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.participant_display_id IS
'Human-readable identifier for the participant, used for display purposes.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.gender IS
'Gender of the participant (e.g., Male, Female, Non-binary, etc.).';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}.age IS
'Age of the participant at the time of study enrollment.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}."Study Arm" IS
'Study arm to which the participant is assigned (e.g., control, treatment group, etc.).';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.participantBase.tableName}."Baseline HbA1C" IS
'Baseline HbA1C level of the participant, indicating average blood glucose levels over time.';


-- Comment on table
COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName} IS
'Stores Continuous Glucose Monitoring (CGM) metrics for study participants, including glucose statistics and time-in-range analysis.';

-- Comments on columns
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.participant_sid IS
'Unique identifier for the study participant, linking CGM data to the individual.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.total_readings IS
'Total number of CGM glucose readings recorded during the study period.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.days_of_wear IS
'Total number of days the CGM device was worn and actively recorded data.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.data_start_date IS
'Start date of the CGM data collection period for the participant.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.data_end_date IS
'End date of the CGM data collection period for the participant.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.avg_glucose IS
'Average glucose level measured during the CGM data collection period.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.glucose_stddev IS
'Standard deviation of glucose levels, indicating glucose variability.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.in_range_count IS
'Number of glucose readings within the target range, as defined by study parameters.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.very_high_count IS
'Number of glucose readings classified as very high, above the upper threshold.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.high_count IS
'Number of glucose readings classified as high, slightly above the target range.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.low_count IS
'Number of glucose readings classified as low, slightly below the target range.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.very_low_count IS
'Number of glucose readings classified as very low, below the lower threshold.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.above_range_count IS
'Total number of glucose readings above the target range, including both high and very high readings.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmMetrics.tableName}.below_range_count IS
'Total number of glucose readings below the target range, including both low and very low readings.';


-- Comment on table
COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.cgmDeviceInfo.tableName} IS
'Stores information about CGM devices and associated data files for each study participant.';

-- Comments on columns
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmDeviceInfo.tableName}.participant_sid IS
'Unique identifier for the study participant, linking CGM device data to the individual.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmDeviceInfo.tableName}.cgm_devices IS
'List of CGM devices used by the participant during the study.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.cgmDeviceInfo.tableName}.cgm_files IS
'References to CGM data files associated with the participant.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName} IS 'Stores information about different nutrition intake status codes, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.id IS 'Unique identifier for the nutrition intake status code.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.code IS 'Code representing the nutrition intake status.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.system IS 'System that defines the nutrition intake status codes.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.display IS 'Display name for the nutrition intake status code.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.nutritionIntakeStatusCode.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName} IS 'Stores information about different units of measurement, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.unit_id IS 'Unique identifier for the nutrition intake status code.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.unit IS 'Unit of measurement for the nutrition intake status code.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.code IS 'Code representing the nutrition intake status.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.system IS 'System that defines the nutrition intake status codes.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.display IS 'Display name for the nutrition intake status code.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitOfMeasurement.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName} IS 'Stores information about different measurement categories, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.id IS 'Unique identifier for the measurement category.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.unit_category_name IS 'Name of the measurement category.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.measurementCategory.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName} IS 'Stores mappings between units of measurement and measurement categories, including their status and metadata.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.mapping_id IS 'Unique identifier for the unit category mapping.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.unit_id IS 'Unique identifier for the unit of measurement.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.category_id IS 'Unique identifier for the measurement category.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.unitCategoryMapping.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName} IS 'Stores information about different meal types, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.meal_type_id IS 'Unique identifier for the meal type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.code IS 'Code representing the meal type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.system IS 'System that defines the meal types.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.display IS 'Display name for the meal type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.mealType.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName} IS 'Stores nutrition intake records for study participants, including details about the meal type, glycemic index, and other relevant metrics.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.id IS 'Unique identifier for the nutrition intake builder.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.status_code IS 'Status code indicating the current state of the nutrition intake record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.subject_id IS 'Unique identifier for the subject (participant) associated with the nutrition intake record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.occurrence_time IS 'Timestamp indicating when the nutrition intake occurred.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.meal_type_id IS 'Unique identifier for the meal type associated with the nutrition intake record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.value_quantity IS 'Quantity of the nutrition intake.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.value_unit_id IS 'Unique identifier for the unit of measurement associated with the nutrition intake record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.glycemic_index IS 'Glycemic index of the nutrition intake.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.glycemic_load IS 'Glycemic load of the nutrition intake.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.nutritionIntakeBuilder.tableName}.note IS 'Additional notes or comments related to the nutrition intake record.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName} IS 'Stores information about different observation methods, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.method_id IS 'Unique identifier for the observation method.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.code IS 'Code representing the observation method.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.system IS 'System that defines the observation methods.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.display IS 'Display name for the observation method.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationMethod.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName} IS 'Stores information about different observation statuses, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.id IS 'Unique identifier for the observation status.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.code IS 'Code representing the observation status.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.system IS 'System that defines the observation statuses.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.display IS 'Display name for the observation status.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationStatus.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName} IS 'Stores information about different observation categories, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.category_id IS 'Unique identifier for the observation category.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.code IS 'Code representing the observation category.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.system IS 'System that defines the observation categories.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.display IS 'Display name for the observation category.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.observationCategory.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName} IS 'Stores information about different activity types, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.activity_id IS 'Unique identifier for the activity type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.code IS 'Code representing the activity type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.system IS 'System that defines the activity types.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.display IS 'Display name for the activity type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.text IS 'Text description of the activity type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityMaster.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName} IS 'Stores information about different activity component types, including their codes and display names.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.component_type_id IS 'Unique identifier for the activity component type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.code IS 'Code representing the activity component type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.system IS 'System that defines the activity component types.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.display IS 'Display name for the activity component type.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityComponentType.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName} IS 'Stores observation records for fitness data, including details about the measurement method, status, and other relevant metrics.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.observation_id IS 'Unique identifier for the observation record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.subject_id IS 'Unique identifier for the subject (participant) associated with the observation record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.effective_datetime IS 'Timestamp indicating when the observation was effective.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.status_id IS 'Unique identifier for the status of the observation.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.category_id IS 'Unique identifier for the category of the observation.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.activity_type_id IS 'Unique identifier for the type of activity associated with the observation.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.device_id IS 'Unique identifier for the device used to collect the observation data.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.intensity_type_id IS 'Unique identifier for the intensity type associated with the observation.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.intensity_value_id IS 'Unique identifier for the intensity value associated with the observation.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.note IS 'Additional notes or comments related to the observation record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessBuilder.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';

COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName} IS 'Stores observation records for fitness components, including details about the measurement method, status, and other relevant metrics.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.component_id IS 'Unique identifier for the observation fitness component record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.observation_id IS 'Unique identifier for the observation record associated with this component.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.component_type_id IS 'Unique identifier for the type of component associated with the observation.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.value IS 'Value of the observation fitness component.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.unit_code IS 'Code representing the unit of measurement for the component value.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.method_id IS 'Unique identifier for the method used to collect the observation data.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.rec_status_id IS 'Record status ID, indicating the current status of the record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.observationFitnessComponentBuilder.tableName}.deleted_by IS 'User ID of the person who deleted the record, if applicable.';


------------------------------------------------------------------------------------------------------------------------------------------------
-------------------------------------------------------------SUBJECT OBSERVATION TABLES---------------------------------------------------------
------------------------------------------------------------------------------------------------------------------------------------------------
-- File Content Type table comments
COMMENT ON TABLE ${masterSchema.sqlNamespace}.file_content_type IS 'Master table for defining different types of file contents and their descriptions';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.id IS 'Primary key - Unique identifier for file content type';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.title IS 'Short descriptive title of the file content type (max 100 chars)';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.description IS 'Detailed description of the file content type';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.rec_status_id IS 'Reference to record status for tracking record state';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.created_by IS 'User who created the record';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.updated_at IS 'Timestamp when the record was last updated';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.updated_by IS 'User who last updated the record';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.deleted_at IS 'Timestamp when the record was soft deleted';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.file_content_type.deleted_by IS 'User who soft deleted the record';

-- Subject Observation Zip Data table comments
COMMENT ON TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName} IS 'Stores compressed/zipped subject observation data files and their metadata';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.id IS 'Primary key - Unique identifier for the zip file record';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.tenant_id IS 'Reference to the organization/tenant owning the data';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.study_id IS 'Reference to the associated research study';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_name IS 'Name of the uploaded zip file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_url IS 'URL/path where the zip file is stored';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_format IS 'Format of the zip file (e.g., ZIP, RAR)';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_content_type_id IS 'Reference to file content type defining the nature of contained data';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.upload_timestamp IS 'Timestamp when the file was uploaded';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.uploaded_by IS 'User who uploaded the file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_size IS 'Size of the uploaded file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.is_processed IS 'Flag indicating if the file has been processed';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.processed_at IS 'Timestamp when the file was processed';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.status IS 'Current status of the file processing';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_metadata IS 'Additional metadata about the file in JSON format';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_content IS 'Content of the file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.file_interaction_id IS 'Reference to file_interaction table, which stores the interaction details of the file.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.rec_status_id IS 'Reference to the record status (foreign key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationZipData.tableName}.deleted_by IS 'User ID of the person who deleted the record.';

-- Subject Observation Upload Data table comments
COMMENT ON TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName} IS 'Stores individual subject observation files extracted from zip archives';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.id IS 'Primary key - Unique identifier for the uploaded file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.file_name IS 'Name of the individual file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.file_url IS 'URL/path where the file is stored';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.zip_file_id IS 'Reference to the parent zip file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.file_content_type_id IS 'Reference to file content type';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.raw_data_json IS 'Raw data content in JSON format';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.upload_timestamp IS 'Timestamp when the file was uploaded';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.uploaded_by IS 'User who uploaded the file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.file_size IS 'Size of the file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.is_processed IS 'Flag indicating if the file has been processed';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.processed_at IS 'Timestamp when the file was processed';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.status IS 'Current status of file processing';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.file_metadata IS 'Additional metadata about the file in JSON format';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.file_type IS 'Type of the observation file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.study_id IS 'Reference to the associated research study';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.tenant_id IS 'Reference to the organization/tenant owning the data';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.database_id IS 'Reference to the associated database';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.rec_status_id IS 'Reference to the record status (foreign key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.deleted_by IS 'User ID of the person who deleted the record.';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.raw_data_csv IS 'Binary data for file data in CSV format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.raw_data_excel IS 'Binary data for file data in Excel format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.raw_data_xml IS 'XML representation of the file data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.raw_data_text IS 'Text representation of the file data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationUploadData.tableName}.file_interaction_id IS 'Reference to file_interaction table, which stores the interaction details of the file.';


-- Subject Observation Extract Data table comments
COMMENT ON TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName} IS 'Stores processed and extracted subject observation data';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.id IS 'Primary key - Unique identifier for the extracted data record';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.subject_observation_upload_id IS 'Reference to the uploaded file containing this data';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.study_id IS 'Reference to the associated research study';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.participant_sid IS 'Reference to the research subject/participant';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.file_content_type_id IS 'Reference to file content type';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.subject_observation_data_json IS 'Extracted observation data in JSON format';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.file_url IS 'URL/path to the source file';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.file_meta_data IS 'Metadata about the source file in JSON format';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.subject_observation_data IS 'Processed observation data in JSON format';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.tenant_id IS 'Reference to the organization/tenant owning the data';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.rec_status_id IS 'Reference to the record status (foreign key).';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.created_by IS 'User ID of the record creator.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.updated_by IS 'User ID of the last updater.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.deleted_at IS 'Timestamp when the record was deleted, if applicable.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.deleted_by IS 'User ID of the person who deleted the record.';

COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.raw_data_csv IS 'Binary data for file data in CSV format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.raw_data_excel IS 'Binary data for file data in Excel format.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.raw_data_xml IS 'XML representation of the file data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.raw_data_text IS 'Text representation of the file data.';
COMMENT ON COLUMN ${rawDataSchema.sqlNamespace}.${ddlTable.subjectObservationExtractData.tableName}.file_interaction_id IS 'Reference to file_interaction table, which stores the interaction details of the file.';
COMMENT ON TABLE ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName} IS 'Stores user credentials for authentication and authorization purposes.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.id IS 'Unique identifier for the user.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.user_id IS 'Unique identifier for the user.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.password_hash IS 'Hashed password for the user.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.password_salt IS 'Salt used for hashing the password.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.password_updated_at IS 'Timestamp when the password was last updated.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.failed_login_attempts IS 'Number of failed login attempts for the user.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.last_failed_login IS 'Timestamp of the last failed login attempt.';
COMMENT ON COLUMN ${authSchema.sqlNamespace}.${ddlTable.userCredentials.tableName}.is_locked IS 'Indicates whether the user account is locked due to too many failed login attempts.';

-- Comments for activity_level table
COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName} IS 'Stores master data for different activity intensity levels and their classifications in research studies.';

COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.id IS 'Primary key: Unique identifier for the activity level.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.level IS 'The level for activity levels.(eg:0,2,3,4,5)';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.title IS 'Unique title for the activity level.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.description IS 'Detailed description or definition of what the activity level means.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.rec_status_id IS 'Foreign key referencing record_status table, indicating current status of the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.created_by IS 'User ID of who created the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.updated_by IS 'User ID of who last updated the record.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.deleted_at IS 'Timestamp when the record was deleted (for soft deletes).';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevel.tableName}.deleted_by IS 'User ID of who deleted the record.';

-- Comments for activity_level_mapping table
COMMENT ON TABLE ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName} IS 'Maps activities to their corresponding intensity levels, providing standardization across different activity types.';

COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.id IS 'Primary key: Unique identifier for the activity-level mapping.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.title IS 'Unique title for the activity level mapping.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.activity_level_id IS 'Foreign key referencing the activity level classification.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.description IS 'Detailed description or definition of what the activity level classification.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.rec_status_id IS 'Foreign key referencing record_status table, indicating current status of the mapping.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.created_at IS 'Timestamp when the mapping record was created.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.created_by IS 'User ID of who created the mapping.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.updated_at IS 'Timestamp when the mapping was last updated.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.updated_by IS 'User ID of who last updated the mapping.';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.deleted_at IS 'Timestamp when the mapping was deleted (for soft deletes).';
COMMENT ON COLUMN ${masterSchema.sqlNamespace}.${ddlTable.activityLevelMapping.tableName}.deleted_by IS 'User ID of who deleted the mapping.';

-- Table Comment
COMMENT ON TABLE ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName} IS 'Tracks user session information including start/end times and user details for audit purposes.';

COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.session_id IS 'Primary identifier for uniquely identifying user sessions.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.start_time IS 'Timestamp when the user session was initiated. Nullable.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.end_time IS 'Timestamp when the user session was terminated. Nullable.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.user_party_id IS 'Reference to the party_id of the user who owns this session. Nullable.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.username IS 'Username of the user who owns this session.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.session_inactive_source IS 'Source of session inactived.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.rec_status_id IS 'Foreign key to record_status table indicating current status of the session record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.created_at IS 'Timestamp when this session record was created.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.created_by IS 'Identifier of the user or system that created this session record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.updated_at IS 'Timestamp when this session record was last updated.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.updated_by IS 'Identifier of the user or system that last updated this session record.';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.deleted_at IS 'Timestamp when this session record was marked as deleted (for soft deletes).';
COMMENT ON COLUMN ${activitySchema.sqlNamespace}.${ddlTable.sessionAuditLog.tableName}.deleted_by IS 'Identifier of the user or system that marked this session record as deleted.';


COMMENT ON TABLE ${researchSchema.sqlNamespace}.participant_file_mapping IS 'Maps participants to their source file uploads for audit and rollback purposes.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.id IS 'Unique identifier for the participant file mapping.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.file_mapping_id IS 'Identifier of the uploaded file from which the participant was sourced. Required for traceability.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.r_subject_id IS 'Reference to the research_subject ID that was inserted from the uploaded file.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.study_id IS 'Optional reference to the study under which the participant was enrolled.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.tenant_id IS 'Reference to the tenant or organization responsible for the participant data.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.rec_status_id IS 'Foreign key indicating the current record status (e.g., active, deleted).';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.created_at IS 'Timestamp when this mapping record was created.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.created_by IS 'User or system identifier that created this mapping record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.updated_at IS 'Timestamp when this mapping record was last updated.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.updated_by IS 'User or system identifier that last updated this mapping record.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.deleted_at IS 'Timestamp when this mapping record was marked as deleted (soft delete).';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.participant_file_mapping.deleted_by IS 'User or system identifier that marked this mapping record as deleted.';



COMMENT ON TABLE ${researchSchema.sqlNamespace}.nutritionintake_mapping IS
'Mapping table that links nutrition intake data with observation mappings and research subjects.';


COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.id IS
'Unique identifier for the nutrition intake mapping record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.nutrition_mapping_id IS
'Reference ID for the nutrition intake data mapping.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.observation_mapping_id IS
'Foreign key referencing the observation mapping record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.r_subject_id IS
'Foreign key referencing the research subject.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.study_id IS
'Foreign key referencing the research study.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.tenant_id IS
'Foreign key referencing the organization (tenant) that owns the data.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.rec_status_id IS
'Foreign key indicating the record status (active, deleted, etc.).';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.created_at IS
'Timestamp when the record was created.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.created_by IS
'User or system identifier that created the record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.updated_at IS
'Timestamp when the record was last updated.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.updated_by IS
'User or system identifier that last updated the record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.deleted_at IS
'Timestamp when the record was deleted, if applicable.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.nutritionintake_mapping.deleted_by IS
'User or system identifier that deleted the record, if applicable.';



COMMENT ON TABLE ${researchSchema.sqlNamespace}.fitness_mapping IS
'Mapping table that links fitness data with observation mappings and research subjects.';


COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.id IS
'Unique identifier for the fitness mapping record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.fitness_mapping_id IS
'Reference ID for the fitness data mapping.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.observation_mapping_id IS
'Foreign key referencing the observation mapping record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.r_subject_id IS
'Foreign key referencing the research subject.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.study_id IS
'Foreign key referencing the research study.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.tenant_id IS
'Foreign key referencing the organization (tenant) that owns the data.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.rec_status_id IS
'Foreign key indicating the record status (active, deleted, etc.).';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.created_at IS
'Timestamp when the record was created.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.created_by IS
'User or system identifier that created the record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.updated_at IS
'Timestamp when the record was last updated.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.updated_by IS
'User or system identifier that last updated the record.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.deleted_at IS
'Timestamp when the record was deleted, if applicable.';

COMMENT ON COLUMN ${researchSchema.sqlNamespace}.fitness_mapping.deleted_by IS
'User or system identifier that deleted the record, if applicable.';



DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
          AND table_name = '${ddlTable.participant.tableName}'
          AND column_name = 'study_id'
    ) THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}
        RENAME COLUMN study_id TO study_display_id;
    END IF;

    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
          AND table_name = '${ddlTable.participant.tableName}'
          AND column_name = 'participant_id'
    ) THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}
        RENAME COLUMN participant_id TO participant_display_id;
    END IF;

    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
          AND table_name = '${ddlTable.fileMetaIngestData.tableName}'
          AND column_name = 'participant_sid'
    ) THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName}
        RENAME COLUMN participant_sid TO participant_display_id;
    END IF;
END $$;



DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
          AND table_name = '${ddlTable.participant.tableName}'
          AND column_name = 'study_display_id'
    ) THEN
        COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.study_display_id
        IS 'Display Identifier for the study to which the participant belongs.';
    END IF;

    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
          AND table_name = '${ddlTable.participant.tableName}'
          AND column_name = 'participant_display_id'
    ) THEN
        COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}.participant_display_id
        IS 'Display identifier for the participant in the study.';
    END IF;

    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
          AND table_name = '${ddlTable.fileMetaIngestData.tableName}'
          AND column_name = 'participant_display_id'
    ) THEN
        COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName}.participant_display_id
        IS 'References the study participant.';
    END IF;
END $$;


DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = '${ddlTable.cgmRawUploadData.tableName}'
        AND constraint_name = 'fk_database_id'
    ) THEN
        ALTER TABLE ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawUploadData.tableName}
                 ADD CONSTRAINT fk_database_id FOREIGN KEY (database_id)
                 REFERENCES ${rawDataSchema.sqlNamespace}.${ddlTable.cgmRawDBData.tableName}(db_file_id);
    END IF;
END $$;



DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = '${ddlTable.fileMetaIngestData.tableName}'
        AND constraint_name = 'file_meta_db_uq'
    ) THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.fileMetaIngestData.tableName}
        ADD CONSTRAINT file_meta_db_uq UNIQUE (file_meta_id, db_file_id);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = '${ddlTable.participant.tableName}'
        AND constraint_name = 'db_study_participant_uq'
    ) THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participant.tableName}
        ADD CONSTRAINT db_study_participant_uq UNIQUE (db_file_id, study_display_id, participant_display_id);
    END IF;
END $$;



DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
                   AND table_name = '${ddlTable.dbMigrationLog.tableName}'
                   AND column_name = 'rollback_status') THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.dbMigrationLog.tableName}
        ADD COLUMN rollback_status BOOLEAN NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
                   AND table_name = '${ddlTable.dbMigrationLog.tableName}'
                   AND column_name = 'rollback_reason') THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.dbMigrationLog.tableName}
        ADD COLUMN rollback_reason TEXT NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
                   AND table_name = '${ddlTable.dbMigrationLog.tableName}'
                   AND column_name = 'rollback_start_time') THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.dbMigrationLog.tableName}
        ADD COLUMN rollback_start_time TIMESTAMPTZ NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = '${dbImportMigrateSchema.sqlNamespace}'
                   AND table_name = '${ddlTable.dbMigrationLog.tableName}'
                   AND column_name = 'rollback_end_time') THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.dbMigrationLog.tableName}
        ADD COLUMN rollback_end_time TIMESTAMPTZ NULL;
    END IF;
END $$;


DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints
        WHERE table_schema = '${researchSchema.sqlNamespace}'
        AND table_name = '${ddlTable.device.tableName}'
        AND constraint_name = 'unique_device_id_name'
    ) THEN
        ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.device.tableName}
        ADD CONSTRAINT unique_device_id_name UNIQUE (id, device_name);
    END IF;
END $$;

COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName} IS 'Stores participant meal and fitness tracking data';

COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}.db_file_id IS 'Unique identifier for the database file';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}.tenant_id IS 'Tenant ID for multi-tenant environments';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}.study_display_id IS 'Study identifier (human-readable)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}.fitness_meal_id IS 'Unique identifier for meal fitness entry';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}.participant_display_id IS 'Unique identifier for the participant';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}.meal_data IS 'Stores meal tracking data in JSON format';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}.fitness_data IS 'Stores fitness tracking data in JSON format';


COMMENT ON TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName} IS 'Stores metadata for studies, including investigator details, publications, and study sites';

COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.db_file_id IS 'Unique identifier for the database file';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.study_meta_id IS 'Unique identifier for the study metadata';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.tenant_id IS 'Tenant ID for multi-tenant environments';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.study_display_id IS 'Study identifier (human-readable)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.study_name IS 'Name of the study';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.start_date IS 'Start date of the study (stored as text)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.end_date IS 'End date of the study (stored as text)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.treatment_modalities IS 'Description of treatment modalities';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.funding_source IS 'Source of funding for the study';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.nct_number IS 'Clinical trial registration number';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.study_description IS 'Detailed study description';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.investigators IS 'List of investigators (JSON array)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.publications IS 'List of related publications (JSON array)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.authors IS 'List of study authors (JSON array)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.institutions IS 'Institutions associated with the study (JSON array)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.labs IS 'Labs associated with the study (JSON array)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.sites IS 'Study sites (JSON array)';
COMMENT ON COLUMN ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}.elaboration IS 'mention the additional data about the study';



COMMENT ON TABLE ${researchSchema.sqlNamespace}.${ddlTable.studyTypeMapping.tableName} IS 'This table maps each study to its type REAL or SYNTHETIC for classification purposes.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyTypeMapping.tableName}.study_id IS 'Foreign key referencing the research study table.';
COMMENT ON COLUMN ${researchSchema.sqlNamespace}.${ddlTable.studyTypeMapping.tableName}.study_type IS 'Indicates whether the study is based on real-world data or is synthetically generated.';

DO $$
BEGIN

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = '${ddlTable.studyMetaData.tableName}'
        AND constraint_name = 'study_meta_data_uq'
    ) THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.studyMetaData.tableName}
        ADD CONSTRAINT study_meta_data_uq UNIQUE (study_meta_id, db_file_id);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_name = '${ddlTable.participantMealFitnessData.tableName}'
        AND constraint_name = 'meal_fitness_data_uq'
    ) THEN
        ALTER TABLE ${dbImportMigrateSchema.sqlNamespace}.${ddlTable.participantMealFitnessData.tableName}
        ADD CONSTRAINT meal_fitness_data_uq UNIQUE (fitness_meal_id, db_file_id);
    END IF;
END $$;

ALTER TABLE ${researchSchema.sqlNamespace}.${ddlTable.studyTypeMapping.tableName} ALTER COLUMN study_type TYPE text USING study_type::text;

  DO $$
  BEGIN
      IF EXISTS (
          SELECT 1
          FROM information_schema.tables
          WHERE table_schema = 'drh_stateful_authentication'
            AND table_name = 'external_auth_mappings'
      ) AND NOT EXISTS (
          SELECT 1
          FROM information_schema.tables
          WHERE table_schema = 'drh_stateful_authentication'
            AND table_name = 'auth_mappings'
      ) THEN
          ALTER TABLE drh_stateful_authentication.external_auth_mappings RENAME TO auth_mappings;
      END IF;

      IF EXISTS (
          SELECT 1
          FROM information_schema.tables
          WHERE table_schema = 'drh_stateful_authentication'
            AND table_name = 'external_auth_mappings'
      ) THEN
          DROP TABLE drh_stateful_authentication.external_auth_mappings;
      END IF;
  END
  $$;


  DO $$
BEGIN
  -- Add column ai_query_type if it doesn't exist
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'drh_stateful_ai_insights'
      AND table_name = 'vanna_ai_request_response'
      AND column_name = 'ai_query_type'
  ) THEN
    ALTER TABLE drh_stateful_ai_insights.vanna_ai_request_response
      ADD COLUMN ai_query_type VARCHAR NOT NULL;
  END IF;

  -- Drop NOT NULL constraint on json_result if it's currently NOT NULL
  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'drh_stateful_ai_insights'
      AND table_name = 'vanna_ai_request_response'
      AND column_name = 'json_result'
      AND is_nullable = 'NO'
  ) THEN
    ALTER TABLE drh_stateful_ai_insights.vanna_ai_request_response
      ALTER COLUMN json_result DROP NOT NULL;
  END IF;

  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'drh_stateful_ai_insights'
      AND table_name = 'vanna_ai_request_response'
      AND column_name = 'results'
      AND is_nullable = 'NO'
  ) THEN
    ALTER TABLE drh_stateful_ai_insights.vanna_ai_request_response
      ALTER COLUMN results DROP NOT NULL;
  END IF;
END
$$;


DO $$
BEGIN
    -- Add fitness_file_metadata column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          and table_name = 'participant_meal_fitness_data'
          AND column_name = 'fitness_file_metadata'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.participant_meal_fitness_data
        ADD COLUMN fitness_file_metadata TEXT;

        COMMENT ON COLUMN drh_stateful_db_import_migration.participant_meal_fitness_data.fitness_file_metadata
            IS 'JSON metadata about the fitness file, including file name, source, format, etc.';
    END IF;

    -- Add meal_file_metadata column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          and table_name = 'participant_meal_fitness_data'
          AND column_name = 'meal_file_metadata'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.participant_meal_fitness_data
        ADD COLUMN meal_file_metadata TEXT;

        COMMENT ON COLUMN drh_stateful_db_import_migration.participant_meal_fitness_data.meal_file_metadata
            IS 'JSON metadata about the meal file, including file name, source, format, etc.';
    END IF;
END;
$$;


DO $$
BEGIN
    -- Drop NOT NULL constraint on zip_file_id if currently NOT NULL
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_raw_data'
          AND table_name = 'subject_observation_upload_data'
          AND column_name = 'zip_file_id'
          AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE drh_stateful_raw_data.subject_observation_upload_data
        ALTER COLUMN zip_file_id DROP NOT NULL;
    END IF;


    IF EXISTS (
      SELECT 1
      FROM information_schema.table_constraints tc
      WHERE tc.table_schema = 'drh_stateful_raw_data'
        AND tc.table_name = 'subject_observation_upload_data'
        AND tc.constraint_name = 'fk_zip_file_id')
    THEN
        ALTER TABLE drh_stateful_raw_data.subject_observation_upload_data
        DROP CONSTRAINT fk_zip_file_id;
    END IF;

END
$$;

-- Add migration_status column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'migration_status'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN migration_status int4;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.migration_status IS
            'Reference to migration_status table indicating the current migration stage';

        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD CONSTRAINT study_metadata_history_migration_status_fkey
        FOREIGN KEY (migration_status) REFERENCES drh_stateful_master.migration_status(stage_id);
    END IF;
END $$;

-- Add updated_by column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_by TEXT;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_by IS
            'User or system that last updated this metadata snapshot';
    END IF;
END $$;

-- Add updated_at column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_at timestamptz;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_at IS
            'Timestamp of the last update to this metadata snapshot';
    END IF;
END $$;



-- Add migration_status column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'migration_status'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN migration_status int4;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.migration_status IS
            'Reference to migration_status table indicating the current migration stage';

        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD CONSTRAINT study_metadata_history_migration_status_fkey
        FOREIGN KEY (migration_status) REFERENCES drh_stateful_master.migration_status(stage_id);
    END IF;
END $$;

-- Add updated_by column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_by TEXT;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_by IS
            'User or system that last updated this metadata snapshot';
    END IF;
END $$;

-- Add updated_at column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_at timestamptz;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_at IS
            'Timestamp of the last update to this metadata snapshot';
    END IF;
END $$;



-- Add migration_status column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'migration_status'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN migration_status int4;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.migration_status IS
            'Reference to migration_status table indicating the current migration stage';

        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD CONSTRAINT study_metadata_history_migration_status_fkey
        FOREIGN KEY (migration_status) REFERENCES drh_stateful_master.migration_status(stage_id);
    END IF;
END $$;

-- Add updated_by column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_by TEXT;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_by IS
            'User or system that last updated this metadata snapshot';
    END IF;
END $$;

-- Add updated_at column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_at timestamptz;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_at IS
            'Timestamp of the last update to this metadata snapshot';
    END IF;
END $$;



-- Add migration_status column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'migration_status'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN migration_status int4;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.migration_status IS
            'Reference to migration_status table indicating the current migration stage';

        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD CONSTRAINT study_metadata_history_migration_status_fkey
        FOREIGN KEY (migration_status) REFERENCES drh_stateful_master.migration_status(stage_id);
    END IF;
END $$;

-- Add updated_by column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_by'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_by TEXT;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_by IS
            'User or system that last updated this metadata snapshot';
    END IF;
END $$;

-- Add updated_at column if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'drh_stateful_db_import_migration'
          AND table_name = 'study_metadata_history'
          AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE drh_stateful_db_import_migration.study_metadata_history
        ADD COLUMN updated_at timestamptz;

        COMMENT ON COLUMN drh_stateful_db_import_migration.study_metadata_history.updated_at IS
            'Timestamp of the last update to this metadata snapshot';
    END IF;
END $$;






END


  `;

/**
 * Generates SQL Data Definition Language (DDL) for the migrations.
 *
 * @returns {string} The SQL DDL for migrations.
 */

function sqlDDLGenerateMigration() {
    return SQLa.SQL<EmitContext>(udm.ddlOptions)`
    \\set ON_ERROR_STOP on
    DO $$
      DECLARE
        table_count int;
        schema_count int;
      BEGIN

        -- Check if the required schemas exist
        SELECT count(*)
        INTO schema_count
        FROM information_schema.schemata
        WHERE schema_name IN ('info_schema_lifecycle', 'info_schema_lifecycle_assurance');

        -- If less than 2 schemas are found, raise an error
        IF schema_count < 2 THEN
            RAISE EXCEPTION 'One or more of the required schemas info_schema_lifecycle, info_schema_lifecycle_assurance are missing';
        END IF;
      END;
      $$;


    ${migrateSP}

    `;
}

export function generated() {
    const ctx = SQLa.typicalSqlEmitContext({
        sqlDialect: SQLa.postgreSqlDialect(),
    });
    const testDependencies: string[] = [];
    for (const filePath of testMigrateDependenciesWithPgtap) {
        try {
            const absolutePath = import.meta.resolve(filePath);
            testDependencies.push(absolutePath);
        } catch (err) {
            console.error(`Error reading filepath ${filePath}:`, err);
            throw err;
        }
    }

    // after this execution `ctx` will contain list of all tables which will beconst researchTables = [
    const researchTables = [
        ddlTable.recordStatus,
        ddlTable.organization,
        ddlTable.location,
        ddlTable.practitioner,
        ddlTable.patient,
        ddlTable.qualification,
        ddlTable.telecom,
        ddlTable.address,
        ddlTable.communication,
        ddlTable.associatedPartyType,
        ddlTable.laboratory,
        ddlTable.researchStudyFocus,
        ddlTable.researchStudyCondition,
        ddlTable.device,
        ddlTable.loincCodes,
        ddlTable.studyVisibility,
        ddlTable.citationIdentifier,
        ddlTable.metricDefinitions,
        ddlTable.researchStudy,
        ddlTable.researchSubject,
        ddlTable.investigatorStudy,
        ddlTable.planDefinition,
        ddlTable.goal,
        ddlTable.activityDefinition,
        ddlTable.subjectObservation,
        ddlTable.citation,
        ddlTable.citationAuthor,

        ddlTable.studyCollaboration,
        ddlTable.externalAuthMappings,
        ddlTable.site,
        ddlTable.cgmData,
        ddlTable.activityLog,
        ddlTable.filterInteraction,
        ddlTable.vannaAiRequestResponse,
        ddlTable.cgmRawZipData,
        ddlTable.cgmRawUploadData,
        ddlTable.rawCgmExtractData,
        ddlTable.cgmRawDBData,
        ddlTable.fileMetaIngestData,
        ddlTable.participant,
        ddlTable.migrationStatus,
        ddlTable.participantMigrationStatus,
        ddlTable.cgmDataMigrationStatus,
        ddlTable.fileInteraction,
        ddlTable.hubInteraction,
        ddlTable.dbMigrationLog,
        ddlTable.studyInteraction,
        ddlTable.studyParticipantInteraction,
        ddlTable.cgmDeviceInfo,
        ddlTable.participantBase,
        ddlTable.cgmMetrics,
    ];

    // passed into `dvts.pumlERD` below (ctx should only be used once)
    const driverGenerateMigrationSQL = ws.unindentWhitespace(
        sqlDDLGenerateMigration().SQL(ctx),
    );

    const pumlERD = (ctx: EmitContext) =>
        diaPUML.plantUmlIE(ctx, function* () {
            for (const table of researchTables) {
                if (SQLa.isGraphEntityDefinitionSupplier(table)) {
                    yield table.graphEntityDefn();
                } else {
                    console.warn(
                        `Skipping table: ${table} (not a graph entity definition supplier)`,
                    );
                }
            }
        }, diaPUML.typicalPlantUmlIeOptions());

    // Attempt to invoke and log the output for debugging
    try {
        const pumlContent = pumlERD(ctx); // Ensure ctx is properly initialized
        //console.log('Generated PlantUML Script:', pumlContent.content || pumlContent);
        const outputPath = "./research-erd.puml";

        // Write the content to a file
        Deno.writeTextFile(outputPath, pumlContent.content || pumlContent);
    } catch (error) {
        console.error("Error generating PlantUML:", error);
    }

    return {
        driverGenerateMigrationSQL,
        //pumlERD: dvts.pumlERD(ctx).content,
        destroySQL: ws.unindentWhitespace(`


      DROP PROCEDURE IF EXISTS "${migrateSP.sqlNS?.sqlNamespace}"."${migrateSP.routineName}" CASCADE;




      `),
        testDependencies,
    };
}
