-- Seed for record_status with idempotency check based on 'code'
INSERT INTO drh_stateful_party.record_status (code, value, created_at, created_by)
VALUES
    ('ACTIVE', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('INPROGRESS', 2, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('INACTIVE', 3, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('PENDING', 4, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('ARCHIVED', 5, <PERSON><PERSON>RENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('DELETED', 6, C<PERSON>RENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('PROCESSING', 7, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('FAILED', 8, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('COMPLETED', 9, <PERSON><PERSON><PERSON><PERSON>_TIMESTAMP AT TIME ZONE 'UTC', '')
ON CONFLICT (code) DO NOTHING;



-- Idempotent seed for study_visibility table with 'Public' and 'Private' values
-- Inserting records with idempotency check based on visibility_name

INSERT INTO drh_stateful_master.study_visibility 
(visibility_id, visibility_name, visibility_description, rec_status_id, created_at, created_by)
VALUES
    (1, 'Public', 'This visibility allows the study to be accessed by everyone.', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN'),
    (2, 'Private', 'This visibility restricts the study access to authorized users only.', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN'),
    (3, 'Internal', 'This visibility allows the study access to users of the same organisation.', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN')
ON CONFLICT (visibility_name) DO NOTHING;


INSERT INTO drh_stateful_master.profile_status_type (profile_status_type_id,code, description) 
VALUES 
    (1,'INCOMPLETE', 'Profile is incomplete and pending information'),
    (2,'COMPLETE', 'Profile is fully completed'),
    (3,'PENDING_VERIFICATION', 'Profile is pending verification from the user or system'),
    (4,'NOT INITIATED', 'Profile creation not started')
ON CONFLICT (code) DO NOTHING;


-- Insert predefined values for FHIR ResearchSubject statuses, making it idempotent

INSERT INTO drh_stateful_master.research_subject_status_master (code,display_name, definition, system_url)
VALUES
    (1,'candidate', 'The person has been identified as a potential participant but has not yet been formally enrolled.', 'http://hl7.org/fhir/researchsubject-status#candidate'),
    (2,'eligible', 'The person meets the criteria for inclusion in the study and is eligible for enrollment.', 'http://hl7.org/fhir/researchsubject-status#eligible'),
    (3,'follow-up', 'The person has completed the intervention phase of the study but is being followed for additional observations or assessments after the study.', 'http://hl7.org/fhir/researchsubject-status#follow-up'),
    (4,'ineligible', 'The person does not meet the criteria for the study and is not eligible to participate.', 'http://hl7.org/fhir/researchsubject-status#ineligible'),
    (5,'not-registered', 'The person has been deemed eligible but has not yet been formally registered into the study.', 'http://hl7.org/fhir/researchsubject-status#not-registered'),
    (6,'off-study', 'The person has formally ended their participation in the study, either voluntarily or by the study''s decision.', 'http://hl7.org/fhir/researchsubject-status#off-study'),
    (7,'on-study', 'The person is currently participating in the study.', 'http://hl7.org/fhir/researchsubject-status#on-study'),
    (8,'pending-on-study', 'The person is expected to be on study soon but has not yet started.', 'http://hl7.org/fhir/researchsubject-status#pending-on-study')
ON CONFLICT (display_name) DO NOTHING;


-- Inserting seed values for LOINC codes into drh_stateful_master.loinc_codes (idempotent)

INSERT INTO drh_stateful_master.loinc_codes (loinc_code_id, loinc_code, loinc_description, loinc_class, loinc_type, rec_status_id, created_at, created_by)
VALUES 
    -- CGM (Continuous Glucose Monitoring)
    (1, '32414-0', 'Glucose [Mass/volume] in Blood by Continuous Glucose Monitoring', 'Laboratory', 'Measurement', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN'),
    
    -- Baseline HbA1c (Hemoglobin A1c)
    (2, '4548-4', 'Hemoglobin A1c/Hemoglobin.total in Blood', 'Laboratory', 'Measurement', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN'),
    
    -- BMI (Body Mass Index)
    (3, '39156-5', 'Body mass index (BMI) [Ratio] by Calculation', 'Laboratory', 'Observation', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN'),
    
    -- Glucose reading (Random or fasting glucose)
    (4, '2339-0', 'Glucose [Mass/volume] in Blood', 'Laboratory', 'Measurement', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN'),
    
    -- Blood Glucose (e.g., Fasting Blood Glucose)
    (5, '20903-4', 'Glucose [Mass/volume] in Blood by Venipuncture', 'Laboratory', 'Measurement', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'UNKNOWN')
ON CONFLICT (loinc_code) DO NOTHING;  -- Avoid insertion if loinc_code already exists


-- Seed Data for Citation Status
INSERT INTO drh_stateful_master.citation_status_master (code, display_name, definition, system_url) VALUES
(1, 'Draft', 'The citation is in the process of being developed and is not yet ready for use.', 'http://hl7.org/fhir/ValueSet/publication-status'),
(2, 'Active', 'The citation is ready for normal use.', 'http://hl7.org/fhir/ValueSet/publication-status'),
(3, 'Retired', 'The citation is no longer active or in use, but is maintained for historical purposes.', 'http://hl7.org/fhir/ValueSet/publication-status'),
(4, 'Unknown', 'The status of the citation is not known.', 'http://hl7.org/fhir/ValueSet/publication-status')
ON CONFLICT (display_name) DO NOTHING;

-- Seed Data for Investigator Study Role
INSERT INTO drh_stateful_master.investigator_study_role (code, role) VALUES
(1, 'Principal Investigator'),
(2, 'Principal Author'),
(3, 'Co-Investigator'),
(4, 'Co-Author')
ON CONFLICT (role) DO NOTHING;

-- Seed Data for Study Status
INSERT INTO drh_stateful_master.study_status_master (code, display_name, definition, system_url) VALUES
(1, 'Overall study', 'Used for documenting the start and end of the overall study, distinct from progress states.', 'http://hl7.org/fhir/research-study-status'),
(2, 'Active', 'Study is opened for accrual.', 'http://hl7.org/fhir/research-study-status'),
(3, 'Active, not recruiting', 'The study is ongoing, but participants are not currently being recruited or enrolled.', 'http://hl7.org/fhir/research-study-status'),
(4, 'Administratively Completed', 'Study is completed prematurely and will not resume.', 'http://hl7.org/fhir/research-study-status'),
(5, 'Approved', 'Protocol is approved by the review board.', 'http://hl7.org/fhir/research-study-status'),
(6, 'Closed to Accrual', 'Study is closed for accrual; patients can be examined and treated.', 'http://hl7.org/fhir/research-study-status'),
(7, 'Closed to Accrual and Intervention', 'Study is closed to accrual and intervention.', 'http://hl7.org/fhir/research-study-status'),
(8, 'Completed', 'The study closed according to the study plan.', 'http://hl7.org/fhir/research-study-status'),
(9, 'Disapproved', 'Protocol was disapproved by the review board.', 'http://hl7.org/fhir/research-study-status'),
(10, 'Enrolling by invitation', 'The study is selecting participants from a population invited in advance.', 'http://hl7.org/fhir/research-study-status'),
(11, 'In Review', 'Protocol is submitted to the review board for approval.', 'http://hl7.org/fhir/research-study-status'),
(12, 'Not yet recruiting', 'The study has not started recruiting participants.', 'http://hl7.org/fhir/research-study-status'),
(13, 'Recruiting', 'The study is currently recruiting participants.', 'http://hl7.org/fhir/research-study-status'),
(14, 'Temporarily Closed to Accrual', 'Study is temporarily closed for accrual.', 'http://hl7.org/fhir/research-study-status'),
(15, 'Temporarily Closed to Accrual and Intervention', 'Study is temporarily closed for accrual and intervention.', 'http://hl7.org/fhir/research-study-status'),
(16, 'Terminated', 'The study has stopped early and will not start again.', 'http://hl7.org/fhir/research-study-status'),
(17, 'Withdrawn', 'Protocol was withdrawn by the lead organization.', 'http://hl7.org/fhir/research-study-status')
ON CONFLICT (display_name) DO NOTHING;

-- Seed for metric_definitions with idempotency check based on 'code'

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints
        WHERE constraint_type = 'PRIMARY KEY'
        AND table_name = 'metric_definitions'
        AND table_schema = 'drh_stateful_master'
    ) THEN
        -- Add the primary key constraint
        ALTER TABLE drh_stateful_master.metric_definitions
        ADD CONSTRAINT metric_id_pkey PRIMARY KEY (metric_id);
    END IF;
END $$;




-- Metric: Time CGM Active
INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info)
VALUES (
    'time_cgm_active', 
    'Time CGM Active', 
    '{"description": "This metric calculates the percentage of time during a specific period (e.g., a day, week, or month) that the CGM device is actively collecting data. It takes into account the total duration of the monitoring period and compares it to the duration during which the device was operational and recording glucose readings.", 
      "formula": "Percentage of time CGM is active = (Duration CGM is active / Total duration of monitoring period) × 100"}'
)ON CONFLICT (metric_id) DO NOTHING;

-- Metric: Number of Days CGM Worn
INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info)
VALUES (
    'number_of_days_cgm_worn', 
    'Number of Days CGM Worn', 
    '{"description": "This metric represents the total number of days the CGM device was worn by the user over a monitoring period. It helps in assessing the adherence to wearing the device as prescribed.", 
      "formula": "Number of days CGM worn = Count of days with CGM data recorded in the monitoring period"}'
)ON CONFLICT (metric_id) DO NOTHING;

-- Metric: Mean Glucose
INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info)
VALUES (
    'mean_glucose', 
    'Mean Glucose', 
    '{"description": "Mean glucose reflects the average glucose level over the monitoring period, serving as an indicator of overall glucose control. It is a simple yet powerful measure in glucose management.", 
      "formula": "Mean glucose = Sum of all glucose readings / Number of readings"}'
)ON CONFLICT (metric_id) DO NOTHING;

-- Metric: Glucose Management Indicator (GMI)
INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info)
VALUES (
    'gmi', 
    'Glucose Management Indicator (GMI)', 
    '{"description": "GMI provides an estimated A1C level based on mean glucose, which can be used as an indicator of long-term glucose control. GMI helps in setting and assessing long-term glucose goals.", 
      "formula": "GMI = (3.31 + 0.02392 × Mean glucose) × 100"}'
)ON CONFLICT (metric_id) DO NOTHING;

-- Metric: Glucose Variability
INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info)
VALUES (
    'glucose_variability', 
    'Glucose Variability', 
    '{"description": "Glucose variability measures fluctuations in glucose levels over time, calculated as the coefficient of variation (%CV). A lower %CV indicates more stable glucose control.", 
      "formula": "Glucose variability = (Standard deviation of glucose / Mean glucose) × 100"}'
)ON CONFLICT (metric_id) DO NOTHING;

-- Insert a single record for AGP metrics and axes
INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info)
VALUES (
    'AGP_metrics',
    'Ambulatory Glucose Profile (AGP)',
    '{"description": "The Ambulatory Glucose Profile (AGP) summarizes glucose monitoring data over a specified period, typically 14 to 90 days. It provides a visual representation of glucose levels, helping to identify patterns and variability in glucose management.",
        "metrics": {
            "time_in_target_range": {
                "description": "This metric indicates the percentage of time glucose levels are within the target range (typically 70-180 mg/dL). It is essential for evaluating the effectiveness of diabetes management.",
                "formula": "Percentage in Target Range = (Time in Target Range / Total Time) × 100"
            },
            "below_70": {
                "description": "Tracks the percentage of time glucose levels are below 70 mg/dL, indicating hypoglycemic episodes. Understanding these periods helps prevent severe lows.",
                "formula": "Percentage below 70 mg/dL = (Time below 70 mg/dL / Total Time) × 100"
            },
            "above_180": {
                "description": "Indicates the percentage of time glucose levels exceed 180 mg/dL, highlighting periods of hyperglycemia. Managing these episodes is critical for overall health.",
                "formula": "Percentage above 180 mg/dL = (Time above 180 mg/dL / Total Time) × 100"
            },
            "quartiles": {
                "description": "Quartiles divide glucose readings into four equal parts, helping to understand glucose level distribution. Q1 is the 25th percentile, Q2 is the median, and Q3 is the 75th percentile.",
                "formula": "Quartiles are calculated from sorted glucose readings: Q1 = 25th percentile, Q2 = 50th percentile (median), Q3 = 75th percentile."
            }
        },
        "axes": {
            "x_axis": {
                "description": "Time of Day - The X-axis represents the time of day, segmented into hourly intervals. It typically includes the following time points: 12 AM, 3 AM, 6 AM, 9 AM, 12 PM, 3 PM, 6 PM, 9 PM, and 11 PM."
            },
            "y_axis": {
                "description": "Glucose Levels -The Y-axis represents glucose levels measured in milligrams per deciliter (mg/dL). It typically displays a range from 0 mg/dL to 350 mg/dL, indicating when glucose levels are within, below, or above the target range."
            }
        }
    }'
)ON CONFLICT (metric_id) DO NOTHING;

-- Seed SQL for metrics definitions with JSON formatted metric_info

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('liability_index', 'Liability Index', '{"description": "The Liability Index quantifies the risk associated with glucose variability, measured in mg/dL.", "formula": "Liability Index = (Total Duration of monitoring period) * (Average of CGM_i)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('hypoglycemic_episodes', 'Hypoglycemic Episodes', '{"description": "This metric counts the number of occurrences when glucose levels drop below a specified hypoglycemic threshold, indicating potentially dangerous low blood sugar events.", "formula": "Hypoglycemic Episodes = COUNT(CASE WHEN CGM_i < Threshold THEN 1 END)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('euglycemic_episodes', 'Euglycemic Episodes', '{"description": "This metric counts the number of instances where glucose levels remain within the target range, indicating stable and healthy glucose control.", "formula": "Euglycemic Episodes = COUNT(CASE WHEN CGM_i BETWEEN LowThreshold AND HighThreshold THEN 1 END)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('hyperglycemic_episodes', 'Hyperglycemic Episodes', '{"description": "This metric counts the number of instances where glucose levels exceed a certain hyperglycemic threshold, indicating potentially harmful high blood sugar events.", "formula": "Hyperglycemic Episodes = COUNT(CASE WHEN CGM_i > Threshold THEN 1 END)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('m_value', 'M Value', '{"description": "The M Value provides a measure of glucose variability, calculated from the mean of the absolute differences between consecutive CGM values over a specified period.", "formula": "M Value = Mean(ABS(CGM_i - CGM_(i-1)))"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('mean_amplitude', 'Mean Amplitude', '{"description": "Mean Amplitude quantifies the average degree of fluctuation in glucose levels over a given time frame, giving insight into glucose stability.", "formula": "Mean Amplitude = Mean(ABS(CGM_i - Mean(CGM)))"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('average_daily_risk_range', 'Average Daily Risk Range', '{"description": "This metric assesses the average risk associated with daily glucose variations, expressed in mg/dL.", "formula": "Average Daily Risk Range = (Max(CGM) - Min(CGM)) / Number of Days"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('j_index', 'J Index', '{"description": "The J Index calculates glycemic variability using both high and low glucose readings, offering a comprehensive view of glucose fluctuations.", "formula": "J Index = (3.0 * Hypoglycemia Component) + (1.6 * Hyperglycemia Component)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('low_blood_glucose_index', 'Low Blood Glucose Index', '{"description": "This metric quantifies the risk associated with low blood glucose levels over a specified period, measured in mg/dL.", "formula": "Low Blood Glucose Index = SUM(CASE WHEN CGM_i < LowThreshold THEN 1 ELSE 0 END)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('high_blood_glucose_index', 'High Blood Glucose Index', '{"description": "This metric quantifies the risk associated with high blood glucose levels over a specified period, measured in mg/dL.", "formula": "High Blood Glucose Index = SUM(CASE WHEN CGM_i > HighThreshold THEN 1 ELSE 0 END)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('glycemic_risk_assessment', 'Glycemic Risk Assessment Diabetes Equation (GRADE)', '{"description": "GRADE is a metric that combines various glucose metrics to assess overall glycemic risk in individuals with diabetes, calculated using multiple input parameters.", "formula": "GRADE = (Weights based on Low, Normal, High CGM values)"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('continuous_overall_net_glycemic_action', 'Continuous Overall Net Glycemic Action (CONGA)', '{"description": "CONGA quantifies the net glycemic effect over time by evaluating the differences between CGM values at specified intervals.", "formula": "CONGA = Mean(ABS(CGM_i - CGM_(i-k))) for k=1 to n"}')ON CONFLICT (metric_id) DO NOTHING;

INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
('mean_of_daily_differences', 'Mean of Daily Differences', '{"description": "This metric calculates the average of the absolute differences between daily CGM readings, giving insight into daily glucose variability.", "formula": "Mean of Daily Differences = Mean(ABS(CGM_i - CGM_(i-1)))"}')ON CONFLICT (metric_id) DO NOTHING;


INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
(
    'daily_glucose_profile', 
    'Daily Glucose Profile', 
    '{
        "description": "The Daily Glucose Profile Chart visualizes a participant''s glucose levels over a specified timeframe, typically the last 14 days. Each point on the graph represents a glucose reading taken at a specific hour, indicating the participant''s response to food intake, exercise, medication, and other lifestyle factors. Monitoring these thresholds helps in identifying periods of risk: hypoglycemia, for glucose levels below 70 mg/dL, and hyperglycemia, for levels above 180 mg/dL. This analysis can guide interventions and adjustments in treatment. A consistently high or low profile may lead to further investigation and modifications in treatment plans.",
        "axes": {
            "y_axis": "The y-axis represents glucose levels in mg/dL, with a lower threshold of 70 mg/dL indicating hypoglycemia risk and an upper threshold of 180 mg/dL indicating hyperglycemia risk.",
            "x_axis": "The x-axis spans a week from Friday to Thursday, displaying data between 12 PM and 10 PM each day, focusing on peak active hours for glucose level variations."
        }
    }'
)ON CONFLICT (metric_id) DO NOTHING;


INSERT  INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info) VALUES
(
    'goals_for_type_1_and_type_2_diabetes_chart_metrics', 
    'Goals for Type 1 and Type 2 Diabetes chart Metrics', 
    '{
        "description": "Goals for Type 1 and Type 2 Diabetes Chart provides a comprehensive view of a participant''s glucose readings categorized into different ranges over a specified period.",
        "metrics": {
            "very_low": {
                "description": "Represents the count and percentage of readings below 54 mg/dL, which may indicate critical hypoglycemia."
            },
            "low": {
                "description": "Represents the count and percentage of readings between 54 mg/dL and 69 mg/dL, indicating a potential risk of hypoglycemia."
            },
            "in_range": {
                "description": "The percentage and count of readings between 70 mg/dL and 180 mg/dL, considered the target glucose range for optimal health.",
                "target": "over 70%"
            },
            "high": {
                "description": "Includes readings between 181 mg/dL and 250 mg/dL, indicating borderline hyperglycemia."
            },
            "very_high": {
                "description": "Represents readings above 250 mg/dL, indicating potentially dangerous hyperglycemia."
            }
        },
        "formula": "The calculation for each category is performed by counting the total readings in each defined glucose range. The chart shows both the total time spent in each range and the percentage of total readings over a defined monitoring period. Example: If a participant has 100 readings and 10 are below 54 mg/dL, the percentage is calculated as (10 / 100) * 100, resulting in 10%. Usage: The chart aids healthcare providers and participants in understanding glucose variability and making informed decisions.",
        "axes": "x-axis: Time intervals and y-axis: Percentages of time in each range"
    }'
)ON CONFLICT (metric_id) DO NOTHING;

INSERT INTO drh_stateful_master.metric_definitions (metric_id, metric_name, metric_info)
VALUES (
  'liability_index',
  'Glycemic Lability Index',
  '{"description": "The Glycemic Lability Index quantifies the risk associated with glucose variability, measured in mg/dL.", "formula": "Glycemic Lability Index = (Total Duration of monitoring period) * (Average of CGM_i)"}'
)
ON CONFLICT (metric_id) DO UPDATE
SET
  metric_name = EXCLUDED.metric_name,
  metric_info = EXCLUDED.metric_info;





-- Seed Data for drh_stateful_master.research_study_focus with idempotency check based on 'code'
INSERT INTO drh_stateful_master.research_study_focus (
    coding_system, code, display, rec_status_id, created_at, created_by
) VALUES 
    ('http://snomed.info/sct', '44054006', 'Type 1 diabetes mellitus', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '44054008', 'Type 2 diabetes mellitus', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '713144005', 'Diabetes prevention', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '422504002', 'Diabetic retinopathy', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '11687002', 'Gestational diabetes', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '414916001', 'Diabetes and obesity', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '473010000', 'Diabetic neuropathy', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '44365000', 'Diabetes and cardiovascular risk', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '419705008', 'Diabetes in the elderly', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '45007003', 'Diabetes and mental health', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '44799002', 'Diabetic kidney disease', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '78200008', 'Type 1 diabetes and hypertension', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '44129007', 'Diabetes and stroke risk', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '1256001', 'Diabetes and foot ulcers', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '11687002', 'Pregnancy and diabetes', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', ''),
    ('http://snomed.info/sct', '72337001', 'Type 2 diabetes and cardiovascular disease', 1, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', '')
ON CONFLICT (code) DO NOTHING;


INSERT INTO drh_stateful_master.contact_point_system (id,code,system, value,description)
VALUES
    ('01JGGVX0042QTX5TRQEDJ7NA0G','phone', 'http://hl7.org/fhir/contact-point-system','Phone', 'Contact via telephone'),
    ('01JGGVYM5D40092CD4Q1RCVR9T','email','http://hl7.org/fhir/contact-point-system', 'Email',  'Contact via email address'),
    ('01JGGVZ4YWXDTSJAX80XBBDPPH','fax', 'http://hl7.org/fhir/contact-point-system','Fax', 'Contact via fax machine'),
    ('01JGGVZFF5A337XNCZTPZ2TV99','pager', 'http://hl7.org/fhir/contact-point-system','Pager', 'Contact via pager'),
    ('01JGGVZTPXR2NQ90EW3P2CRFXT','url','http://hl7.org/fhir/contact-point-system', 'URL', 'Contact via website or URL'),
    ('01JGGW03V54H046A9X203J9WFX','sms', 'http://hl7.org/fhir/contact-point-system','SMS', 'Contact via text messaging'),
    ('01JGGW0ERN4Y2MB0R3172JEF1T','other','http://hl7.org/fhir/contact-point-system', 'Other', 'Other types of contact')
ON CONFLICT (code) DO NOTHING;


INSERT INTO drh_stateful_master.contact_point_use (id, code, system, value, description)
VALUES
    ('01JGGW6555060JAM9TT5ESQYPP','home','http://hl7.org/fhir/contact-point-use','Home', 'Personal communication use'),
    ('01JGGW6E5XGENVJ580JBQXA56C','work','http://hl7.org/fhir/contact-point-use', 'Work', 'Work-related communication use'),
    ('01JGGW6PGDMMZSKM56EATZ9272','temp', 'http://hl7.org/fhir/contact-point-use','Temporary', 'Temporary communication use'),
    ('01JGGW6YEDCK61CKMEWSREKG5Y','old','http://hl7.org/fhir/contact-point-use', 'Old', 'Previous or outdated communication use'),
    ('01JGGW7N2D9GXJ6D7J1GBTWQS3','mobile','http://hl7.org/fhir/contact-point-use','Mobile', 'Mobile communication use')
ON CONFLICT (code) DO NOTHING;


INSERT INTO drh_stateful_master.contact_point_address_type (id,code,system, value, description)
VALUES
    ('01JGGWAY6PN2NBRHNCV1X2VA39','physical','http://hl7.org/fhir/address-type','Physical', 'A physical address that can be visited.'),
    ('01JGGWB48NX48841AJ7BRAECQX','postal','http://hl7.org/fhir/address-type', 'Postal', 'An address for mailing, such as a PO Box.'),
    ('01JGGWB99DD9MNS2MCN5QYSXPF','both', 'http://hl7.org/fhir/address-type','Both', 'An address that serves as both physical and postal.')
ON CONFLICT (code) DO NOTHING;


INSERT INTO drh_stateful_master.contact_point_address_use (id,code,system, value, description)
VALUES
    ('01JGGWDYS6KBG7117614038VQZ','home','http://hl7.org/fhir/address-use', 'Home', 'A home address.'),
    ('01JGGWE9V6YW66AJGE7P6JVFB8','work', 'http://hl7.org/fhir/address-use','Work', 'A work or office address.'),
    ('01JGGWEJG6DKDGQZ2RGMX5FYJE','temp','http://hl7.org/fhir/address-use', 'Temporary', 'A temporary address.'),
    ('01JGGWETTPH7WTJVJSTVTC0VV7','old', 'http://hl7.org/fhir/address-use','Old', 'A previous or outdated address.'),
    ('01JGGWF33YJEKATEJQA5YC2RPC','billing','http://hl7.org/fhir/address-use', 'Billing', 'An address used for billing purposes.')
ON CONFLICT (code) DO NOTHING;

INSERT INTO drh_stateful_master.ethnicity_type (
    ethnicity_type_id, code, system_uri,system_oid, display, ethnicity_text,created_by
) VALUES
    ('01JH56B8WN6R5SN2WVTVH0GRH1', '2135-2', 'http://hl7.org/fhir/us/core/ValueSet/omb-ethnicity-category','urn:oid:2.16.840.1.113883.6.238', 'Hispanic or Latino', 'Hispanic','admin'),
    ('01JH56BN5J0RQM4V8AZMG3PAMC', '2186-5', 'http://hl7.org/fhir/us/core/ValueSet/omb-ethnicity-category','urn:oid:2.16.840.1.113883.6.238', 'Not Hispanic or Latino', 'Not Hispanic','admin'),
    ('01JH5CGD1D3FXVTDNKQYBBDXX2', 'ASKU', 'http://hl7.org/fhir/us/core/ValueSet/omb-ethnicity-category',NULL, 'Asked but unknown', 'Information was sought but not found (e.g., patient was asked but did not know)','admin'),
    ('01JH5CGMHMBK71JVKM6JFRE8Q3', 'UNK', 'http://hl7.org/fhir/us/core/ValueSet/omb-ethnicity-category',NULL, 'Unknown', 'A proper value is applicable, but not known','admin')
ON CONFLICT (code) DO NOTHING;



INSERT INTO drh_stateful_master.race_type (
    race_type_id, code, system_uri, system_oid, display, race_text, created_by
) VALUES
    ('01JH56BWN1QAZ6WNGNX1F4F2M5', '2028-9', 'http://hl7.org/fhir/us/core/StructureDefinition/us-core-race', 'urn:oid:2.16.840.1.113883.6.238', 'Asian', 'Asian', 'admin'),
    ('01JH56C3AJ7DQ7K4BEF43590MP', '2054-5', 'http://hl7.org/fhir/us/core/StructureDefinition/us-core-race', 'urn:oid:2.16.840.1.113883.6.238', 'Black or African American', 'Black', 'admin'),
    ('01JH56C8TJQGF45PEB1KCMW98P', '2106-3', 'http://hl7.org/fhir/us/core/StructureDefinition/us-core-race', 'urn:oid:2.16.840.1.113883.6.238', 'White', 'White', 'admin'),
    ('01JH56MCJX26P0S35BMSHMMC5Y', '2076-8', 'http://hl7.org/fhir/us/core/StructureDefinition/us-core-race', 'urn:oid:2.16.840.1.113883.6.238', 'Native Hawaiian or Other Pacific Islander', 'Native Hawaiian', 'admin'),
    ('01JH56MPGNP6PMC9PKHVR5RA22', '1002-5', 'http://hl7.org/fhir/us/core/StructureDefinition/us-core-race', 'urn:oid:2.16.840.1.113883.6.238', 'American Indian or Alaska Native', 'American Indian', 'admin'),
    ('01JH56UNK1QAZ6WNGNX1F4F2M5', 'UNK', 'http://hl7.org/fhir/v3/NullFlavor', 'urn:oid:2.16.840.1.113883.6.238', 'Unknown', 'Unknown', 'admin')
ON CONFLICT (code, system_uri, system_oid) DO NOTHING;


INSERT INTO drh_stateful_master.research_study_party_role (study_party_role_id, code, system, display, description, rec_status_id, created_at)
VALUES 
  ('01JH78KA11H7JEFYVK90R7B8CW', 'sponsor', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Sponsor', 'sponsor', 1, CURRENT_TIMESTAMP),
  ('01JH78KPFE2FAVDM5796ATF4V7', 'lead-sponsor', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Lead-sponsor', 'lead-sponsor', 1, CURRENT_TIMESTAMP),
  ('01JH78M1CE2XA7E5FZY3R1M2BS', 'sponsor-investigator', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Sponsor-investigator', 'sponsor-investigator', 1, CURRENT_TIMESTAMP),
  ('01JH78MAM6TJQZDGYHVKEH3JH5', 'primary-investigator', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Primary-investigator', 'primary-investigator', 1, CURRENT_TIMESTAMP),
  ('01JH78MNJ6KTG3TWC2G2XRSFJN', 'collaborator', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Collaborator', 'collaborator', 1, CURRENT_TIMESTAMP),
  ('01JH78MZMECW0K8NC24SXQE0TX', 'funding-source', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Funding-source', 'funding-source', 1, CURRENT_TIMESTAMP),
  ('01JH78N8XE47RKV2S7HHZGYGS8', 'general-contact', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'General-contact', 'general-contact', 1, CURRENT_TIMESTAMP),
  ('01JH78NJ1E5BH6E1716WCBXXQW', 'recruitment-contact', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Recruitment-contact', 'recruitment-contact', 1, CURRENT_TIMESTAMP),
  ('01JH78NVRDP9J1QZX4RPFVTKP7', 'sub-investigator', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Sub-investigator', 'sub-investigator', 1, CURRENT_TIMESTAMP),
  ('01JH78P66P5QM0RZW9TTN68CBJ', 'study-director', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Study-director', 'study-director', 1, CURRENT_TIMESTAMP),
  ('01JH78PEPXRHR81NS1RG3R2R4V', 'study-chair', 'http://hl7.org/fhir/ValueSet/research-study-party-role', 'Study-chair', 'study-chair', 1, CURRENT_TIMESTAMP),
  ('01JHHXS7F8W0RZ2MKTQ9CH5K4P', 'co-investigator',  '',  'Co-Investigator',  'The team of investigators responsible for conducting the study.', 1,CURRENT_TIMESTAMP),
  ('01JHQNCQTSSKNJCJY3N6A9SBC4', 'principal-author',  '',  'Principal Author',  'The lead or primary contributor to a scholarly work, research paper, or publication.', 1,CURRENT_TIMESTAMP),
  ('01JHQNCES539B1AAQ0D6DYRAT1', 'co-author',  '',  'Co-Author',  'an individual who significantly contributes to the creation of a scholarly work, research paper, or publication alongside other authors.', 1,CURRENT_TIMESTAMP) ,
  ('01JH78KA11H7JEFYVK90R7B8CX', 'nominated-principal-investigator', '', 'Nominated-Principal Investigator', 'nominated-principal-investigator', 1, CURRENT_TIMESTAMP),
  ('01JH78KA11H7JEFYVK90R7B8CY', 'study-team', '', 'Study Team Member', 'study-team', 1, CURRENT_TIMESTAMP)    
ON CONFLICT (code) DO NOTHING;


INSERT INTO drh_stateful_master.organization_type (
    organization_type_id,
    code,
    system_uri,
    display,
    description,
    rec_status_id,
    created_at
) VALUES 
    ('01JH7B6Q579CPM8JN0Y39555VV', 'nih', 'http://hl7.org/fhir/research-study-party-organization-type', 'NIH', 'NIH organization type', 1, CURRENT_TIMESTAMP),
    ('01JH7B732PPA6HS9XSH9DETWMS', 'fda', 'http://hl7.org/fhir/research-study-party-organization-type', 'FDA', 'FDA organization type', 1, CURRENT_TIMESTAMP),
    ('01JH7B7B26XFZPCKJF69FDC9EE', 'government', 'http://hl7.org/fhir/research-study-party-organization-type', 'Government', 'Government organization type', 1, CURRENT_TIMESTAMP),
    ('01JH7B7JMYVKNK8E241T3FR57K', 'nonprofit', 'http://hl7.org/fhir/research-study-party-organization-type', 'Nonprofit', 'Nonprofit organization type', 1, CURRENT_TIMESTAMP),
    ('01JH7B7W860EEED5GD23ZGP3CZ', 'academic', 'http://hl7.org/fhir/research-study-party-organization-type', 'Academic', 'Academic organization type', 1, CURRENT_TIMESTAMP),
    ('01JH7B86GEYDVB25TFBAZAQSGG', 'industry', 'http://hl7.org/fhir/research-study-party-organization-type', 'Industry', 'Industry organization type', 1, CURRENT_TIMESTAMP),
    ('01JH7B6Q579CPM8JN0Y39555VZ', 'funder', 'http://hl7.org/fhir/research-study-party-organization-type', 'Funder', 'Funder organization type', 1, CURRENT_TIMESTAMP),
    ('01JH7B732PPA6HS9XSH9DETWNE', 'research', 'http://hl7.org/fhir/research-study-party-organization-type', 'Research', 'Research organization type', 1,CURRENT_TIMESTAMP) 
    ON CONFLICT (code) DO NOTHING;



INSERT INTO drh_stateful_research_study.device (
    id,
    manufacturer,
    device_name,
    status,
    rec_status_id
) VALUES 
    ('c780ceea-6d3c-4efa-9076-dcac9052bee8', 'Abbott', 'Freestyle Libre', 'Active', 1),
    ('5793e1ee-ee2a-420c-a9ed-e4873127aca4', 'Dexcom', 'Clarity', 'Active', 1),
    ('75ea28b4-dd01-43c5-84c0-cd6f46f4ef9a', 'Medtronic', 'Carelink', 'Active', 1),
    ('c91ae678-1cd4-4c87-b0b9-4857f5d2140d', 'Tidepool', 'Tidepool', 'Active', 1),    
    ('1a2b3c4d-1111-2222-3333-444455556666', 'Dexcom', 'Dexcom G4', 'Active', 1),
    ('2b3c4d5e-2222-3333-4444-555566667777', 'Dexcom', 'Dexcom G5', 'Active', 1),
    ('3c4d5e6f-3333-4444-5555-666677778888', 'Dexcom', 'Dexcom G6', 'Active', 1),
    ('4d5e6f7g-4444-5555-6666-777788889999', 'Dexcom', 'Dexcom G7', 'Active', 1),
    ('5e6f7g8h-5555-6666-7777-888899990000', 'Dexcom', 'Stelo', 'Active', 1),    
    ('7g8h9i0j-7777-8888-9999-000011112222', 'Abbott', 'FreeStyle Libre 2', 'Active', 1),
    ('8h9i0j1k-8888-9999-0000-111122223333', 'Abbott', 'FreeStyle Libre 3', 'Active', 1),
    ('9i0j1k2l-9999-0000-1111-222233334444', 'Abbott', 'FreeStyle Libre Pro', 'Active', 1),
    ('0j1k2l3m-0000-1111-2222-333344445555', 'Medtronic', 'Guardian Connect', 'Active', 1),
    ('1k2l3m4n-1111-2222-3333-444455556666', 'Medtronic', 'Simplera CGM System', 'Active', 1),
    ('2l3m4n5o-2222-3333-4444-555566667777', 'Senseonics', 'Eversense', 'Active', 1),
    ('3m4n5o6p-3333-4444-5555-666677778888', 'Senseonics', 'Eversense XL', 'Active', 1),
    ('4n5o6p7q-4444-5555-6666-777788889999', 'Senseonics', 'Eversense E3', 'Active', 1),
    ('5o6p7q8r-5555-6666-7777-888899990000', 'Senseonics', 'Eversense 365', 'Active', 1),
    ('6p7q8r9s-6666-7777-8888-999900001111', 'Abbott', 'FreeStyle Navigator', 'Active', 1),
    ('7q8r9s0t-7777-8888-9999-000011112222', 'Medtronic', 'Medtronic Paradigm', 'Active', 1),
    ('a1b2c3d4-aaaa-bbbb-cccc-ddddeeeeffff', 'Dexcom', 'Dexcom G4 Platinum', 'Active', 1),
    ('b2c3d4e5-bbbb-cccc-dddd-eeeeffff1111', 'Medtronic', 'Medtronic MiniMed', 'Active', 1),
    ('c3d4e5f6-cccc-dddd-eeee-ffff11112222', 'Medtronic', 'MiniMed Continuous Glucose Monitor', 'Active', 1)
ON CONFLICT (id, device_name) DO NOTHING;



INSERT INTO drh_stateful_master.accepted_file_formats (title) 
VALUES 
    ('csv'),
    ('excel'),
    ('text')
ON CONFLICT (title) DO NOTHING;



INSERT INTO drh_stateful_master.role_type (role_type_id, code, system, display, definition,rec_status_id)
VALUES
  ('01JK5AQNK0G0JXG8RDJMYN96DC', 'doctor', 'http://terminology.hl7.org/CodeSystem/practitioner-role', 'Doctor', 'A qualified/registered medical practitioner.',1),
  ('01JK5AQXMHMZE51JFN4WJ9Z1FE', 'nurse', 'http://terminology.hl7.org/CodeSystem/practitioner-role', 'Nurse', 'A practitioner with nursing experience that may be qualified/registered.',1),
  ('01JK5AR49HZQFYP0K6F4K89GXK', 'pharmacist', 'http://terminology.hl7.org/CodeSystem/practitioner-role', 'Pharmacist', 'A qualified/registered/licensed pharmacist.',1),
  ('01JK5ARERH8XYDAFX6Y11J5TYQ', 'administrator', 'http://terminology.hl7.org/CodeSystem/practitioner-role', 'Administrator', 'Manages administrative tasks.',1),
  ('01JK5ARR12H9543X43YJ554CFS', 'researcher', 'http://terminology.hl7.org/CodeSystem/practitioner-role', 'Researcher', 'A practitioner that may perform research.',1),
  ('01JK5ARXT98KTC34NH80AJ5046', 'ict', 'http://terminology.hl7.org/CodeSystem/practitioner-role', 'ICT', 'Someone qualified in Information and Communication Technologies.',1),
  ('01JK5AS3C9NXF82TZRGJY2THPY', 'patient', 'http://terminology.hl7.org/CodeSystem/practitioner-role', 'Patient', 'An individual receiving medical care.',1)
ON CONFLICT (role_type_id) DO NOTHING;

INSERT INTO drh_stateful_master.role(role_id, role_type_id, org_party_id, role_name, description, is_system_role,rec_status_id)
VALUES
  ('01JK5B9F63Q9958P8NP8X0GB3B', '01JK5ARERH8XYDAFX6Y11J5TYQ', NULL, 'Admin', 'Full system access', false,1),
  ('01JK5B9NV46S9KHAZEAHD27CE2', '01JK5ARR12H9543X43YJ554CFS', NULL, 'Researcher', 'Department management access', false,1),
  ('01JK5B9VWK4ZPJF00KJ7GTHMR2', '01JK5AS3C9NXF82TZRGJY2THPY', NULL, 'Patient', 'Patient', true,1),
  ('06JNBYPZXYKBXC45CS89AAPM0V', '01JK5ARERH8XYDAFX6Y11J5TYQ', NULL, 'Guest','Non authenticated User',true,1),
  ('07JHBYUZXDKBDJ45CR89AGNU0M', '01JK5ARERH8XYDAFX6Y11J5TYQ', NULL, 'Super Admin','Full system access',true,1),
  ('01HV6PVA43DHKZ9DQ4S2DFJKC6', '01JK5ARERH8XYDAFX6Y11J5TYQ', NULL, 'Developer','Full system access',true,1),
  -- New roles for Vanna AI usage
  ('01VANNAADM1NROLE00000000001', '01JK5ARERH8XYDAFX6Y11J5TYQ', NULL, 'vanna_user_admin', 'Admin role for Vanna AI', true, 1),
  ('01VANNANORMROLE00000000002', '01JK5ARERH8XYDAFX6Y11J5TYQ', NULL, 'vanna_user_normal', 'Regular user role for Vanna AI', true, 1)
ON CONFLICT (role_id) DO NOTHING;

INSERT INTO drh_stateful_master.group_type (group_type_id,code, system, display, definition,rec_status_id)
VALUES
  ('01JK5KNNGMGEX3MBWCZPY41ZMF','person', 'http://hl7.org/fhir/group-type', 'Person', 'Group contains person Patient resources.',1),
  ('01JK5KNY6C47A729VPR986ZKAW','animal', 'http://hl7.org/fhir/group-type', 'Animal', 'Group contains animal Patient resources.',1),
  ('01JK5KPMS4RQFG0RYP5C93687R','practitioner', 'http://hl7.org/fhir/group-type', 'Practitioner', 'Group contains healthcare practitioner resources (Practitioner or PractitionerRole).',1),
  ('01JK5KQHB4QEZWTJXGW1FB9WB3','device', 'http://hl7.org/fhir/group-type', 'Device', 'Group contains Device resources.',1),
  ('01JK5KQWTCP521VH514H3D3ZQE','careteam', 'http://hl7.org/fhir/group-type', 'CareTeam', 'Group contains CareTeam resources.',1),
  ('01JK5KR8VCH9B0FA95QD1Y021D','healthcareservice', 'http://hl7.org/fhir/group-type', 'HealthcareService', 'Group contains HealthcareService resources.',1),
  ('01JK5KRHYC61NCXKHXNEC2H0XB','location', 'http://hl7.org/fhir/group-type', 'Location', 'Group contains Location resources.',1),
  ('01JK5KRYEMZHE08H126EJNMYAP','organization', 'http://hl7.org/fhir/group-type', 'Organization', 'Group contains Organization resources.',1),
  ('01JK5KSQDW6PSZMXV03XVWP7QD','relatedperson', 'http://hl7.org/fhir/group-type', 'RelatedPerson', 'Group contains RelatedPerson resources.',1),
  ('01JK7P1P31341RMZN06128JTA9','specimen', 'http://hl7.org/fhir/group-type', 'Specimen', 'Group contains Specimen resources.',1)
  ON CONFLICT (group_type_id) DO NOTHING;

INSERT INTO drh_stateful_master.consent_category (category_id, code, system, display, definition, rec_status_id, created_at, created_by)
VALUES
  ('01JK5K5NX15SKNHCY12SX20ME1', 'research', 'http://terminology.hl7.org/CodeSystem/consentscope', 'Research', 'Consent to participate in research protocol and information sharing required', 1, now(), NULL),
  ('01JK5K6CMHX2KW1J0D3P4XSS89','patient-privacy', 'http://terminology.hl7.org/CodeSystem/consentscope', 'Privacy Consent', 'Agreement to collect, access, use or disclose (share) information', 1, now(), NULL),
  ('01JK5K6MQHY5S6A7R8EYFWDFGA', 'treatment', 'http://terminology.hl7.org/CodeSystem/consentcategorycodes', 'Treatment', 'Consent to undergo a specific treatment. [VALUE SET: ActConsentType (2.16.840.1.113883.1.11.19897)]', 1, now(), NULL),
  ('01JK5K6YBHQFBZQSNRWD2G4X07', 'rsdid', 'http://terminology.hl7.org/CodeSystem/consentcategorycodes', 'De-identified Information Access', 'Consent to have de-identified healthcare information in an electronic health record that is accessed for research purposes, but without consent to re-identify the information under any circumstance. [VALUE SET: ActConsentType (2.16.840.1.113883.1.11.19897)]', 1, now(), NULL),
  ('01JK5K7CCSVXEE6SZ5QJ8CQE8J', 'rsreid', 'http://terminology.hl7.org/CodeSystem/consentcategorycodes', 'Re-identifiable Information Access', 'Consent to have de-identified healthcare information in an electronic health record that is accessed for research purposes re-identified under specific circumstances outlined in the consent. [VALUE SET: ActConsentType (2.16.840.1.113883.1.11.19897)]', 1, now(), NULL),
  ('01JK5K7TM9T6YJBK6WEXRNP697', 'INFASO', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'Access and Save Only', 'Consent to access and save only, which entails that access to the saved copy will remain locked.', 1, now(), NULL),
  ('01JK5K83MS4X98BRV515H1ZHXV', 'IRDSCL', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'Information Redisclosure', 'Information re-disclosed without the patient’s consent.', 1, now(), NULL),
  ('01JK5K8DVS78X549G8952Y99PV', 'RESEARCH', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'Research Information Access', 'Consent to have healthcare information in an electronic health record accessed for research purposes.', 1, now(), NULL),
  ('01JK5K8Q9A9ET43XXG8Q17PZ57', 'RSDID', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'De-identified Information Access', 'Consent to have de-identified healthcare information in an electronic health record that is accessed for research purposes, but without consent to re-identify the information under any circumstance.', 1, now(), NULL),
  ('01JK5K91XJ26K7MMM7K6FKVP75', 'RSREID', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'Re-identifiable Information Access', 'Consent to have de-identified healthcare information in an electronic health record that is accessed for research purposes re-identified under specific circumstances outlined in the consent. Example: Where there is a need to inform the subject of potential health issues.', 1, now(), NULL),
  ('01JK5K9B510GGVAEHE01WDYFV4', '59284-0', 'http://loinc.org', 'Patient Consent', NULL, 1, now(), NULL),
  ('01JK5K9KVJHQQ952Y7J2KVX3ER', '57017-6', 'http://loinc.org', 'Privacy Policy Organization Document', NULL, 1, now(), NULL),
  ('01JK5KA1W1GQPTZEER4FMW7DXS', '64292-6', 'http://loinc.org', 'Release of Information Consent', NULL, 1, now(), NULL)
ON CONFLICT (category_id) DO NOTHING;

INSERT INTO drh_stateful_master.consent_status_code (status_code_id, code, system, display, definition, rec_status_id, created_at, created_by)
VALUES 
  ('01JK5M086EHGJAGCZJFT903HK7', 'draft', 'http://hl7.org/fhir/consent-state-codes', 'Pending', 'The consent is in development or awaiting use but is not yet intended to be acted upon.', 1, NOW(), NULL),
  ('01JK5M0QCPSVACZM8P24RHPYYM', 'active', 'http://hl7.org/fhir/consent-state-codes', 'Active', 'The consent is to be followed and enforced.', 1, NOW(), NULL),
  ('01JK5M0WVEAYFPDGJT9TN0RF2R', 'inactive', 'http://hl7.org/fhir/consent-state-codes', 'Inactive', 'The consent is terminated or replaced.', 1, NOW(), NULL),
  ('01JK5M2616SE2B0QA96X4WV0TB', 'not-done', 'http://hl7.org/fhir/consent-state-codes', 'Abandoned', 'The consent development has been terminated prior to completion.', 1, NOW(), 'system'),
  ('01JK5M2M6N3VVHXBHSG8S2Z4BD', 'entered-in-error', 'http://hl7.org/fhir/consent-state-codes', 'Entered in Error', 'The consent was created wrongly (e.g. wrong patient) and should be ignored.', 1, NOW(), 'system'),
  ('01JK5M2WPZSRR7TBG7CD053JXN', 'unknown', 'http://hl7.org/fhir/consent-state-codes', 'Unknown', 'The resource is in an indeterminate state.', 1, NOW(), 'system')
  ON CONFLICT (status_code_id) DO NOTHING;

INSERT INTO drh_stateful_master.consent_decision_type (decision_type_id,code, system, display, definition, rec_status_id, created_at, created_by)
VALUES 
    ('01JK5MV0KVBQN9KRNC54ASECMP','deny', 'http://hl7.org/fhir/consent-provision-type', 'Deny', 'Consent is denied for actions meeting these rules.', 1, NOW(),NULL),
    ('01JK5MV9XBMQ8Z8JYH172AR3MY','permit', 'http://hl7.org/fhir/consent-provision-type', 'Permit', 'Consent is provided for actions meeting these rules.', 1, NOW(), NULL)
ON CONFLICT (decision_type_id) DO NOTHING;


INSERT INTO drh_stateful_master.contract_type (contract_type_id, code, system, display, definition, rec_status_id, created_at, created_by, updated_at, updated_by)
VALUES
  ('01JK5VSC90XSZRXY5PN6V2M3F0', 'privacy', 'http://terminology.hl7.org/CodeSystem/contract-type', 'Privacy', 'Privacy policy.', 1, NOW(),NULL, NOW(), NULL),
  ('01JK5VSMJZSS6F5XZQ4P60YY2F', 'disclosure', 'http://terminology.hl7.org/CodeSystem/contract-type', 'Disclosure', 'Information disclosure policy.', 1, NOW(),NULL, NOW(), NULL),
  ('01JK5VSWJZ18QY5AA6SX9TJJV2', 'healthinsurance', 'http://terminology.hl7.org/CodeSystem/contract-type', 'Health Insurance', 'Health Insurance policy.', 1, NOW(),NULL, NOW(), NULL),
  ('01JK5VT4CQ934SYHTRH0Z0QASE', 'supply', 'http://terminology.hl7.org/CodeSystem/contract-type', 'Supply Contract', 'Contract to supply goods or services.', 1, NOW(),NULL, NOW(), NULL),
  ('01JK5VTCJQHG35123X4NY1Q2Q1', 'consent', 'http://terminology.hl7.org/CodeSystem/contract-type', 'Consent', 'Consent Directive.', 1, NOW(),NULL, NOW(), NULL)
ON CONFLICT (contract_type_id) DO NOTHING;


INSERT INTO drh_stateful_master.contract_sub_type (contract_sub_type_id, code, system, display, definition, rec_status_id, created_at, created_by, updated_at, updated_by)
VALUES
  ('01JK5W6V6SGJZEGBT717C56DF7', 'disclosure-ca', 'http://terminology.hl7.org/CodeSystem/contractsubtypecodes', 'Disclosure-CA', 'Canadian health information disclosure policy.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5W74ZSDVKXKSCCS2VGX7HP', 'disclosure-us', 'http://terminology.hl7.org/CodeSystem/contractsubtypecodes', 'Disclosure-US', 'United States health information disclosure policy.', 1, NOW(), NULL, NOW(), NULL)
ON CONFLICT (contract_sub_type_id) DO NOTHING;

INSERT INTO drh_stateful_master.contract_signer_type (signer_type_id, code, system, display, definition, rec_status_id, created_at, created_by, updated_at, updated_by)
VALUES
  ('01JK5WFPK2H0470PCE6TJ8XSFC', 'PRIMAUTH', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Primary Author', 'An entity that is the primary or sole author of information content. In the healthcare context, there can be only one primary author of health information content in a record entry or document.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WFYN267FNE790967ZVHST', 'RECIP', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Recipient', 'An entity that may, should receive, or has received information or an object, which might not have been primarily addressed to it. For example, the staff of a provider, a clearinghouse, or other intermediary.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WG7KJWDJHD343CT1RQVYG', 'WIT', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Witness', 'A person witnessing the signature of another party. A witness is not knowledgeable about the content being signed, much less approves of anything stated in the content. For example, an advanced directive witness or a witness that a party to a contract signed that certain demographic or financial information is truthful.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WGH825EE38PS96R9CCBR0', 'POWATT', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Power of Attorney', 'A person who has been granted the authority to represent or act on another’s behalf generally in a manner which is a legally binding upon the person giving such authority as if he or she personally were to do the acts. Examples include (1) exercising specific legal rights belonging to the grantee such as signing a contract; (2) performing specific legal duties on behalf of a grantee such as making loan payments; and (3) making specific legal decisions concerning a grantee such as financial investment decisions.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WGTCACZ0YXVYWGNBVM67K', 'HPROV', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Healthcare Provider', 'An entity that is authorized to provide health care services by an authorizing organization or jurisdiction.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WH33V6DEZB2NHBCFQSW1H', 'INSBJ', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Investigation Subject', 'An entity that is the subject of an investigation. This role is scoped by the party responsible for the investigation.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WHBD2ZX4B2H8W9TKWJEJR', 'GRANTEE', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Grantee', 'A person who grants to another person the authority to represent or act on that person’s behalf. Examples include (1) exercising specific rights belonging to the grantee; (2) performing specific duties on behalf of a grantee; and (3) making specific decisions concerning a grantee.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WHM022XQ0XFT3K5Z8GHVM', 'GRANTOR', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Grantor', 'A person who has been granted the authority to represent or act on another’s behalf. Examples include (1) exercising specific rights belonging to the grantee; (2) performing specific duties on behalf of a grantee; and (3) making specific decisions concerning a grantee.', 1, NOW(), NULL, NOW(), NULL),
  ('01JK5WHX8AMGA28S25GQFC7QKN', 'GUAR', 'http://terminology.hl7.org/CodeSystem/contractsignertypecodes', 'Guarantor', 'A person or organization contractually recognized by the issuer as an entity that has assumed fiscal responsibility (e.g., by making or giving a promise, assurance, or pledge) for another entity’s financial obligations by guaranteeing to pay for amounts owed to a particular account. In a healthcare context, the account may be a patient’s billing account for services rendered by a provider or a health plan premium account.', 1, NOW(), NULL, NOW(), NULL)
  ON CONFLICT (signer_type_id) DO NOTHING;


INSERT INTO drh_stateful_master.legal_state_code (state_code_id,code, system, display, definition,rec_status_id)
VALUES
  ('01JK5X1P2X6DGEDDHM3GXSEBDD','amended', 'http://hl7.org/fhir/contract-legalstate', 'Amended', 'Contract is augmented with additional information to correct errors in a predecessor or to updated values in a predecessor. Usage: Contract altered within effective time. Precedence Order = 9. Comparable FHIR and v.3 status codes: revised; replaced.',1),
  ('01JK5X1Z40DMR2NWPYHZBRPH1J','appended', 'http://hl7.org/fhir/contract-legalstate', 'Appended', 'Contract is augmented with additional information that was missing from a predecessor Contract. Usage: Contract altered within effective time. Precedence Order = 9. Comparable FHIR and v.3 status codes: updated, replaced.',1),
  ('01JK5X280NE6AY6HPZ81VSJJPY','cancelled', 'http://hl7.org/fhir/contract-legalstate', 'Cancelled', 'Contract is terminated due to failure of the Grantor and/or the Grantee to fulfil one or more contract provisions. Usage: Abnormal contract termination. Precedence Order = 10. Comparable FHIR and v.3 status codes: stopped; failed; aborted.',1),
  ('01JK5X2HF5TF6290ZJ2J4W9CXR','disputed', 'http://hl7.org/fhir/contract-legalstate', 'Disputed', 'Contract is pended to rectify failure of the Grantor or the Grantee to fulfil contract provision(s). E.g., Grantee complaint about Grantor’s failure to comply with contract provisions. Usage: Contract pended. Precedence Order = 7. Comparable FHIR and v.3 status codes: on hold; pended; suspended.',1),
  ('01JK5X2TPY8RDXA5YH1A66G7RC','entered-in-error', 'http://hl7.org/fhir/contract-legalstate', 'Entered in Error', 'Contract was created in error. No Precedence Order. Status may be applied to a Contract with any status.',1),
  ('01JK5X32XXM0THN5SJG1GAEGKA','executable', 'http://hl7.org/fhir/contract-legalstate', 'Executable', 'Contract execution pending; may be executed when either the Grantor or the Grantee accepts the contract provisions by signing. I.e., where either the Grantor or the Grantee has signed, but not both. E.g., when an insurance applicant signs the insurers application, which references the policy. Usage: Optional first step of contract execution activity. May be skipped and contracting activity moves directly to executed state. Precedence Order = 3. Comparable FHIR and v.3 status codes: draft; preliminary; planned; intended; active.',1),
  ('01JK5X3B352PW0YTEESECC3P87','executed', 'http://hl7.org/fhir/contract-legalstate', 'Executed', 'Contract is activated for period stipulated when both the Grantor and Grantee have signed it. Usage: Required state for normal completion of contracting activity. Precedence Order = 6. Comparable FHIR and v.3 status codes: accepted; completed.',1),
  ('01JK5X3K6DFZ8M6G4X6M15E86D','negotiable', 'http://hl7.org/fhir/contract-legalstate', 'Negotiable', 'Contract execution is suspended while either or both the Grantor and Grantee propose and consider new or revised contract provisions. I.e., where the party which has not signed proposes changes to the terms. E.g., a life insurer declines to agree to the signed application because the life insurer has evidence that the applicant, who asserted to being younger or a non-smoker to get a lower premium rate - but offers instead to agree to a higher premium based on the applicants actual age or smoking status. Usage: Optional contract activity between executable and executed state. Precedence Order = 4. Comparable FHIR and v.3 status codes: in progress; review; held.',1),
  ('01JK5X3W1DFBY4N1VFWZ6XWWTF','offered', 'http://hl7.org/fhir/contract-legalstate', 'Offered', 'Contract is a proposal by either the Grantor or the Grantee. Aka - A Contract hard copy or electronic ‘template’, ‘form’ or ‘application’. E.g., health insurance application; consent directive form. Usage: Beginning of contract negotiation, which may have been completed as a precondition because used for 0..* contracts. Precedence Order = 2. Comparable FHIR and v.3 status codes: requested; new.',1),
  ('01JK5X446E61Z4KFEJ7B0KFJVX','policy', 'http://hl7.org/fhir/contract-legalstate', 'Policy', 'Contract template is available as the basis for an application or offer by the Grantor or Grantee. E.g., health insurance policy; consent directive policy. Usage: Required initial contract activity, which may have been completed as a precondition because used for 0..* contracts. Precedence Order = 1. Comparable FHIR and v.3 status codes: proposed; intended.',1),
  ('01JK5X4D2NN3116F8JS2FGPPXK','rejected', 'http://hl7.org/fhir/contract-legalstate', 'Rejected', 'Execution of the Contract is not completed because either or both the Grantor and Grantee decline to accept some or all of the contract provisions. Usage: Optional contract activity between executable and abnormal termination. Precedence Order = 5. Comparable FHIR and v.3 status codes: stopped; cancelled.',1),
  ('01JK5X4MW56M1HBXT7WA7JKFNJ','renewed', 'http://hl7.org/fhir/contract-legalstate', 'Renewed', 'Beginning of a successor Contract at the termination of predecessor Contract lifecycle. Usage: Follows termination of a preceding Contract that has reached its expiry date. Precedence Order = 13. Comparable FHIR and v.3 status codes: superseded.',1),
  ('01JK5X4WZDXH2845JY8FJE4S12','revoked', 'http://hl7.org/fhir/contract-legalstate', 'Revoked', 'A Contract that is rescinded. May be required prior to replacing with an updated Contract. Comparable FHIR and v.3 status codes: nullified.',1),
  ('01JK5X54M608SJ60PP4162PKDY','resolved', 'http://hl7.org/fhir/contract-legalstate', 'Resolved', 'Contract is reactivated after being pended because of faulty execution. *E.g., competency of the signer(s), or where the policy is substantially different from and did not accompany the application/form so that the applicant could not compare them. Aka - ”reactivated”. Usage: Optional stage where a pended contract is reactivated. Precedence Order = 8. Comparable FHIR and v.3 status codes: reactivated.',1),
  ('01JK5X5DD5G45ZHH5RG43RF09A','terminated', 'http://hl7.org/fhir/contract-legalstate', 'Terminated', 'Contract reaches its expiry date. It might or might not be renewed or renegotiated. Usage: Normal end of contract period. Precedence Order = 12. Comparable FHIR and v.3 status codes: Obsoleted.',1)
   ON CONFLICT (state_code_id) DO NOTHING;



INSERT INTO drh_stateful_master.contract_expiration_type (expiration_type_id,code, system, display, definition,rec_status_id)
VALUES
  ('01JK5Y4P63DWWGMR5D00JGR7QE','breach', 'http://hl7.org/fhir/contract-expiration-type', 'Breach', 'To be completed',1),
  ('01JK5Y4YZB0VEFK1SECDT54MF6','fulfilled', 'http://hl7.org/fhir/contract-expiration-type', 'Fulfilled', 'The contract has ended because all obligations were completed.',1),
  ('01JK5Y572V7Y88R5P8HS91HEPN','cancelled', 'http://hl7.org/fhir/contract-expiration-type', 'Cancelled', 'The contract was terminated by one or more parties before fulfillment.',1),
  ('01JK5Y5FKVPJ0EA3CYFZ6Y8MK7','mutual', 'http://hl7.org/fhir/contract-expiration-type', 'Mutual Agreement', 'The contract ended due to mutual agreement between the parties.',1),
  ('01JK5Y5RPBBM385H2QF5P3HSF7','breach', 'http://hl7.org/fhir/contract-expiration-type', 'Breach of Terms', 'The contract was terminated due to a breach by one or more parties.',1),
  ('01JK5Y60NWPYWRSDE6VHV2ZV2V','expired', 'http://hl7.org/fhir/contract-expiration-type', 'Expired', 'The contract expired naturally after reaching its end date.',1),
  ('01JK5Y68EVSCDX1EM3HVDBE0XH','superseded', 'http://hl7.org/fhir/contract-expiration-type', 'Superseded', 'The contract was replaced by another agreement.',1),
  ('01JK5Y6GKM3CPW309VR21KK9AJ','revoked', 'http://hl7.org/fhir/contract-expiration-type', 'Revoked', 'The contract was revoked due to external factors or regulatory requirements.',1),
  ('01JK5Y6RFBP61QX351B9R8QB5B','terminated', 'http://hl7.org/fhir/contract-expiration-type', 'Terminated', 'The contract was actively ended by one or more parties, not fulfilling terms.',1)
 ON CONFLICT (expiration_type_id) DO NOTHING;


INSERT INTO drh_stateful_master.contract_status (contract_status_type_id, code, system, display, definition, rec_status_id, created_at)
VALUES 
  ('01JK7S5F1H2XRW4GZD499FD5PC', 'amended', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Amended', 'Contract is augmented with additional information to correct errors in a predecessor or to update values in a predecessor. Usage: Contract altered within effective time. Precedence Order = 9. Comparable FHIR and v.3 status codes: revised; replaced.', 1, NOW()),
  ('01JK7S8T4DY75909B63SNSCFS5', 'appended', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Appended', 'Contract is augmented with additional information that was missing from a predecessor Contract. Usage: Contract altered within effective time. Precedence Order = 9. Comparable FHIR and v.3 status codes: updated, replaced.', 1, NOW()),
  ('01JK7S94PX4TNK3EP8BHNF4A6F', 'cancelled', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Cancelled', 'Contract is terminated due to failure of the Grantor and/or the Grantee to fulfil one or more contract provisions. Usage: Abnormal contract termination. Precedence Order = 10. Comparable FHIR and v.3 status codes: stopped; failed; aborted.', 1, NOW()),
  ('01JK7S9DTX72Q732AD4WN2J68Y', 'disputed', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Disputed', 'Contract is pended to rectify failure of the Grantor or the Grantee to fulfil contract provision(s). E.g., Grantee complaint about Grantor''s failure to comply with contract provisions. Usage: Contract pended. Precedence Order = 7. Comparable FHIR and v.3 status codes: on hold; pended; suspended.', 1, NOW()),
  ('01JK7S9NZNAJNTYWCBHHVAXB36', 'entered-in-error', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Entered in Error', 'Contract was created in error. No Precedence Order. Status may be applied to a Contract with any status.', 1, NOW()),
  ('01JK7S9YMN3CN0MEXCZVQJ46A8', 'executable', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Executable', 'Contract execution pending; may be executed when either the Grantor or the Grantee accepts the contract provisions by signing. I.e., where either the Grantor or the Grantee has signed, but not both. E.g., when an insurance applicant signs the insurer''s application, which references the policy. Usage: Optional first step of contract execution activity. May be skipped and contracting activity moves directly to executed state. Precedence Order = 3. Comparable FHIR and v.3 status codes: draft; preliminary; planned; intended; active.', 1, NOW()),
  ('01JK7SA6VXH03EMXKH3RJKZXDB', 'executed', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Executed', 'Contract is activated for period stipulated when both the Grantor and Grantee have signed it. Usage: Required state for normal completion of contracting activity. Precedence Order = 6. Comparable FHIR and v.3 status codes: accepted; completed.', 1, NOW()),
  ('01JK7SAF1E5FPYPC0KWZWRB7N5', 'negotiable', 'https://hl7.org/fhir/codesystem-contract-status.xml', 'Negotiable', 'Contract execution is suspended while either or both the Grantor and Grantee propose and consider new or revised contract provisions. I.e., where the party which has not signed proposes changes to the terms. E.g., a life insurer declines to agree to the signed application because the life insurer has evidence that the applicant, who asserted to being younger or a non-smoker to get a lower premium rate - but offers instead to agree to a higher premium based on the applicant''s actual age or smoking status. Usage: Optional contract activity between executable and executed state. Precedence Order = 4. Comparable FHIR and v.3 status codes: in progress; review; held.', 1, NOW())
  ON CONFLICT (contract_status_type_id) DO NOTHING;



INSERT INTO drh_stateful_master.decision_mode (decision_mode_id,code, system, display, definition,rec_status_id)
VALUES
  ('01JK7TFFZP3QHDFGD5808J3P02','policy', 'http://hl7.org/fhir/contract-decision-mode', 'Policy', 'To be completed',1)
  ON CONFLICT (decision_mode_id) DO NOTHING;


INSERT INTO drh_stateful_master.decision_type (decision_type_id, code, system, display, definition, rec_status_id
) 
VALUES ('01JK8DFV7EWN1H6WDBSFN50Y83', 'EMRGONLY', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'Emergency only', 'Privacy consent directive restricting or prohibiting access, use, or disclosure of personal information, including de-identified information, and personal effects,', 1),
('01JK8DG1W6G7THYMPZWF4RKWHD', 'GRANTORCHOICE	', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'grantor choice', 'A grantors terms of agreement to which a grantee may assent or dissent, and which may include an opportunity for a grantee to request restrictions or extensions.', 1),
('01JK8DJYK71ABAQJ8C22XARFS5', 'IMPLIED', 'http://terminology.hl7.org/CodeSystem/v3-ActCode', 'implied consent', 'A grantors presumed assent to the grantees terms of agreement is based on the grantors behavior, which may result from not expressly assenting to the consent directive offered.', 1)   ON CONFLICT (decision_type_id) DO NOTHING;


-- Idempotent seeding of migration statuses
INSERT INTO drh_stateful_master.migration_status (stage_id, stage_name, stage_description,created_at, updated_at)
VALUES
    (1,'PENDING', 'The migration is yet to start.',CURRENT_TIMESTAMP,NULL),
    (2,'IN_PROGRESS', 'The migration is currently in progress.',CURRENT_TIMESTAMP,NULL),
    (3,'ERROR', 'An error occurred during migration.',CURRENT_TIMESTAMP,NULL),
    (4,'COMPLETED', 'The migration process has been completed successfully.',CURRENT_TIMESTAMP,NULL),
    (5,'ROLLBACK', 'The migration process has been rolled back.',CURRENT_TIMESTAMP,NULL)
ON CONFLICT (stage_name) DO NOTHING; -- Ensures no duplicate entries for stage_name


-- Idempotent seeding of migration statuses
INSERT INTO drh_stateful_master.interaction_status (id, title)
VALUES
    (1,'IN PROGRESS'),
    (2,'SUCCESS'),
    (3,'SKIP PROCESSING'),
    (4,'FAILED'),
    (5,'ROLLBACK'),
    (6,'PARTIAL SUCCESS'),
    (7,'ROLLBACK FAILURE')
ON CONFLICT (title) DO NOTHING; -- Ensures no duplicate entries for title

-- Idempotent seeding of migration statuses
INSERT INTO drh_stateful_master.interaction_action_type (id, title)
VALUES
    (1,'DB FILE UPLOAD'),
    (2,'S3 BUCKET UPLOAD'),
    (3,'CONTENTS VERIFICATION'),
    (4,'DUCKDB COPY'),
    (5,'SAVE DB'),
    (6,'STUDY METADATA MIGRATION'),
    (7,'PARTICIPANT MIGRATION'),
    (8,'CGM MIGRATION'),
    (9,'MEAL AND FITNESS MIGRATION'),
    (10,'SUBMIT FOR CONTENT VERIFICATION'),
    (11,'DATABASE MIGRATION'),
    (12,'UPLOAD CGM FILE'),
    (13,'VALIDATE CGM FILE'),
    (14,'COPY FILE FOR PROCESSING'),
    (15,'SAVE CGM FILE DATA'),
    (16,'UPLOAD PARTICIPANT FILE'),
    (17,'VALIDATE PARTICIPANT FILE'),
    (18,'COPY PARTICIPANT FILE FOR PROCESSING'),
    (19,'SAVE PARTICIPANT DATA'),
    (20, 'PARTICIPANT ROLLBACK'),
    (21, 'CGM ROLLBACK'),
    (22, 'STUDY METADATA ROLLBACK'),
    (23,'PREPARE AND VALIDATE METADATA'),
    (24,'VALIDATE CGM DATE AND VALUE'),
    (25,'VALIDATE PARTICIPANT FILE TYPE'),
    (26,'PARTICIPANT CONTENT VALIDATION'),
    (27,'CREATE STUDY'),
    (28,'VIEW STUDY'),
    (29,'UPDATE STUDY'),
    (30,'DELETE STUDY'),
    (31,'SAVE COLLABORATION TEAM'),
    (32,'MEAL MIGRATION'),
    (33, 'MEAL ROLLBACK'),
    (34,'FITNESS MIGRATION'),
    (35, 'FITNESS ROLLBACK'),    
    (36, 'UPDATE COLLABORATION TEAM'),    
    (37, 'CREATE PARTICIPANT'),    
    (38, 'UPDATE PARTICIPANT'),    
    (39, 'DELETE PARTICIPANT'),    
    (40, 'VIEW PARTICIPANT'),
    (41, 'CGM PARTITION MIGRATION'),
    (42, 'SAVE MEALS DATA'),
    (43, 'SAVE FITNESS DATA'),
    (44, 'UPLOAD MEALS FILE'),
    (45, 'VALIDATE MEALS FILE CONTENT'),
    (46, 'VALIDATE MEALS FILE TYPE'),
    (47, 'PARTICIPANT MEALS VALIDATION'),
    (48, 'UPLOAD FITNESS FILE'),
    (49, 'VALIDATE FITNESS FILE CONTENT'),
    (50, 'VALIDATE FITNESS FILE TYPE'),
    (51, 'PARTICIPANT FITNESS VALIDATION'),
    (52, 'MEAL PARTITION MIGRATION'),    
    (53, 'FITNESS PARTITION MIGRATION'),
    (54, 'DB CLEANUP'),
    (55, 'ROLLBACK CGM FILE DATA'),
    (56, 'ROLLBACK MEAL FILE DATA'),
    (57, 'ROLLBACK FITNESS FILE DATA'),
    (58, 'ROLLBACK PARTICIPANT FILE DATA')
ON CONFLICT (title) DO NOTHING; -- Ensures no duplicate entries for title

-- Insert new records with ULIDs
INSERT INTO drh_stateful_master.permission (permission_id, code, permission_name, description, resource_type, action_type, rec_status_id, parent_id) VALUES
  ('01HV8B9R8RC35ZP2FD4AYNG8D3', 'HOME', 'Home', NULL, 'HOME', 'VIEW', 1, '01HV8B9R9B8VDM3HZM8M3TYE8F'),
('01HV8B9R9B8VDM3HZM8M3TYE8F', 'STUDIES', 'Studies', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV8B9RA53WE7NSRY1AFQHPHP', 'DASHBOARD', 'Dashboard', NULL, 'STUDIES', 'VIEW', 1, '01HV8B9R9B8VDM3HZM8M3TYE8F'),
('01HV8B9RB7JYZNVFSK7YTWGYNV', 'POPULATION_PERCENTAGE', 'Population Percentage', NULL, 'STUDIES', 'VIEW', 1, '01HV8B9R9B8VDM3HZM8M3TYE8F'),
('01HV8B9RC66Z4K0ZQH4EZH22Q3', 'ALL_STUDIES', 'All Studies', NULL, 'STUDIES', 'VIEW', 1, '01HV8B9R9B8VDM3HZM8M3TYE8F'),
('01HV8B9RDATY08E3CTQNSHRS9R', 'MY_STUDIES', 'My Studies', NULL, 'STUDIES', 'VIEW/EDIT', 1, '01HV8B9R9B8VDM3HZM8M3TYE8F'),
('01HV8B9REW2PBFFMTGJ7RQHZFE', 'CREATE_NEW_STUDY', 'Create New Study', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV8B9RGQZHDYZPKW8ZPWS1GZ', 'VIEW_STUDY', 'View Study', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV8B9RH3B7M5VZN7PE0HJ7HH', 'UPLOAD_STUDY_DB', 'Upload Study Db', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV8B9RKCS7GHK7Y4THPS3YJX', 'ADD_PARTICIPANT', 'Add Participant', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV8B9RMGZJ85PQGEGB2M0TFB', 'EDIT_PARTICIPANT', 'Edit Participant', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV8B9RP8ZXBR99GAB4Y79R99', 'VIEW_CGM_DATA', 'CGM Data', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV8B9RQ2H0NWVS8VMQQFRD12', 'UPLOAD_CGM_DATA', 'Upload Cgm Data', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV8B9RSNP50MXCSZ6PCF0NNA', 'VIEW_METRICS', 'View Metrics', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV8B9RTXP1WPDNBX2SYW4V5J', 'EDIT_STUDY_INLINE', 'Edit Study Inline', NULL, 'STUDIES', 'EDIT', 1, NULL),
('01HV8B9RW3GJ8PMKHY45EDZ3X3', 'ARCHIVE_UNARCHIVE_STUDY', 'Archive Unarchive Study', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV8B9RZ50N8Q7VYKJKC7D48M', 'DELETE_STUDY', 'Delete Study', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV8B9S1PM3TAM9XYMJDV6PWZ', 'ASK_DRH_DATA', 'Ask DRH Data', NULL, 'ASK DRH', 'VIEW', 1, '01HV8B9S2GB9MYDWH8D6QK5MV5'),
('01HV8B9S2GB9MYDWH8D6QK5MV5', 'ASK_DRH', 'Ask DRH', NULL, 'ASK DRH', 'VIEW', 1, NULL),
('01HV8B9S3RTT9M04TANR5TZFB0', 'ASK_DRH_RESEARCH_JOURNAL', 'Ask DRH Research Journal', NULL, 'ASK DRH', 'VIEW', 1, '01HV8B9S2GB9MYDWH8D6QK5MV5'),
('01HV8B9S5Y9R6RTK2RTE4X9QWH', 'ASK_DRH_ICODE', 'Ask DRH iCODE', NULL, 'ASK DRH', 'VIEW', 1, '01HV8B9S2GB9MYDWH8D6QK5MV5'),
('01HV8B9S6TP90JNRKAGKAFEG8B', 'STUDY_INTERACTION', 'Study Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, NULL),
('01HV8B9S8DMZZPXZMR5D3KKNAG', 'FILE_INTERACTION', 'File Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, NULL),
('01HV8B9S9TQ7XSKGBZK9MZDDGW', 'PARTICIPANT_INTERACTION', 'Participant Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, NULL),
('01HV8B9SBYPDVNZQTXJ17J30K7', 'ACTIVITY_LOG', 'Activity Log', NULL, 'INTERACTIONS', 'VIEW', 1, NULL),
('01HV8B9SCZG9S3S3NAX7B2KZKH', 'HEALTH_INFORMATION', 'Health Information', NULL, 'CONSOLE', 'VIEW', 1, NULL),
('01HV8B9SDRHTGZX0Y9K93R68Y2', 'PROJECT', 'Project', NULL, 'CONSOLE', 'VIEW', 1, NULL),
('01HV8B9SEPMKZRZKFS0CY6P67X', 'SCHEMA', 'Schema', NULL, 'CONSOLE', 'VIEW', 1, NULL),
('01HV8B9SFTXJVN6DC9G3VTE3GR', 'DRH_OPENAPI_UI', 'DRH OpenAPI UI', NULL, 'DOCUMENTATION', 'VIEW', 1, NULL),
('01HV8B9SGQ3EYWTAM2TV1JXN8K', 'ANNOUNCEMENTS', 'Announcements', NULL, 'DOCUMENTATION', 'VIEW', 1, NULL),
('01HV8B9SHSV7W9PDXBTM1EN3MC', 'CHANGE_USER_ROLES', 'Change User Roles', NULL, 'ADMINISTRATION', 'VIEW/EDIT', 1, NULL),
('01HV8B9SK4QZ2EVR10NGCJ4KHP', 'TRACK_STUDY_DB_EXTRACTION', 'Track Study Db Extraction', NULL, 'ADMINISTRATION', 'VIEW/EDIT', 1, NULL),
('01HV8B9SNF42BD6B91Z0XD0G1N', 'SEARCH_USERS', 'Search Users', NULL, 'ADMINISTRATION', 'VIEW', 1, NULL),
('01HV8B9SP13T97X2RKNB9BBY54', 'ADD_NEW_USER', 'Add New User', NULL, 'ADMINISTRATION', 'VIEW/EDIT', 1, NULL),
('01HV8B9SRRZ3RQXRFTAH46ZBVM', 'TRAIN_ENGLISH_TO_SQL', 'Train English To Sql', NULL, 'SITES', 'VIEW', 1, NULL),
('01HV8B9SSP8AZW1ZXM2A1P0B3B', 'RESEARCH_AI', 'Research Ai', NULL, 'SITES', 'VIEW', 1, NULL),
('01HTXKYNDXXD6AZKYD1CFN3Y8H', 'DRH_HOME', 'DRH Home', NULL, 'SITES', 'VIEW', 1, NULL),
('01HTXKYPF5VBDNVZHTSC8VHZFG', 'DTS_HOME', 'DTS Home', NULL, 'SITES', 'VIEW', 1, NULL),
('01HV6NVYNYJVD31NYNGZQ7X6XW', 'VIEW_PUBLICATIONS', 'View Publications', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV6NVYQ18B37F9EJNEHEGXPM', 'EDIT_PUBLICATIONS', 'Edit Publications', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV6NVYRH8AXEKPZPKQYWZ24F', 'VIEW_PARTICIPANT', 'View Participant', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV6NVYS4T8NSR39Z0G8Z6TPD', 'VIEW_COLLABORATION_AND_TEAMS', 'View Collaboration and Teams', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV6NVYTJ3CJJN1T9KMAQ7ZMV', 'COLLABORATION_AND_TEAMS', 'Collaboration and Teams', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV6P1DCVPKF1M66E3J1B8K3N', 'STUDY_SETTINGS', 'Study Settings', NULL, 'STUDIES', 'EDIT', 1, NULL),
('01HV6P6J4NNQWRB63PM03EFJ3H', 'EDIT_VISIBILITY', 'Edit Visibility', NULL, 'STUDIES', 'EDIT', 1, NULL),
('01HV6PF92RDGMD1K68Y04W6FJK', 'PUBLICATION_SETTINGS', 'Publication Settings', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV6PF94YP1G7MHR3TZS8F0D4', 'MEALS_DATA', 'Meals Data', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV6PF96B3VN5R40YJ8R56D80', 'FITNESS_DATA', 'Fitness Data', NULL, 'STUDIES', 'VIEW', 1, NULL),
('01HV6PKMZZ0M0F3T9YJNBAY47M', 'EDIT_MEALS_DATA', 'Edit Meals Data', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV6PKN0V23Y12YAXFHRV4Z3H', 'EDIT_FITNESS_DATA', 'Edit Fitness Data', NULL, 'STUDIES', 'VIEW/EDIT', 1, NULL),
('01HV6PKN0V23Y12YAXFHRV4Z5H', 'INTERACTIONS', 'Interactions', NULL, 'INTERACTIONS', 'VIEW/EDIT', 1, NULL),
('01HV6PKN0V23Y12YAXFHRV4Z6H', 'CONSOLE', 'Console', NULL, 'CONSOLE', 'VIEW/EDIT', 1, NULL),
('01HV6PKN0V23Y12YAXFHRV4Z7H', 'DOCUMENTATION', 'Documentation', NULL, 'DOCUMENTATION', 'VIEW/EDIT', 1, NULL),
('01HV6PKN0V23Y12YAXFHRV4Z8H', 'SITES', 'Sites', NULL, 'SITES', 'VIEW/EDIT', 1, NULL),
('01HV8B9S2GB9MYDWH8D6QK4MV5', 'ASK_DRH_SUB', 'Ask DRH', NULL, 'ASK DRH', 'VIEW', 1, NULL),
('01HV8B9S2GB9MYDWH8D6QK5MV6', 'ADMINISTRATION', 'Administration', NULL, 'ADMINISTRATION', 'VIEW', 1, NULL),
('01HV8B9SBYPDVNZQTXJ17J30K8', 'USER_SESSION', 'User Session', NULL, 'INTERACTIONS', 'VIEW', 1, NULL),
('01HV8B9SBYPDVNZQTXJ17J30L8', 'ORGANIZATION_SESSION', 'Organization Session', NULL, 'INTERACTIONS', 'VIEW', 1, NULL),
('01HV8B9SBYPDVNZQTXJ17J30M8', 'ADMINISTRATION_SESSION', 'Administration Session', NULL, 'INTERACTIONS', 'VIEW', 1, NULL),
('01HV8B9SBYPDVNZQTXJ17J31M8', 'SUCCESSFUL_STUDY_INTERACTION', 'Successful Study Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S6TP90JNRKAGKAFEG8B'),
('01HV8B9SBYPDVNZQTXJ17J32M8', 'FAILED_STUDY_INTERACTION', 'Failed Study Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S6TP90JNRKAGKAFEG8B'),
('01HV8B9SBYPDVNZQTXJ17J33M8', 'SUCCESSFUL_PARTICIPANT_INTERACTION', 'Successful Participant Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S9TQ7XSKGBZK9MZDDGW'),
('01HV8B9SBYPDVNZQTXJ17J34M8', 'FAILED_PARTICIPANT_INTERACTION', 'Failed Participant Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S9TQ7XSKGBZK9MZDDGW'),
('01HV8B9SBYPDVNZQTXJ17J35M8', 'DATABASE_FILE_INTERACTION', 'Database Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S8DMZZPXZMR5D3KKNAG'),
('01HV8B9SBYPDVNZQTXJ17J36M8', 'SUCCESSFUL_PARTICIPANT_FILE_INTERACTION', 'Successful Participant File Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S8DMZZPXZMR5D3KKNAG'),
('01HV8B9SBYPDVNZQTXJ17J37M8', 'FAILED_PARTICIPANT_FILE_INTERACTION', 'Failed Participant File Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S8DMZZPXZMR5D3KKNAG'),
('01HV8B9SBYPDVNZQTXJ17J38M8', 'SUCCESSFUL_CGM_FILE_INTERACTION', 'Successful CGM File Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S8DMZZPXZMR5D3KKNAG'),
('01HV8B9SBYPDVNZQTXJ17J41M8', 'FAILED_CGM_FILE_INTERACTION', 'Failed CGM File Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S8DMZZPXZMR5D3KKNAG'),
('01HV8B9SBYPDVNZQTXJ17J42M8', 'SUCCESSFUL_MEAL_FITNESS_FILE_INTERACTION', 'Successful Meals Or Fitness Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S8DMZZPXZMR5D3KKNAG'),
('01HV8B9SBYPDVNZQTXJ17J43M8', 'FAILED_MEAL_FITNESS_FILE_INTERACTION', 'Failed Meals Or Fitness Interaction', NULL, 'INTERACTIONS', 'VIEW', 1, '01HV8B9S8DMZZPXZMR5D3KKNAG'),
('01HV6P1DCVPKF1M66E3J1B8J3N', 'STUDY_SETTINGS_SUB', 'Study Settings', NULL, 'STUDIES', 'EDIT', 1, '01HV6P1DCVPKF1M66E3J1B8K3N')
ON CONFLICT (permission_id) DO NOTHING;

-- 01JK5B9F63Q9958P8NP8X0GB3B - Admin
-- 01JK5B9NV46S9KHAZEAHD27CE2 - Researcher
-- 01JK5B9VWK4ZPJF00KJ7GTHMR2 - Patient 
-- 06JNBYPZXYKBXC45CS89AAPM0V - Guest 
-- 07JHBYUZXDKBDJ45CR89AGNU0M - Super Admin

-- Insert role_permission data with randomly generated ULIDs
INSERT INTO drh_stateful_authentication.role_permission(role_permission_id, role_id, permission_id, rec_status_id) 
VALUES
  -- Permission: HOME (01HV8B9R8RC35ZP2FD4AYNG8D3) - Roles: Super Admin, Admin, Researcher, Guest, Patient, Developer
  ('01V7CZQN3D6WPZ9X4BMJFK2MR5', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9R8RC35ZP2FD4AYNG8D3', 1),
  ('01V7CZQN8FJGH2QS5VPKDM9E7T', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9R8RC35ZP2FD4AYNG8D3', 1),
  ('01HV6PVA5G0JG5X0S2VQ3TX6EK', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9R8RC35ZP2FD4AYNG8D3', 1),
  ('01V7CZQND5XKTRM32YPW7EF8HV', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9R8RC35ZP2FD4AYNG8D3', 1),
  ('01V7CZQNJ9KDS4B6P3ZM8RX5TV', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9R8RC35ZP2FD4AYNG8D3', 1),
  ('01V7CZQNP2YW5TQH7GBF9XVK3D', '01JK5B9VWK4ZPJF00KJ7GTHMR2', '01HV8B9R8RC35ZP2FD4AYNG8D3', 1),
  -- Permission: STUDIES (01HV8B9R9B8VDM3HZM8M3TYE8F) - Roles: Super Admin, Admin, Researcher, Guest, Developer
  ('01V7CZQNWFJH84V2PKMN3XR6TB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9R9B8VDM3HZM8M3TYE8F', 1),
  ('01V7CZQP36RDT9NMB2VKZF4H7X', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9R9B8VDM3HZM8M3TYE8F', 1),
  ('01HV6PVA6S8BPV1P90M2BR9EPB', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9R9B8VDM3HZM8M3TYE8F', 1),
  ('01V7CZQP8KVS5MRGF9WPZD3X6B', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9R9B8VDM3HZM8M3TYE8F', 1),
  ('01V7CZQPDS7JF2BT84XVMK9P5G', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9R9B8VDM3HZM8M3TYE8F', 1),
  -- Permission: DASHBOARD (01HV8B9RA53WE7NSRY1AFQHPHP) - Roles: Super Admin, Admin, Researcher, Guest, Developer
  ('01V7CZQPJ4RXG7TK32VBNM5H9D', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9RA53WE7NSRY1AFQHPHP', 1),
  ('01V7CZQPP8KDS3XV97MGF2R6TB', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9RA53WE7NSRY1AFQHPHP', 1),
  ('01HV6PVA89CGZ0XWZY0CWWJ9S9', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9RA53WE7NSRY1AFQHPHP', 1),
  ('01V7CZQPW5TFG9BK84XHR3V7JM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RA53WE7NSRY1AFQHPHP', 1),
  ('01V7CZQQ39KVS2HM57PXG4D8FR', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9RA53WE7NSRY1AFQHPHP', 1),
  -- Permission: POPULATION_PERCENTAGE (01HV8B9RB7JYZNVFSK7YTWGYNV) - Roles: Super Admin, Admin, Researcher, Guest, Developer
  ('01V7CZQQ84WJH3FRT6VPZ5G9KD', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9RB7JYZNVFSK7YTWGYNV', 1),
  ('01V7CZQQD7TGK9PV32BMXR4H6S', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9RB7JYZNVFSK7YTWGYNV', 1),
  ('01HV6PVAA9MJG1N15TPN6NP9FV', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9RB7JYZNVFSK7YTWGYNV', 1),
  ('01V7CZQQJ3RVF8WM54XKP6D9GN', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RB7JYZNVFSK7YTWGYNV', 1),
  ('01V7CZQQP6WHS9XK73TGR2V5BM', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9RB7JYZNVFSK7YTWGYNV', 1),
    -- Permission: ALL_STUDIES (01HV8B9RC66Z4K0ZQH4EZH22Q3) - Roles: Super Admin, Admin, Researcher, Guest, Developer
  ('01V7CZQQV9KTD4RX57MGF3B8HP', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9RC66Z4K0ZQH4EZH22Q3', 1),
  ('01V7CZQR35WFJ8TP62VHK9D4GR', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9RC66Z4K0ZQH4EZH22Q3', 1),
  ('01X1A1B2C3D4E5F6G7H8I9J0K1', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9RC66Z4K0ZQH4EZH22Q3', 1),
  ('01V7CZQR83TGK7XR94SMP5F2JV', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RC66Z4K0ZQH4EZH22Q3', 1),
  ('01V7CZQRD7PHV2XB85JDR3T6GM', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9RC66Z4K0ZQH4EZH22Q3', 1),
  -- Permission: MY_STUDIES (01HV8B9RDATY08E3CTQNSHRS9R) - Roles: Researcher, Developer
  ('01HV6PVABSK39JD2AKF4HR8YWM', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9RDATY08E3CTQNSHRS9R', 1),
  ('01V7CZQRV5XJD7ZP24WKM9F3TB', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RDATY08E3CTQNSHRS9R', 1),
    -- Permission: CREATE_NEW_STUDY (01HV8B9REW2PBFFMTGJ7RQHZFE) - Roles: Researcher
  ('01V7CZQS86WJT3FP57XHK9D4BR', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9REW2PBFFMTGJ7RQHZFE', 1),
    -- Permission: VIEW_STUDY (01HV8B9RGQZHDYZPKW8ZPWS1GZ) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01V7CZQSD3RVG8KM42TXP6F9HB', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RGQZHDYZPKW8ZPWS1GZ', 1),
  ('01HT22ZXJX1RMH4BY2A7K4W2PM', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9RGQZHDYZPKW8ZPWS1GZ', 1),
  ('01HT22ZY6TW3MFDF0A31YHNR26', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9RGQZHDYZPKW8ZPWS1GZ', 1),
  ('01HV6PVAD0H55W6D0Q1X8Y9QX2', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9RGQZHDYZPKW8ZPWS1GZ', 1),
  ('01HT22ZZ5SH3FD8TE9EK2CPKHZ', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9RGQZHDYZPKW8ZPWS1GZ', 1),
    -- Permission: UPLOAD_STUDY_DB (01HV8B9RH3B7M5VZN7PE0HJ7HH) - Roles: Researcher
  ('01V7CZQSJ7TGK3XR56BVP8D2FM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RH3B7M5VZN7PE0HJ7HH', 1),
    -- Permission: ADD_PARTICIPANT (01HV8B9RKCS7GHK7Y4THPS3YJX) - Roles: Researcher
  ('01V7CZQSP4WJT7DH93XVK5G8MB', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RKCS7GHK7Y4THPS3YJX', 1),
    -- Permission: EDIT_PARTICIPANT (01HV8B9RMGZJ85PQGEGB2M0TFB) - Roles: Researcher
  ('01V7CZQSV8KTF3RP57XBG4D9HM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RMGZJ85PQGEGB2M0TFB', 1),
    -- Permission: VIEW_CGM_DATA (01HV8B9RP8ZXBR99GAB4Y79R99) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01HT23002SRY4HJYZ41QMW80A5', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9RP8ZXBR99GAB4Y79R99', 1),
  ('01HT2300G43V9Y2FKJWGD6J64J', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9RP8ZXBR99GAB4Y79R99', 1),
  ('01HV6PVAE44X4RM6C29T1KQY2J', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9RP8ZXBR99GAB4Y79R99', 1),
  ('01V7CZQT35WJD7XM62VGF9K3PR', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RP8ZXBR99GAB4Y79R99', 1),
  ('01HT2301D3QHS6THPH9XPKV1N5', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9RP8ZXBR99GAB4Y79R99', 1),
    -- Permission: UPLOAD_CGM_DATA (01HV8B9RQ2H0NWVS8VMQQFRD12) - Roles: Researcher
  ('01V7CZQT89PHV2DS34TKM6F7JB', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RQ2H0NWVS8VMQQFRD12', 1),
    -- Permission: VIEW_METRICS (01HV8B9RSNP50MXCSZ6PCF0NNA) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01V7CZQTD3RVF8KP75GXB2D9HM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RSNP50MXCSZ6PCF0NNA', 1),
  ('01HT23025S92XJ7V3HR27FF12P', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9RSNP50MXCSZ6PCF0NNA', 1),
  ('01HT2302KDY38J7P2S6F1YVX6Z', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9RSNP50MXCSZ6PCF0NNA', 1),
  ('01HV6PVAF9ZK8X3DCZ7GDAK2K7', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9RSNP50MXCSZ6PCF0NNA', 1),
  ('01HT2303HJX8Q71Y5W46ZYCNXE', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9RSNP50MXCSZ6PCF0NNA', 1),
    -- Permission: EDIT_STUDY_META_DATA (01HV8B9RTXP1WPDNBX2SYW4V5J) - Roles: Researcher
  ('01V7CZQTJ7TGK4XR96BVP8F3DM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RTXP1WPDNBX2SYW4V5J', 1),
    -- Permission: ARCHIVE_UNARCHIVE_STUDY (01HV8B9RW3GJ8PMKHY45EDZ3X3) - Roles: Researcher
  ('01V7CZQTP4WJT5DH37XSK6G9NB', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RW3GJ8PMKHY45EDZ3X3', 1),
    -- Permission: DELETE_STUDY (01HV8B9RZ50N8Q7VYKJKC7D48M) - Roles: Researcher
  ('01V7CZQTV8KTF2RP43XDG9F7JM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9RZ50N8Q7VYKJKC7D48M', 1),
    -- Permission: ASK_DRH_DATA (01HV8B9S1PM3TAM9XYMJDV6PWZ) - Roles: Admin, SuperAdmin, Developer
  ('01HT23044M20KS2HZMTK9APXZ3', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9S1PM3TAM9XYMJDV6PWZ', 1),
  ('01HT2304C56KDJMKPK2Q5NNHEE', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9S1PM3TAM9XYMJDV6PWZ', 1),
  ('01HV6PVAGKHPCZTPMZ8XFKKJ09', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9S1PM3TAM9XYMJDV6PWZ', 1),
  -- adding ASK_DRH_DATA permission for Researcher
   ('01Y2B3C4D5E6F7G8H9I0J1K2L3', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9S1PM3TAM9XYMJDV6PWZ', 1),
  -- Permission: ASK_DRH (01HV8B9S2GB9MYDWH8D6QK5MV5) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01HT2305XWTG4BX6WTNRHR12VR', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9S2GB9MYDWH8D6QK5MV5', 1),
  ('01HT23068S7B3DR3RRRZV2H6MN', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9S2GB9MYDWH8D6QK5MV5', 1),
  ('01HV6PX9RC3JMD8J8DE3VE35N3', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9S2GB9MYDWH8D6QK5MV5', 1),
  ('01HT2306S6TASWF4X0WDZNN80B', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9S2GB9MYDWH8D6QK5MV5', 1),
  ('01V7CZQU89PHV5DS23TKR6F7JB', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9S2GB9MYDWH8D6QK5MV5', 1),  
  -- Permission: ASK_DRH_RESEARCH_JOURNAL (01HV8B9S3RTT9M04TANR5TZFB0) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01HT2307K94QW52KHSRA7XQX89', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9S3RTT9M04TANR5TZFB0', 1),
  ('01HV6PC9H4FR92VMT1W7R85N2B', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9S3RTT9M04TANR5TZFB0', 1),
  ('01HV6PX9T0Y9N4YW7VPXZKT5GM', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9S3RTT9M04TANR5TZFB0', 1),
  ('01HV6PC9J7FJ7YNS5M6Q81WJCT', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9S3RTT9M04TANR5TZFB0', 1),
  ('01V7CZQUD3RVF7KP94GXB8D5HR', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9S3RTT9M04TANR5TZFB0', 1),  
  -- Permission: ASK_DRH_ICODE (01HV8B9S5Y9R6RTK2RTE4X9QWH) - Roles: Guest
  ('01HV6PC9KD79BKTZ99CH7SX9QN', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9S5Y9R6RTK2RTE4X9QWH', 1),
  ('01HV6PC9M2MFTF8AWPNC0T7KEC', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9S5Y9R6RTK2RTE4X9QWH', 1),
  ('01HV6PX9V3DB5HQ1ZCQZGPMYYE', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9S5Y9R6RTK2RTE4X9QWH', 1),
  ('01HV6PC9NTJHDCXVGKTXTAE3EQ', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9S5Y9R6RTK2RTE4X9QWH', 1),
  ('01V7CZQUJ7TGK2XR85BVJ9F3DM', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9S5Y9R6RTK2RTE4X9QWH', 1),  
  -- Permission: STUDY_INTERACTION (01HV8B9S6TP90JNRKAGKAFEG8B) - Roles: Super Admin, Admin, Researcher, Developer
  ('01V7CZQUP4WJT9DH27XSP6G9NB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9S6TP90JNRKAGKAFEG8B', 1),
  ('01V7CZQUV8KTF3RP56XDG4F7JM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9S6TP90JNRKAGKAFEG8B', 1),  
  ('01HV6PX9QK0XKVC2N1X79RB5F8', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9S6TP90JNRKAGKAFEG8B', 1),  
  ('01HV6PA3ZBQEVR14W4GBV4TVKG', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9S6TP90JNRKAGKAFEG8B', 1),
  -- Permission: FILE_INTERACTION (01HV8B9S8DMZZPXZMR5D3KKNAG) - Roles: Super Admin, Admin, Developer
  ('01V7CZQVD3RVF6KP24GXB5D9HR', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9S8DMZZPXZMR5D3KKNAG', 1),
  ('01V7CZQVJ7TGK3XR85BVJ2F3DM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9S8DMZZPXZMR5D3KKNAG', 1), 
  ('01HV6PX9W84NCH09B4QH9D9J0T', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9S8DMZZPXZMR5D3KKNAG', 1), 
  ('01HV6PA3ZX49N31QDR0FY9VPQ2', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9S8DMZZPXZMR5D3KKNAG', 1), 
  -- Permission: SUCCESSFUL_PARTICIPANT_INTERACTION (01HV8B9S9TQ7XSKGBZK9MZDDGW) - Roles: Super Admin, Admin,Researcher, Developer
  ('01V7CZQVP4WJT5DH76XSK9G2NB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9S9TQ7XSKGBZK9MZDDGW', 1),
  ('01V7CZQVV8KTF2RP43XDG5F7JM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9S9TQ7XSKGBZK9MZDDGW', 1),  
  ('01HV6PX9Y3VG19WT5HFNRXMK6F', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9S9TQ7XSKGBZK9MZDDGW', 1),  
  ('01HV6PA403C0DNRRN7QW8CJ3XR', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9S9TQ7XSKGBZK9MZDDGW', 1),    
  -- Permission: ACTIVITY_LOG (01HV8B9SBYPDVNZQTXJ17J30K7) - Roles: Super Admin, Admin, Researcher, Developer
  ('01V7CZQWD3RVF8KP54GXB7D2HR', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SBYPDVNZQTXJ17J30K7', 1),
  ('01V7CZQWJ7TGK5XR26BVJ4F3DM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SBYPDVNZQTXJ17J30K7', 1),  
  ('01HV6PX9ZGHM68Y7K0PYJJQNQW', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SBYPDVNZQTXJ17J30K7', 1),  
  ('01HV6PSF5Z53RC0ZMEF82G9APM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV8B9SBYPDVNZQTXJ17J30K7', 1),  
  -- Permission: HEALTH_INFORMATION (01HV8B9SCZG9S3S3NAX7B2KZKH) - Roles: Super Admin, Admin, Developer
  ('01V7CZQWP4WJT6DH87XSK3G9NB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SCZG9S3S3NAX7B2KZKH', 1),
  ('01V7CZQWV8KTF4RP53XDG8F7JM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SCZG9S3S3NAX7B2KZKH', 1),  
  ('01HV6PXA0PNKY3RWJ5JSTF7F3D', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SCZG9S3S3NAX7B2KZKH', 1),  
  -- Permission: PROJECT (01HV8B9SDRHTGZX0Y9K93R68Y2) - Roles: Super Admin, Admin, Developer
  ('01V7CZQX35WJD9XM58VPF4D7KB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SDRHTGZX0Y9K93R68Y2', 1),
  ('01V7CZQX89PHV3DS43TKR7F2JB', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SDRHTGZX0Y9K93R68Y2', 1),  
  ('01HV6PXA2ECX59R2MH6CVCYNKZ', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SDRHTGZX0Y9K93R68Y2', 1),  
  -- Permission: SCHEMA (01HV8B9SEPMKZRZKFS0CY6P67X) - Roles: Super Admin, Admin, Developer
  ('01V7CZQXD3RVF5KP74GXB2D4HR', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SEPMKZRZKFS0CY6P67X', 1),
  ('01V7CZQXJ7TGK6XR56BVJ8F9DM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SEPMKZRZKFS0CY6P67X', 1),  
  ('01HV6PXA3KFSXKXV98Q4Z7EQJW', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SEPMKZRZKFS0CY6P67X', 1),  
  -- Permission: DRH_OPENAPI_UI (01HV8B9SFTXJVN6DC9G3VTE3GR) - Roles: Super Admin, Admin, Developer
  ('01V7CZQXP4WJT2DH57XSK6G3NB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SFTXJVN6DC9G3VTE3GR', 1),
  ('01V7CZQXV8KTF9RP63XDG2F5JM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SFTXJVN6DC9G3VTE3GR', 1),  
  ('01HV6PZ78D1KTKEDMW7EMW4V7W', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SFTXJVN6DC9G3VTE3GR', 1),  
  -- Permission: ANNOUNCEMENTS (01HV8B9SGQ3EYWTAM2TV1JXN8K) - Roles: Super Admin, Admin
  ('01V7CZQY35WJD4XM98VPF7D3KB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SGQ3EYWTAM2TV1JXN8K', 1),
  ('01V7CZQY89PHV7DS73TKR5F4JB', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SGQ3EYWTAM2TV1JXN8K', 1),  
  ('01HV6PZ7A3SVC5J2X4D69C58VP', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SGQ3EYWTAM2TV1JXN8K', 1),  
  -- Permission: CHANGE_USER_ROLES (01HV8B9SHSV7W9PDXBTM1EN3MC) - Roles: Super Admin, Admin
  ('01V7CZQYD3RVF2KP34GXB9D6HR', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SHSV7W9PDXBTM1EN3MC', 1),
  ('01V7CZQYJ7TGK9XR76BVJ3F5DM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SHSV7W9PDXBTM1EN3MC', 1),  
  ('01HV6PZ7BP4VDJCHBF9DJJDVWT', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SHSV7W9PDXBTM1EN3MC', 1),  
  -- Permission: TRACK_STUDY_DB_EXTRACTION (01HV8B9SK4QZ2EVR10NGCJ4KHP) - Roles: Super Admin, Admin
  ('01V7CZQYP4WJT8DH37XSK2G6NB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SK4QZ2EVR10NGCJ4KHP', 1),
  ('01V7CZQYV8KTF5RP93XDG4F2JM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SK4QZ2EVR10NGCJ4KHP', 1),  
  ('01HV6PZ7CTVEFE3VN12TC5KMQJ', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SK4QZ2EVR10NGCJ4KHP', 1),  
  -- Permission: SEARCH_USERS (01HV8B9SNF42BD6B91Z0XD0G1N) - Roles: Super Admin, Admin, Developer
  ('01V7CZQZ35WJD3XM48VPF2D9KB', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SNF42BD6B91Z0XD0G1N', 1),
  ('01V7CZQZ89PHV6DS53TKR9F7JB', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SNF42BD6B91Z0XD0G1N', 1),  
  ('01HV6PZ7E36ZX0V7YCG4D3B46Y', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SNF42BD6B91Z0XD0G1N', 1),  
  -- Permission: ADD_NEW_USER (01HV8B9SP13T97X2RKNB9BBY54) - Roles: Super Admin, Admin, Developer
  ('01V7CZQZD3RVF4KP64GXB5D2HR', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SP13T97X2RKNB9BBY54', 1),
  ('01V7CZQZJ7TGK8XR26BVJ7F4DM', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SP13T97X2RKNB9BBY54', 1),  
  ('01HV6PZ7F6EVHZ9WED94NVN8Y2', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SP13T97X2RKNB9BBY54', 1),  
  -- Permission: TRAIN_ENGLISH_TO_SQL (01HV8B9SRRZ3RQXRFTAH46ZBVM) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01HX1D0P1Y8TN2YFBBFJ8SNRWT', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SRRZ3RQXRFTAH46ZBVM', 1),
  ('01HX1D0PB0W0EN9Z8AMCDDT8M1', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SRRZ3RQXRFTAH46ZBVM', 1),
  ('01HV6PZ7GAZZBD7NYCRMQN49PH', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SRRZ3RQXRFTAH46ZBVM', 1),
  ('01V7CZQZP4WJT7DH67XSK4G8NB', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9SRRZ3RQXRFTAH46ZBVM', 1),  
  -- Permission: RESEARCH_AI (01HV8B9SSP8AZW1ZXM2A1P0B3B) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01HX1D0PF9JCCN9A3SXDA95R60', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV8B9SSP8AZW1ZXM2A1P0B3B', 1),
  ('01HX1D0PG0XH8XWPR4M6ZX4EAK', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV8B9SSP8AZW1ZXM2A1P0B3B', 1),
  ('01HV6PZ7HCJEZXDNFPC84TX1QN', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV8B9SSP8AZW1ZXM2A1P0B3B', 1),
  ('01V7CZQZV8KTF6RP23XDG7F9JM', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV8B9SSP8AZW1ZXM2A1P0B3B', 1),
  -- Permission: DRH_HOME (01HTXKYNDXXD6AZKYD1CFN3Y8H) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01HTXKZ2W8WYWMB0J7MQZXGG52', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HTXKYNDXXD6AZKYD1CFN3Y8H', 1),
  ('01HTXKZ4ZP3M3VV7W6DR2SFSYH', '01JK5B9F63Q9958P8NP8X0GB3B', '01HTXKYNDXXD6AZKYD1CFN3Y8H', 1),
  ('01HV6PZ7J43B9W7RZ08ZQJD4N7', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HTXKYNDXXD6AZKYD1CFN3Y8H', 1),
  ('01HTXKZ6SXXTZRWF09PC5WKV9T', '01JK5B9NV46S9KHAZEAHD27CE2', '01HTXKYNDXXD6AZKYD1CFN3Y8H', 1),
  ('01HTXKZ88SK3ZJZR9EYQMT6KZQ', '06JNBYPZXYKBXC45CS89AAPM0V', '01HTXKYNDXXD6AZKYD1CFN3Y8H', 1),
  -- Permission: DTS_HOME (01HTXKYPF5VBDNVZHTSC8VHZFG) - Roles: Guest,Admin,Researcher,superAdmin, Developer
  ('01HTXKYX8GSQWWYJJFBCMG15XA', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HTXKYPF5VBDNVZHTSC8VHZFG', 1),
  ('01HTXKZ0ZXFXWQKNKX6D0ZYTPH', '01JK5B9F63Q9958P8NP8X0GB3B', '01HTXKYPF5VBDNVZHTSC8VHZFG', 1),
  ('01HV6PZ7K7Y44HHJFDSEJ7N5KV', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HTXKYPF5VBDNVZHTSC8VHZFG', 1),
  ('01HTXKZB7Q60Y9NBPMJ0CNK7NB', '01JK5B9NV46S9KHAZEAHD27CE2', '01HTXKYPF5VBDNVZHTSC8VHZFG', 1),
  ('01HTXKZCQ2Z3A3Y5TCCMZFMEQ7', '06JNBYPZXYKBXC45CS89AAPM0V', '01HTXKYPF5VBDNVZHTSC8VHZFG', 1),
  -- Permission: VIEW_PUBLICATIONS (01HV6NVYNYJVD31NYNGZQ7X6XW) - Roles: Researcher
  ('01HV6P6HRE9CWZKQ0H9V84WW84', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6NVYNYJVD31NYNGZQ7X6XW', 1),
  -- Permission: EDIT_PUBLICATIONS (01HV6NVYQ18B37F9EJNEHEGXPM) - Roles: Researcher
  ('01HV6P6HTK5PB0Z2S6A94MGBD2', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6NVYQ18B37F9EJNEHEGXPM', 1),
  -- Permission: VIEW_PARTICIPANT (01HV6NVYRH8AXEKPZPKQYWZ24F) - Roles: Super Admin, Admin, Researcher, Guest, Developer
  ('01HV6P6HV4M07R9NZMKD8M6S0H', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV6NVYRH8AXEKPZPKQYWZ24F', 1),
  ('01HV6P6HX3RTYQZ2TPR1K81Y8R', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV6NVYRH8AXEKPZPKQYWZ24F', 1),
  ('01HV6P6HX3RTYQZ2TPR1K81Y8R', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV6NVYRH8AXEKPZPKQYWZ24F', 1),
  ('01HV6P6HYAT2NMB3J9M2M6PAMV', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6NVYRH8AXEKPZPKQYWZ24F', 1),
  ('01HV6P6J0SPB0HCK2AXJCVXFZC', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV6NVYRH8AXEKPZPKQYWZ24F', 1),
  -- Permission: VIEW_COLLABORATORS_AND_TEAMS (01HV6NVYS4T8NSR39Z0G8Z6TPD) - Roles: Researcher
  ('01HV6P6HTK5PB0Z2S6A94MGBD2', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6NVYS4T8NSR39Z0G8Z6TPD', 1),
  -- Permission: EDIT_COLLABORATORS_AND_TEAMS (01HV6NVYTJ3CJJN1T9KMAQ7ZMV) - Roles: Researcher
  ('01HV6P6J1WC5NZTCYMNZJ5RTPN', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6NVYTJ3CJJN1T9KMAQ7ZMV', 1),
  -- Permission: EDIT_STUDY_SETTINGS (01HV6P1DCVPKF1M66E3J1B8K3N) - Roles: Researcher
  ('01HV6P6J3Q1TEY0Y2DWJW9D7ZM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6P1DCVPKF1M66E3J1B8K3N', 1),
  -- Permission: EDIT_VISIBILITY (01HV6P6J4NNQWRB63PM03EFJ3H) - Roles: Researcher
  ('01HV6P6J61Y1KCS6ZC5HPABSR8', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6P6J4NNQWRB63PM03EFJ3H', 1),
  -- Permission: PUBLICATION_SETTINGS (01HV8B9S9TQ7XSKGBZK9MZDDGW) - Roles: Super Admin, Admin,Researcher, Developer
  ('01HV6PF97M71V1MSXW7AZR6Q23', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV6PF92RDGMD1K68Y04W6FJK', 1),
  ('01HV6PF98YVD6J49XKVMBKSP7E', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV6PF92RDGMD1K68Y04W6FJK', 1),  
  ('01HV6Q0HZR8GAYE3PV13ZTYHDH', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV6PF92RDGMD1K68Y04W6FJK', 1),  
  ('01HV6PH71ZD7X7RM47N0T7PZ2X', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6PF92RDGMD1K68Y04W6FJK', 1),
  -- Permission: MEALS_DATA (01HV6PF94YP1G7MHR3TZS8F0D4) - Roles: Super Admin, Admin,Researcher, Guest, Developer
  ('01HV6PH73228KWS7MK0S6TPZYK', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV6PF94YP1G7MHR3TZS8F0D4', 1),
  ('01HV6PH74EZ3CV1NHYQR3NX5S4', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV6PF94YP1G7MHR3TZS8F0D4', 1),  
  ('01HV6Q0J0BB0G2V6PY8J4R1F9K', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV6PF94YP1G7MHR3TZS8F0D4', 1),  
  ('01HV6PH7609QXXEP6GJ7M6Y7N1', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6PF94YP1G7MHR3TZS8F0D4', 1),
  ('01HV6PKN1ZKHYJGJ3PWJ5JQ56X', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV6PF94YP1G7MHR3TZS8F0D4', 1),
  -- Permission: FITNESS_DATA (01HV6PF96B3VN5R40YJ8R56D80) - Roles: Super Admin, Admin,Researcher, Guest, Developer
  ('01HV6PH77D9GV84XG2RZAY2MW7', '07JHBYUZXDKBDJ45CR89AGNU0M', '01HV6PF96B3VN5R40YJ8R56D80', 1),
  ('01HV6PH78PNV6X7R0M8YJD9M7P', '01JK5B9F63Q9958P8NP8X0GB3B', '01HV6PF96B3VN5R40YJ8R56D80', 1),  
  ('01HV6Q0J1X44M6DZ2NH4ZNEP6B', '01HV6PVA43DHKZ9DQ4S2DFJKC6', '01HV6PF96B3VN5R40YJ8R56D80', 1),  
  ('01HV6PH79XMJ1P5R87C35A9FXR', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6PF96B3VN5R40YJ8R56D80', 1),
  ('01HV6PKN35AHTA4VKTESPCND5T', '06JNBYPZXYKBXC45CS89AAPM0V', '01HV6PF96B3VN5R40YJ8R56D80', 1),
  -- Permission: EDIT_MEALS_DATA (01HV6PKMZZ0M0F3T9YJNBAY47M) - Roles: Researcher
  ('01HV6PKN78FRPKY8Y2X0ZFY2CM', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6PKMZZ0M0F3T9YJNBAY47M', 1),
  -- Permission: EDIT_FITNESS_DATA (01HV6PKN0V23Y12YAXFHRV4Z3H) - Roles: Researcher
  ('01HV6PKNB3Z6YWKPP7TX2YDKJS', '01JK5B9NV46S9KHAZEAHD27CE2', '01HV6PKN0V23Y12YAXFHRV4Z3H', 1)
  ON CONFLICT (role_permission_id) DO NOTHING;  
  
  --Remove the privileges EDIT_MEALS_DATA and EDIT_FITNESS_DATA from the admin role.
  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01HV6PKN4NV6N4VDE1H8H2SSZ2';
  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01HV6PKN5ZG5JYJ69A2J7J22QZ';
  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01HV6Q0J3E7BAXVPA0RS0MHCH3';

  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01HV6PKN8E4KQZZ3MDDG5Z0EKF';
  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01HV6PKN9V2PBKTCQY4TBWHEW2';
  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01HV6Q0J4RYXFP8Z97K5ZJYB59';
  --Remove admin privillage for MY_STUDIES
  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01V7CZQRP8TGF3XM97BKR2D4JV';
  DELETE FROM drh_stateful_authentication.role_permission WHERE role_permission_id='01V7CZQRJ4WKT9DS63VPZ8G5HB';

  INSERT INTO drh_stateful_master.meal_type
(meal_type_id, code, "system", display, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
    ('01JPSGH0CFC1D9EPK1ESZTVA0V', '414056003', 'http://snomed.info/sct', 'Breakfast', 1, '2025-04-07 10:21:29.343', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGH0CF6P1TX71FYNW0K8B2', '414057007', 'http://snomed.info/sct', 'Lunch', 1, '2025-04-07 10:22:32.768', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGH0CF9T8ADMWFQ165WKAC', '414058002', 'http://snomed.info/sct', 'Dinner', 1, '2025-04-07 10:23:05.838', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGH0CG84WH60WPAHX4ZMT2', '225559008', 'http://snomed.info/sct', 'Snack', 1, '2025-04-07 10:23:38.595', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING;

INSERT INTO drh_stateful_master.nutrition_intake_status_code
(id, code, "system", display, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
    ('06JR1TKDDR63R02NF04PGXDEH2', 'preparation', 'http://hl7.org/fhir/event-status', 'Preparation', 1, '2025-04-07 10:27:43.689', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR1TKQVR504BVC39CNBJP9F0', 'in-progress', 'http://hl7.org/fhir/event-status', 'In Progress', 1, '2025-04-07 10:29:27.549', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR1TKR7S8TQVVM6KS5FN9XDQ', 'not-done', 'http://hl7.org/fhir/event-status', 'Not Done', 1, '2025-04-07 10:30:21.212', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR1TKW1PPR285XF5FW7XER1P', 'on-hold', 'http://hl7.org/fhir/event-status', 'On Hold', 1, '2025-04-07 10:31:11.040', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR1TKZT83VSAQYGW76M6HYQN', 'stopped', 'http://hl7.org/fhir/event-status', 'Stopped', 1, '2025-04-07 10:31:11.040', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR1TM1ETHYHKTANAN6MXCW4X', 'completed', 'http://hl7.org/fhir/event-status', 'Completed', 1, '2025-04-07 10:31:11.040', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR1TQ2NPMW0G184C6SA7W2N8', 'entered-in-error', 'http://hl7.org/fhir/event-status', 'Entered in Error', 1, '2025-04-07 10:31:11.040', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR1TQ6NJQSF81KWM7EP40C3S', 'unknown', 'http://hl7.org/fhir/event-status', 'Unknown', 1, '2025-04-07 10:31:11.040', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING;

INSERT INTO drh_stateful_master.unit_of_measurement
(unit_id, unit, code, "system", display, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
    ('01JPSGJENES9NQ7M1760Q8G6SK', '', 'kcal', 'http://unitsofmeasure.org', 'Kilocalories', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENEE2XA7C1YTTJNRJ8M', '', 'g', 'http://unitsofmeasure.org', 'Grams', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENEBAT0NESKGKXD1PEP', '', 'mg', 'http://unitsofmeasure.org', 'Milligrams', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENEBTFKC7PYY4AWRGBQ', '', 'ml', 'http://unitsofmeasure.org', 'Milliliters', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENEQMYMEH8HKMBDH0RP', '', 'oz', 'http://unitsofmeasure.org', 'Ounces', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENEK86WZJBQQPM9573G', '', 'kg', 'http://unitsofmeasure.org', 'Kilograms', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENFS00ESTCSGMZCGWK4', '', 'steps', 'http://unitsofmeasure.org', 'Steps', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENFMR1C0CZ00H86Y7Z1', '', 'km', 'http://unitsofmeasure.org', 'Kilometers', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENFY68851ANZ7PQ2KSV', '', 'min', 'http://unitsofmeasure.org', 'Minutes', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGJENFY68851ANZ7PQ2OUY', '', 'bpm', 'http://unitsofmeasure.org', 'Beats per Minute', 1, '2025-04-07 11:03:00.491', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING;

INSERT INTO drh_stateful_master.measurement_category
(id, unit_category_name, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
    ('01JPSGN60D76VHC04X8PQW171P', 'Nutrition', 1, '2025-04-07 11:07:24.161', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('01JPSGN60DR2A4ASZTVY4H4HVT', 'Fitness', 1, '2025-04-07 11:07:24.161', 'UNKNOWN', NULL, NULL, NULL, NULL)
 ON CONFLICT (id) DO NOTHING;   

INSERT INTO drh_stateful_master.unit_category_mapping
(mapping_id, unit_id, category_id, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
    ('01JPSGMCA6XG1HSQN1K1QG71VM', '01JPSGJENES9NQ7M1760Q8G6SK', '01JPSGN60DR2A4ASZTVY4H4HVT', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA62A5DD3K24XXR4CS4', '01JPSGJENEE2XA7C1YTTJNRJ8M', '01JPSGN60D76VHC04X8PQW171P', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA6W3KGFDA3NVPMESBX', '01JPSGJENEBAT0NESKGKXD1PEP', '01JPSGN60DR2A4ASZTVY4H4HVT', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA6VGRQ2JEPJE60VHTG', '01JPSGJENEBTFKC7PYY4AWRGBQ', '01JPSGN60DR2A4ASZTVY4H4HVT', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA6Q4NHTFXB3WHN6M77', '01JPSGJENEQMYMEH8HKMBDH0RP', '01JPSGN60DR2A4ASZTVY4H4HVT', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA6KD0526D0NQWH9MKM', '01JPSGJENEK86WZJBQQPM9573G', '01JPSGN60DR2A4ASZTVY4H4HVT', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA71P7T6STCA6PM0N6S', '01JPSGJENFS00ESTCSGMZCGWK4', '01JPSGN60D76VHC04X8PQW171P', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA7M8KCGQSVN67PTVZZ', '01JPSGJENFMR1C0CZ00H86Y7Z1', '01JPSGN60D76VHC04X8PQW171P', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA7N0FS5PESHSP0FTTK', '01JPSGJENFY68851ANZ7PQ2KSV', '01JPSGN60D76VHC04X8PQW171P', 1, NULL, NULL, NULL, NULL, NULL, NULL),
    ('01JPSGMCA7XRH18CFM3YM8C6X1', '01JPSGJENFY68851ANZ7PQ2OUY', '01JPSGN60D76VHC04X8PQW171P', 1, NULL, NULL, NULL, NULL, NULL, NULL)
ON CONFLICT (mapping_id) DO NOTHING;    

INSERT INTO drh_stateful_master.file_content_type
(id, title, description, rec_status_id)
VALUES
    ('01HV6N7RCZ6F7SB6Q2K5JYRPTG', 'Meals', 'File content type of meals',1),
    ('01HV6N7RK5EVNRFK6CD3W6TGMN', 'Fitness', 'File content type of fitness',1)
ON CONFLICT (title) DO NOTHING; 

INSERT INTO drh_stateful_master.observation_method
(method_id, code, "system", display, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
    ('06JR2N8432P2MKWPMM7D986YGY', 'manual-entry', 'http://snomed.info/sct', 'Manual Entry', 1, '2025-04-07 10:21:29.343', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR2N8586GPHG2YPAK05PHZR2', 'pedometer', 'http://snomed.info/sct', 'Pedometer', 1, '2025-04-07 10:22:32.768', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR2N85C7VH96M011CJHBSKYK', 'accelerometer', 'http://snomed.info/sct', 'Accelerometer', 1, '2025-04-07 10:23:05.838', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR2N86GC1M7QXR1VWG0C49CT', 'heart-rate-sensor', 'http://snomed.info/sct', 'Heart Rate Sensor', 1, '2025-04-07 10:23:38.595', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING;

INSERT INTO drh_stateful_master.activity_master
(activity_id, code, "system", display, "text", rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
  ('01HZYXTDCN8RZXB94H9YCFDS45', '55423-8', 'http://loinc.org', 'Physical Activity', 'Physical Activity', 1, '2025-04-10 10:25:29.130', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('01HZYXTDCN8RZXB94H9YCFDS78', '55411-3', 'http://loinc.org', 'Walking', 'Walking', 1, '2025-04-10 10:25:29.130', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('01HZYXTDCN8RZXB94H9YCFDS67', '229065009', 'http://snomed.info', 'Bicycling', 'Cycling', 1, '2025-04-10 10:25:29.130', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING;

INSERT INTO drh_stateful_master.observation_category
(category_id, code, "system", display, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
  ('06JR3R242C7QVDBDEAPQVC72PR', 'social-history', 'http://hl7.org/fhir', 'Social History', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R28200N9KSHDW07WYH07H', 'vital-signs', 'http://hl7.org/fhir', 'Vital Signs', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R2BSPGEQYGVRJMDA41VZ2', 'imaging', 'http://hl7.org/fhir', 'Imaging', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R2C6HBPPTJTPQW9ETPM3D', 'laboratory', 'http://hl7.org/fhir', 'Laboratory', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R2EM41QWC3AQM2PKFSZR8', 'procedure', 'http://hl7.org/fhir', 'Procedure', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R2FZZSQSW6AP03KRAGW29', 'survey', 'http://hl7.org/fhir', 'Survey', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R2HD83PJAHW6S9W1R1ZW6', 'exam', 'http://hl7.org/fhir', 'Exam', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R2KTJQNC5BSF7T18NEC5H', 'therapy', 'http://hl7.org/fhir', 'Therapy', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R2PKC1TJW204PPSB1D454', 'activity', 'http://hl7.org/fhir', 'Activity', 1, '2025-04-10 10:30:03.305', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING;


INSERT INTO drh_stateful_master.observation_status
(id, code, "system", display, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
    ('06JR3R6JMG6VYDEKWYQG7G0C2Q', 'registered', 'http://hl7.org/fhir', 'Registered', 1, '2025-04-10 11:13:08.144', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR3R6ND43ZXR6M6SFQX7SKYZ', 'preliminary', 'http://hl7.org/fhir', 'Preliminary', 1, '2025-04-10 11:13:08.151', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR3R6TP18R6C4N91AC6ZZZXE', 'final', 'http://hl7.org/fhir', 'Final', 1, '2025-04-10 11:13:08.153', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR3R6W6PQNC041AM77RPNW2Z', 'amended', 'http://hl7.org/fhir', 'Amended', 1, '2025-04-10 11:13:08.155', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR3R6YN8719TP1C9E6HG6ZVF', 'corrected', 'http://hl7.org/fhir', 'Corrected', 1, '2025-04-10 11:13:08.157', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR3R70005QXX87ZTK9DH99EC', 'cancelled', 'http://hl7.org/fhir', 'Cancelled', 1, '2025-04-10 11:13:08.158', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR3R71BW1SFDDYH87016QHAA', 'entered-in-error', 'http://hl7.org/fhir', 'Entered in Error', 1, '2025-04-10 11:13:08.160', 'UNKNOWN', NULL, NULL, NULL, NULL),
    ('06JR3R73VG6PHZXJPTPJHYPZRN', 'unknown', 'http://hl7.org/fhir', 'Unknown', 1, '2025-04-10 11:13:08.161', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING; 

INSERT INTO drh_stateful_master.activity_component_type
(component_type_id, code, "system", display, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
VALUES
  ('06JR3R7G2S8VZTH41JNR02NPKW', '55423-8', 'http://loinc.org', 'Duration', 1, '2025-04-10 11:19:38.163', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R7JNZZN8EK98C13REKFW0', '41981-2', 'http://loinc.org', 'Calories Burned', 1, '2025-04-10 11:19:38.169', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R7M2VZTJHB3Z6QBTR5PNH', '41950-7', 'http://loinc.org', 'Steps Count', 1, '2025-04-10 11:19:38.171', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R7PJC3ZXDC1CKYQTSC43Z', '55416-2', 'http://loinc.org', 'Distance', 1, '2025-04-10 11:19:38.173', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R7R4C5R7N9VTR6G5FYX8T', '8867-4', 'http://loinc.org', 'Heart Rate (BPM)', 1, '2025-04-10 11:19:38.175', 'UNKNOWN', NULL, NULL, NULL, NULL),
  ('06JR3R7TP7SJPHAEKZWG7NEVVC', '2708-6', 'http://loinc.org', 'Oxygen Saturation', 1, '2025-04-10 11:19:38.176', 'UNKNOWN', NULL, NULL, NULL, NULL)
ON CONFLICT (code) DO NOTHING;

-- Idempotent seeding of migration statuses
INSERT INTO drh_stateful_master.activity_type (id, code, title, description, rec_status_id)
VALUES
    ('01HXMF0A1K5Z7RR4Z4D0FYX3AA', 'ASK_DRH_ICODE', 'Ask DRH iCODE', NULL, 1),
    ('01HXMF0A3YJKFK0YADMP05YGRQ', 'PROJECT', 'Project', NULL, 1),
    ('01HXMF0A63GE60KR93ZKTAX5QM', 'SKIP_LOGIN', 'Skip Login', NULL, 1),
    ('01HXMF0A8S1FXPSRSHC4KGZ4B8', 'GITHUB_LOGIN', 'GitHub Login', NULL, 1),
    ('01HXMF0ABTXXGFJ7BCHXH2WBTV', 'PROFILE', 'Profile', NULL, 1),
    ('01HXMF0AE9AGG1ZPW5QQN3S2TZ', 'CONSOLE', 'Console', NULL, 1),
    ('01HXMF0AGT71VN8A20WWNAJBG6', 'MY_STUDIES', 'My Studies', NULL, 1),
    ('01HXMF0AJ79RAGPK9KEQHRGE6Z', 'POPULATION_PERCENTAGE', 'Population Percentage', NULL, 1),
    ('01HXMF0AM53M8GSZJQNRNFZXNZ', 'ASK_DRH_DATA', 'Ask DRH Data', NULL, 1),
    ('01HXMF0AP15PXTA2Z6JZ6CZETJ', 'STUDY_DETAILS', 'Study Details', NULL, 1),
    ('01HXMF0ARVB9XBDW46Y4NY1GKN', 'SCHEMA', 'Schema', NULL, 1),
    ('01HXMF0AU6Y9M9KX8YMPY2XSEK', 'ERROR', 'Error', NULL, 1),
    ('01HXMF0AXG6D0DGR3CGT1EYBWB', 'DRH_OPEN_API_UI', 'DRH Open API UI', NULL, 1),
    ('01HXMF0AZPTTCMQGMJ6TRBAEFD', 'PRACTITIONER_LOGIN', 'Practitioner Login', NULL, 1),
    ('01HXMF0B2PST2E32M0GZTKD51C', 'ASK_DRH', 'Ask DRH', NULL, 1),
    ('01HXMF0B5C4A0PD2P7GZ92MRC2', 'HOME', 'Home', NULL, 1),
    ('01HXMF0B7Q0S2Z1K5H2P3ZRCBZ', 'ASK_DRH_RESEARCH_JOURNAL', 'Ask DRH Research Journal', NULL, 1),
    ('01HXMF0BAJ5CBNRZ19STGNJK5C', 'ALL_STUDIES', 'All Studies', NULL, 1),
    ('01HXMF0BD37NY13N8BFFM2RT65', 'DOCUMENTATION', 'Documentation', NULL, 1),
    ('01HXMF0BFM8HDZJESWE63ZTN54', 'DASHBOARD', 'Dashboard', NULL, 1),
    ('01HXMF0BI8KDA5FSBHVF16N6VY', 'HEALTH_INFORMATION', 'Health Information', NULL, 1),
    ('01HXMF0BKZVE2SXZ6CFD97V2H5', 'ORCID_LOGIN', 'Orcid Login', NULL, 1),
    ('02HXMF0CKZVE2SXZ6CFD97V2H6', 'CREATE_STUDY', 'Create Study', NULL, 1),

    ('01HXHXE1AVN9ZKBAYTS6Q8YVDP', 'ADD_NEW_PARTICIPANT', 'Add New Participant', NULL, 1),
    ('01HXHXE1BXTJ52X43Q8KZ2J5XT', 'EDIT_PARTICIPANT', 'Edit Participant', NULL, 1),
    ('01HXHXE1CXXNZXK62JKSHXHY0R', 'UPLOAD_PARTICIPANT_DATA', 'Upload Participant Data', NULL, 1),
    ('01HXHXE1HX31NP6M15YV5P3R5M', 'CREATE_PRACTITIONER_PROFILE', 'Create Practioner Profile', NULL, 1),
    ('01HXHXE1MZZSJEZ8PQ92H8TVWA', 'UPDATE_PROFILE_DATA', 'Update Profile Details', NULL, 1),
    ('01HXHXE1KYSJ9F9N2D0ENHYT4E', 'SAVE_CITATION_AUTHORS', 'Save Citation Authors', NULL, 1),
    ('01J0Y7N7G9S15PVTDZCVTYK25Z', 'UPDATE_RESEARCH_STUDY', 'Update Research Study', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8B', 'UPDATE_RESEARCH_STUDY_SETTINGS', 'Update Research Study Settings', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8C', 'SAVE_STUDY_CITATION', 'Save Study Citation', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8D', 'SAVE_STUDY_TEAM_MEMBERS', 'Save Study Team Members', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8E', 'SAVE_COLLAB_TEAM', 'Save Collab Team', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8A', 'UPDATE_PUBLICATION', 'Update Publication', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8F', 'UPDATE_ARCHIVE_STATUS', 'Update Archive Status', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8G', 'UPDATE_VISIBILITY', 'Update Visibility', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8H', 'CREATE_USER_PROFILE', 'Create User Profile', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8I', 'UPDATE_USER_ROLES', 'Update User ROles', NULL, 1),
    ('01JZ2QK9V8WJ8YQK2R4F6T7N8J', 'UPSERT_USER_VERIFICATION_LOG', 'Upsert User Verification Log', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3Q', 'INSERT_CGM_RAW_AND_EXTRACT', 'Insert CGM Raw and Extract', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3R', 'INSERT_CGM_RAW_DB', 'Insert CGM Raw DB', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3S', 'SAVE_CGM_RAW_DATA', 'Save CGM Raw Data', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3T', 'SAVE_SUBJECT_OBSERVATION_DATA', 'Save Subject Observation Data', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3U', 'UPLOAD_STUDY_DATABASE', 'Upload Study Database', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3V', 'STUDY_SETTINGS', 'Study Settings', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3W', 'COLLABORATION_AND_TEAMS', 'Collaboration and Teams', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3X', 'PUBLICATION_SETTINGS', 'Publication Settings', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W3Y', 'EDIT_PARTICIPANT', 'Edit Participant', NULL, 1),
    ('01K0Y6V8B6QJ7Z9X2R4F8N1W43', 'EDIT_USER_PROFILE', 'Edit User Profile', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2X4P', 'PARTICIPANT_DASHBOARD', 'Participant Dashboard', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2X4Q', 'CGM_DATA', 'CGM Data', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2X4R', 'MEALS_DATA', 'Meals Data', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2X4S', 'FITNESS_DATA', 'Fitness Data', NULL, 1),
    
    ('01HXHXE1DQRCHM63EZFQ4CDAB2', 'PRACTITIONER_LOGIN', 'Practitioner Login', NULL, 1),
    ('01HXHXE1FG47YXQCCAW83RZBR7', 'ANONYMOUS_LOGIN', 'Anonymous Login', NULL, 1),
    ('01HXHXE1GZR5MV8E6Y64SNZBGW', 'GUEST_LOGIN', 'Guest Login', NULL, 1),
    ('01K9Z1P8QGZVX5R7SAYXG27RVF', 'SUCCESSFUL_STUDY_INTERACTION', 'Successful Study Interaction', NULL, 1),
    ('01K9Z1P8QKCAZNMP0MCNP20X4Q', 'FAILED_STUDY_INTERACTION', 'Failed Study Interaction', NULL, 1),
    ('01K9Z1P8QN2B6DE0JS44BWX4BP', 'SUCCESSFUL_PARTICIPANT_INTERACTION', 'Successful Participant Interaction', NULL, 1),
    ('01K9Z1P8QQAKHDMVMJY0R8CZTY', 'FAILED_PARTICIPANT_INTERACTION', 'Failed Participant Interaction', NULL, 1),
    ('01K9Z1P8QS43ZJYQ1WT2HTNNSD', 'DATABASE_INTERACTION', 'Database Interaction', NULL, 1),
    ('01K9Z1P8QV9D3JYKP2YYNPDFZE', 'SUCCESSFUL_PARTICIPANT_FILE_INTERACTION', 'Successful Participant File Interaction', NULL, 1),
    ('01K9Z1P8QYAZ9H8AKF1Y58V1E1', 'FAILED_PARTICIPANT_FILE_INTERACTION', 'Failed Participant File Interaction', NULL, 1),
    ('01K9Z1P8R1D8ZX6XP2BFG9MDXQ', 'SUCCESSFUL_CGM_FILE_INTERACTION', 'Successful CGM File Interaction', NULL, 1),
    ('01K9Z1P8R3D8EVTCXHZFGK1MZD', 'FAILED_CGM_FILE_INTERACTION', 'Failed CGM File Interaction', NULL, 1),
    ('01K9Z1P8R61Y1PK03MAZ6AQNCF', 'SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION', 'Successful Meals Or Fitness Interaction', NULL, 1),
    ('01K9Z1P8R8SDM3MVWM82PNQGM2', 'FAILED_MEALS_OR_FITNESS_INTERACTION', 'Failed Meals Or Fitness Interaction', NULL, 1),
    ('01K9Z1P8RBDB4A1DBZW90A9RE0', 'USER_SESSION', 'User Session', NULL, 1),
    ('01KXHXE1A1P9NR4QPKT3EFY2SZ', 'PROFILE_EMAIL', 'Profile Email', NULL, 1),
    ('01KXHXE1A2KQG5MZ0JZ6C3P4NR', 'VERIFY_OTP', 'Verify OTP', NULL, 1),
    ('01KXHXE1A3C2H9YMK9ZK2RQ78M', 'SAVE_STUDY', 'Save Study', NULL, 1),
    ('01KXHXE1A4TZCN3V1Y3M1ZF9XW', 'SAVE_STUDY_SETTINGS', 'Save Study Settings', NULL, 1),
    ('01KXHXE1A5J7DKQ74HQNV8T2WF', 'SAVE_USER_PROFILE', 'Save Practitioner Profile', NULL, 1),
    ('01KXHXE1A6VG2NPY2RM7DTW4V9', 'ORGANIZATION_SESSION', 'Organization Session', NULL, 1),
    ('01KXHXE1A78Z7ZKM0MF23RH9KR', 'APPLICATION_SESSION', 'Application Session', NULL, 1),
    ('01KXHXE1A8SXKNPQ7BX3MYF9Z1', 'STUDY_DATA_INLINE_EDIT', 'Study Data Inline Edit', NULL, 1),
    ('01KXHXE1A9QCHYMW3K9RMDJ62G', 'CHANGE_STUDY_ARCHIVE_STATUS', 'Change Study Archive Status', NULL, 1),
    ('01KXHXE1AAKN4CQ6ZK0Y2VPJX6', 'CHANGE_PUBLICATION_AUTHOR', 'Change Publication Author', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2E5S', 'CHANGE_STUDY_VISIBILITY', 'Change Study Visibility', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2E6S', 'CHANGE_STUDY_CITATIONS', 'Change Study Citations', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2E7S', 'CHANGE_USER_ROLE', 'Change User Role', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2E9S', 'SAVE_PROFILE', 'Save Profile', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2E8S', 'SAVE_PARTICIPANT', 'Save Participant', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2F0S', 'CHANGE_PARTICIPANT_DETAILS', 'Change Participant Details', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2F1S', 'UPLOAD_PARTICIPANT_FILE', 'Upload Participant File', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2F2S', 'SAVE_CGM_DATA', 'Save CGM Data', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2F4S', 'SAVE_COLLABORATION_TEAM', 'Save Collaboration Team', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2F3S', 'SAVE_MEALS_FITNESS_DATA', 'Save Meals or Fitness Data', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2F5S', 'EDIT_STUDY_SETTINGS', 'Edit Study Settings', NULL, 1),
    ('01K1Z7W9C7RK8A0Y3S5G9M2F6S', 'DELETE_RESEARCH_STUDY', 'Delete Research Study', NULL, 1)
ON CONFLICT (code) DO NOTHING;

-- Activity Level table inserts
INSERT INTO drh_stateful_master.activity_level 
(id, title, level, rec_status_id, created_at) VALUES
('01H2XBMS6P8JQ9K4Z7R3N5Y1WF', 'ERROR_LOG_LEVELS', 0, 1, CURRENT_TIMESTAMP),
('01H2XBMS7R4KQ9N5P8T6V2Y3WG', 'PRIMARY_MENU', 1, 1, CURRENT_TIMESTAMP),
('01H2XBMS8T7V2Y4N6P9R5K3W1H', 'SECONDARY_MENU', 2, 1, CURRENT_TIMESTAMP),
('01H2XBMS9V9Y5R7K4N2P6T3W8J', 'TERNARY_APPLICATION_LEVEL', 3, 1, CURRENT_TIMESTAMP),
('01H2XBMSBX2R4K7N9P5T8V6W3L', 'QUATERNARY_APPLICATION_LEVEL', 4, 1, CURRENT_TIMESTAMP),
('01H2XBMSCY4T6N8P2K7R3V9W5M', 'QUINARY_APPLICATION_LEVEL', 5, 1, CURRENT_TIMESTAMP),
('01H2XBMSDZ7V9P3K5N8T2R6W4N', 'LESS_PRIORITY_LOG_LEVELS', 6, 1, CURRENT_TIMESTAMP),
('02H2XBMSDZ7V9P3K5N9T2R6W4K', 'DB_LEVEL_LOG', 7, 1, CURRENT_TIMESTAMP),
('02H2XBMSDZ7V9P3K5N9T2R6W5K', 'CRUD_LEVEL_LOG', 8, 1, CURRENT_TIMESTAMP)
ON CONFLICT (title) DO NOTHING;

-- Activity Level Mapping table inserts
INSERT INTO drh_stateful_master.activity_level_mapping 
(id, title, activity_level_id, rec_status_id, created_at) VALUES
-- ERROR_LOG_LEVELS
('01H2XBMSEF2K4N7P9R5T8V3W6P', 'ERROR', '01H2XBMS6P8JQ9K4Z7R3N5Y1WF', 1, CURRENT_TIMESTAMP),

-- PRIMARY_MENU
('01H2XBMSFH4N7P2K5R8T6V9W3Q', 'SKIP_LOGIN', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSGJ6P9K4N2R5T8V3W7R', 'ORCID_LOGIN', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSHK8R2N6P4T7V9W5Y3S', 'GIT_LOGIN', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSJM3T5P8K2N6R9W4Y7T', 'HOME', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSKN5V7R2P4K8T3W6Y9U', 'STUDIES', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSLP7Y4T2N6K9R5W8V3V', 'COHORT', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSMQ9R4K6N8T3W7Y5V2W', 'ASK_DRH', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSNR2T6P8K4N7W5Y3V9X', 'CONSOLE', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSPT4V8R2K6N9Y7W5V3Y', 'DOCUMENTATION', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),
('01H2XBMSQV6Y3T5P8K2R7W4N9Z', 'PROFILE', '01H2XBMS7R4KQ9N5P8T6V2Y3WG', 1, CURRENT_TIMESTAMP),

-- SECONDARY_MENU
('01H2XBMSRW8R4K7N2P5T9V3Y6A', 'DASHBOARD', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMSSY2T6N9P4K7R3W8V5B', 'ALL_STUDIES', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMSTZ4V8P2K6N9R5Y7W3C', 'POPULATION_PERCENTAGE', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMSV16Y3R5N8K2T7W4P9D', 'MY_STUDIES', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMSW38R7K4N2P6T9Y5V3E', 'ASK_DRH_DATA', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMSX52T9N6P4K8R3W7Y5F', 'ASK_DRH_RESEARCH_JOURNAL', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMSY74V2P8K6N3T5R9W7G', 'ASK_DRH_ICODE', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMSZ96Y4R2N8K5V7T3P9H', 'CONSOLE_PROJECT', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMT1B8R6K4P2N7Y5V3T9J', 'CONSOLE_HEALTH_INFORMATION', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMT2D2T8N6K4P9R7W5Y3K', 'CONSOLE_SCHEMA', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMT3F4V2P8K6N3T5R9W7L', 'DOCUMENTATION_OPEN_API', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),
('01H2XBMT4H6Y4R2N8K5V7T3P9M', 'DOCUMENTATION_ANNOUNCEMENTS', '01H2XBMS8T7V2Y4N6P9R5K3W1H', 1, CURRENT_TIMESTAMP),

-- TERNARY_APPLICATION_LEVEL
('01H2XBMT5J8R6K4P2N7Y5V3T9N', 'STUDIES_INDIVIDUAL', '01H2XBMS9V9Y5R7K4N2P6T3W8J', 1, CURRENT_TIMESTAMP),
('01H2XBMT6L2T8N6K4P9R7W5Y3P', 'STUDIES_PARTICIPANT', '01H2XBMS9V9Y5R7K4N2P6T3W8J', 1, CURRENT_TIMESTAMP),
('01H2XBMT7N4V2P8K6N3T5R9W7Q', 'ALL_STUDIES_CGM', '01H2XBMS9V9Y5R7K4N2P6T3W8J', 1, CURRENT_TIMESTAMP),
('01H2XBMT8P6Y4R2N8K5V7T3P9R', 'STUDIES_CGM', '01H2XBMS9V9Y5R7K4N2P6T3W8J', 1, CURRENT_TIMESTAMP),

-- QUATERNARY_APPLICATION_LEVEL
('01H2XBMT9R8R6K4P2N7Y5V3T9S', 'AI_ASK_DRH_DATA', '01H2XBMSBX2R4K7N9P5T8V6W3L', 1, CURRENT_TIMESTAMP),
('01H2XBMTBT2T8N6K4P9R7W5Y3T', 'AI_ASK_DRH_RESEARCH_JOURNAL', '01H2XBMSBX2R4K7N9P5T8V6W3L', 1, CURRENT_TIMESTAMP),
('01H2XBMTCV4V2P8K6N3T5R9W7U', 'AI_ASK_DRH_ICODE', '01H2XBMSBX2R4K7N9P5T8V6W3L', 1, CURRENT_TIMESTAMP),

-- QUINARY_APPLICATION_LEVEL
('01H2XBMTDX6Y4R2N8K5V7T3P9V', 'LOGIN', '01H2XBMSCY4T6N8P2K7R3V9W5M', 1, CURRENT_TIMESTAMP),
('01H2XBMTFZ8R6K4P2N7Y5V3T9W', 'ACTIVITY_LOG', '01H2XBMSCY4T6N8P2K7R3V9W5M', 1, CURRENT_TIMESTAMP),

-- LESS_PRIORITY_LOG_LEVELS
('01H2XBMTG12T8N6K4P9R7W5Y3X', 'DEFAULT', '01H2XBMSDZ7V9P3K5N8T2R6W4N', 1, CURRENT_TIMESTAMP),
('01H2XBMTH34V2P8K6N3T5R9W7Y', 'SAVE_ACTIVITY_LOG', '01H2XBMSDZ7V9P3K5N8T2R6W4N', 1, CURRENT_TIMESTAMP)
ON CONFLICT (title) DO NOTHING;

-- Idempotent seeding of migration statuses
INSERT INTO drh_stateful_master.user_verification_status (id, code, title, description, rec_status_id)
VALUES
    ('01HZAY3AY2V5AY1F8K43TP4P1H', 'PENDING', 'Pending', NULL, 1),
    ('01HZAY3B0EQK6NVHPM5K5X8FEM', 'FAILED', 'Failed', NULL, 1),
    ('01HZAY3B5XYEKMH2FVN6F8CKRQ', 'COMPLETED', 'Completed', NULL, 1)
ON CONFLICT (code) DO NOTHING;
