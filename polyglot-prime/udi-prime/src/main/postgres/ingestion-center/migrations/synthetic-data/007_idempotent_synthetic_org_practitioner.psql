CREATE OR REPLACE PROCEDURE drh_stateless_research_study.seed_organizations()
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
BEGIN
    -- University of Virginia
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'University of Virginia'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'University of Virginia',
            '{"ROR": "https://ror.org/04ezjnq35"}'::jsonb,
            'UVA',
            'EDU, FND',
            'Education, Funder',
            'Charlottesville',
            NULL,
            'United States',
            'https://virginia.edu',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- Jaeb Center for Health Research
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'Jaeb Center for Health Research'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'Jaeb Center for Health Research',
            '{
                "GRID": "grid.414912.b",
                "ISNI": "0000 0004 0586 473X",
                "Crossref Funder ID": "100017113"
            }'::jsonb,
            'JCHR',
            'FND, NPO',
            'Funder, Nonprofit',
            'Tampa',
            NULL,
            'United States',
            'http://www.jaeb.org/',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- Stanley Medical Research Institute
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'Stanley Medical Research Institute'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'Stanley Medical Research Institute',
            '{
                "GRID": "grid.453353.7",
                "ISNI": "0000 0004 0473 2858",
                "Crossref Funder ID": "100007123"
            }'::jsonb,
            'SMRI',
            'FND, NPO',
            'Funder, Nonprofit',
            'Chevy Chase',
            NULL,
            'United States',
            'http://www.stanleyresearch.org/',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- Lurie Children's Hospital
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'Lurie Children''s Hospital'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'Lurie Children''s Hospital',
            '{
                "GRID": "grid.413808.6",
                "ISNI": "0000 0004 0388 2248",
                "Crossref Funder ID": "100007175",
                "Wikidata": "Q6704935"
            }'::jsonb,
            'Ann & Robert H. Lurie Children''s Hospital of Chicago, Children''s Memorial Hospital',
            'FND, HCF',
            'Funder, Healthcare',
            'Chicago',
            NULL,
            'United States',
            'https://www.luriechildrens.org/en-us/Pages/index.aspx',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- University of California, San Francisco
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'University of California, San Francisco'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'University of California, San Francisco',
            '{
                "GRID": "grid.266102.1",
                "ISNI": "0000 0001 2297 6811",
                "Crossref Funder ID": "100008069",
                "Wikidata": "Q1061104"
            }'::jsonb,
            'UCSF',
            'EDU, FND',
            'Education, Funder',
            'San Francisco',
            NULL,
            'United States',
            'https://www.ucsf.edu/',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- Mills Peninsula Health Services
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'Mills Peninsula Health Services'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'Mills Peninsula Health Services',
            '{
                "GRID": "grid.415665.5",
                "ISNI": "0000 0004 0450 9138"
            }'::jsonb,
            'Mills Memorial Hospital',
            'NPO',
            'Nonprofit',
            'Burlingame',
            NULL,
            'United States',
            'http://www.mills-peninsula.org/',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- Diabetes Technology Society
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'Diabetes Technology Society'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'Diabetes Technology Society',
            '{
                "GRID": "grid.478728.6",
                "ISNI": "0000 0004 5899 3033",
                "Crossref Funder ID": "100008427"
            }'::jsonb,
            'DTS',
            'FND, NPO',
            'Funder, Nonprofit',
            'Burlingame',
            NULL,
            'United States',
            'https://www.diabetestechnology.org/',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- University of Nebraska Medical Center
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'University of Nebraska Medical Center'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'University of Nebraska Medical Center',
            '{
                "GRID": "grid.266813.8",
                "ISNI": "0000 0001 0666 4105",
                "Crossref Funder ID": "100006518",
                "Wikidata": "Q7895888"
            }'::jsonb,
            'Centro Médico de la Universidad de Nebraska, UNMC',
            'FND, HCF',
            'Funder, Healthcare',
            'Omaha',
            NULL,
            'United States',
            'https://www.unmc.edu',
            NULL,
            NULL,
            NULL
        );
    END IF;

    -- University of Pavia
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'University of Pavia'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'University of Pavia',
            '{
                "ROR": "https://ror.org/00s6t1f81",
                "GRID": "grid.8982.b",
                "ISNI": "0000 0004 1762 5736",
                "Crossref Funder ID": "501100004769",
                "Wikidata": "Q219317"
            }'::jsonb,
            'UNIPV',
            'EDU, FND',
            'Education, Funder',
            'Pavia',
            NULL,
            'Italy',
            'http://www.unipv.eu/site/en/home.html',
            NULL,
            NULL,
            NULL
        );
    END IF;


     -- Netspective
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'Netspective'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'Netspective',
            null,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL
        );
    END IF;


    -- Netspective Infrastructure
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'Netspective Infrastructure'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'Netspective Infrastructure',
            null,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL
        );
    END IF;

END;
$procedure$
;


CALL drh_stateless_research_study.seed_organizations();


CREATE OR REPLACE PROCEDURE drh_stateless_research_study.practitioners_synthetic_data()
LANGUAGE plpgsql
SECURITY DEFINER
AS $procedure$
BEGIN
    -- Seed practitioner: David Klonoff
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'David Klonoff' AND system_identifier = '0000-0001-6394-6862'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'David Klonoff',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'Diabetes Technology Society' LIMIT 1), -- p_organization_party_id
            '0000-0001-6394-6862',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    -- Seed practitioner: Boris Kovatchev
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Boris Kovatchev' AND system_identifier = '0000-0003-0495-3901'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Boris Kovatchev',                        -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],     -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-0495-3901',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    -- Seed practitioner: Chiara Fabris
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Chiara Fabris' AND system_identifier = '0000-0002-2094-5154'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Chiara Fabris',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-2094-5154',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    -- Seed practitioner: Riccardo Bellazzi
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Riccardo Bellazzi' AND system_identifier = '0000-0002-6974-9808'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Riccardo Bellazzi',                      -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],   -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Pavia' LIMIT 1), -- p_organization_party_id
            '0000-0002-6974-9808',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    -- Seed practitioner: Eric Williams
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Eric Williams' AND system_identifier = '0009-0002-8700-6722'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Eric Williams',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'Lurie Children''s Hospital' LIMIT 1), -- p_organization_party_id
            '0009-0002-8700-6722',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

END;
$procedure$;

-- CALL drh_stateless_research_study.practitioners_synthetic_data();

DROP FUNCTION IF EXISTS drh_stateless_raw_observation.insert_cgm_raw_and_extract_to_observation(
    p_study_id text,
    p_participant_id text,
    p_org_party_id text,
    p_createdby text,
    p_file_size text,
    p_file_metadata jsonb,
    p_cgm_data jsonb,
    p_cgm_raw_data_csv bytea,
    p_cgm_raw_data_excel bytea,
    p_cgm_raw_data_xml xml,
    p_cgm_raw_data_text bytea
);
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.insert_cgm_raw_and_extract_to_observation(
    p_study_id text,
    p_participant_id text,
    p_org_party_id text,
    p_createdby text,
    p_file_size text,
    p_file_metadata jsonb,
    p_cgm_data jsonb,
    p_cgm_raw_data_csv bytea,
    p_cgm_raw_data_excel bytea,
    p_cgm_raw_data_xml xml,
    p_cgm_raw_data_text bytea,
    p_activity_json jsonb
);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.insert_cgm_raw_and_extract_to_observation(p_study_id text, p_participant_id text, p_org_party_id text, p_createdby text, p_file_size text, p_file_metadata jsonb, p_cgm_data jsonb, p_cgm_raw_data_csv bytea DEFAULT NULL::bytea, p_cgm_raw_data_excel bytea DEFAULT NULL::bytea, p_cgm_raw_data_xml xml DEFAULT NULL::xml, p_cgm_raw_data_text bytea DEFAULT NULL::bytea,p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    study_id TEXT;
    participant_sid TEXT;
    tenant_id TEXT;
    created_by TEXT;
    p_cgm_raw_file_id TEXT;
    p_cgm_raw_data_id TEXT;
    result JSONB := jsonb_build_object('status', 'failure', 'message', 'Error occurred during CGM data insertion');  -- Initialize result to failure
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;

    function_name text := 'drh_stateless_raw_observation.insert_cgm_raw_and_extract_to_observation'; 
    current_query text := pg_catalog.current_query(); 

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;

BEGIN
    -- Debug: Start of the function
    RAISE NOTICE 'Starting insert_cgm_raw_and_extract_to_observation for study_id: %, participant_id: %, org_party_id: %', p_study_id, p_participant_id, p_org_party_id;

    -- Fetch Tenant ID
    SELECT opv.organization_id INTO tenant_id 
    FROM drh_stateless_research_study.organization_party_view opv
    WHERE opv.organization_party_id = p_org_party_id
    LIMIT 1;

    -- Debug: Tenant ID fetched
    RAISE NOTICE 'Fetched tenant_id: %', tenant_id;

    -- Insert into cgm_raw_upload_data table
    INSERT INTO drh_stateful_raw_data.cgm_raw_upload_data (
        cgm_raw_file_id,
        file_name,
        zip_file_id,
        cgm_raw_data_json,
        upload_timestamp,
        uploaded_by,
        file_size,
        is_processed,
        processed_at,
        status,
        file_metadata,
        file_type,
        study_id,
        tenant_id,
        rec_status_id,
        created_at,
        created_by,
        updated_at,
        updated_by,
        deleted_at,
        deleted_by,
        cgm_raw_data_csv,
        cgm_raw_data_excel,
        cgm_raw_data_xml,
        cgm_raw_data_text
    )
    VALUES (
        drh_stateless_util.get_unique_id(),
        (p_file_metadata->>'file_name')::text,
        NULL,
        NULL,
        CURRENT_TIMESTAMP,
        p_createdby,
        p_file_size,
        false,
        NULL,
        NULL,
        p_file_metadata,
        (p_file_metadata->>'file_format')::text,
        p_study_id,
        tenant_id,
        (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        p_createdby,
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        null,
        null,
        NULL) 
       RETURNING cgm_raw_file_id INTO p_cgm_raw_file_id;

    -- Debug: cgm_raw_file_id generated
    RAISE NOTICE 'Generated cgm_raw_file_id: %', p_cgm_raw_file_id;

    -- Insert into raw_cgm_extract_data table
    INSERT INTO drh_stateful_raw_data.raw_cgm_extract_data (
        cgm_raw_data_id,
        raw_file_id,
        study_id,
        participant_sid,  
        cgm_raw_data_json,
        file_meta_data,
        cgm_data,
        tenant_id,
        rec_status_id,
        created_at,
        created_by,
        cgm_raw_data_csv,
        cgm_raw_data_excel,
        cgm_raw_data_text,
        cgm_raw_data_xml
    ) 
    VALUES (
        drh_stateless_util.get_unique_id(),  
        p_cgm_raw_file_id,
        p_study_id,
        p_participant_id,
        p_cgm_data,
        p_file_metadata,
        p_cgm_data,
        tenant_id,
        (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        p_createdby,
        NULL, 
        NULL, 
        NULL, 
        NULL
    ) RETURNING cgm_raw_data_id INTO p_cgm_raw_data_id;

    -- Debug: cgm_raw_data_id generated
    RAISE NOTICE 'Generated cgm_raw_data_id: %', p_cgm_raw_data_id;

    -- Call the function to save CGM observation and handle success or failure
    BEGIN
        PERFORM drh_stateless_raw_observation.save_cgm_observation(p_cgm_raw_data_id, p_createdby);  

        -- Activity log feature
        IF p_activity_json IS NOT NULL AND p_activity_json ? 'session_id' THEN
            -- Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='INSERT_CGM_RAW_AND_EXTRACT' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Insert CGM Raw and Extract',
                'activity_description', format('Inserted CGM raw data for study_id %s, participant_id %s', p_study_id, p_participant_id)
            );

            -- Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_createdby, v_activity_log_json);
        END IF;
        
        -- If the inner function succeeds, modify result for success
        result := jsonb_build_object(
            'status', 'success',
            'message', 'CGM data inserted and processed successfully'
        );
        RAISE NOTICE 'Function executed successfully, returning result: %', result;

        RETURN result;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details from the inner function
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log error details for the inner function
        INSERT INTO drh_stateful_activity_audit.exception_log (
            function_name,
            error_code,
            error_message,
            error_detail,
            error_hint,
            error_context,
            query,
            parameters,
            occurred_at,
            resolved,
            resolved_at,
            resolver_comments
        )
        VALUES (
            'save_cgm_observation',  
            err_state,
            err_message,
            err_detail,
            err_hint,
            err_context,
            current_query,
            jsonb_build_object(
                'p_cgm_raw_data_id', p_cgm_raw_data_id,
                'p_createdby', p_createdby
            ),
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            'No',
            NULL,
            NULL
        );

        -- Modify result for failure if inner function fails
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during CGM observation save',
            'error_details', jsonb_build_object(
                'error', err_message,
                'detail', err_detail,
                'hint', err_hint,
                'context', err_context,
                'state', err_state
            )
        );

        RETURN result;
    END;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details from the outer function
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log error details for the outer function
    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name,
        error_code,
        error_message,
        error_detail,
        error_hint,
        error_context,
        query,
        parameters,
        occurred_at,
        resolved,
        resolved_at,
        resolver_comments
    )
    VALUES (
        'insert_cgm_raw_upload_data',  
        err_state,
        err_message,
        err_detail,
        err_hint,
        err_context,
        current_query,
        jsonb_build_object(
            'p_cgm_raw_file_id', p_cgm_raw_file_id,
            'p_uploaded_by', p_createdby
        ),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        'No',
        NULL,
        NULL
    );

    -- Debug: Error occurred
    RAISE NOTICE 'Error occurred: %', err_message;

    -- Modify result for failure if outer function fails
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during CGM data insertion',
        'error_details', jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        )
    );

    RETURN result;
END;
$function$
;
-- DROP PROCEDURE drh_stateless_research_study.practitioners_synthetic_data_v1();

CREATE OR REPLACE PROCEDURE drh_stateless_research_study.practitioners_synthetic_data_v1()
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
BEGIN


    -- create organization University of Virginia
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.organization WHERE name = 'University of Virginia'
    ) THEN
        PERFORM drh_stateless_research_study.create_organization(
            'University of Virginia',
            '{"ROR": "https://ror.org/04ezjnq35"}'::jsonb,
            'UVA',
            'EDU, FND',
            'Education, Funder',
            'Charlottesville',
            NULL,
            'United States',
            'https://virginia.edu',
            NULL,
            NULL,
            NULL
        );
    END IF;  
    -- Seed practitioner: Stacey M. Anderson
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'David Klonoff' AND system_identifier = '0000-0002-8994-1514'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Stacey M. Anderson',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-8994-1514',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

   -- Seed practitioner: John Lum
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'John Lum' AND system_identifier = '0000-0002-1045-0625'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'John Lum',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-1045-0625',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

 -- Seed practitioner: Yogish C. Kudva
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Yogish C. Kudva' AND system_identifier = '0000-0003-0285-6622'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Yogish C. Kudva',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-0285-6622',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

 -- Seed practitioner: Lori M. Laffel
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Lori M. Laffel' AND system_identifier = '0000-0002-9675-3001'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Lori M. Laffel',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9675-3001',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


 -- Seed practitioner: Carol Levy
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Carol Levy' AND system_identifier = '0000-0002-3492-2578'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Carol Levy',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-3492-2578',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;
    
     -- Seed practitioner: Jordan E. Pinsker
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Jordan E. Pinsker' AND system_identifier = '0009-0004-7621-5267'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Jordan E. Pinsker',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0009-0004-7621-5267',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;
    
    
     -- Seed practitioner: R. Paul Wadwa
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'R. Paul Wadwa' AND system_identifier = '0000-0002-4139-2122'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'R. Paul Wadwa',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-4139-2122',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

       -- Seed practitioner: Bruce Buckingham
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Bruce Buckingham' AND system_identifier = '0000-0003-4581-4887'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Bruce Buckingham',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-4581-4887',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

           -- Seed practitioner: Francis J. Doyle III
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Francis J. Doyle III' AND system_identifier = '0000-0001-7558-3130'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Francis J. Doyle III',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-7558-3130',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    -- Seed practitioner: Francis J. Doyle III
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Francis J. Doyle III' AND system_identifier = '0000-0001-7558-3130'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Francis J. Doyle III',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-7558-3130',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

        -- Seed practitioner: Sue A. Brown
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Sue A. Brown' AND system_identifier = '0000-0002-9484-4428'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Sue A. Brown',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9484-4428',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


          -- Seed practitioner: Mei Mei Church
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Mei Mei Church' AND system_identifier = '0000-0003-3393-2042'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Mei Mei Church',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3393-2042',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;



    -- Seed practitioner: Laya Ekhlaspour
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Laya Ekhlaspour' AND system_identifier = '0000-0001-5333-6892'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Laya Ekhlaspour',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-5333-6892',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

      -- Seed practitioner: Gregory P. Forlenza
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Gregory P. Forlenza' AND system_identifier = '0000-0003-3607-9788'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Gregory P. Forlenza',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3607-9788',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


          -- Seed practitioner: Elvira Isganaitis
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Elvira Isganaitis' AND system_identifier = '0000-0003-3477-0305'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Elvira Isganaitis',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3477-0305',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


    -- Seed practitioner: David W. Lam
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'David W. Lam' AND system_identifier = '0009-0008-2348-0226'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'David W. Lam',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0009-0008-2348-0226',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


        -- Seed practitioner: Roy W. Beck
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Roy W. Beck' AND system_identifier = '0000-0003-3121-4530'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Roy W. Beck',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3121-4530',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


 
    -- Seed practitioner: Federico Boscari
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Federico Boscari' AND system_identifier = '0000-0002-9670-615X'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Federico Boscari',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9670-615X',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


     
    -- Seed practitioner: Eric Renard
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Eric Renard' AND system_identifier = '0000-0002-3407-7263'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Eric Renard',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-3407-7263',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


    -- Seed practitioner: Bruce A Buckingham
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Bruce A Buckingham' AND system_identifier = '0000-0003-4581-4887'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Bruce A Buckingham',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-4581-4887',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


       -- Seed practitioner: Revital Nimri
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Revital Nimri' AND system_identifier = '0000-0003-3571-4938'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Revital Nimri',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3571-4938',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


           -- Seed practitioner: Sue A Brown
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Sue A Brown' AND system_identifier = '0000-0002-9484-4428'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Sue A Brown',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9484-4428',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

           -- Seed practitioner: Patrick Keith-Hynes
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Patrick Keith-Hynes' AND system_identifier = '0000-0001-6383-8909'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Patrick Keith-Hynes',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-6383-8909',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


    -- Seed practitioner: Daniel Chernavvsky
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Daniel Chernavvsky' AND system_identifier = '0000-0002-8488-9016'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Daniel Chernavvsky',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-7395-1841',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


    -- Seed practitioner: Wendy C Bevier
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Wendy C Bevier' AND system_identifier = '0000-0003-1355-3432'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Wendy C Bevier',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-1355-3432',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    
    -- Seed practitioner: Paige K Bradley
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Paige K Bradley' AND system_identifier = '0000-0002-3213-4957'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Paige K Bradley',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-3213-4957',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    -- Seed practitioner: Daniela Bruttomesso
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Daniela Bruttomesso' AND system_identifier = '0000-0002-2426-8955'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Daniela Bruttomesso',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-2426-8955',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

       -- Seed practitioner: Simone Del Favero
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Simone Del Favero' AND system_identifier = '0000-0002-8214-2752'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Simone Del Favero',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-8214-2752',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


    -- Seed practitioner: Roberta Calore
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Roberta Calore' AND system_identifier = '0000-0003-1263-9250'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Roberta Calore',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-1263-9250',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

        -- Seed practitioner: Claudio Cobelli
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Claudio Cobelli' AND system_identifier = '0000-0002-0169-6682'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Claudio Cobelli',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-0169-6682',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


     -- Seed practitioner: Angelo Avogaro
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Angelo Avogaro' AND system_identifier = '0000-0002-1177-0516'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Angelo Avogaro',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-1177-0516',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


         -- Seed practitioner: Anne Farret
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Anne Farret' AND system_identifier = '0000-0003-3334-5040'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Anne Farret',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3334-5040',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


             -- Seed practitioner: Jerome Place
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Jerome Place' AND system_identifier = '0000-0002-4820-0074'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Jerome Place',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-4820-0074',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


    -- Seed practitioner: Trang T Ly
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Trang T Ly' AND system_identifier = '0000-0002-3186-4034'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Trang T Ly',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
           '0000-0002-3186-4034',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;


   -- Seed practitioner: Moshe Phillip
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Moshe Phillip' AND system_identifier = '0000-0002-6616-5612'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Moshe Phillip',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
           '0000-0002-6616-5612',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
    END IF;

    -- Seed practitioner: Craig Kollman
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Craig Kollman' AND system_identifier = '0009-0008-6696-0334'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Craig Kollman',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0009-0008-6696-0334',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

        -- Seed practitioner: G.P. Forlenza
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'G.P. Forlenza' AND system_identifier = '0000-0003-3607-9788'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'G.P. Forlenza',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3607-9788',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

        -- Seed practitioner: E. Isganaitis
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'E. Isganaitis' AND system_identifier = '0000-0003-3477-0305'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'E. Isganaitis',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3477-0305',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


         -- Seed practitioner: Luis Vigil
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Luis Vigil' AND system_identifier = '0000-0001-7484-2911'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Luis Vigil',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-7484-2911',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


             -- Seed practitioner: Borja Vargas
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Borja Vargas' AND system_identifier = '0000-0002-5553-8372'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Borja Vargas',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-5553-8372',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


                    -- Seed practitioner: David Cuesta–Frau
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'David Cuesta–Frau' AND system_identifier = '0000-0002-0076-0515'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'David Cuesta–Frau',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-0076-0515',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        -- Seed practitioner: Manuel Varela
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Manuel Varela' AND system_identifier = '0000-0001-8667-7853'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Manuel Varela',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-8667-7853',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        


             -- Seed practitioner:Nelly Mauras, MD
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Nelly Mauras, MD' AND system_identifier = '0000-0003-1175-3852'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Nelly Mauras, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-1175-3852',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


             
               -- Seed practitioner:William V. Tamborlane, MD
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'William V. Tamborlane, MD' AND system_identifier = '0000-0002-9928-559X'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'William V. Tamborlane, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9928-559X',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        -- Seed practitioner:Kathleen F. Janz, EdD
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Kathleen F. Janz, EdD' AND system_identifier = '0000-0002-2537-0601'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Kathleen F. Janz, EdD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-2537-0601',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        -- Seed practitioner:H. Peter Chase, MD
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'H. Peter Chase, MD' AND system_identifier = '0009-0003-7571-1018'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'H. Peter Chase, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0009-0003-7571-1018',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


                -- Seed practitioner:Tim Wysocki, PhD, ABPP
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Tim Wysocki, PhD, ABPP' AND system_identifier = '0000-0003-4099-4639'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Tim Wysocki, PhD, ABPP',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-4099-4639',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

        -- Seed practitioner:Stuart A. Weinzimer, MD
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Stuart A. Weinzimer, MD' AND system_identifier = '0000-0002-7768-5189'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Stuart A. Weinzimer, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-7768-5189',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


          -- Seed practitioner:Craig Kollman, PhD
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Craig Kollman, PhD' AND system_identifier = '0009-0008-6696-0334'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Craig Kollman, PhD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0009-0008-6696-0334',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


                -- Seed practitioner:Dongyuan Xing, MPH
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Dongyuan Xing, MPH' AND system_identifier = '0000-0001-8242-1332'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Dongyuan Xing, MPH',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-8242-1332',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



                -- Seed practitioner:Rosanna Fiallo-Scharer, MD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Rosanna Fiallo-Scharer, MD' AND system_identifier = '0000-0002-8939-4784'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Rosanna Fiallo-Scharer, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-8939-4784',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



      -- Seed practitioner:Jennifer H. Fisher, ND, RN

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Jennifer H. Fisher, ND, RN' AND system_identifier = '0000-0003-3421-3947'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Jennifer H. Fisher, ND, RN',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3421-3947',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



      -- Seed practitioner:Eva Tsalikian, MD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Eva Tsalikian, MD' AND system_identifier = '0000-0003-3429-2796'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Eva Tsalikian, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-3429-2796',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


            -- Seed practitioner:Michael J. Tansey, MD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Michael J. Tansey, MD' AND system_identifier = '0000-0003-4450-9418'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Michael J. Tansey, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-4450-9418',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



                    -- Seed practitioner:Linda F. Larson, RN

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Linda F. Larson, RN' AND system_identifier = '0000-0002-6003-0565'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Linda F. Larson, RN',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-6003-0565',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        -- Seed practitioner:Julie Coffey, MSN

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Julie Coffey, MSN' AND system_identifier = '0000-0002-9467-6161'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Julie Coffey, MSN',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9467-6161',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

         -- Seed practitioner:Larry A. Fox, MD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Larry A. Fox, MD' AND system_identifier = '0000-0002-2254-6560'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Larry A. Fox, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-2254-6560',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


     -- Seed practitioner:Bruce A. Buckingham, MD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Bruce A. Buckingham, MD' AND system_identifier = '0000-0003-4581-4887'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Bruce A. Buckingham, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-4581-4887',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



     -- Seed practitioner:Darrell M. Wilson, MD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Darrell M. Wilson, MD' AND system_identifier = '0000-0001-9589-6011'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Darrell M. Wilson, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-9589-6011',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        -- Seed practitioner:Paula Clinton, RD, CDE

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Paula Clinton, RD, CDE' AND system_identifier = '0000-0002-0999-4254'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Paula Clinton, RD, CDE',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-0999-4254',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        -- Seed practitioner:Elizabeth A. Doyle, MSN

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Elizabeth A. Doyle, MSN' AND system_identifier = '0000-0002-9682-2513'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Elizabeth A. Doyle, MSN',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9682-2513',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        

        -- Seed practitioner:Michael W. Steffes, MD, PhD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Michael W. Steffes, MD, PhD' AND system_identifier = '0000-0001-6600-271X'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Michael W. Steffes, MD, PhD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-6600-271X',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


              


        -- Seed practitioner:Carol A. Van Hale, CLS

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Carol A. Van Hale, CLS' AND system_identifier = '0000-0003-0477-4372'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Carol A. Van Hale, CLS',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-0477-4372',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

          -- Seed practitioner:Barbara Linder, MD, PhD


        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Barbara Linder, MD, PhD' AND system_identifier = '0000-0002-6489-9065'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Barbara Linder, MD, PhD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-6489-9065',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

            -- Seed practitioner:Karen K. Winer, MD


        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Karen K. Winer, MD' AND system_identifier = '0000-0001-8261-5188'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Karen K. Winer, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-8261-5188',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

              -- Seed practitioner:Dorothy M. Becker, MBBCh

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Dorothy M. Becker, MBBCh' AND system_identifier = '0000-0002-1311-351X'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Dorothy M. Becker, MBBCh',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-1311-351X',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


        -- Seed practitioner:Christopher Cox, PhD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Christopher Cox, PhD' AND system_identifier = '0000-0002-9568-6994'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Christopher Cox, PhD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-9568-6994',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

          -- Seed practitioner:Christopher M. Ryan, PhD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Christopher M. Ryan, PhD' AND system_identifier = '0000-0002-1032-3829'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Christopher M. Ryan, PhD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-1032-3829',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

        -- Seed practitioner:Neil H. White, MD, CDE

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Neil H. White, MD, CDE' AND system_identifier = '0000-0002-4705-2321'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Neil H. White, MD, CDE',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-4705-2321',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

            -- Seed practitioner:Perrin C. White, MD

        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Perrin C. White, MD' AND system_identifier = '0000-0001-6262-0289'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Perrin C. White, MD',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0001-6262-0289',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



        

             -- Seed practitioner:Alessandro Bisio
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Alessandro Bisio' AND system_identifier = '0000-0003-1530-3111'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Alessandro Bisio',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-1530-3111',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



             -- Seed practitioner:Lisa Norlander
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Lisa Norlander' AND system_identifier = '0000-0003-1530-3111'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Lisa Norlander',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-1530-3111',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

            -- Seed practitioner:Grenye O Malley
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Grenye O Malley' AND system_identifier = '0000-0003-1530-3111'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Grenye O Malley',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-1530-3111',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;
        

        
                      -- Seed practitioner:Selassie Ogyaadu
        IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Selassie Ogyaadu' AND system_identifier = '0000-0002-8923-158X'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Selassie Ogyaadu',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-8923-158X',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

  
  -- Seed practitioner:Marc D. Breton
          IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Marc D. Breton' AND system_identifier = '0000-0002-8488-9016'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Marc D. Breton',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-8488-9016',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


  


          -- Seed practitioner:Boris P Kovatchev
          IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Boris P Kovatchev' AND system_identifier = '0000-0003-0495-3901'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Boris P Kovatchev',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-0495-3901',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



          -- Seed practitioner:Laura Kollar
          IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Laura Kollar' AND system_identifier = '0009-0005-8131-5147'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Laura Kollar',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0009-0005-8131-5147',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;




          -- Seed practitioner:Charlotte Barnett
          IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Charlotte Barnett' AND system_identifier = '0000-0003-4615-7778'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Charlotte Barnett',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-4615-7778',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;



        



          -- Seed practitioner:Kelly Carr
          IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Kelly Carr' AND system_identifier = '0000-0002-8911-3161'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Kelly Carr',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-8911-3161',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;


          -- Seed practitioner:Christian A Wakeman
          IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Christian A Wakeman' AND system_identifier = '0000-0003-1884-8942'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Christian A Wakeman',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0003-1884-8942',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

       -- Seed practitioner:Sooy M
          IF NOT EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE name = 'Sooy M' AND system_identifier = '0000-0002-6598-8828'
        ) THEN
            PERFORM drh_stateless_research_study.create_practitioner_profile(
                'Sooy M',                          -- p_name
                'ORCiD',                                  -- p_auth_system
                ARRAY['<EMAIL>'],       -- p_email
                (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
                WHERE opv.organization_name = 'University of Virginia' LIMIT 1), -- p_organization_party_id
            '0000-0002-6598-8828',                    -- p_auth_system_identifier
                NULL                                      -- p_createdby
            );
        END IF;

   
       
END;
$procedure$;


 -- CALL drh_stateless_research_study.practitioners_synthetic_data_v1();




 CREATE OR REPLACE PROCEDURE drh_stateless_research_study.practitioners_synthetic_data_v3()
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $procedure$
DECLARE
        uva_org_id text;
BEGIN
    
    
    -- Seed practitioner: Eric Williams
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Eric Williams' AND system_identifier = '0009-0002-8700-6722'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Eric Williams',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'Lurie Children''s Hospital' LIMIT 1), -- p_organization_party_id
            '0009-0002-8700-6722',                    -- p_auth_system_identifier
            NULL                                      -- p_createdby
        );
       RAISE NOTICE 'Inserted: Eric Williams';
    END IF;
      
    -- Lawerence (GitHub)
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Lawerence' AND system_identifier = 'LawerenceLett'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Lawerence',
            'GitHub',
            ARRAY['<EMAIL>'],
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'Lurie Children''s Hospital' LIMIT 1),
            null,
            'LawerenceLett'
        );
       RAISE NOTICE 'Inserted: lawrence ';
    END IF;

    
    -- Juan Espinoza (GitHub)
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'Juan Espinoza' AND system_identifier = 'juanespinoza1'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'Juan Espinoza',
            'GitHub',
            ARRAY['<EMAIL>'],
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'Lurie Children''s Hospital' LIMIT 1),
            null,
            'juanespinoza1'
        );
    END IF;


    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_research_study.practitioner
        WHERE name = 'David Klonoff' AND system_identifier = '0000-0001-6394-6862'
    ) THEN
        PERFORM drh_stateless_research_study.create_practitioner_profile(
            'David Klonoff',                          -- p_name
            'ORCiD',                                  -- p_auth_system
            ARRAY['<EMAIL>'],       -- p_email
            (SELECT opv.organization_party_id FROM drh_stateless_research_study.organization_party_view opv 
             WHERE opv.organization_name = 'Diabetes Technology Society' LIMIT 1), -- p_organization_party_id
            '0000-0001-6394-6862',                    
            NULL                                     
        );
    END IF;

END;
$procedure$
;


CALL drh_stateless_research_study.practitioners_synthetic_data_v3();



CREATE OR REPLACE FUNCTION drh_stateless_authentication.create_vanna_user_account(p_fullname text, p_email text, p_organization_name text, p_password text, p_is_pass_encrypted boolean, p_role_name text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    user_account_id TEXT;
    v_user_party_id text;
    p_createdby TEXT;
    org_id TEXT;
    org_party_id text;
    v_active_status_id INT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.create_vanna_user_account';
    current_query TEXT := pg_catalog.current_query();
BEGIN
    IF p_email IS NULL OR p_email = '' THEN
        RETURN jsonb_build_object('status', 'error', 'message', 'Email address is required.');
    END IF;

    IF EXISTS (
        SELECT 1 FROM drh_stateful_authentication.user_account 
        WHERE email = p_email
    ) THEN
        RETURN jsonb_build_object('status', 'error', 'message', 'Email already exists.');
    END IF;

    SELECT op.organization_id , op.organization_party_id INTO org_id,org_party_id
	FROM drh_stateless_research_study.organization_party_view op
	WHERE organization_name = p_organization_name;
       


    IF org_id IS NULL THEN
        RETURN jsonb_build_object('status', 'error', 'message', 'Organization not found.');
    END IF;

    SELECT rs.value INTO v_active_status_id 
    FROM drh_stateful_party.record_status rs 
    WHERE rs.code = 'ACTIVE';

    BEGIN
        INSERT INTO drh_stateful_party.party (
            party_id, party_type_id, party_name, created_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            (SELECT party_type_id FROM drh_stateful_party.party_type WHERE code = 'PERSON'),
            p_fullname,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        ) returning party_id  into v_user_party_id;

        p_createdby := v_user_party_id;

        INSERT INTO drh_stateful_authentication.user_account (
            user_id, username, email, first_name, profile_status, rec_status_id, created_at, created_by, party_id
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            p_email,
            p_email,
            p_fullname,
            (SELECT profile_status_type_id FROM drh_stateless_master.profile_status_type_view WHERE code = 'COMPLETE' LIMIT 1),
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby,
            v_user_party_id
        )
        RETURNING user_id INTO user_account_id;

        INSERT INTO drh_stateful_authentication.user_credentials (
            id, user_id, password_hash, password_salt, password_updated_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            CASE
                WHEN p_is_pass_encrypted THEN p_password
                ELSE drh_stateless_util.crypt(p_password, drh_stateless_util.gen_salt('bf'))
            END,
            'bcrypt',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        INSERT INTO drh_stateful_authentication.auth_mappings (
            id, user_id, auth_provider, provider_user_id, access_token,
            refresh_token, status, rec_status_id, created_at, updated_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            'vanna',
            p_email,
            '', '', 'ACTIVE',
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        INSERT INTO drh_stateful_research_study.telecom (
            id, party_id, telecom_type, telecom_value, contact_point_system_id, contact_point_use_type_id, tenant_id, rec_status_id, created_at, created_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(), 
            v_user_party_id,
            'email', 
            p_email,
            (SELECT id FROM drh_stateless_master.contact_point_system_view WHERE code = 'email' LIMIT 1), 
            (SELECT id FROM drh_stateless_master.contact_point_use_view WHERE code = 'work' LIMIT 1),
            org_id, 
            v_active_status_id,  
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
            p_createdby
        );

        INSERT INTO drh_stateful_authentication.user_role (
            user_role_id, user_id, role_id, rec_status_id,
            created_at, created_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            (SELECT role_id FROM drh_stateful_master.role WHERE role_name = p_role_name LIMIT 1),
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby
        );

        RETURN jsonb_build_object(
            'status', 'success',
            'message', 'Vanna user profile successfully created.',
            'party_id', v_user_party_id,
            'user_account_id', user_account_id,
            'organization_party_id', org_party_id
        );
    END;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name, error_code, error_message, error_detail,
        error_hint, error_context, query, parameters,
        occurred_at, resolved
    )
    VALUES (
        function_name, err_state, err_message, err_detail,
        err_hint, err_context, current_query, NULL,
        CURRENT_TIMESTAMP, 'No'
    );

    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during Vanna user profile creation',
        'error_details', error_details_json
    );
END;
$function$
;

CREATE OR REPLACE PROCEDURE drh_stateless_authentication.seed_vanna_users()
LANGUAGE plpgsql
AS $$
BEGIN
    -- Admin 1
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_authentication.user_account WHERE email = '<EMAIL>'
    ) THEN
        PERFORM drh_stateless_authentication.create_vanna_user_account(
            'drh_ai_vienna',
            '<EMAIL>',
            'Netspective',
            'Z7p@3xLv#qRt',
            FALSE,
            'vanna_user_admin'
        );
    END IF;

    -- Admin 2
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_authentication.user_account WHERE email = '<EMAIL>'
    ) THEN
        PERFORM drh_stateless_authentication.create_vanna_user_account(
            'drh_netspective_admin',
            '<EMAIL>',
            'Netspective',
            'm$K9!vAz2B#w',
            FALSE,
            'vanna_user_admin'
        );
    END IF;

    -- Normal User
    IF NOT EXISTS (
        SELECT 1 FROM drh_stateful_authentication.user_account WHERE email = '<EMAIL>'
    ) THEN
        PERFORM drh_stateless_authentication.create_vanna_user_account(
            'drh_netspective_normal',
            '<EMAIL>',
            'Netspective',
            'Tg8^uR#1XpLj',
            FALSE,
            'vanna_user_normal'
        );
    END IF;
END;
$$;


CALL drh_stateless_authentication.seed_vanna_users();


CREATE OR REPLACE VIEW drh_stateless_vanna.vanna_user_profile_view
AS
SELECT
    ua.user_id,
    ua.party_id,    
    ua.username,
    uc.password_hash,     
    uc.password_salt ,
    uc.failed_login_attempts ,
    uc.is_locked ,
    uc.last_failed_login ,
    (select role_name from drh_stateful_master.role where role_id = ur.role_id limit 1)as user_role,
    ua.profile_status AS profile_status_id,
    (
        SELECT pstv.code
        FROM drh_stateful_master.profile_status_type pstv
        WHERE pstv.profile_status_type_id = ua.profile_status
        LIMIT 1
    ) AS profile_status   
FROM drh_stateful_authentication.user_account ua
JOIN drh_stateful_party.party pt ON pt.party_id = ua.party_id
join drh_stateful_authentication.user_credentials uc  on uc.user_id =ua.user_id 
join drh_stateful_authentication.user_role ur  on ur.user_id = ua.user_id 
JOIN drh_stateful_authentication.auth_mappings em
       ON em.user_id = ua.user_id AND em.auth_provider = 'vanna'
WHERE pt.party_type_id = (
    SELECT party_type_id
    FROM drh_stateful_party.party_type
    WHERE code = 'PERSON'
);
