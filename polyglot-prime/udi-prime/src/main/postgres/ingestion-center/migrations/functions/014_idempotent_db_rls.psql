------------------------create authenticated db role------------------------------
--authenticated: an authenticated request (the user is logged in)

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'authenticated') THEN
        CREATE ROLE "authenticated" WITH NOLOGIN;
    END IF;
END $$;

 DO $$ 
DECLARE schema_name TEXT;
BEGIN 
    FOR schema_name IN (SELECT s.schema_name FROM information_schema.schemata s 
                        WHERE s.schema_name NOT LIKE 'pg_%' AND s.schema_name <> 'information_schema') 
    LOOP  
		EXECUTE format('REVOKE USAGE ON SCHEMA %I FROM authenticated', schema_name);       
 		EXECUTE format('REVOKE ALL ON ALL TABLES IN SCHEMA %I FROM authenticated', schema_name);
		EXECUTE format('REVOKE ALL ON ALL SEQUENCES IN SCHEMA %I FROM authenticated', schema_name);
		EXECUTE format('REVO<PERSON> ALL ON ALL FUNCTIONS IN SCHEMA %I FROM authenticated', schema_name);
    END LOOP; 
END $$;

DO $$ 
DECLARE 
    schema_name TEXT;
    schema_names TEXT[] := ARRAY[
        'drh_stateful_activity_audit',
        'drh_stateful_master', 
        'drh_stateful_party', 
        'drh_stateful_raw_data', 
        'drh_stateful_raw_observation', 
        'drh_stateful_research_study',
        'drh_stateful_db_import_migration', 
        'drh_stateful_authentication',
        'drh_stateless_activity_audit', 
        'drh_stateless_authentication',
        'drh_stateless_db_import_migration',
        'drh_stateless_master',
        'drh_stateless_raw_data',
        'drh_stateless_raw_observation',
        'drh_stateless_research_study',
        'drh_stateless_util'
    ];
BEGIN 
    FOREACH schema_name IN ARRAY schema_names
    LOOP 
        EXECUTE format('GRANT USAGE ON SCHEMA %I TO authenticated', schema_name);
    END LOOP; 
END $$;

DO $$ 
DECLARE 
    schema_name TEXT;
    schema_names TEXT [] := ARRAY[
        'drh_stateful_activity_audit',
        'drh_stateful_master', 
        'drh_stateful_party', 
        'drh_stateful_raw_data', 
        'drh_stateful_raw_observation', 
        'drh_stateful_research_study',
        'drh_stateful_db_import_migration',
        'drh_stateful_authentication',
        'drh_stateless_activity_audit',
        'drh_stateless_authentication',
        'drh_stateless_db_import_migration',
        'drh_stateless_master',
        'drh_stateless_raw_data',
        'drh_stateless_raw_observation',
        'drh_stateless_research_study',
        'drh_stateless_util'
    ];
BEGIN 
   FOREACH schema_name IN ARRAY schema_names
    LOOP 
        EXECUTE format('GRANT SELECT ON ALL TABLES IN SCHEMA %I TO authenticated', schema_name);		
    END LOOP; 
END $$;

DO $$ 
DECLARE 
    schema_name TEXT;
    schema_names TEXT [] := ARRAY[        
        'drh_stateful_db_import_migration',
        'drh_stateless_activity_audit',
        'drh_stateless_authentication',
        'drh_stateless_db_import_migration',
        'drh_stateless_master',
        'drh_stateless_raw_data',
        'drh_stateless_raw_observation',
        'drh_stateless_research_study',
        'drh_stateless_util'
    ];
BEGIN 
   FOREACH schema_name IN ARRAY schema_names
    LOOP 
        EXECUTE format('GRANT SELECT, INSERT, UPDATE, DELETE, TRUNCATE, REFERENCES, TRIGGER ON ALL TABLES IN SCHEMA %I TO authenticated', schema_name);
		EXECUTE format('GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA  %I TO authenticated', schema_name);
		EXECUTE format('GRANT EXECUTE ON ALL PROCEDURES IN SCHEMA %I TO authenticated', schema_name);
    END LOOP; 
END $$;

DO $$ 
DECLARE 
    schema_name TEXT;
    schema_names TEXT [] := ARRAY[
        'drh_stateful_activity_audit',
        'drh_stateful_master', 
        'drh_stateful_party', 
        'drh_stateful_raw_data', 
        'drh_stateful_raw_observation', 
        'drh_stateful_research_study',
        'drh_stateful_db_import_migration',
        'drh_stateful_authentication',
        'drh_stateless_activity_audit',
        'drh_stateless_authentication',
        'drh_stateless_db_import_migration',
        'drh_stateless_master',
        'drh_stateless_raw_data',
        'drh_stateless_raw_observation',
        'drh_stateless_research_study',
        'drh_stateless_util'
    ];
BEGIN 
    FOREACH schema_name IN ARRAY schema_names
    LOOP 
        EXECUTE format('ALTER DEFAULT PRIVILEGES IN SCHEMA %I GRANT SELECT ON TABLES TO authenticated', schema_name);
    END LOOP; 
END $$;


------------------------create unauthenticated db role------------------------------
-- anon: an unauthenticated request (the user is not logged in)

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'anon') THEN
        CREATE ROLE "anon" WITH NOLOGIN;
    END IF;
END $$;

 DO $$ 
DECLARE schema_name TEXT;
BEGIN 
    FOR schema_name IN (SELECT s.schema_name FROM information_schema.schemata s 
                        WHERE s.schema_name NOT LIKE 'pg_%' AND s.schema_name <> 'information_schema') 
    LOOP    
		EXECUTE format('REVOKE USAGE ON SCHEMA %I FROM anon', schema_name);       
 		EXECUTE format('REVOKE ALL ON ALL TABLES IN SCHEMA %I FROM anon', schema_name);
		EXECUTE format('REVOKE ALL ON ALL SEQUENCES IN SCHEMA %I FROM anon', schema_name);
		EXECUTE format('REVOKE ALL ON ALL FUNCTIONS IN SCHEMA %I FROM anon', schema_name);
    END LOOP; 
END $$;

DO $$ 
DECLARE 
    schema_name TEXT;
    schema_names TEXT[] := ARRAY[
        'drh_stateful_activity_audit',
        'drh_stateful_master', 
        'drh_stateful_party', 
        'drh_stateful_raw_data', 
        'drh_stateful_raw_observation', 
        'drh_stateful_research_study',
        'drh_stateful_db_import_migration', 
        'drh_stateful_authentication',
        'drh_stateless_activity_audit', 
        'drh_stateless_authentication',
        'drh_stateless_db_import_migration',
        'drh_stateless_master',
        'drh_stateless_raw_data',
        'drh_stateless_raw_observation',
        'drh_stateless_research_study',
        'drh_stateless_util'
    ];
BEGIN 
    FOREACH schema_name IN ARRAY schema_names
    LOOP 
        EXECUTE format('GRANT USAGE ON SCHEMA %I TO anon', schema_name);
    END LOOP; 
END $$;

DO $$ 
DECLARE 
    schema_name TEXT;
    schema_names TEXT [] := ARRAY[
        'drh_stateful_activity_audit',
        'drh_stateful_master', 
        'drh_stateful_party', 
        'drh_stateful_raw_data', 
        'drh_stateful_raw_observation', 
        'drh_stateful_research_study',
        'drh_stateful_db_import_migration',
        'drh_stateful_authentication',
        'drh_stateless_activity_audit',
        'drh_stateless_authentication',
        'drh_stateless_db_import_migration',
        'drh_stateless_master',
        'drh_stateless_raw_data',
        'drh_stateless_raw_observation',
        'drh_stateless_research_study',
        'drh_stateless_util'
    ];
BEGIN 
   FOREACH schema_name IN ARRAY schema_names
    LOOP 
        EXECUTE format('GRANT SELECT ON ALL TABLES IN SCHEMA %I TO anon', schema_name);		
    END LOOP; 
END $$;

DO $$ 
DECLARE 
    schema_name TEXT;
    schema_names TEXT [] := ARRAY[
        'drh_stateful_activity_audit',
        'drh_stateful_master', 
        'drh_stateful_party', 
        'drh_stateful_raw_data', 
        'drh_stateful_raw_observation', 
        'drh_stateful_research_study',
        'drh_stateful_db_import_migration',
        'drh_stateful_authentication',
        'drh_stateless_activity_audit',
        'drh_stateless_authentication',
        'drh_stateless_db_import_migration',
        'drh_stateless_master',
        'drh_stateless_raw_data',
        'drh_stateless_raw_observation',
        'drh_stateless_research_study',
        'drh_stateless_util'
    ];
BEGIN 
    FOREACH schema_name IN ARRAY schema_names
    LOOP 
        EXECUTE format('ALTER DEFAULT PRIVILEGES IN SCHEMA %I GRANT SELECT ON TABLES TO anon', schema_name);
    END LOOP; 
END $$;

------------------------Assign permission to app_user------------------------------
/*
CREATE ROLE app_user WITH 
    LOGIN 
    PASSWORD '' 
    NOSUPERUSER -- ensure it’s not a superuser
    NOCREATEDB 
    NOCREATEROLE 
    INHERIT -- allow inheriting privileges of roles it’s a member of
    BYPASSRLS;

    GRANT CONNECT ON DATABASE postgres TO app_user;
*/

DO $$
DECLARE
    schema_name TEXT;
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'app_user') THEN        
            -- Grant specific privileges to the user if needed
            -- Example: Grant CONNECT and USAGE on a specific database
                      
                FOR schema_name IN (SELECT s.schema_name FROM information_schema.schemata s 
                                    WHERE s.schema_name NOT LIKE 'pg_%' AND s.schema_name <> 'information_schema') 
                LOOP 
                    EXECUTE format('GRANT USAGE ON SCHEMA %I TO app_user', schema_name);
                END LOOP; 
            
                FOR schema_name IN (SELECT s.schema_name FROM information_schema.schemata s 
                                    WHERE s.schema_name NOT LIKE 'pg_%' AND s.schema_name <> 'information_schema') 
                LOOP 
                    EXECUTE format('GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA %I TO app_user', schema_name);
                    EXECUTE format('GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA %I TO app_user', schema_name);
                    EXECUTE format('GRANT EXECUTE ON ALL PROCEDURES IN SCHEMA %I TO app_user', schema_name);
                END LOOP; 
            
                FOR schema_name IN (SELECT s.schema_name FROM information_schema.schemata s 
                                    WHERE s.schema_name NOT LIKE 'pg_%' AND s.schema_name <> 'information_schema') 
                LOOP 
                    EXECUTE format('ALTER DEFAULT PRIVILEGES IN SCHEMA %I GRANT SELECT ON TABLES TO app_user', schema_name);
                END LOOP; 

            GRANT anon TO app_user;
            GRANT authenticated TO app_user;
    END IF;
END $$; 

-------------------------Enable RLS on the table------------------

ALTER TABLE drh_stateful_research_study.research_study ENABLE ROW LEVEL SECURITY;

------------------------study policy------------------------------

-- DROP FUNCTION IF EXISTS drh_stateless_util.check_research_study_access(text, int4,text);

CREATE OR REPLACE FUNCTION drh_stateless_util.check_research_study_access(study_creator text,visibility int4,tenant_id text)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE
AS $function$
BEGIN    
  RETURN 
    -- Case 1: The user is a Super Admin
    EXISTS (
      SELECT 1
      FROM unnest(string_to_array(current_setting('drh.current_user_roles', true), ',')) AS role
      WHERE LOWER(trim(role)) = 'super admin'
    )
    OR
    -- Case 2: The user is an Admin and the tenant matches
    (
        EXISTS (
            SELECT 1
            FROM unnest(string_to_array(current_setting('drh.current_user_roles', true), ',')) AS role
            WHERE LOWER(trim(role)) = 'admin'
        )
        AND tenant_id = current_setting('drh.current_user_tenant_id', true)::TEXT
    )
    OR
    -- Case 3: The user is the creator
    study_creator = current_setting('drh.current_user_party_id', true)::TEXT
    OR
    -- Case 4: The study is tenant-shared and the tenant matches
    (visibility = 3 AND tenant_id = current_setting('drh.current_user_tenant_id', true)::TEXT) 
    OR
    -- Case 5: The study is public
    visibility = 1; 
END;
$function$
;

DROP POLICY IF EXISTS research_study_select_rls_policy ON drh_stateful_research_study.research_study;

CREATE POLICY research_study_select_rls_policy 
ON drh_stateful_research_study.research_study 
FOR SELECT 
USING (drh_stateless_util.check_research_study_access(created_by, visibility,tenant_id));

DROP POLICY IF EXISTS research_study_insert_rls_policy ON drh_stateful_research_study.research_study;

CREATE POLICY research_study_insert_rls_policy
ON drh_stateful_research_study.research_study
FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1
        FROM unnest(string_to_array(current_setting('drh.current_user_roles', true), ',')) AS role
        WHERE LOWER(trim(role)) IN ('super admin', 'admin', 'researcher')
    )
);

DROP POLICY IF EXISTS research_study_update_rls_policy ON drh_stateful_research_study.research_study;

CREATE POLICY research_study_update_rls_policy
ON drh_stateful_research_study.research_study
FOR UPDATE
USING (drh_stateless_util.check_research_study_access(created_by,visibility,tenant_id));