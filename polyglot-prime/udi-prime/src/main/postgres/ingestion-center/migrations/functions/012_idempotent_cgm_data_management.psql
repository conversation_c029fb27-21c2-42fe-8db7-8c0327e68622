-----------------------------COMMON FUNCTION FOR FILE INTERACTION LOG SAVE----------------------------------
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_file_interaction_log(p_last_file_interaction_id text, p_created_by text, p_interaction_action_type text, p_interaction_status text);
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_file_interaction_log(p_params jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.save_file_interaction_log(p_params jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    file_data RECORD;
    interaction_action_id INTEGER;
    interaction_status_id INTEGER;
    v_interaction_hierarchy JSONB;
    new_file_interaction_id TEXT;
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.save_file_interaction_log';
    current_query TEXT := pg_catalog.current_query();
    created_by_user text;
   	v_file_category TEXT;
   	v_created_at timestamp;
    v_response JSONB;
    v_error_response JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Generate new file interaction ID
    new_file_interaction_id := drh_stateless_util.get_unique_id()::TEXT;
    parameters_lst := jsonb_build_object(
    'p_params', p_params    
    );

            

    -- Fetch last interaction details
    SELECT * INTO file_data 
    FROM drh_stateful_activity_audit.file_interaction 
    WHERE file_interaction_id = p_params->>'last_file_interaction_id';

    -- Get interaction action and status IDs
    SELECT id INTO interaction_action_id 
    FROM drh_stateful_master.interaction_action_type 
    WHERE title = p_params->>'interaction_action_type' 
    LIMIT 1;

    SELECT id INTO interaction_status_id 
    FROM drh_stateful_master.interaction_status 
    WHERE title = p_params->>'interaction_status' 
    LIMIT 1;

   v_created_at := CASE 
	    WHEN interaction_status_id IS NOT NULL THEN 
	        (CURRENT_TIMESTAMP + (interaction_status_id || ' second')::interval) AT TIME ZONE 'UTC'
	    ELSE 
	        CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
	END;
    -- Ensure interaction hierarchy is a JSON array
    v_interaction_hierarchy := COALESCE(file_data.interaction_hierarchy, '[]'::jsonb);
    
    -- Append last interaction ID
    IF p_params->>'last_file_interaction_id' IS NOT NULL THEN
        v_interaction_hierarchy := jsonb_insert(
            v_interaction_hierarchy, '{-1}', to_jsonb(p_params->>'last_file_interaction_id'), true
        );
    END IF;
   
    created_by_user := COALESCE(p_params->>'created_by', file_data.created_by);
   
   -- Handle file_category
    v_file_category := COALESCE(p_params->>'file_category', file_data.file_category, 'Database');
   
    v_response := CASE WHEN p_params ? 'response' THEN p_params -> 'response' ELSE NULL END;
    v_error_response := CASE WHEN p_params ? 'error_response' THEN p_params -> 'error_response' ELSE NULL END;

    
    -- Insert new file interaction record
    INSERT INTO drh_stateful_activity_audit.file_interaction (
        file_interaction_id,
        hub_interaction_id,
        study_id,
        organization_party_id,
        description,
        db_file_id,
        file_name,
        file_category,
        file_processing_initiated_at,
        file_processing_completed_at,
        created_by,
        created_at,
        interaction_hierarchy,
        interaction_action_type_id,
        interaction_status_id,
        uri,
        participant_id,
        response,
        error_response
    ) VALUES (
        new_file_interaction_id,
        file_data.hub_interaction_id,
        file_data.study_id,
        file_data.organization_party_id,
        COALESCE(p_params->>'description', file_data.description),
        COALESCE(p_params->>'db_file_id', file_data.db_file_id),
        COALESCE(p_params->>'file_name', file_data.file_name),
        v_file_category,
        file_data.file_processing_initiated_at,
        file_data.file_processing_completed_at,
        created_by_user,
        v_created_at,
        v_interaction_hierarchy,
        interaction_action_id,
        interaction_status_id,
        file_data.uri,
        file_data.participant_id,
        v_response,
        v_error_response
    );

    -- Return success response
    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'File interaction inserted successfully',
        'file_interaction_id', new_file_interaction_id
    );

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with error details
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during file interaction insertion',
        'error_details', error_details_json
    );
END;
$function$
;




---------------------cgm row db view-------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_raw_data.cgm_raw_db_view;

CREATE OR REPLACE VIEW drh_stateless_raw_data.cgm_raw_db_view 
WITH (security_invoker = true) AS
SELECT 
    db_file_id,
    file_name,
    file_url,
    upload_timestamp,
    uploaded_by,
    file_size,
    is_processed,
    process_status,
    db_type,
    study_id,
    tenant_id,
    rec_status_id,
    rs.code
FROM drh_stateful_raw_data.cgm_raw_db db
JOIN drh_stateful_party.record_status rs ON db.rec_status_id = rs.id;

-----------------------file device metadata and cgm observation----------------------------------------------
-- Drop the view if it exists
DROP VIEW IF EXISTS drh_stateless_raw_observation.file_device_cgm_observation_view;

-- Create or replace the view
CREATE OR REPLACE VIEW drh_stateless_raw_observation.file_device_cgm_observation_view
WITH (security_invoker = true) AS 
    SELECT 
    cgm_obs.raw_cgm_extract_data_id AS cgm_raw_data_id,
    cgm_obs.study_id,
    cgm_obs.research_subject_id AS participant_sid,
    cgm_obs.tenant_id,
    cgm_obs.cgm_value,
    cgm_obs.date_time,
    rced.file_meta_data->>'device_id' AS device_id,
    rced.file_meta_data->>'devicename' AS devicename,
    rced.file_meta_data ->> 'source_platform'::text AS source_platform,
    rced.file_meta_data->>'file_format' AS file_format,
    CONCAT(rced.file_meta_data->>'file_name', '.', rced.file_meta_data->>'file_format') AS file_name
FROM 
    drh_stateful_raw_observation.cgm_observation cgm_obs
JOIN drh_stateful_raw_data.raw_cgm_extract_data rced 
    ON cgm_obs.raw_cgm_extract_data_id = rced.cgm_raw_data_id
WHERE 
    cgm_obs.deleted_at IS NULL
    AND rced.deleted_at IS NULL;


-----------------------study_participant_dashboard_metrics----------------------------------------------
-- Drop the view if it exists
DROP VIEW IF EXISTS drh_stateless_research_study.study_participant_dashboard_metrics_view;
CREATE OR REPLACE VIEW drh_stateless_research_study.study_participant_dashboard_metrics_view
WITH (security_invoker = true) AS
WITH calculated_metrics AS (
         SELECT cgm_metrics.participant_sid,
            cgm_metrics.days_of_wear,
            cgm_metrics.data_start_date,
            cgm_metrics.data_end_date,
            round(cgm_metrics.in_range_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric, 2) AS tir,
            round(cgm_metrics.very_high_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric, 2) AS tar_vh,
            round(cgm_metrics.high_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric, 2) AS tar_h,
            round(cgm_metrics.low_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric, 2) AS tbr_l,
            round(cgm_metrics.very_low_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric, 2) AS tbr_vl,
            round(cgm_metrics.above_range_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric, 2) AS tar,
            round(cgm_metrics.below_range_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric, 2) AS tbr,
            ceil(cgm_metrics.avg_glucose * 0.155::double precision + 95::double precision) AS gmi,
            round((cgm_metrics.glucose_stddev::double precision / NULLIF(cgm_metrics.avg_glucose, 0::double precision) * 100::double precision)::numeric, 2) AS percent_gv,
            round(3.0 * (cgm_metrics.very_low_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric + 0.8 * (cgm_metrics.low_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric)) + 1.6 * (cgm_metrics.very_high_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric + 0.5 * (cgm_metrics.high_count::numeric * 100.0 / NULLIF(cgm_metrics.total_readings, 0)::numeric)), 2) AS gri,
            ROUND(
                COALESCE(
                    cgm_metrics.days_of_wear::numeric * 100.0 / 
                    NULLIF((cgm_metrics.data_end_date::date - cgm_metrics.data_start_date::date + 1), 0), 
                    0
                ), 
            2) AS wear_time_percentage
           FROM drh_stateful_research_study.cgm_metrics
        )
 SELECT pb.organization_party_id,
    pb.organization_id,
    pb.study_id,
    pb.study_display_id,
    pb.participant_id,
    pb.participant_display_id,
    pb.gender,
    pb.age,
    pb."Study Arm",
    pb."Baseline HbA1C",
    cdi.cgm_devices,
    cdi.cgm_files,
    cm.tir,
    cm.tar_vh,
    cm.tar_h,
    cm.tbr_l,
    cm.tbr_vl,
    cm.tar,
    cm.tbr,
    cm.gmi,
    cm.percent_gv,
    cm.gri,
    cm.days_of_wear,
    cm.data_start_date,
    cm.data_end_date,
    cm.wear_time_percentage
   FROM drh_stateful_research_study.participant_base pb
     LEFT JOIN drh_stateful_research_study.cgm_device_info cdi ON cdi.participant_sid = pb.participant_id
     LEFT JOIN calculated_metrics cm ON cm.participant_sid = pb.participant_id
  ORDER BY pb.participant_display_id;


-- ************************************************************************************************
-- Function Name: save_cgm_observation
-- Purpose:
-- This function saves a Continuous Glucose Monitoring (CGM) observation to the database.
-- Parameters:
--  - p_cgm_raw_data_id: The unique identifier for the raw_cgm_extract_data.
--  - p_created_by: The ID of the user creating the observation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************

DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_cgm_observation(varchar, varchar);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.save_cgm_observation(p_cgm_raw_data_id character varying, p_created_by character varying)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_map_field_of_cgm_date TEXT;
    v_map_field_of_cgm_value TEXT;    
    v_study_id TEXT;
    v_participant_sid TEXT;
    v_tenant_id TEXT;
    result JSONB;
    observation JSONB;
    formatted_date TIMESTAMP;
    numeric_value NUMERIC;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.save_cgm_observation';
    current_query TEXT := pg_catalog.current_query();
    raw_date TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'CGM observation saved successfully');

    parameters_lst := jsonb_build_object(
    'p_cgm_raw_data_id', p_cgm_raw_data_id,
    'p_created_by', p_created_by
    );


    -- Fetch required fields and metadata
    SELECT COALESCE(file_meta_data->>'map_field_of_cgm_date', 'date_time'), 
           COALESCE(file_meta_data->>'map_field_of_cgm_value', 'cgm_value'),
           study_id,
           tenant_id,
           participant_sid
    INTO v_map_field_of_cgm_date, v_map_field_of_cgm_value, v_study_id, v_tenant_id, v_participant_sid
    FROM drh_stateful_raw_data.raw_cgm_extract_data
    WHERE cgm_raw_data_id = p_cgm_raw_data_id;

    IF v_map_field_of_cgm_date IS NULL OR v_map_field_of_cgm_value IS NULL THEN
        RAISE EXCEPTION 'Mapping fields for CGM date or value are NULL';
    END IF;

    -- Loop through each CGM observation
    FOR observation IN
        SELECT value
        FROM jsonb_array_elements(
            (SELECT cgm_data 
             FROM drh_stateful_raw_data.raw_cgm_extract_data 
             WHERE cgm_raw_data_id = p_cgm_raw_data_id)
        )
    LOOP
        -- Get the raw date value
        raw_date := observation->>v_map_field_of_cgm_date;
        
        -- Try different date formats
        BEGIN
            -- First try the standard format
            formatted_date := TO_TIMESTAMP(raw_date, 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"');
        EXCEPTION WHEN OTHERS THEN
            BEGIN
                -- Try DD-MM-YYYY format
                formatted_date := TO_TIMESTAMP(raw_date, 'DD-MM-YYYY"T"HH24:MI:SS.MS"Z"');
            EXCEPTION WHEN OTHERS THEN
                BEGIN
                    -- Try MM-DD-YYYY format
                    formatted_date := TO_TIMESTAMP(raw_date, 'MM-DD-YYYY"T"HH24:MI:SS.MS"Z"');
                EXCEPTION WHEN OTHERS THEN
                    RAISE EXCEPTION 'Cannot parse date: %. Supported formats are YYYY-MM-DD, DD-MM-YYYY, MM-DD-YYYY with time', raw_date;
                END;
            END;
        END;

        -- Convert and validate CGM value
        BEGIN
            numeric_value := (observation->>v_map_field_of_cgm_value)::NUMERIC;
        EXCEPTION WHEN OTHERS THEN
            RAISE EXCEPTION 'Invalid numeric format: %', observation->>v_map_field_of_cgm_value;
        END;

        -- Create dynamic partition if not exists
        -- PERFORM drh_stateless_raw_observation.create_partitions_if_not_exists(v_tenant_id, v_study_id, v_participant_sid); 

        -- Create participant partition if not exists
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS drh_stateful_raw_observation.cgm_observation_research_subject_%s
            PARTITION OF drh_stateful_raw_observation.cgm_observation
            FOR VALUES IN (%L);',
            replace(v_participant_sid, '-', '_'), v_participant_sid
        );

        -- Insert observation
        INSERT INTO drh_stateful_raw_observation.cgm_observation (
            id,
            study_id,
            research_subject_id,
            raw_cgm_extract_data_id,
            period,
            date_time,
            cgm_value,
            unit,
            tenant_id,
            rec_status_id,
            created_at,
            created_by
        ) VALUES (
            drh_stateless_util.get_unique_id()::TEXT,
            v_study_id,
            v_participant_sid, 
            p_cgm_raw_data_id,
            'Baseline', 
            formatted_date,
            numeric_value,
            '', 
            v_tenant_id,
            (SELECT id FROM drh_stateful_party.record_status WHERE code = 'ACTIVE' LIMIT 1), 
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_created_by
        );
    END LOOP;
    
    -- Call the save_cgm_metrics function
    PERFORM drh_stateless_raw_observation.save_cgm_metrics(v_participant_sid);

    -- Return success
    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during CGM observation save',
        'error_details', error_details_json
    );
END;
$function$;

--*********************SAVE CGM FILE UPLOAD DATA*********************************
-- Function: drh_stateless_raw_observation.save_cgm_raw_data(p_input_params JSONB)
-- Description: 
-- This function processes raw CGM (Continuous Glucose Monitoring) data and saves it into 
-- appropriate database tables. It extracts relevant metadata and file content from the 
-- input JSON, inserts the raw data and optional zip file details into their respective tables, 
-- and ensures the data is saved with necessary status and audit information. If the insertion 
-- is successful, it calls an external function to save further CGM observations.
--*******************************************************************************
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_cgm_raw_data(p_input_params jsonb);
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_cgm_raw_data(p_input_params jsonb,jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.save_cgm_raw_data(p_input_params jsonb, p_activity_json jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    rows_inserted INTEGER;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.save_cgm_raw_data';
    current_query TEXT := pg_catalog.current_query();

    -- Extracted fields from JSON input
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_current_user_id TEXT;
    p_participant_sid TEXT;
    p_upload_timestamp TIMESTAMPTZ;
    p_uploaded_by TEXT;
    p_file_size INT;
    p_status VARCHAR;
    p_rec_status_id INT := 1;
    p_is_processed BOOLEAN := FALSE;
    p_processed_at TIMESTAMPTZ := NULL;

    -- File metadata JSON
    file_metadata JSONB;
    v_filename VARCHAR;
   	v_file_url TEXT;
    v_file_format VARCHAR;
    v_upload_id TEXT;
    v_zip_file_id TEXT;
    v_raw_file_id TEXT;
   	p_cgm_raw_data_json JSONB;
   	p_cgm_data JSONB;
  	p_file_type TEXT;
  
  	p_cgm_raw_data_csv BYTEA;
   	p_cgm_raw_data_excel BYTEA;
    p_cgm_raw_data_xml XML;
   	p_cgm_raw_data_text BYTEA;

    zip_filename VARCHAR;
   	zip_file_url TEXT;
    zip_file_format VARCHAR;
    zip_upload_timestamp TIMESTAMPTZ;
    zip_uploaded_by TEXT;
    zip_file_size VARCHAR;
    zip_file_status VARCHAR;
    zip_file_metadata JSONB;
    zip_file_content BYTEA;
    zip_is_processed BOOLEAN := FALSE;
    zip_processed_at TIMESTAMPTZ := NULL;
   	
   observation_result jsonb;
  
   	cgm_raw_upload_data_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
   	raw_cgm_extract_data_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
    
    study_created_by TEXT;
   	current_visibility TEXT;
   	is_archived BOOLEAN;
   
   	file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
	v_file_name text;
	last_file_interaction_id text;
	v_file_interaction_params JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN

    parameters_lst := jsonb_build_object(
    'p_input_params', p_input_params    
    );

    -- Extract fields from JSON input
    p_study_id := p_input_params->>'study_id';
    --p_tenant_id := p_input_params->>'tenant_id';
    p_current_user_id := p_input_params->>'current_user_id';
    p_participant_sid := p_input_params->>'participant_sid';
    p_upload_timestamp := (p_input_params->>'upload_timestamp')::TIMESTAMPTZ;
    p_uploaded_by := p_input_params->>'uploaded_by';
    p_file_size := (p_input_params->>'file_size')::INT;
    p_status := p_input_params->>'status';
    v_file_url := (p_input_params->>'file_url')::TEXT;
    v_raw_file_id := (p_input_params->>'raw_file_id')::TEXT;
   	p_cgm_raw_data_json:=p_input_params->>'cgm_raw_data_json';
   	p_cgm_data:=p_input_params->>'cgm_data';
   	p_file_type:=p_input_params->>'file_type';
    p_cgm_raw_data_csv:=p_input_params->>'cgm_raw_data_csv';
   	p_cgm_raw_data_excel:=p_input_params->>'cgm_raw_data_excel';
    p_cgm_raw_data_xml:=p_input_params->>'cgm_raw_data_xml';
   	p_cgm_raw_data_text:=p_input_params->>'cgm_raw_data_text';

    last_file_interaction_id:=p_input_params->>'last_file_interaction_id';
   
   -- Log failure in file interaction log
    IF last_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', last_file_interaction_id,
            'interaction_action_type', 'SAVE CGM FILE DATA',
            'interaction_status', 'IN PROGRESS',
            'description', 'Save CGM file data started',
            'db_file_id', v_raw_file_id,
            'file_name', p_input_params->'file_metadata'->>'file_name',
            'file_category', 'CGM',
            'created_by', p_current_user_id
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   		-- Extract file_interaction_id from result
	    IF file_interaction_result ->> 'status' = 'success' THEN
	       inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
	    ELSE
	        RETURN jsonb_build_object(
	            'status', 'failure',
	            'message', 'Failed to insert file interaction',
	            'error_details', file_interaction_result -> 'error_details'
	        );
	    END IF;	
    END IF;

    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = p_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != p_current_user_id THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;
   
   -- Query to find the tenant_id using organization party id
    SELECT id INTO p_tenant_id
    FROM drh_stateful_research_study.organization
    WHERE party_id = p_input_params->>'org_party_id';

    -- Construct the file_metadata JSON
    file_metadata := jsonb_build_object(
        'devicename', p_input_params->'file_metadata'->>'device_name',
        'device_id', p_input_params->'file_metadata'->>'device_id',
        'source_platform', p_input_params->'file_metadata'->>'source_platform',
        'file_name', p_input_params->'file_metadata'->>'file_name',
        'file_format', p_input_params->'file_metadata'->>'file_format',
        'file_upload_date', p_input_params->'file_metadata'->>'file_upload_date',        
        'map_field_of_cgm_date', p_input_params->'file_metadata'->>'map_field_of_cgm_date',
        'map_field_of_cgm_value', p_input_params->'file_metadata'->>'map_field_of_cgm_value'
    );

    v_filename := file_metadata->>'file_name';
    v_file_format := file_metadata->>'file_format';

    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'CGM data saved successfully');

    -- Insert CGM raw data into the table
    begin

        -- Handle cgm_raw_zip_data if provided
    IF p_input_params->'cgm_raw_zip_data' IS NOT NULL THEN
        v_zip_file_id := drh_stateless_util.get_unique_id()::TEXT;
        zip_file_metadata := jsonb_build_object(
            'devicename', p_input_params->'cgm_raw_zip_data'->'file_metadata'->>'devicename',
            'device_id', p_input_params->'cgm_raw_zip_data'->'file_metadata'->>'device_id',
            'source_platform', p_input_params->'cgm_raw_zip_data'->'file_metadata'->>'source_platform',
            'file_name', p_input_params->'cgm_raw_zip_data'->>'file_name',
            'file_format', p_input_params->'cgm_raw_zip_data'->'file_metadata'->>'file_format',
            'file_upload_date', p_input_params->'cgm_raw_zip_data'->'file_metadata'->>'file_upload_date',
            'map_field_of_cgm_date', p_input_params->'cgm_raw_zip_data'->'file_metadata'->>'map_field_of_cgm_date',
            'map_field_of_cgm_value', p_input_params->'cgm_raw_zip_data'->'file_metadata'->>'map_field_of_cgm_value'
        );
       
        zip_file_content := p_input_params->'cgm_raw_zip_data'->'file_content';
        zip_file_url := p_input_params->'cgm_raw_zip_data'->'file_url';
        zip_filename := zip_file_metadata->>'file_name';
        zip_file_format := zip_file_metadata->>'file_format';
        zip_file_size := (p_input_params->'cgm_raw_zip_data'->>'file_size');
        zip_upload_timestamp := (p_input_params->'cgm_raw_zip_data'->>'upload_timestamp')::TIMESTAMPTZ;
        zip_uploaded_by := p_input_params->'cgm_raw_zip_data'->'cgm_raw_zip_data'->>'uploaded_by';
        zip_file_status := p_input_params->'cgm_raw_zip_data'->'cgm_raw_zip_data'->>'status';
        zip_is_processed := p_input_params->'cgm_raw_zip_data'->'is_processed';
        IF p_input_params->'cgm_raw_zip_data'->'processed_at' IS NULL OR p_input_params->'cgm_raw_zip_data'->'processed_at' = 'null' THEN
		    zip_processed_at := NULL;
		ELSE
		    zip_processed_at := (p_input_params->'cgm_raw_zip_data'->'processed_at')::TIMESTAMPTZ;
		END IF;

    
        -- Insert into cgm_raw_zip_data table
        INSERT INTO drh_stateful_raw_data.cgm_raw_zip_data (
            zip_file_id,
            tenant_id,
            study_id,
            file_name,
            file_url,
            file_format,
            upload_timestamp,
            uploaded_by,
            file_size,
            is_processed,
            processed_at,
            status,
            file_metadata,
            file_content,
            rec_status_id,
            created_at,
            created_by,
		    file_interaction_id
        ) VALUES (
            v_zip_file_id,
            p_tenant_id,
            p_study_id,
            zip_filename,
            zip_file_url,
            zip_file_format,
            zip_upload_timestamp,
            zip_uploaded_by,
            zip_file_size::VARCHAR,
            FALSE,--zip_is_processed,
            zip_processed_at,--p_input_params->'cgm_raw_zip_data'->'processed_at',
            zip_file_status,
            zip_file_metadata,
            zip_file_content, 
            (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_current_user_id,
            inprogress_file_interaction_id
        );
       
    END IF;
	    
	    
        INSERT INTO drh_stateful_raw_data.cgm_raw_upload_data (
		    cgm_raw_file_id,
		    file_name,
		    file_url,
		    zip_file_id,
		    cgm_raw_data_json,
		    upload_timestamp,
		    uploaded_by,
		    file_size,
		    is_processed,
		    processed_at,
		    status,
		    file_metadata,
		    file_type,
		    study_id,
		    tenant_id,
		    rec_status_id,
		    created_at,
		    created_by,		 
		    cgm_raw_data_csv,
		    cgm_raw_data_excel,
		    cgm_raw_data_xml,
		    cgm_raw_data_text,
		    file_interaction_id 
		) VALUES (
		    cgm_raw_upload_data_id,
		    p_input_params->'file_metadata'->>'file_name',
		    v_file_url,
		    v_zip_file_id,  -- Assuming no zip file ID for this example
		    p_cgm_raw_data_json,
		    p_upload_timestamp,
		    p_current_user_id,
		    p_file_size,
		    FALSE,
		    NULL,
		    'Pending',
		    file_metadata,
		    p_file_type,
		    p_study_id,
		    p_tenant_id,
		    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
		    p_current_user_id,		 
		    p_cgm_raw_data_csv,
		   	p_cgm_raw_data_excel,
		    p_cgm_raw_data_xml,
		   	p_cgm_raw_data_text,
		   	inprogress_file_interaction_id
		) RETURNING cgm_raw_file_id INTO v_upload_id;
		
        -- Insert the generated ID and all fields into raw_cgm_extract_data
        INSERT INTO drh_stateful_raw_data.raw_cgm_extract_data (
		    cgm_raw_data_id,
		    raw_file_id,
		    file_url,
		    study_id,
		    participant_sid,
		    cgm_raw_data_json,
		    file_meta_data,
		    cgm_data,
		    tenant_id,
		    rec_status_id,
		    created_at,
		    created_by,	
		    cgm_raw_data_csv,
		    cgm_raw_data_excel,
		    cgm_raw_data_xml,
		    cgm_raw_data_text,
		    file_interaction_id 
		    
		) VALUES (
		    raw_cgm_extract_data_id,
		    v_upload_id, 
		    v_file_url,
		    p_study_id,
		    p_participant_sid,
		    p_cgm_raw_data_json,
		    file_metadata,
		    p_cgm_data,
		    p_tenant_id,
		    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
		    p_current_user_id,
		    p_cgm_raw_data_csv,
		   	p_cgm_raw_data_excel,
		    p_cgm_raw_data_xml,
		   	p_cgm_raw_data_text,
		   	inprogress_file_interaction_id
		           
		);
		
        -- Get the number of rows inserted
        GET DIAGNOSTICS rows_inserted = ROW_COUNT;

        -- save into observation table after extract process
	    IF rows_inserted > 0 THEN
	        -- Capture the result from save_cgm_observation
	        observation_result := drh_stateless_raw_observation.save_cgm_observation(raw_cgm_extract_data_id, p_current_user_id);
	        
	        -- Check if save_cgm_observation returned a failure status
	        IF observation_result->>'status' = 'failure' THEN
	            -- Return the error details from save_cgm_observation
	            RETURN observation_result;
	        END IF;
	        
	       -- Log failure in file interaction log
		    IF inprogress_file_interaction_id IS NOT NULL THEN 
		       v_file_interaction_params := jsonb_build_object(
		            'last_file_interaction_id', inprogress_file_interaction_id,
		            'interaction_action_type', 'SAVE CGM FILE DATA',
		            'interaction_status', 'SUCCESS',
		            'description', 'Save CGM file data completed',
		            'db_file_id', v_raw_file_id,
		            'file_name', p_input_params->'file_metadata'->>'file_name',
		            'file_category', 'CGM',
		            'created_by', p_current_user_id
		        );
		       	-- Call save_file_interaction_log to get the file interaction ID   
		   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
		   		-- Extract file_interaction_id from result
			    IF file_interaction_result ->> 'status' = 'success' THEN
			        completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
			       
			        --Update completed interaction id into 
                    IF p_input_params->'cgm_raw_zip_data' IS NOT NULL THEN
			       	    UPDATE drh_stateful_raw_data.cgm_raw_zip_data SET file_interaction_id =completed_file_interaction_id WHERE zip_file_id = v_zip_file_id;
                    END IF;                    
			      	UPDATE drh_stateful_raw_data.cgm_raw_upload_data SET file_interaction_id =completed_file_interaction_id WHERE cgm_raw_file_id = cgm_raw_upload_data_id;
			      	UPDATE drh_stateful_raw_data.raw_cgm_extract_data SET file_interaction_id =completed_file_interaction_id WHERE cgm_raw_data_id = raw_cgm_extract_data_id;
			      	
			      
			    ELSE
			        RETURN jsonb_build_object(
			            'status', 'failure',
			            'message', 'Failed to insert file interaction',
			            'error_details', file_interaction_result -> 'error_details'
			        );
			    END IF;	
		    END IF;

            -- Activity log feature
            IF p_activity_json IS NOT NULL AND p_activity_json ? 'session_id' THEN
                SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
                SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='SAVE_CGM_RAW_DATA' AND deleted_at IS NULL;

                v_activity_log_json := p_activity_json || jsonb_build_object(
                    'activity_type_id', v_activity_type_id,
                    'activity_level_id', v_activity_level_id,
                    'activity_name', 'Save CGM Raw Data',
                    'activity_description', format('Saved CGM raw data file: %s for study_id %s', v_filename, p_study_id)
                );

                PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_current_user_id, v_activity_log_json);
            END IF;
	        -- If save_cgm_observation succeeded, return success
	        result := jsonb_build_object(
	            'status', 'success',
	            'message', 'CGM data saved and processed successfully',
	            'file_interaction_id',completed_file_interaction_id
	        );
	        RETURN result;
	    else
	    
	    	-- Log failure in file interaction log
		    IF inprogress_file_interaction_id IS NOT NULL THEN 
		       v_file_interaction_params := jsonb_build_object(
		            'last_file_interaction_id', inprogress_file_interaction_id,
		            'interaction_action_type', 'SAVE CGM FILE DATA',
		            'interaction_status', 'FAILED',
		            'description', 'Save CGM file data failed',
		            'db_file_id', v_raw_file_id,
		            'file_name', p_input_params->'file_metadata'->>'file_name',
		            'file_category', 'CGM',
		            'created_by', p_current_user_id
		        );
		       	-- Call save_file_interaction_log to get the file interaction ID   
		   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
		    END IF;

	        result := jsonb_build_object(
	            'status', 'failure',
	            'message', 'No rows inserted. Check the input parameters.'
	        );
	        RETURN result;
	    END IF;
       

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        
            -- Log failure in file interaction log
	    IF inprogress_file_interaction_id IS NOT NULL THEN 
	       v_file_interaction_params := jsonb_build_object(
	            'last_file_interaction_id', inprogress_file_interaction_id,
	            'interaction_action_type', 'SAVE CGM FILE DATA',
	            'interaction_status', 'FAILED',
	            'description', 'Save CGM file data failed',
	            'db_file_id', v_raw_file_id,
	            'file_name', p_input_params->'file_metadata'->>'file_name',
	            'file_category', 'CGM',
	            'created_by', p_current_user_id
	        );
	       	-- Call save_file_interaction_log to get the file interaction ID   
	   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
	    END IF;

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while saving CGM data',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final success result
    RETURN result;
END;
$function$
;

-- ************************************************************************************************
-- ****************************COMMON FUNCTION FOR FILE UPLOAD*************************************
-- ************************************************************************************************
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_subject_observation_data(p_input_params jsonb);
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_subject_observation_data(p_input_params jsonb, jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.save_subject_observation_data(p_input_params jsonb, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    rows_inserted INTEGER;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.save_subject_observation_data';
    current_query TEXT := pg_catalog.current_query();

    -- Extracted fields from JSON input
    p_study_id TEXT;
    p_tenant_id TEXT;
    p_current_user_id TEXT;
    p_participant_sid TEXT;
    p_upload_timestamp TIMESTAMPTZ;
    p_uploaded_by TEXT;
    p_file_size INT;
    p_status VARCHAR;
    p_rec_status_id INT := 1;
    p_is_processed BOOLEAN := FALSE;
    p_processed_at TIMESTAMPTZ := NULL;
    p_file_content_type_id TEXT;
    p_file_name TEXT;
   	v_meal_type_id TEXT;
   	v_fitness_type_id TEXT;
   	v_participant_display_id TEXT;

   	v_file_url TEXT;
    v_upload_id TEXT;
    v_zip_file_id TEXT;
    v_raw_file_id TEXT;
   	p_raw_data_json JSONB;
   	p_raw_data JSONB;
  	p_file_type TEXT;
  
  	p_raw_data_csv BYTEA;
   	p_raw_data_excel BYTEA;
    p_raw_data_xml XML;
   	p_raw_data_text BYTEA;

    zip_filename VARCHAR;
   	zip_file_url TEXT;
    zip_file_format VARCHAR;
    zip_upload_timestamp TIMESTAMPTZ;
    zip_uploaded_by TEXT;
    zip_file_size VARCHAR;
    zip_file_status VARCHAR;
    zip_file_content BYTEA;
    zip_is_processed BOOLEAN := FALSE;
    zip_processed_at TIMESTAMPTZ := NULL;
   	
    observation_result jsonb;
  
   	raw_upload_data_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
   	raw_extract_data_id TEXT := drh_stateless_util.get_unique_id()::TEXT;
    
    study_created_by TEXT;
   	current_visibility TEXT;
   	is_archived BOOLEAN;
   
   	file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
	v_file_name text;
	last_file_interaction_id text;
	v_file_interaction_params JSONB;

	-- Variables for dynamic file interaction messages
	v_content_type_name TEXT;
	v_save_content_type TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
	v_obs_id text;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    
	-- fetch meals and fitness ids from file content table
	SELECT id INTO v_meal_type_id from drh_stateful_master.file_content_type fct WHERE title='Meals' AND deleted_at IS NULL;
	SELECT id INTO v_fitness_type_id from drh_stateful_master.file_content_type fct WHERE title='Fitness' AND deleted_at IS NULL;

    parameters_lst := jsonb_build_object(
    'p_input_params', p_input_params
    );

	    
	-- Extract fields from JSON input
    p_study_id := p_input_params->>'study_id';
    --p_tenant_id := p_input_params->>'tenant_id';
    p_current_user_id := p_input_params->>'current_user_id';
    p_participant_sid := p_input_params->>'participant_sid';
    p_upload_timestamp := (p_input_params->>'upload_timestamp')::TIMESTAMPTZ;
    p_uploaded_by := p_input_params->>'uploaded_by';
    p_file_size := (p_input_params->>'file_size')::INT;
    p_status := p_input_params->>'status';
    v_file_url := (p_input_params->>'file_url')::TEXT;
    v_raw_file_id := (p_input_params->>'raw_file_id')::TEXT;
   	p_raw_data_json:=p_input_params->>'subject_observation_data_json';
   	p_raw_data:=p_input_params->>'subject_observation_data';
   	p_file_type:=p_input_params->>'file_type';
    p_raw_data_csv:=p_input_params->>'subject_observation_data_csv';
   	p_raw_data_excel:=p_input_params->>'subject_observation_data_excel';
    p_raw_data_xml:=p_input_params->>'subject_observation_data_xml';
   	p_raw_data_text:=p_input_params->>'subject_observation_data_text';
    p_file_content_type_id := p_input_params->>'file_content_type_id';
    p_file_name := p_input_params->>'file_name';
    last_file_interaction_id:=p_input_params->>'last_file_interaction_id';
    
    -- Determine content type name for dynamic messaging
    IF p_file_content_type_id = v_meal_type_id THEN
        v_content_type_name := 'Meals';
       	v_save_content_type := 'SAVE MEALS DATA';
    ELSIF p_file_content_type_id = v_fitness_type_id THEN
        v_content_type_name := 'Fitness';
       	v_save_content_type := 'SAVE FITNESS DATA';
    ELSE
        v_content_type_name := '';
       	v_save_content_type := 'SAVE CGM DATA';
    END IF;
   
   -- Log in-progress status in file interaction log
    IF last_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', last_file_interaction_id,
            'interaction_action_type', v_save_content_type,
            'interaction_status', 'IN PROGRESS',
            'description', 'Save ' || v_content_type_name || ' file data started',
            'db_file_id', v_raw_file_id,
            'file_name', p_file_name,
            'file_category', v_content_type_name,
            'created_by', p_current_user_id
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
   		-- Extract file_interaction_id from result
	    IF file_interaction_result ->> 'status' = 'success' THEN
	       inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
	    ELSE
	        RETURN jsonb_build_object(
	            'status', 'failure',
	            'message', 'Failed to insert file interaction',
	            'error_details', file_interaction_result -> 'error_details'
	        );
	    END IF;	
    END IF;

    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = p_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != p_current_user_id THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;
   
   -- Query to find the tenant_id using organization party id
    SELECT id INTO p_tenant_id
    FROM drh_stateful_research_study.organization
    WHERE party_id = p_input_params->>'org_party_id';

    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'File data saved successfully');

    -- Insert data into the table
    begin

        -- Handle subject_observation_zip_data if provided
    IF p_input_params->'zip_data' IS NOT NULL THEN
        v_zip_file_id := drh_stateless_util.get_unique_id()::TEXT;      
       
        zip_file_content := p_input_params->'zip_data'->>'file_content';
        zip_file_url := p_input_params->'zip_data'->>'file_url';
        zip_filename := p_input_params->'zip_data'->>'file_name';
        zip_file_format := p_input_params->'zip_data'->>'file_format';
        zip_file_size := (p_input_params->'zip_data'->>'file_size');
        zip_upload_timestamp := (p_input_params->'zip_data'->>'upload_timestamp')::TIMESTAMPTZ;
        zip_uploaded_by := p_input_params->'zip_data'->>'uploaded_by';
        zip_file_status := p_input_params->'zip_data'->>'status';
        zip_is_processed := p_input_params->'zip_data'->>'is_processed';
        IF p_input_params->'zip_data'->>'processed_at' IS NULL OR p_input_params->'zip_data'->>'processed_at' = 'null' THEN
		    zip_processed_at := NULL;
		ELSE
		    zip_processed_at := (p_input_params->'zip_data'->>'processed_at')::TIMESTAMPTZ;
		END IF;

    
        -- Insert into zip_data table
        INSERT INTO drh_stateful_raw_data.subject_observation_zip_data (
            id,
            tenant_id,
            study_id,
            file_name,
            file_url,
            file_format,
            upload_timestamp,
            uploaded_by,
            file_size,
            is_processed,
            processed_at,
            status,
            file_content,
            rec_status_id,
            created_at,
            created_by,
		    file_interaction_id,
            file_content_type_id
        ) VALUES (
            v_zip_file_id,
            p_tenant_id,
            p_study_id,
            zip_filename,
            zip_file_url,
            zip_file_format,
            zip_upload_timestamp,
            zip_uploaded_by,
            zip_file_size::VARCHAR,
            FALSE,
            zip_processed_at,
            zip_file_status,
            zip_file_content, 
            (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_current_user_id,
            inprogress_file_interaction_id,
            p_file_content_type_id
        );
       
    END IF;
	    
	    
        INSERT INTO drh_stateful_raw_data.subject_observation_upload_data (
		    id,
		    file_name,
		    file_url,
		    zip_file_id,
		    raw_data_json,
		    upload_timestamp,
		    uploaded_by,
		    file_size,
		    is_processed,
		    processed_at,
		    status,
		    file_type,
		    study_id,
		    tenant_id,
		    rec_status_id,
		    created_at,
		    created_by,		 
		    raw_data_csv,
		    raw_data_excel,
		    raw_data_xml,
		    raw_data_text,
		    file_interaction_id,
            file_content_type_id 
		) VALUES (
		    raw_upload_data_id,
		    p_file_name,
		    v_file_url,
		    v_zip_file_id,  -- Assuming no zip file ID for this example
		    p_raw_data_json,
		    p_upload_timestamp,
		    p_current_user_id,
		    p_file_size,
		    FALSE,
		    NULL,
		    'Pending',
		    p_file_type,
		    p_study_id,
		    p_tenant_id,
		    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
		    p_current_user_id,		 
		    p_raw_data_csv,
		   	p_raw_data_excel,
		    p_raw_data_xml,
		   	p_raw_data_text,
		   	inprogress_file_interaction_id,
            p_file_content_type_id
		) RETURNING id INTO v_upload_id;
		
        -- Insert the generated ID and all fields into subject_observation_extract_data
        INSERT INTO drh_stateful_raw_data.subject_observation_extract_data (
		    id,
		    subject_observation_upload_id,
		    file_url,
		    study_id,
		    participant_sid,
		    subject_observation_data_json,
		    subject_observation_data,
		    tenant_id,
		    rec_status_id,
		    created_at,
		    created_by,	
		    raw_data_csv,
		    raw_data_excel,
		    raw_data_xml,
		    raw_data_text,
		    file_interaction_id, 
		    file_content_type_id
		) VALUES (
		    raw_extract_data_id,
		    v_upload_id, 
		    v_file_url,
		    p_study_id,
		    p_participant_sid,
		    p_raw_data_json,
		    p_raw_data,
		    p_tenant_id,
		    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
		    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
		    p_current_user_id,
		    p_raw_data_csv,
		   	p_raw_data_excel,
		    p_raw_data_xml,
		   	p_raw_data_text,
		   	inprogress_file_interaction_id,
		    p_file_content_type_id       
		)RETURNING id into v_obs_id;
		
        -- Get the number of rows inserted
        GET DIAGNOSTICS rows_inserted = ROW_COUNT;

        -- save into observation table after extract process
	    IF rows_inserted > 0 THEN
			
	    	SELECT rs.participant_identifier INTO v_participant_display_id from drh_stateful_research_study.research_subject rs WHERE rs.rsubject_id =p_participant_sid AND deleted_at IS NULL; 
	    	
	    	--MIGRATE MEALS DATA IF FILE CONTENT TYPE IS MEALS
	    	if p_file_content_type_id = v_meal_type_id then	    		
                observation_result := drh_stateless_db_import_migration.extract_file_meal_data(p_study_id, p_tenant_id, v_participant_display_id, p_raw_data, p_current_user_id,v_obs_id);
                -- Check if migration returned a failure status
                IF observation_result->>'status' = 'failure' THEN
                    --Return the error details from migration
                    RETURN observation_result;
                END IF;
                --LOAD PARTITIONING FOR MEALS
		   		call  drh_stateless_db_import_migration.load_meal_partitions_file(p_study_id, inprogress_file_interaction_id);
	    	end if;
	    	--MIGRATE FITNESS DATA IF FILE CONTENT TYPE IS FITNESS
	    	if p_file_content_type_id = v_fitness_type_id then
                observation_result := drh_stateless_db_import_migration.extract_file_fitness_data(p_study_id, p_tenant_id, v_participant_display_id, p_raw_data, p_current_user_id,v_obs_id);
                -- Check if migration returned a failure status
                IF observation_result->>'status' = 'failure' THEN
                    --Return the error details from migration
                    RETURN observation_result;
                END IF;
                --LOAD PARTITIONING FOR FITNESS
				call  drh_stateless_db_import_migration.load_fitness_partitions_file(p_study_id, inprogress_file_interaction_id);
	    	end if;
	        
	       -- Log success in file interaction log
		    IF inprogress_file_interaction_id IS NOT NULL THEN 
		       v_file_interaction_params := jsonb_build_object(
		            'last_file_interaction_id', inprogress_file_interaction_id,
		            'interaction_action_type', v_save_content_type,
		            'interaction_status', 'SUCCESS',
		            'description', 'Save ' || v_content_type_name || ' file data completed',
		            'db_file_id', v_raw_file_id,
		            'file_name', p_file_name,
		            'file_category', v_content_type_name,
		            'created_by', p_current_user_id
		        );
		       	-- Call save_file_interaction_log to get the file interaction ID   
		   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
		   		-- Extract file_interaction_id from result
			    IF file_interaction_result ->> 'status' = 'success' THEN
			        completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
			       
			        --Update completed interaction id into 
                    IF p_input_params->'zip_data' IS NOT NULL THEN
			       	    UPDATE drh_stateful_raw_data.subject_observation_zip_data SET file_interaction_id = completed_file_interaction_id WHERE id = v_zip_file_id;
                    END IF;                    
			      	UPDATE drh_stateful_raw_data.subject_observation_upload_data SET file_interaction_id = completed_file_interaction_id WHERE id = raw_upload_data_id;
			      	UPDATE drh_stateful_raw_data.subject_observation_extract_data SET file_interaction_id = completed_file_interaction_id WHERE id = raw_extract_data_id;
			      	
			      
			    ELSE
			        RETURN jsonb_build_object(
			            'status', 'failure',
			            'message', 'Failed to insert file interaction',
			            'error_details', file_interaction_result -> 'error_details'
			        );
			    END IF;	
		    END IF;

            -- Activity log feature
            IF p_activity_json IS NOT NULL AND p_activity_json ? 'session_id' THEN             
                    SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
                    SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='SAVE_SUBJECT_OBSERVATION_DATA' AND deleted_at IS NULL;
                    
                    v_activity_log_json := p_activity_json || jsonb_build_object(
                        'activity_type_id', v_activity_type_id,
                        'activity_level_id', v_activity_level_id,
                        'activity_name', 'Save Subject Observation Data',
                        'activity_description', format('Saved subject observation file: %s for study_id %s', p_file_name, p_study_id)
                    );

                PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_current_user_id, v_activity_log_json);
            END IF;

	        -- If migration succeeded, return success
	        result := jsonb_build_object(
	            'status', 'success',
	            'message', v_content_type_name || ' data saved and processed successfully',
	            'file_interaction_id', completed_file_interaction_id
	        );
	        RETURN result;
	    else
	    
	    	-- Log failure in file interaction log
		    IF inprogress_file_interaction_id IS NOT NULL THEN 
		       v_file_interaction_params := jsonb_build_object(
		            'last_file_interaction_id', inprogress_file_interaction_id,
		            'interaction_action_type', v_save_content_type,
		            'interaction_status', 'FAILED',
		            'description', 'Save ' || v_content_type_name || ' file data failed',
		            'db_file_id', v_raw_file_id,
		            'file_name', p_file_name,
		            'file_category', v_content_type_name,
		            'created_by', p_current_user_id
		        );
		       	-- Call save_file_interaction_log to get the file interaction ID   
		   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
		    END IF;

	        result := jsonb_build_object(
	            'status', 'failure',
	            'message', 'No rows inserted. Check the input parameters.'
	        );
	        RETURN result;
	    END IF;
       

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

       
       	-- Log failure in file interaction log
	    IF inprogress_file_interaction_id IS NOT NULL THEN 
	       v_file_interaction_params := jsonb_build_object(
	            'last_file_interaction_id', inprogress_file_interaction_id,
	            'interaction_action_type', v_save_content_type,
	            'interaction_status', 'FAILED',
	            'description', 'Save ' || v_content_type_name || ' file data failed with error: ' || err_message,
	            'db_file_id', v_raw_file_id,
	            'file_name', p_file_name,
	            'file_category', v_content_type_name,
	            'created_by', p_current_user_id
	        );
	       	-- Call save_file_interaction_log to get the file interaction ID   
	   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
	    END IF;

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while saving ' || v_content_type_name || ' data',
            'error_details', error_details_json
        );
        RETURN result;
    END;

    -- Return the final success result
    RETURN result;
END;
$function$
;



-- ************************************************************************************************
-- Function Name: get_participant_cgm_dates
-- Purpose:
-- This metrics function is to get the participant cgm date range.
-- Parameters:
--  - p_study_id: The unique identifier for the study.
--  - p_participant_id: The ID of the participant.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.get_participant_cgm_dates(text, text);

-- CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_participant_cgm_dates(p_study_id text, p_participant_id text)
--  RETURNS jsonb
--  LANGUAGE plpgsql
--  SECURITY DEFINER
-- AS $function$
-- DECLARE
--     result JSONB;
--     p_tenant_id TEXT;
--     err_context TEXT;
--     err_state TEXT;
--     err_message TEXT;
--     err_detail TEXT;
--     err_hint TEXT;
--     function_name TEXT := 'drh_stateless_raw_observation.get_participant_cgm_dates';
--     current_query TEXT := pg_catalog.current_query();
--     date_range_data JSONB;
--     study_exists BOOLEAN;
-- BEGIN
--     BEGIN
--         -- Fetch tenant_id for the given study_id and participant_id
--         SELECT DISTINCT ON (tenant_id) tenant_id  
--         INTO p_tenant_id 
--         FROM drh_stateless_raw_observation.file_device_cgm_observation_view
--         WHERE study_id = p_study_id and participant_sid = p_participant_id;

--      -- Validate study_id
--         IF NOT EXISTS (
--             SELECT 1
--             FROM drh_stateless_raw_observation.file_device_cgm_observation_view
--             WHERE study_id = p_study_id
--         ) THEN
--             RETURN jsonb_build_object(
--                 'status', 'failure',
--                 'message', 'Invalid study_id',
--                 'details', jsonb_build_object('study_id', p_study_id)
--             );
--         END IF;


--      -- Validate p_participant_id
--         IF NOT EXISTS (
--             SELECT 1
--             FROM drh_stateless_raw_observation.file_device_cgm_observation_view
--             WHERE participant_sid = p_participant_id
--         ) THEN
--             RETURN jsonb_build_object(
--                 'status', 'failure',
--                 'message', 'Invalid participant id',
--                 'details', jsonb_build_object('participant_id', p_participant_id)
--             );
--         END IF;

--         -- Check if the participant belongs to the study
-- 	    study_exists = EXISTS (
-- 	        SELECT 1
-- 	        FROM drh_stateless_research_study.participant_data_view  pdv
-- 	        WHERE pdv.study_id = p_study_id
-- 	          AND pdv.participant_id = participant_id
-- 	    );
	
-- 	    -- If not exists, return failure message
-- 	    IF NOT study_exists THEN
-- 	        RETURN
-- 	            jsonb_build_object(
-- 	                'status', 'failure',
-- 	                'message', 'Participant does not belong to the study',
-- 	                'details', jsonb_build_object(
-- 	                    'study_id', p_study_id,
-- 	                    'participant_id', participant_id
-- 	                )
-- 	            );
	       
-- 	    END IF;


--          -- Prepare the date range data separately
--         SELECT jsonb_build_object(
--                    'min_start_date', min((cgm_data.date_time)::date),
--                    'max_end_date', max((cgm_data.date_time)::date)
--                )
--         INTO date_range_data
--         FROM drh_stateless_raw_observation.file_device_cgm_observation_view cgm_data
--         WHERE cgm_data.study_id = p_study_id 
--           AND cgm_data.tenant_id = p_tenant_id
--           AND cgm_data.participant_sid = p_participant_id;

--         -- Default to an empty object if no rows match
--         IF date_range_data IS NULL THEN
--             date_range_data := '{}'::JSONB;
--         END IF;

--         -- Build the result JSONB with the prepared date range data
--         result := jsonb_build_object(
--             'status', 'success',
--             'message', 'Date range fetched successfully',
--             'data', date_range_data
--         );


--     EXCEPTION WHEN OTHERS THEN
--         -- Capture error details
--          GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
--                                  err_state = RETURNED_SQLSTATE,
--                                  err_message = MESSAGE_TEXT,
--                                  err_detail = PG_EXCEPTION_DETAIL,
--                                  err_hint = PG_EXCEPTION_HINT;

--         -- Log the error details in the exception log table
--         INSERT INTO drh_stateful_activity_audit.exception_log (
--             function_name,
--             error_code,
--             error_message,
--             error_detail,
--             error_hint,
--             error_context,
--             query,
--             parameters,
--             occurred_at,
--             resolved,
--             resolved_at,
--             resolver_comments
--         )
--         VALUES (
--             function_name,
--             err_state,
--             err_message,
--             err_detail,
--             err_hint,
--             err_context,
--            current_query,
--             jsonb_build_object('p_study_id', p_study_id, 'p_participant_id', p_participant_id),
--             CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
--             'No',
--             NULL,
--             NULL
--         );

--         -- Prepare error JSON response
--         result := jsonb_build_object(
--             'status', 'failure',
--             'message', 'Error occurred while fetching date range',
--             'error_details', jsonb_build_object(
--                 'error', err_message,
--                 'detail', err_detail,
--                 'hint', err_hint,
--                 'context', err_context,
--                 'state', err_state
--             )
--         );
--     END;

--     -- Return the result
--     RETURN result;
-- END;
-- $function$;


CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_participant_cgm_dates(p_study_id text, p_participant_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    date_range_data JSONB;
   	err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.get_participant_cgm_dates';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_participant_id', p_participant_id
    );

    -- Single query to validate and get date range
    SELECT 
        CASE 
            WHEN COUNT(*) = 0 THEN
                jsonb_build_object(
                    'status', 'failure',
                    'message', 'No data found for study/participant combination',
                    'details', jsonb_build_object(
                        'study_id', p_study_id,
                        'participant_id', p_participant_id
                    )
                )
            ELSE
                jsonb_build_object(
                    'status', 'success',
                    'message', 'Date range fetched successfully',
                    'data', jsonb_build_object(
                        'min_start_date', MIN((cgm_data.date_time)::date),
                        'max_end_date', MAX((cgm_data.date_time)::date)
                    )
                )
        END
    INTO result
    FROM drh_stateless_raw_observation.file_device_cgm_observation_view cgm_data
    WHERE cgm_data.study_id = p_study_id 
    AND cgm_data.participant_sid = p_participant_id;

    RETURN COALESCE(result, 
        jsonb_build_object(
            'status', 'failure',
            'message', 'No data found',
            'data', '{}'::jsonb
        )
    );

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
        exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with error details
    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during fetching date range',
        'error_details', error_details_json
    );
END;
$function$
;

-- ************************************************************************************************
-- Function Name: get_participant_metrics
-- Purpose:
-- This metrics function is to get the following metrics values.
    --getMeanGlucose
    --getNumberOfDaysCGMWorn
    --getPercentageTimeCGMActive
    --getGlucoseManagementIndicator
    --getCoefficientOfVariation
-- Parameters:
--  - p_study_id: The unique identifier for the study.
--  - p_participant_id: The ID of the participant.
--   - start_date : The start date of the observation.
--   - end_date : The end date of the observation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.get_participant_metrics(text, text, date, date);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_participant_metrics(p_study_id text, p_participant_id text, start_date date, end_date date)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    function_name TEXT := 'drh_stateless_raw_observation.get_participant_metrics';
    mean_glucose FLOAT;
    number_of_days_cgm_worn INT := 0;
    percentage_time_cgm_active FLOAT := 0;
    glucose_management_indicator FLOAT:= 0;
    coefficient_of_variation FLOAT:= 0;
    study_exists BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_participant_id', p_participant_id,
        'start_date', start_date,
        'end_date', end_date
    );


    BEGIN
        -- Validate study_id
        IF NOT EXISTS (
            SELECT 1
            FROM drh_stateless_raw_observation.file_device_cgm_observation_view
            WHERE study_id = p_study_id
        ) THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Invalid study_id',
                'details', jsonb_build_object('study_id', p_study_id)
            );
        END IF;

        -- Validate p_participant_id
        IF NOT EXISTS (
            SELECT 1
            FROM drh_stateless_raw_observation.file_device_cgm_observation_view
            WHERE participant_sid = p_participant_id
        ) THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Invalid participant_id',
                'details', jsonb_build_object('participant_id', p_participant_id)
            );
        END IF;

        -- Validate start_date is less than or equal to end_date
        IF start_date::date > end_date::date THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Invalid date range: start_date must be less than or equal to end_date',
                'details', jsonb_build_object(
                    'start_date', start_date,
                    'end_date', end_date
                )
            );
        END IF;

           -- Check if the participant belongs to the study
	    study_exists = EXISTS (
	        SELECT 1
	        FROM drh_stateless_research_study.participant_data_view
	        WHERE study_id = p_study_id
	          AND participant_id = p_participant_id
	    );
	
	    -- If not exists, return failure message
	    IF NOT study_exists THEN
	        RETURN
	            jsonb_build_object(
	                'status', 'failure',
	                'message', 'Participant does not belong to the study',
	                'details', jsonb_build_object(
	                    'study_id', p_study_id,
	                    'participant_id', p_participant_id
	                )
	            );
	       
	    END IF;

        -- Calculating mean_glucose
        SELECT ROUND(AVG(cgm_value)::numeric, 2)::double precision INTO mean_glucose
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view cgm_data
        WHERE cgm_data.participant_sid = p_participant_id AND cgm_data.study_id = p_study_id AND cgm_data.date_time BETWEEN start_date AND end_date;

        -- Calculating number_of_days_cgm_worn
        SELECT COUNT(DISTINCT DATE(date_time)) 
        INTO number_of_days_cgm_worn
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view cgm_data
        WHERE cgm_data.participant_sid = p_participant_id 
        AND cgm_data.study_id = p_study_id 
        AND cgm_data.date_time BETWEEN start_date AND end_date;-- + INTERVAL '1 day';

        -- Calculating glucose_management_indicator (GMI)
        SELECT ROUND(AVG(cgm_value)::numeric * 0.155 + 95, 2) INTO glucose_management_indicator
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view cgm_data
        WHERE cgm_data.participant_sid = p_participant_id AND cgm_data.study_id = p_study_id AND cgm_data.date_time BETWEEN start_date AND end_date;

        -- Calculating percentage_time_cgm_active
        SELECT ROUND(
            (COUNT(DISTINCT DATE(date_time)) * 100.0) /
            (DATE(end_date) - DATE(start_date) + 1),
            2
        ) INTO percentage_time_cgm_active
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view cgm_data
        WHERE cgm_data.participant_sid = p_participant_id AND cgm_data.study_id = p_study_id AND cgm_data.date_time BETWEEN start_date AND end_date;

        -- Calculating coefficient_of_variation
        SELECT ROUND(
            (SQRT(
                AVG(cgm_value * cgm_value) - AVG(cgm_value) * AVG(cgm_value)
            ) / AVG(cgm_value))::numeric * 100, 2
        ) INTO coefficient_of_variation
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view cgm_data
        WHERE cgm_data.participant_sid = p_participant_id AND cgm_data.study_id = study_id AND cgm_data.date_time BETWEEN start_date AND end_date;
        
        -- Building the result JSON
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Metrics calculation completed successfully',
            'data', jsonb_build_object(
                'mean_glucose', mean_glucose,
                'number_of_days_cgm_worn', number_of_days_cgm_worn,
                'percentage_active', percentage_time_cgm_active,
                'glucose_management_indicator', glucose_management_indicator,
                'coefficient_of_variation', coefficient_of_variation
            )
        );

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON response
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while fetching data',
            'error_details', jsonb_build_object(
                'error', err_message,
                'detail', err_detail,
                'hint', err_hint,
                'context', err_context,
                'state', err_state
            )
        );
    END;

    -- Return the result
    RETURN result;
END;
$function$;



-- ************************************************************************************************
-- Function Name: get_daily_glucose_profile
-- Purpose:
-- This metrics function is to get the retrieves a daily glucose profile for a specific participant in a research study over a given date range. 
   
-- Parameters:
--  - p_study_id: The unique identifier for the study.
--  - p_participant_id: The ID of the participant.
--   - start_date : The start date of the observation.
--   - end_date : The end date of the observation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************

 DROP FUNCTION IF EXISTS drh_stateless_raw_observation.get_daily_glucose_profile(text, text, date, date);


CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_daily_glucose_profile(p_study_id text, p_participant_id text, start_date date, end_date date)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    p_tenant_id TEXT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    function_name TEXT := 'drh_stateless_raw_observation.get_daily_glucose_profile';
    current_query TEXT := pg_catalog.current_query();
    daily_glucose_profile JSONB;
    study_exists BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_participant_id', p_participant_id,
        'start_date', start_date,
        'end_date', end_date
    );


    BEGIN
        -- Fetch tenant_id for the given study_id
        SELECT tenant_id 
        INTO p_tenant_id 
        FROM drh_stateful_research_study.research_study 
        WHERE study_id = p_study_id;

        -- Validate study_id
        IF NOT EXISTS (
            SELECT 1
            FROM drh_stateless_raw_observation.file_device_cgm_observation_view
            WHERE study_id = p_study_id
        ) THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Invalid study_id',
                'details', jsonb_build_object('study_id', p_study_id)
            );
        END IF;

        -- Validate p_participant_id
        IF NOT EXISTS (
            SELECT 1
            FROM drh_stateless_raw_observation.file_device_cgm_observation_view
            WHERE participant_sid = p_participant_id
        ) THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Invalid participant id',
                'details', jsonb_build_object('participant_id', p_participant_id)
            );
        END IF;

        -- Check if the participant belongs to the study
        study_exists = EXISTS (
            SELECT 1
            FROM drh_stateless_research_study.participant_data_view pdv
            WHERE pdv.study_id = p_study_id
              AND pdv.participant_id = p_participant_id
        );
    
        -- If not exists, return failure message
        IF NOT study_exists THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Participant does not belong to the study',
                'details', jsonb_build_object(
                    'study_id', p_study_id,
                    'participant_id', p_participant_id
                )
            );
        END IF;

             -- Validate start_date is less than or equal to end_date
        IF start_date > end_date THEN
            RETURN jsonb_build_object(
                'status', 'failure',
                'message', 'Invalid date range: start_date must be less than or equal to end_date',
                'details', jsonb_build_object(
                    'start_date', start_date,
                    'end_date', end_date
                )
            );
        END IF;


        -- Prepare the glucose profile with a CTE to handle the averaging first
        WITH hourly_averages AS (
			SELECT 
			    date_time,
			    TO_CHAR(date_time, 'YYYY-MM-DD"T"HH24:00:00') AS formatted_datetime,
			    TO_CHAR(date_time, 'YYYY-MM-DD') AS formatted_date,
			    TO_CHAR(date_time, 'HH24') AS hour,
			    ROUND(AVG(cgm_value::numeric), 2) AS avg_glucose
			FROM 
			    drh_stateless_raw_observation.file_device_cgm_observation_view
			WHERE 
			    participant_sid = p_participant_id
			    AND study_id = p_study_id
			    AND DATE(date_time) BETWEEN start_date::date AND end_date::date
			GROUP BY 
			    TO_CHAR(date_time, 'YYYY-MM-DD"T"HH24:00:00'),
			    TO_CHAR(date_time, 'YYYY-MM-DD'),
			    TO_CHAR(date_time, 'HH24'),
			    date_time
			ORDER BY 
			    date_time , hour ASC

        )
        SELECT jsonb_agg(
            jsonb_build_object(
                'date_time', formatted_datetime,
                'date', formatted_date,
                'hour', hour,
                'glucose', avg_glucose
            )
            ORDER BY date_time ASC
        )
        INTO daily_glucose_profile
        FROM hourly_averages;

        -- Default to an empty array if no rows match
        IF daily_glucose_profile IS NULL THEN
            daily_glucose_profile := '[]'::JSONB;
        END IF;

        -- Build the result JSONB with the prepared date range data
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Daily glucose profile calculated successfully',
            'data', daily_glucose_profile
        );

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Log the error details in the exception log table
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON response
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while fetching daily glucose profile',
            'error_details', jsonb_build_object(
                'error', err_message,
                'detail', err_detail,
                'hint', err_hint,
                'context', err_context,
                'state', err_state
            )
        );
    END;

    -- Return the result
    RETURN result;
END;
$function$;




CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_time_range_stacked_data(
    p_study_id text,
    p_participant_id text,
    p_start_date date,
    p_end_date date
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    study_exists BOOLEAN;
    result jsonb;
    
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_raw_observation.get_time_range_stacked_data'; 
    current_query text := pg_catalog.current_query(); 
    metrics_data jsonb;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_participant_id', p_participant_id,
        'start_date', p_start_date,
        'end_date', p_end_date
    );

    -- Validate study_id
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE study_id = p_study_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid study_id',
            'details', jsonb_build_object('study_id', p_study_id)
        );
    END IF;

    -- Validate p_participant_id
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE participant_sid = p_participant_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid participant_id',
            'details', jsonb_build_object('participant_id', p_participant_id)
        );
    END IF;

    -- Validate start_date is less than or equal to end_date
    IF p_start_date > p_end_date THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid date range: start_date must be less than or equal to end_date',
            'details', jsonb_build_object(
                'start_date', p_start_date,
                'end_date', p_end_date
            )
        );
    END IF;

    -- Check if the participant belongs to the study
    study_exists = EXISTS (
        SELECT 1
        FROM drh_stateless_research_study.participant_data_view
        WHERE study_id = p_study_id
          AND participant_id = p_participant_id
    );

    -- If not exists, return failure message
    IF NOT study_exists THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant does not belong to the study',
            'details', jsonb_build_object(
                'study_id', p_study_id,
                'participant_id', p_participant_id
            )
        );
    END IF;

    -- Calculate glucose metrics
    WITH GlucoseMetrics AS (
        SELECT
            participant_sid AS participant_id,
            COUNT(*) AS total_readings,
            SUM(CASE WHEN cgm_value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) AS time_below_range_low,
            SUM(CASE WHEN cgm_value < 54 THEN 1 ELSE 0 END) AS time_below_range_very_low,
            SUM(CASE WHEN cgm_value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) AS time_in_range,
            SUM(CASE WHEN cgm_value > 250 THEN 1 ELSE 0 END) AS time_above_vh,
            SUM(CASE WHEN cgm_value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) AS time_above_range_high
        FROM
            drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE
            participant_sid = p_participant_id
            AND DATE(date_time) BETWEEN p_start_date AND p_end_date
        GROUP BY
            participant_sid
    ), Defaults AS (
        SELECT
            0 AS total_readings,
            0 AS time_below_range_low,
            0 AS time_below_range_very_low,
            0 AS time_in_range,
            0 AS time_above_vh,
            0 AS time_above_range_high
    )
    SELECT
        jsonb_build_object(
            'time_below_range_low_percentage', COALESCE(CASE WHEN gm.total_readings = 0 THEN 0 ELSE (gm.time_below_range_low * 100.0 / gm.total_readings) END, 0),
            'time_below_range_low', COALESCE(gm.time_below_range_low, 0),
            'time_below_range_low_string', COALESCE(CASE WHEN gm.total_readings = 0 THEN '00 hours, 00 minutes' ELSE TO_CHAR((gm.time_below_range_low * 5) / 60, 'FM00') || ' hours, ' || TO_CHAR((gm.time_below_range_low * 5) % 60, 'FM00') || ' minutes' END, '00 hours, 00 minutes'),
            'time_below_range_very_low_percentage', COALESCE(CASE WHEN gm.total_readings = 0 THEN 0 ELSE (gm.time_below_range_very_low * 100.0 / gm.total_readings) END, 0),
            'time_below_range_very_low', COALESCE(gm.time_below_range_very_low, 0),
            'time_below_range_very_low_string', COALESCE(CASE WHEN gm.total_readings = 0 THEN '00 hours, 00 minutes' ELSE TO_CHAR((gm.time_below_range_very_low * 5) / 60, 'FM00') || ' hours, ' || TO_CHAR((gm.time_below_range_very_low * 5) % 60, 'FM00') || ' minutes' END, '00 hours, 00 minutes'),
            'time_in_range_percentage', COALESCE(CASE WHEN gm.total_readings = 0 THEN 0 ELSE (gm.time_in_range * 100.0 / gm.total_readings) END, 0),
            'time_in_range', COALESCE(gm.time_in_range, 0),
            'time_in_range_string', COALESCE(CASE WHEN gm.total_readings = 0 THEN '00 hours, 00 minutes' ELSE TO_CHAR((gm.time_in_range * 5) / 60, 'FM00') || ' hours, ' || TO_CHAR((gm.time_in_range * 5) % 60, 'FM00') || ' minutes' END, '00 hours, 00 minutes'),
            'time_above_vh_percentage', COALESCE(CASE WHEN gm.total_readings = 0 THEN 0 ELSE (gm.time_above_vh * 100.0 / gm.total_readings) END, 0),
            'time_above_vh', COALESCE(gm.time_above_vh, 0),
            'time_above_vh_string', COALESCE(CASE WHEN gm.total_readings = 0 THEN '00 hours, 00 minutes' ELSE TO_CHAR((gm.time_above_vh * 5) / 60, 'FM00') || ' hours, ' || TO_CHAR((gm.time_above_vh * 5) % 60, 'FM00') || ' minutes' END, '00 hours, 00 minutes'),
            'time_above_range_high_percentage', COALESCE(CASE WHEN gm.total_readings = 0 THEN 0 ELSE (gm.time_above_range_high * 100.0 / gm.total_readings) END, 0),
            'time_above_range_high', COALESCE(gm.time_above_range_high, 0),
            'time_above_range_high_string', COALESCE(CASE WHEN gm.total_readings = 0 THEN '00 hours, 00 minutes' ELSE TO_CHAR((gm.time_above_range_high * 5) / 60, 'FM00') || ' hours, ' || TO_CHAR((gm.time_above_range_high * 5) % 60, 'FM00') || ' minutes' END, '00 hours, 00 minutes')
        ) INTO metrics_data
    FROM
        Defaults d
        LEFT JOIN GlucoseMetrics gm ON 1 = 1;

    -- Building the result JSON
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Metrics calculation completed successfully',
        'data', metrics_data            
    );
    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during metrics retrieval',
        'error_details', error_details_json
    );

    RETURN result;
END;
$function$;

-- ************************************************************************************************
-- Function Name: get_ambulatory_glucose_profile
-- Purpose:
-- This metrics function is to get the ambulatory glucose profile.
-- Parameters:
--  - p_study_id: The unique identifier for the study.
--  - p_participant_id: The ID of the participant.
--  - start_date : The start date of the observation.
--  - end_date : The end date of the observation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************


DROP FUNCTION IF EXISTS drh_stateless_raw_observation.get_ambulatory_glucose_profile(text, text, date, date);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_ambulatory_glucose_profile(p_study_id text, p_participant_id text, p_start_date date, p_end_date date)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result jsonb;
    study_exists BOOLEAN;
    glucose_data jsonb;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_raw_observation.get_ambulatory_glucose_profile';
    current_query text := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_participant_id', p_participant_id,
        'start_date', p_start_date,
        'end_date', p_end_date
    );


    -- Validate `p_study_id`
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE study_id = p_study_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid study_id',
            'details', jsonb_build_object('study_id', p_study_id)
        );
    END IF;

    -- Validate `p_participant_id`
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE participant_sid = p_participant_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid participant_id',
            'details', jsonb_build_object('participant_id', p_participant_id)
        );
    END IF;

    -- Validate date range
    IF p_start_date > p_end_date THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid date range: start_date must be less than or equal to end_date',
            'details', jsonb_build_object(
                'start_date', p_start_date,
                'end_date', p_end_date
            )
        );
    END IF;

    -- Check if the participant belongs to the study
    study_exists = EXISTS (
        SELECT 1
        FROM drh_stateless_research_study.participant_data_view
        WHERE study_id = p_study_id
          AND participant_id = p_participant_id
    );

    -- If not exists, return failure message
    IF NOT study_exists THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant does not belong to the study',
            'details', jsonb_build_object(
                'study_id', p_study_id,
                'participant_id', p_participant_id
            )
        );
    END IF;

    -- Get the ambulatory glucose profile data

     WITH date_range AS (
        SELECT 
            p_start_date::date AS start_date, 
            p_end_date::date AS end_date
    ),
    glucose_data AS (
        SELECT
            v.participant_sid AS participant_id,
            v.date_time AS timestamp,
            to_char(v.date_time, 'YYYY-MM-DD HH24') AS hour_value,
            v.cgm_value AS glucose_level
        FROM
            drh_stateless_raw_observation.file_device_cgm_observation_view v
        WHERE 
            v.participant_sid = p_participant_id 
            AND v.study_id = p_study_id
            AND v.date_time BETWEEN p_start_date::date AND p_end_date::date
    ),
    ranked_data AS (
        SELECT
            participant_id,
            hour_value,
            glucose_level,
            ROW_NUMBER() OVER (PARTITION BY hour_value ORDER BY glucose_level) AS row_num,
            COUNT(*) OVER (PARTITION BY hour_value) AS total_count
        FROM glucose_data
    ),
    percentiles AS (
        SELECT
            hour_value AS hour,
            MAX(CASE WHEN row_num = CEIL(0.05 * total_count) THEN glucose_level END) AS p5,
            MAX(CASE WHEN row_num = CEIL(0.25 * total_count) THEN glucose_level END) AS p25,
            MAX(CASE WHEN row_num = CEIL(0.50 * total_count) THEN glucose_level END) AS p50,
            MAX(CASE WHEN row_num = CEIL(0.75 * total_count) THEN glucose_level END) AS p75,
            MAX(CASE WHEN row_num = CEIL(0.95 * total_count) THEN glucose_level END) AS p95
        FROM ranked_data
        GROUP BY hour_value
    ),
    hourly_averages AS (
        SELECT
            SUBSTR(hour, 1, 10) AS date,
            SUBSTR(hour, 12) AS hour,
            COALESCE(AVG(p5), 0) AS p5,
            COALESCE(AVG(p25), 0) AS p25,
            COALESCE(AVG(p50), 0) AS p50,
            COALESCE(AVG(p75), 0) AS p75,
            COALESCE(AVG(p95), 0) AS p95
        FROM percentiles
        GROUP BY date, hour
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'participant_id', p_participant_id,
            'hour', hour,
            'p5', COALESCE(p5, 0),
            'p25', COALESCE(p25, 0),
            'p50', COALESCE(p50, 0),
            'p75', COALESCE(p75, 0),
            'p95', COALESCE(p95, 0)
        )
    ) INTO glucose_data
    FROM hourly_averages;

    -- Build success response
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Ambulatory glucose profile calculation completed successfully',
        'data', glucose_data
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
   -- Capture error details
    GET STACKED DIAGNOSTICS
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Return failure JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during metrics retrieval',
        'error_details', error_details_json
    );
END;
$function$;

-- ************************************************************************************************
-- Function Name: get_advanced_metrics
-- Purpose:
-- This metrics function is to get the following matrices.

--labilityIndex
--getMeanAmplitude
--calculateMValue
--calculateTimeInTightRange
--calculateAverageDailyRisk
--calculateJIndex
--calculateLBGIandHBGI
--calculateGRADE
--calculateMeanOfDailyDifferences
--calculateCONGA

-- Parameters:
--  - p_study_id: The unique identifier for the study.
--  - p_participant_id: The ID of the participant.
--  - start_date : The start date of the observation.
--  - end_date : The end date of the observation.
-- Returns:
--  - JSONB object indicating the success or failure of the operation along with any error details.
-- ************************************************************************************************

DROP FUNCTION IF EXISTS drh_stateless_raw_observation.get_advanced_metrics(text, text, date, date);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_advanced_metrics(p_study_id text, p_participant_id text, p_start_date date, p_end_date date)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    study_exists boolean;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_raw_observation.get_advanced_metrics'; 
    current_query text := pg_catalog.current_query(); 

    avg_risk_score FLOAT;
    mean_amplitude FLOAT;
    m_value FLOAT;
    average_daily_risk FLOAT;
    lbgi FLOAT;
    hbgi FLOAT;
    mean_daily_diff FLOAT;
    conga_hourly FLOAT;
    liability_index FLOAT;
    j_index FLOAT;
    avg_risk_score_value FLOAT;

    -- Declare the variables to store the results
    hypoglycemic_episodes_variable FLOAT := 0;
    euglycemic_episodes_variable FLOAT := 0;
    hyperglycemic_episodes_variable FLOAT := 0;

-- Declare the variables for time in tight range
    time_in_tight_range_percentage FLOAT;
    time_in_tight_range_percentage_var FLOAT;
    time_in_tight_range INT;
    time_in_tight_range_value INT;
    time_in_tight_range_value_var INT;
    time_range_string TEXT;
    time_in_tight_range_var INT;
    time_range_string_var TEXT;
    in_tight_range_count INT;
    result JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_participant_id', p_participant_id,
        'start_date', p_start_date,
        'end_date', p_end_date
    );
    
    -- Validate study_id
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE study_id = p_study_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid study_id',
            'details', jsonb_build_object('study_id', p_study_id)
        );
    END IF;

    -- Validate p_participant_id
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE participant_sid = p_participant_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid participant_id',
            'details', jsonb_build_object('participant_id', p_participant_id)
        );
    END IF;

    -- Validate start_date is less than or equal to end_date
    IF p_start_date > p_end_date THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid date range: start_date must be less than or equal to end_date',
            'details', jsonb_build_object(
                'start_date', p_start_date,
                'end_date', p_end_date
            )
        );
    END IF;

    -- Check if the participant belongs to the study
    study_exists = EXISTS (
        SELECT 1
        FROM drh_stateless_research_study.participant_data_view
        WHERE study_id = p_study_id
          AND participant_id = p_participant_id
    );

    -- If not exists, return failure message
    IF NOT study_exists THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant does not belong to the study',
            'details', jsonb_build_object(
                'study_id', p_study_id,
                'participant_id', p_participant_id
            )
        );
    END IF;
   
    -- Calculate avg_risk_score
    WITH risk_scores AS (
        SELECT 
            participant_sid,
            CASE
                WHEN cgm_value < 90 THEN 10 * (5 - (cgm_value / 18.0)) * (5 - (cgm_value / 18.0))
                WHEN cgm_value > 180 THEN 10 * ((cgm_value / 18.0) - 10) * ((cgm_value / 18.0) - 10)
                ELSE 0
            END AS risk_score
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE 
            participant_sid = p_participant_id AND study_id = p_study_id
            AND DATE(date_time) BETWEEN p_start_date::date AND p_end_date::date
    )
    SELECT AVG(risk_score) INTO avg_risk_score FROM risk_scores;
    SELECT AVG(avg_risk_score) INTO avg_risk_score_value ;

    -- Calculate mean_amplitude
  WITH amplitude_data AS (
    SELECT 
        ABS(MAX(cgm_value) - MIN(cgm_value)) AS amplitude
    FROM drh_stateless_raw_observation.file_device_cgm_observation_view
    WHERE 
        participant_sid = p_participant_id 
        AND study_id = p_study_id
        AND date_time::date BETWEEN p_start_date AND p_end_date  -- Ensures date comparison
    GROUP BY date_time::date  -- Ensures grouping by date
)
    SELECT AVG(amplitude) INTO mean_amplitude FROM amplitude_data;

    -- Calculate m_value
    WITH participant_min_max AS (
        SELECT 
            MIN(cgm_value) AS min_glucose,
            MAX(cgm_value) AS max_glucose,
            MIN(date_time) AS start_time,
            MAX(date_time) AS end_time
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE 
            participant_sid = p_participant_id AND study_id = p_study_id 
            AND date_time::date  BETWEEN  p_start_date::date AND p_end_date::date
        GROUP BY participant_sid
    )

SELECT 
    (max_glucose - min_glucose) / 
    ((EXTRACT(EPOCH FROM end_time::timestamp) - EXTRACT(EPOCH FROM start_time::timestamp)) / 60.0) 
INTO m_value 
FROM participant_min_max;

    -- Calculate average_daily_risk

WITH DailyRisk AS (
    SELECT 
        cgm_obs.participant_sid AS participant_id, 
        cgm_obs.study_id, 
        DATE(cgm_obs.date_time) AS day, 
        MAX(cgm_obs.cgm_value) - MIN(cgm_obs.cgm_value) AS daily_range 
    FROM 
        drh_stateless_raw_observation.file_device_cgm_observation_view cgm_obs
    WHERE 
        DATE(cgm_obs.date_time) BETWEEN p_start_date::date AND p_end_date::date  
    GROUP BY 
        cgm_obs.participant_sid, 
        DATE(cgm_obs.date_time), 
        cgm_obs.study_id 
), 
AverageDailyRisk AS ( 
    SELECT 
        participant_id, 
        study_id,
        AVG(daily_range) AS avg_daily_risk  -- Renamed to avoid conflict
    FROM 
        DailyRisk 
    GROUP BY 
        participant_id, 
        study_id
) 
SELECT  
    avg_daily_risk  -- Use new column name
INTO average_daily_risk  -- Correctly storing the value in PL/pgSQL variable
FROM 
    AverageDailyRisk  
WHERE 
    participant_id = p_participant_id 
    AND study_id = p_study_id;



    -- Calculate lbgi and hbgi

   WITH lbgi_hbgi AS (
     SELECT 
        ROUND(CAST(SUM(
            CASE 
                WHEN (cgm_value - 2.5) / 2.5 > 0 
                THEN POWER((cgm_value - 2.5) / 2.5, 2) 
                ELSE 0 
            END) * 5 AS numeric), 2) AS lbgi_value,  -- Renamed to avoid conflict
       ROUND(CAST(
    SUM(
        CASE 
            WHEN (cgm_value - 9.5) / 9.5 > 0 
            THEN POWER((cgm_value - 9.5) / 9.5, 2) 
            ELSE 0 
        END
    ) * 5 AS numeric), 2) AS hbgi_value
  -- Renamed to avoid conflict
    FROM drh_stateless_raw_observation.file_device_cgm_observation_view
    WHERE 
        participant_sid = p_participant_id 
        AND study_id = p_study_id
        AND DATE(date_time) BETWEEN p_start_date::date AND p_end_date::date
)
    SELECT lbgi_value, hbgi_value 
    INTO lbgi, hbgi  -- Assign to PL/pgSQL variables
    FROM lbgi_hbgi;


    -- Calculate mean_daily_diff
    WITH daily_diffs AS (
        SELECT 
            cgm_value - LAG(cgm_value) OVER (PARTITION BY participant_sid ORDER BY date_time) AS daily_diff
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE 
            participant_sid = p_participant_id AND study_id = p_study_id
            AND DATE(date_time) BETWEEN p_start_date::date AND p_end_date::date
    )
    SELECT AVG(daily_diff) INTO mean_daily_diff FROM daily_diffs WHERE daily_diff IS NOT NULL;

    -- Calculate conga_hourly
    WITH lag_values AS (
        SELECT 
            LAG(cgm_value) OVER (PARTITION BY participant_sid ORDER BY date_time) AS lag_cgm_value,
            cgm_value
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE 
            participant_sid = p_participant_id AND study_id = p_study_id
            AND DATE(date_time) BETWEEN p_start_date::date AND p_end_date::date
    )
    SELECT SQRT(AVG((cgm_value - lag_cgm_value) * (cgm_value - lag_cgm_value))) INTO conga_hourly 
    FROM lag_values WHERE lag_cgm_value IS NOT NULL;


    -- Calculate liability_index
        WITH liability_index_calc AS (
            SELECT
                SUM(CASE WHEN cgm_value < 70 THEN 1 ELSE 0 END) AS hypoglycemic_episodes, 
                SUM(CASE WHEN cgm_value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) AS euglycemic_episodes, 
                SUM(CASE WHEN cgm_value > 180 THEN 1 ELSE 0 END) AS hyperglycemic_episodes, 
                COUNT(*) AS total_records
            FROM drh_stateless_raw_observation.file_device_cgm_observation_view
            WHERE 
            participant_sid = p_participant_id AND study_id = p_study_id
                AND DATE(date_time) BETWEEN p_start_date::date AND p_end_date::date
        )
        SELECT 
            hypoglycemic_episodes,
            euglycemic_episodes,
            hyperglycemic_episodes,
            CASE 
                WHEN total_records > 0 
                THEN ROUND(CAST((hypoglycemic_episodes + hyperglycemic_episodes) / total_records AS numeric), 2) 
                ELSE 0 -- Default to 0 when there are no records
            END 
        INTO  
            hypoglycemic_episodes_variable,
            euglycemic_episodes_variable,
            hyperglycemic_episodes_variable,
            liability_index 
        FROM liability_index_calc;


    -- Calculate j_index
        WITH glucose_stats AS (
            SELECT
                participant_sid AS participant_id,
                AVG(cgm_value) AS mean_glucose, 
                (AVG(cgm_value * cgm_value) - AVG(cgm_value) * AVG(cgm_value)) AS variance_glucose
            FROM drh_stateless_raw_observation.file_device_cgm_observation_view
            WHERE 
                participant_sid = p_participant_id 
                AND study_id = p_study_id
                AND DATE(date_time) BETWEEN p_start_date::date AND p_end_date::date
            GROUP BY participant_sid
        )
        SELECT
            ROUND(
                CAST(
                    0.001 * (mean_glucose + 
                        CASE 
                            WHEN variance_glucose >= 0 THEN SQRT(variance_glucose)
                            ELSE 0  -- Avoid negative square roots
                        END
                    ) * (mean_glucose + 
                        CASE 
                            WHEN variance_glucose >= 0 THEN SQRT(variance_glucose)
                            ELSE 0
                        END
                    ) AS numeric
                ), 
                2
            ) AS j_index
        INTO j_index 
        FROM glucose_stats;



        -- Calculate time in tight range
        WITH time_in_range AS (
            SELECT 
                participant_sid,
                SUM(CASE WHEN cgm_value BETWEEN 70 AND 140 THEN 1 ELSE 0 END) AS in_tight_range_count,
                (SUM(CASE WHEN cgm_value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 5) / 60 AS minutes_part,
                (SUM(CASE WHEN cgm_value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 5) % 60 AS seconds_part,
                SUM(CASE WHEN cgm_value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) AS time_in_range,
                COUNT(*) AS total_records
            FROM drh_stateless_raw_observation.file_device_cgm_observation_view
            WHERE 
                participant_sid = p_participant_id
                AND study_id = p_study_id
                AND DATE(date_time) BETWEEN p_start_date::date AND p_end_date::date
            GROUP BY participant_sid
        )
        SELECT 
            (tir.in_tight_range_count * 100.0 / NULLIF(tir.time_in_range, 0)) AS time_in_tight_range_percentage,
            tir.in_tight_range_count,
            CONCAT(tir.minutes_part, ' minutes ', tir.seconds_part, ' seconds') AS time_range_string
        INTO time_in_tight_range_percentage_var, time_in_tight_range_var, time_range_string_var
        FROM time_in_range tir
        LIMIT 1;

-- Prepare result in JSON format without array
result := jsonb_build_object(
    'data', jsonb_build_object(
        'avg_risk_score', avg_risk_score,
        'mean_amplitude', mean_amplitude,
        'm_value', m_value,
        'average_daily_risk', average_daily_risk,
        'lbgi', lbgi,
        'hbgi', hbgi,
        'mean_daily_diff', mean_daily_diff,
        'conga_hourly', conga_hourly,
        'liability_index', liability_index,
        'j_index', j_index,
         'hypoglycemic_episodes', COALESCE(hypoglycemic_episodes_variable, 0),
         'euglycemic_episodes', COALESCE(euglycemic_episodes_variable, 0),
         'hyperglycemic_episodes', COALESCE(hyperglycemic_episodes_variable, 0),
        'GRADE', COALESCE(avg_risk_score_value, 0),
        'time_in_tight_range', time_in_tight_range_var,
        'time_in_tight_range_percentage',time_in_tight_range_percentage_var,
        'time_range_string',time_range_string_var
           
    ),
    'status', 'success',
    'message', 'Advanced metrics calculation completed successfully'
);





RETURN result;

EXCEPTION WHEN OTHERS THEN
   
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during metrics retrieval',
        'error_details', error_details_json
    );

    RETURN result;
END;
$function$;



--*************************************Glycemic Risk Indicator**************************************

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.get_glycemic_risk_indicator(p_study_id text, p_participant_id text, p_start_date DATE, p_end_date DATE)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result jsonb;
    study_exists BOOLEAN;
    glucose_data jsonb;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_raw_observation.get_glycemic_risk_indicator';
    current_query text := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_participant_id', p_participant_id,
        'start_date', p_start_date,
        'end_date', p_end_date
    );

    -- Validate `p_study_id`
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE study_id = p_study_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid study_id',
            'details', jsonb_build_object('study_id', p_study_id)
        );
    END IF;

    -- Validate `p_participant_id`
    IF NOT EXISTS (
        SELECT 1
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE participant_sid = p_participant_id
    ) THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid participant_id',
            'details', jsonb_build_object('participant_id', p_participant_id)
        );
    END IF;

    -- Validate date range
    IF p_start_date > p_end_date THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Invalid date range: start_date must be less than or equal to end_date',
            'details', jsonb_build_object(
                'start_date', p_start_date,
                'end_date', p_end_date
            )
        );
    END IF;

    -- Check if the participant belongs to the study
    study_exists := EXISTS (
        SELECT 1
        FROM drh_stateless_research_study.participant_data_view
        WHERE study_id = p_study_id
          AND participant_id = p_participant_id
    );

    -- If not exists, return failure message
    IF NOT study_exists THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Participant does not belong to the study',
            'details', jsonb_build_object(
                'study_id', p_study_id,
                'participant_id', p_participant_id
            )
        );
    END IF;

    -- Get the ambulatory glucose profile data
    SELECT jsonb_build_object(
        'time_above_VH_percentage', ROUND(COALESCE((SUM(CASE WHEN cgm_value > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2),
        'time_above_H_percentage', ROUND(COALESCE((SUM(CASE WHEN cgm_value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2),
        'time_in_range_percentage', ROUND(COALESCE((SUM(CASE WHEN cgm_value BETWEEN 70 AND 180 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2),
        'time_below_low_percentage', ROUND(COALESCE((SUM(CASE WHEN cgm_value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2),
        'time_below_VL_percentage', ROUND(COALESCE((SUM(CASE WHEN cgm_value < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 0), 2),
        'Hypoglycemia_Component', ROUND(COALESCE(
            (SUM(CASE WHEN cgm_value < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + 
            (0.8 * (SUM(CASE WHEN cgm_value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))), 0), 2),
        'Hyperglycemia_Component', ROUND(COALESCE(
            (SUM(CASE WHEN cgm_value > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + 
            (0.5 * (SUM(CASE WHEN cgm_value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))), 0), 2),
        'GRI', ROUND(COALESCE(
            (3.0 * ((SUM(CASE WHEN cgm_value < 54 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + 
                    (0.8 * (SUM(CASE WHEN cgm_value BETWEEN 54 AND 69 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))))) + 
            (1.6 * ((SUM(CASE WHEN cgm_value > 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) + 
                    (0.5 * (SUM(CASE WHEN cgm_value BETWEEN 181 AND 250 THEN 1 ELSE 0 END) * 100.0 / COUNT(*))))), 0), 2)
    ) INTO glucose_data
    FROM drh_stateless_raw_observation.file_device_cgm_observation_view
    WHERE participant_sid = p_participant_id
        AND DATE(date_time) BETWEEN p_start_date AND p_end_date
        AND study_id = p_study_id
    GROUP BY participant_sid;

    -- Build success response
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Glycemic risk indicator calculation completed successfully',
        'data', glucose_data
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Return failure JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during metrics retrieval',
        'error_details', error_details_json
    );
END;
$function$
;


--**********************************************************************************
--******************************SAVE DB DETAILS*************************************
--**********************************************************************************
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.insert_cgm_raw_db(p_input_json jsonb);
DROP FUNCTION IF EXISTS drh_stateless_raw_observation.insert_cgm_raw_db(p_input_json jsonb, jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.insert_cgm_raw_db(p_input_json jsonb, p_activity_json jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    rows_inserted INTEGER;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.insert_cgm_raw_db';
    current_query TEXT := pg_catalog.current_query();

    v_db_file_id TEXT := p_input_json->>'db_file_id';
    v_file_name TEXT := p_input_json->>'file_name';
    v_file_url TEXT := p_input_json->>'file_url';
    v_upload_timestamp TIMESTAMPTZ := COALESCE((p_input_json->>'upload_timestamp')::timestamptz, CURRENT_TIMESTAMP);
    v_uploaded_by TEXT := p_input_json->>'uploaded_by';
    v_file_size TEXT := p_input_json->>'file_size';
    v_study_id TEXT := p_input_json->>'study_id';
    v_rec_status_id INTEGER;
    v_created_by TEXT := COALESCE(p_input_json->>'current_user_id', 'UNKNOWN');
    v_process_status TEXT := NULL; -- Default value
    v_tenant_id TEXT;
   
    last_file_interaction_id TEXT; 
 	file_interaction_result JSONB;
 	inprogress_file_interaction_id TEXT;
	completed_file_interaction_id TEXT;

	v_file_interaction_params JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_input_json', p_input_json        
    );

   -- Extract values from JSON input
    last_file_interaction_id := p_input_json->>'last_file_interaction_id';
    --v_interaction_hierarchy := COALESCE(p_input_json->'interaction_hierarchy', '[]'::jsonb); -- Ensure it's a JSON array

   	-- Call save_file_interaction_log to get the file interaction ID
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', last_file_interaction_id,
        'interaction_action_type', 'SAVE DB',
        'interaction_status', 'IN PROGRESS',
        'description', 'Database save process initiated',
        'db_file_id', v_db_file_id,
        'file_name', v_file_name,
        'file_category', 'Database',
        'created_by', v_created_by
    );
   	-- Call save_file_interaction_log to get the file interaction ID   
	file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   
   -- Extract file_interaction_id from result
    IF file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    ELSE
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Failed to insert file interaction',
            'error_details', file_interaction_result -> 'error_details'
        );
    END IF;
    -- Get ACTIVE record status ID
    SELECT rs.value INTO v_rec_status_id 
    FROM drh_stateful_party.record_status rs 
    WHERE rs.code = 'ACTIVE' 
    LIMIT 1;
   
   	-- Query to find the tenant_id using organization party id
    SELECT id INTO v_tenant_id
    FROM drh_stateful_research_study.organization
    WHERE party_id = p_input_json->>'org_party_id';

    -- Insert data only if required fields are present
    IF v_db_file_id IS NOT NULL AND v_file_name IS NOT NULL AND v_study_id IS NOT NULL AND v_tenant_id IS NOT NULL THEN
        INSERT INTO drh_stateful_raw_data.cgm_raw_db (
            db_file_id,
            file_name,
            file_url,
            upload_timestamp,
            uploaded_by,
            file_size,
            is_processed,
            process_status,
            db_type,
            study_id,
            tenant_id,
            rec_status_id,
            created_at,
            created_by,
            file_interaction_id
        ) VALUES (
            v_db_file_id,
            v_file_name,
            v_file_url,
            v_upload_timestamp,
            v_uploaded_by,
            v_file_size,
            false,
            v_process_status,
            'SQLite', -- Default SQLite
            v_study_id,
            v_tenant_id,
            v_rec_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            v_created_by,
            inprogress_file_interaction_id
        );
    ELSE
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Some mandatory fields are missing. Please provide all required fields.'
        );    
    END IF;

   -- Call save_file_interaction_log to get the file interaction ID
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', inprogress_file_interaction_id,
        'interaction_action_type', 'SAVE DB',
        'interaction_status', 'SUCCESS',
        'description', 'Database save process completed',
        'db_file_id', v_db_file_id,
        'file_name', v_file_name,
        'file_category', 'Database',
        'created_by', v_created_by
    );
   	-- Call save_file_interaction_log to get the file interaction ID   
	file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   
   -- Extract file_interaction_id from result
    IF file_interaction_result ->> 'status' = 'success' THEN
       completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
       --Update file interaction completion id into cgm_raw_db table
       UPDATE drh_stateful_raw_data.cgm_raw_db set file_interaction_id =completed_file_interaction_id where db_file_id=v_db_file_id;
    ELSE
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Failed to insert file interaction',
            'error_details', file_interaction_result -> 'error_details'
        );
    END IF;

    -- Activity log feature
    IF p_activity_json IS NOT NULL AND p_activity_json ? 'session_id' THEN
        -- Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='INSERT_CGM_RAW_DB' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Insert CGM Raw DB',
            'activity_description', format('Inserted CGM raw DB file: %s for study_id %s', v_file_name, v_study_id)
        );

        -- Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(v_created_by, v_activity_log_json);
    END IF;
   
   -- Return success response with file_interaction_id
    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'Database details added successfully.',
        'file_interaction_id', completed_file_interaction_id
    );


EXCEPTION 
WHEN OTHERS THEN
    	-- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

    -- Return failure with the error details
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Failed to insert database details',
        'error_details', error_details_json
    );

    RETURN result;
END;
$function$
;



--------------------------------------------------------------------------------------------------------

/*
 * Function: create_partitions_if_not_exists
 * 
 * Description:
 * This function creates partitions for CGM (Continuous Glucose Monitoring) observation data if they do not already exist.
 * It creates partitions at three levels: tenant, study, and participant.
 * 
 * Parameters:
 * - v_tenant_id (TEXT): The tenant ID for which the partition is to be created.
 * - v_study_id (TEXT): The study ID for which the partition is to be created.
 * - v_participant_sid (TEXT): The participant SID for which the partition is to be created.
 * 
 * Returns:
 * - JSONB: A JSONB object indicating the status of the partition creation process. 
 *   On success, it returns a JSONB object with 'status' set to 'success' and a success message.
 *   On failure, it returns a JSONB object with 'status' set to 'failure' and detailed error information.
 * 
 * Error Handling:
 * If an error occurs during the partition creation process, the function captures the error details and logs them into the 
 * 'drh_stateful_activity_audit.exception_log' table. The error details include the function name, error code, error message, 
 * error detail, error hint, error context, the query being executed, and the parameters passed to the function.
 * The function then returns a JSONB object with 'status' set to 'failure' and the error details.
 * 
 * Example Usage:
 * SELECT drh_stateless_raw_observation.create_partitions_if_not_exists('tenant1', 'study1', 'participant1');
 */


CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.create_partitions_if_not_exists(v_tenant_id text, v_study_id text, v_participant_sid text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.create_partitions_if_not_exists';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
    'v_tenant_id', v_tenant_id,
    'v_study_id', v_study_id,
    'v_participant_sid', v_participant_sid
    );

    BEGIN
        -- Create tenant partition if not exists
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS drh_stateful_raw_observation.cgm_observation_tenant_%s
            PARTITION OF drh_stateful_raw_observation.cgm_observation
            FOR VALUES IN (%L)
            PARTITION BY LIST (study_id)',
            v_tenant_id, v_tenant_id
        );

        -- Create study partition if not exists
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS drh_stateful_raw_observation.cgm_observation_study_id_%s
            PARTITION OF drh_stateful_raw_observation.cgm_observation_tenant_%s
            FOR VALUES IN (%L)
            PARTITION BY LIST (research_subject_id)',
            v_study_id, v_tenant_id, v_study_id
        );

        -- Create participant partition if not exists
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS drh_stateful_raw_observation.cgm_observation_research_subject_%s
            PARTITION OF drh_stateful_raw_observation.cgm_observation_study_id_%s
            FOR VALUES IN (%L);',
            replace(v_participant_sid, '-', '_'), v_study_id, v_participant_sid
        );

        -- Return success response
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Partitions created successfully'
        );
        RETURN result;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while creating partitions',
            'error_details', error_details_json
        );
        RETURN result;
    END;
END;
$function$;

/*
Function: drh_stateless_raw_observation.save_cgm_metrics

Description:
This function saves Continuous Glucose Monitoring (CGM) metrics for a given participant. It performs the following steps:
1. Deletes existing records related to the participant from the `participant_base`, `cgm_metrics`, and `cgm_device_info` tables.
2. Inserts new records into the `participant_base` table from the `participant_data_view`.
3. Inserts new records into the `cgm_metrics` table by aggregating data from the `file_device_cgm_observation_view`.
4. Inserts new records into the `cgm_device_info` table by aggregating device and file information from the `file_device_cgm_observation_view`.
5. Returns a JSON response indicating success or failure.

Parameters:
- p_participant_id (text): The ID of the participant for whom the CGM metrics are to be saved.

Returns:
- jsonb: A JSON object indicating the status of the operation. On success, it contains a success message. On failure, it contains error details.

Error Handling:
- If an error occurs during the execution of the function, the error details are captured and logged into the `exception_log` table.
- The function returns a JSON object with the error details.

Example Usage:
SELECT drh_stateless_raw_observation.save_cgm_metrics('participant_id');
*/

DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_cgm_metrics(text);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.save_cgm_metrics(p_participant_id text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.save_cgm_metrics';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_participant_id', p_participant_id);
        
    BEGIN
        -- Delete existing records before insert
        DELETE FROM drh_stateful_research_study.participant_base 
        WHERE participant_id = p_participant_id;

        DELETE FROM drh_stateful_research_study.cgm_metrics 
        WHERE participant_sid = p_participant_id;

        DELETE FROM drh_stateful_research_study.cgm_device_info 
        WHERE participant_sid = p_participant_id;

        -- Insert new records into participant_base
        INSERT INTO drh_stateful_research_study.participant_base 
        SELECT 
            pd.organization_party_id,
            opv.organization_id,
            pd.study_id,
            pd.study_display_id,
            pd.participant_id,
            pd.participant_display_id,
            pd.participant_gender AS gender,
            pd.participant_age AS age,
            pd.study_arm AS "Study Arm",
            pd.baseline_hba1c AS "Baseline HbA1C"
        FROM drh_stateless_research_study.participant_data_view pd
        LEFT JOIN drh_stateless_research_study.organization_party_view opv 
            ON opv.organization_party_id = pd.organization_party_id
        WHERE pd.participant_id = p_participant_id;

        -- Insert new records into cgm_metrics
        INSERT INTO drh_stateful_research_study.cgm_metrics 
        SELECT 
            file_device_cgm_observation_view.participant_sid,
            count(file_device_cgm_observation_view.cgm_value) AS total_readings,
            count(DISTINCT date(file_device_cgm_observation_view.date_time)) AS days_of_wear,
            min(date(file_device_cgm_observation_view.date_time)) AS data_start_date,
            max(date(file_device_cgm_observation_view.date_time)) AS data_end_date,
            avg(file_device_cgm_observation_view.cgm_value) AS avg_glucose,
            stddev(file_device_cgm_observation_view.cgm_value::numeric) AS glucose_stddev,
            sum(
                CASE
                    WHEN file_device_cgm_observation_view.cgm_value >= 70::double precision AND file_device_cgm_observation_view.cgm_value <= 180::double precision THEN 1
                    ELSE 0
                END) AS in_range_count,
            sum(
                CASE
                    WHEN file_device_cgm_observation_view.cgm_value > 250::double precision THEN 1
                    ELSE 0
                END) AS very_high_count,
            sum(
                CASE
                    WHEN file_device_cgm_observation_view.cgm_value >= 181::double precision AND file_device_cgm_observation_view.cgm_value <= 250::double precision THEN 1
                    ELSE 0
                END) AS high_count,
            sum(
                CASE
                    WHEN file_device_cgm_observation_view.cgm_value >= 54::double precision AND file_device_cgm_observation_view.cgm_value <= 69::double precision THEN 1
                    ELSE 0
                END) AS low_count,
            sum(
                CASE
                    WHEN file_device_cgm_observation_view.cgm_value < 54::double precision THEN 1
                    ELSE 0
                END) AS very_low_count,
            sum(
                CASE
                    WHEN file_device_cgm_observation_view.cgm_value > 180::double precision THEN 1
                    ELSE 0
                END) AS above_range_count,
            sum(
                CASE
                    WHEN file_device_cgm_observation_view.cgm_value < 70::double precision THEN 1
                    ELSE 0
                END) AS below_range_count
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE file_device_cgm_observation_view.participant_sid = p_participant_id
        GROUP BY file_device_cgm_observation_view.participant_sid;

        -- Insert new records into cgm_device_info
        INSERT INTO drh_stateful_research_study.cgm_device_info 
        SELECT 
            file_device_cgm_observation_view.participant_sid,
            string_agg(DISTINCT file_device_cgm_observation_view.devicename, ', '::text) AS cgm_devices,
            string_agg(DISTINCT (file_device_cgm_observation_view.file_name || '.'::text) || lower(file_device_cgm_observation_view.file_format), ', '::text) AS cgm_files
        FROM drh_stateless_raw_observation.file_device_cgm_observation_view
        WHERE file_device_cgm_observation_view.participant_sid = p_participant_id
        GROUP BY file_device_cgm_observation_view.participant_sid;

        -- Return success response
        result := jsonb_build_object(
            'status', 'success',
            'message', 'CGM metrics saved successfully'
        );
        RETURN result;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while saving CGM metrics',
            'error_details', error_details_json
        );
        RETURN result;
    END;
END;
$function$;

/*
Function: drh_stateless_raw_observation.save_all_cgm_metrics

Description:
This function saves Continuous Glucose Monitoring (CGM) metrics for all participants. It performs the following steps:
1. Retrieves all participant IDs from the `participant_data_view`.
2. Loops through each participant ID and calls the `save_cgm_metrics` function.
3. Returns a JSON response indicating success or failure.

Returns:
- jsonb: A JSON object indicating the status of the operation. On success, it contains a success message. On failure, it contains error details.

Error Handling:
- If an error occurs during the execution of the function, the error details are captured and logged into the `exception_log` table.
- The function returns a JSON object with the error details.

Example Usage:
SELECT drh_stateless_raw_observation.save_all_cgm_metrics();


DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_all_cgm_metrics();

*/

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.save_all_cgm_metrics()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    participant RECORD;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.save_all_cgm_metrics';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    BEGIN
        -- Loop through each participant and call save_cgm_metrics
        FOR participant IN
            SELECT participant_id
            FROM drh_stateless_research_study.participant_data_view
        LOOP
            PERFORM drh_stateless_raw_observation.save_cgm_metrics(participant.participant_id);
        END LOOP;

        -- Return success response
        result := jsonb_build_object(
            'status', 'success',
            'message', 'CGM metrics saved for all participants successfully'
        );
        RETURN result;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', NULL  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while saving CGM metrics for all participants',
            'error_details', error_details_json
        );
        RETURN result;
    END;
END;
$function$;



CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.delete_partitions_if_exists(v_tenant_id text, v_study_id text, v_participant_sid text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.delete_partitions_if_exists';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'v_tenant_id', v_tenant_id,
        'v_study_id', v_study_id,
        'v_participant_sid', v_participant_sid
    );

    BEGIN
        -- Drop participant partition if exists
        EXECUTE format('
            DROP TABLE IF EXISTS drh_stateful_raw_observation.cgm_observation_research_subject_%s;
        ', replace(v_participant_sid, '-', '_'));

        -- Drop study partition if exists
        EXECUTE format('
            DROP TABLE IF EXISTS drh_stateful_raw_observation.cgm_observation_study_id_%s;
        ', v_study_id);

        -- Drop tenant partition if exists
        EXECUTE format('
            DROP TABLE IF EXISTS drh_stateful_raw_observation.cgm_observation_tenant_%s;
        ', v_tenant_id);

        -- Return success response
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Partitions deleted successfully'
        );
        RETURN result;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while deleting partitions',
            'error_details', error_details_json
        );
        RETURN result;
    END;
END;
$function$
;



CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.revert_cgm_metrics(p_participant_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.revert_cgm_metrics';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_participant_id', p_participant_id);

    BEGIN
        -- Delete from cgm_device_info first (last inserted)
        DELETE FROM drh_stateful_research_study.cgm_device_info 
        WHERE participant_sid = p_participant_id;

        -- Delete from cgm_metrics
        DELETE FROM drh_stateful_research_study.cgm_metrics 
        WHERE participant_sid = p_participant_id;        

        -- Return success response
        result := jsonb_build_object(
            'status', 'success',
            'message', 'CGM metrics reverted successfully'
        );
        RAISE NOTICE ' Removed metrics for participant: %', p_participant_id;
        RETURN result;
    
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while reverting CGM metrics',
            'error_details', error_details_json
        );
        RETURN result;
    END;
END;
$function$
;


/*
Function: drh_stateless_raw_observation.save_participant_base

Description:
This function saves the participant base data for a given participant ID. It first deletes any existing records for the participant in the `drh_stateful_research_study.participant_base` table and then inserts new records from the `drh_stateless_research_study.participant_data_view` view. If an error occurs during the process, it logs the error details in the `drh_stateful_activity_audit.exception_log` table and returns a JSON object with the error details.

Parameters:
- p_participant_id (text): The ID of the participant whose base data is to be saved.

Returns:
- jsonb: A JSON object indicating the status of the operation. If successful, it returns a JSON object with a status of 'success' and a message indicating that the participant base was saved successfully. If an error occurs, it returns a JSON object with a status of 'failure' and includes the error details.

Error Handling:
- The function uses a nested BEGIN...EXCEPTION block to handle any errors that occur during the deletion or insertion of records.
- Error details are captured using the GET STACKED DIAGNOSTICS statement and logged into the `drh_stateful_activity_audit.exception_log` table.
- The function returns a JSON object with the error details if an error occurs.
*/

DROP FUNCTION IF EXISTS drh_stateless_raw_observation.save_participant_base(text);

CREATE OR REPLACE FUNCTION drh_stateless_raw_observation.save_participant_base(p_participant_id text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_raw_observation.save_participant_base';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_participant_id', p_participant_id);

    BEGIN
        -- Delete existing records before insert
        DELETE FROM drh_stateful_research_study.participant_base 
        WHERE participant_id = p_participant_id;
        
        -- Insert new records into participant_base
        INSERT INTO drh_stateful_research_study.participant_base 
        SELECT 
            pd.organization_party_id,
            opv.organization_id,
            pd.study_id,
            pd.study_display_id,
            pd.participant_id,
            pd.participant_display_id,
            pd.participant_gender AS gender,
            pd.participant_age AS age,
            pd.study_arm AS "Study Arm",
            pd.baseline_hba1c AS "Baseline HbA1C"
        FROM drh_stateless_research_study.participant_data_view pd
        LEFT JOIN drh_stateless_research_study.organization_party_view opv 
            ON opv.organization_party_id = pd.organization_party_id
        WHERE pd.participant_id = p_participant_id;

        -- Return success response
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Participant base saved successfully'
        );
        RETURN result;

    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                 err_state = RETURNED_SQLSTATE,
                                 err_message = MESSAGE_TEXT,
                                 err_detail = PG_EXCEPTION_DETAIL,
                                 err_hint = PG_EXCEPTION_HINT;

        -- Log the error details
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred while saving participant base',
            'error_details', error_details_json
        );
        RETURN result;
    END;
END;
$function$;


-----------------------file device metadata and meal observation----------------------------------------------
-- Drop the view if it exists
DROP VIEW IF EXISTS drh_stateless_raw_observation.file_device_meal_observation_view;

-- Create or replace the view
CREATE OR REPLACE VIEW drh_stateless_raw_observation.file_device_meal_observation_view
WITH (security_invoker = true)
AS
SELECT DISTINCT
    meal.id,
    meal.subject_id AS participant_id,
    meal.value_quantity,
    meal.occurrence_time,
    mt.display AS meal_type,

    -- Device ID
    CASE
        WHEN rced.file_meta_data IS NULL THEN NULL
        WHEN jsonb_typeof(rced.file_meta_data) = 'array' THEN jsonb_path_query_first(rced.file_meta_data, '$[0].device_id')::text::jsonb #>> '{}'
        ELSE rced.file_meta_data ->> 'device_id'
    END AS device_id,

    -- Device Name
    CASE
        WHEN rced.file_meta_data IS NULL THEN NULL
        WHEN jsonb_typeof(rced.file_meta_data) = 'array' THEN jsonb_path_query_first(rced.file_meta_data, '$[0].devicename')::text::jsonb #>> '{}'
        ELSE rced.file_meta_data ->> 'devicename'
    END AS devicename,

    -- Source Platform
    CASE
        WHEN rced.file_meta_data IS NULL THEN NULL
        WHEN jsonb_typeof(rced.file_meta_data) = 'array' THEN jsonb_path_query_first(rced.file_meta_data, '$[0].source')::text::jsonb #>> '{}'
        ELSE rced.file_meta_data ->> 'source'
    END AS source_platform,

    -- File Format
    CASE
        WHEN rced.file_meta_data IS NULL THEN NULL
        WHEN jsonb_typeof(rced.file_meta_data) = 'array' THEN jsonb_path_query_first(rced.file_meta_data, '$[0].file_format')::text::jsonb #>> '{}'
        ELSE rced.file_meta_data ->> 'file_format'
    END AS file_format,

    -- File Name
    CASE
        WHEN rced.file_meta_data IS NULL THEN soud.file_name
        WHEN jsonb_typeof(rced.file_meta_data) = 'array' THEN
            CASE
                WHEN jsonb_path_query_first(rced.file_meta_data, '$[0].file_name') IS NOT NULL
                     AND jsonb_path_query_first(rced.file_meta_data, '$[0].file_format') IS NOT NULL
                THEN concat(
                    jsonb_path_query_first(rced.file_meta_data, '$[0].file_name')::text::jsonb #>> '{}',
                    '.',
                    jsonb_path_query_first(rced.file_meta_data, '$[0].file_format')::text::jsonb #>> '{}'
                )
                ELSE NULL
            END
        ELSE
            CASE
                WHEN rced.file_meta_data ->> 'file_name' IS NOT NULL AND rced.file_meta_data ->> 'file_format' IS NOT NULL
                THEN concat(rced.file_meta_data ->> 'file_name', '.', rced.file_meta_data ->> 'file_format')
                ELSE NULL
            END
    END AS file_name

FROM 
    drh_stateful_research_study.nutrition_intake meal
JOIN 
    drh_stateful_raw_data.subject_observation_extract_data rced 
    ON meal.subject_id = rced.participant_sid
JOIN 
    drh_stateful_master.meal_type mt 
    ON meal.meal_type_id = mt.meal_type_id
LEFT JOIN 
    drh_stateful_raw_data.subject_observation_upload_data soud 
    ON soud.id = rced.subject_observation_upload_id
WHERE 
    rced.file_content_type_id = (
        SELECT id
        FROM drh_stateful_master.file_content_type
        WHERE title = 'Meals'
        LIMIT 1
    )
    AND TRIM(soud.status) != (
    SELECT TRIM(CAST(stage_id AS VARCHAR))
    FROM drh_stateless_master.migration_status_view
    WHERE stage_name = 'ROLLBACK'
    LIMIT 1)   
    AND rced.deleted_at IS NULL;



-----------------------file device metadata and fitness observation----------------------------------------------
-- Drop the view if it exists
DROP VIEW IF EXISTS drh_stateless_raw_observation.file_device_fitness_observation_view;

-- Create or replace the view
CREATE OR REPLACE VIEW drh_stateless_raw_observation.file_device_fitness_observation_view
WITH(security_invoker=true)
AS SELECT fit.observation_id,
    fit.subject_id AS participant_id,
    fit.effective_datetime::date AS effective_datetime,
    ofc.value,
    act.display AS component_type,
        CASE
            WHEN rced.file_meta_data IS NULL THEN NULL::text
            WHEN jsonb_typeof(rced.file_meta_data) = 'array'::text THEN jsonb_path_query_first(rced.file_meta_data, '$[0]."device_id"'::jsonpath)::text::jsonb #>> '{}'::text[]
            ELSE rced.file_meta_data ->> 'device_id'::text
        END AS device_id,
        CASE
            WHEN rced.file_meta_data IS NULL THEN NULL::text
            WHEN jsonb_typeof(rced.file_meta_data) = 'array'::text THEN jsonb_path_query_first(rced.file_meta_data, '$[0]."devicename"'::jsonpath)::text::jsonb #>> '{}'::text[]
            ELSE rced.file_meta_data ->> 'devicename'::text
        END AS devicename,
        CASE
            WHEN rced.file_meta_data IS NULL THEN NULL::text
            WHEN jsonb_typeof(rced.file_meta_data) = 'array'::text THEN jsonb_path_query_first(rced.file_meta_data, '$[0]."source"'::jsonpath)::text::jsonb #>> '{}'::text[]
            ELSE rced.file_meta_data ->> 'source'::text
        END AS source_platform,
        CASE
            WHEN rced.file_meta_data IS NULL THEN NULL::text
            WHEN jsonb_typeof(rced.file_meta_data) = 'array'::text THEN jsonb_path_query_first(rced.file_meta_data, '$[0]."file_format"'::jsonpath)::text::jsonb #>> '{}'::text[]
            ELSE rced.file_meta_data ->> 'file_format'::text
        END AS file_format,
        CASE
            WHEN rced.file_meta_data IS NULL THEN soud.file_name
            WHEN jsonb_typeof(rced.file_meta_data) = 'array'::text THEN
            CASE
                WHEN jsonb_path_query_first(rced.file_meta_data, '$[0]."file_name"'::jsonpath) IS NOT NULL AND jsonb_path_query_first(rced.file_meta_data, '$[0]."file_format"'::jsonpath) IS NOT NULL THEN concat(jsonb_path_query_first(rced.file_meta_data, '$[0]."file_name"'::jsonpath)::text::jsonb #>> '{}'::text[], '.', jsonb_path_query_first(rced.file_meta_data, '$[0]."file_format"'::jsonpath)::text::jsonb #>> '{}'::text[])
                ELSE NULL::text
            END
            ELSE
            CASE
                WHEN (rced.file_meta_data ->> 'file_name'::text) IS NOT NULL AND (rced.file_meta_data ->> 'file_format'::text) IS NOT NULL THEN concat(rced.file_meta_data ->> 'file_name'::text, '.', rced.file_meta_data ->> 'file_format'::text)
                ELSE NULL::text
            END
        END AS file_name
   FROM drh_stateful_research_study.observation_fitness fit
     LEFT JOIN drh_stateful_raw_data.subject_observation_extract_data rced ON fit.subject_id = rced.participant_sid
     JOIN drh_stateful_research_study.observation_fitness_component ofc ON fit.observation_id = ofc.observation_id
     JOIN drh_stateful_master.activity_component_type act ON ofc.component_type_id = act.component_type_id
     LEFT JOIN drh_stateful_raw_data.subject_observation_upload_data soud 
        ON soud.id = rced.subject_observation_upload_id
  WHERE rced.file_content_type_id = (( SELECT file_content_type.id
           FROM drh_stateful_master.file_content_type
          WHERE file_content_type.title::text = 'Fitness'::text
         LIMIT 1)) AND TRIM(BOTH FROM soud.status) <> (( SELECT TRIM(BOTH FROM migration_status_view.stage_id::character varying) AS btrim
           FROM drh_stateless_master.migration_status_view
          WHERE migration_status_view.stage_name = 'ROLLBACK'::text
         LIMIT 1)) AND rced.deleted_at IS NULL;


-----------------------file device metadata and fitness observation tabular----------------------------------------------
CREATE EXTENSION IF NOT EXISTS "tablefunc" SCHEMA drh_stateless_util;

-- Drop the view if it exists
DROP VIEW IF EXISTS drh_stateless_raw_observation.file_device_fitness_observation_tab_view;  

CREATE OR REPLACE VIEW drh_stateless_raw_observation.file_device_fitness_observation_tab_view
WITH (security_invoker = true)
AS
SELECT *
FROM drh_stateless_util.crosstab(
  $$
    SELECT
      observation_id::text,
      participant_id::text,
      effective_datetime::timestamptz,
      device_id::text,
      devicename::text,
      source_platform::text,
      file_format::text,
      file_name::text,
      component_type,
      value
    FROM drh_stateless_raw_observation.file_device_fitness_observation_view
    ORDER BY observation_id, component_type
  $$,
  $$
    VALUES 
      ('Steps Count'),
      ('Duration'),
      ('Calories Burned'),
      ('Distance'),
      ('Heart Rate (BPM)')
  $$
) AS ct(
  observation_id       text,
  participant_id       text,
  effective_datetime   date,
  device_id            text,
  devicename           text,
  source_platform      text,
  file_format          text,
  file_name            text,
  steps                float8,
  exercise_minutes     float8,
  calories_burned      float8,
  distance             float8,
  heart_rate           float8
);



/*
------------------------------------------------------------------------------
Function: drh_stateless_research_study.rollback_cgmdata_for_participant_by_fileid

Purpose:
    Performs a complete rollback of Continuous Glucose Monitoring (CGM) data 
    for a specific participant and file. This includes:
        - Logging the rollback interaction.
        - Reverting previously computed CGM metrics.
        - Deleting raw CGM observations.
        - Recomputing metrics post-deletion.
        - Updating the CGM file status to 'ROLLBACK'.
        - Logging the outcome of the rollback (SUCCESS or FAILURE).
------------------------------------------------------------------------------
*/

CREATE OR REPLACE FUNCTION drh_stateless_research_study.rollback_cgmdata_for_participant_by_fileid(
    p_study_id TEXT,
    p_participant_id TEXT,
    p_file_id TEXT,
    p_interaction_id TEXT
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;

    revert_status JSONB;
    metrics_stats TEXT;
    result JSONB;

    p_file_name TEXT;

    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;

    exception_log_json JSONB;
    parameters_lst JSONB;
    error_details_json JSONB;
    current_query TEXT := pg_catalog.current_query();
    procedure_name TEXT := 'drh_stateless_research_study.rollback_cgmdata_for_participant_by_fileid';
BEGIN
    -- Default failure response
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'CGM file data rollback failure',
        'p_file_id', p_file_id,
        'interaction_id', p_interaction_id
    );

    -- Log input parameters
    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_interaction_id', p_interaction_id,
        'p_file_id', p_file_id,
        'p_participant_id', p_participant_id
    );

    -- Validate input
    IF p_study_id IS NULL OR p_participant_id IS NULL OR p_file_id IS NULL OR p_interaction_id IS NULL THEN
        RAISE NOTICE 'Missing required input parameters';
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Missing required input parameters',
            'parameters', parameters_lst
        );
    END IF;

    -- Get file name
    SELECT fdcov.file_name INTO p_file_name
    FROM drh_stateless_raw_observation.file_device_cgm_observation_view fdcov
    WHERE fdcov.cgm_raw_data_id = p_file_id
    LIMIT 1;

    IF p_file_name IS NULL THEN
        RAISE EXCEPTION 'No file found for file_id: %', p_file_id;
    END IF;

    -- Insert initial file interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'ROLLBACK CGM FILE DATA',
        'interaction_status', 'IN PROGRESS',
        'description', 'CGM File Data Rollback started',
        'study_id', p_study_id,
        'file_name', p_file_name,
        'file_category', 'CGM',
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
        RAISE NOTICE 'Initial file interaction log inserted (interaction_id: %)', inprogress_file_interaction_id;
    ELSE
        RAISE NOTICE 'Failed to insert initial file interaction log. Reversal will proceed but logging might be incomplete.';
    END IF;

    -- Step 1: Revert metrics
    revert_status := drh_stateless_raw_observation.revert_cgm_metrics(p_participant_id);
    RAISE NOTICE 'Revert status: %', revert_status;

    IF revert_status ->> 'status' = 'success' THEN

        -- Step 2: Delete CGM observations
        DELETE FROM drh_stateful_raw_observation.cgm_observation
        WHERE raw_cgm_extract_data_id = p_file_id;
        RAISE NOTICE 'Deleted CGM observations for file_id: %', p_file_id;

        -- Step 3: Recompute metrics
        metrics_stats := drh_stateless_raw_observation.save_cgm_metrics(p_participant_id);
        RAISE NOTICE 'Recomputed CGM metrics for participant_id: %', p_participant_id;

        -- Step 4: Update file status
        UPDATE drh_stateful_raw_data.cgm_raw_upload_data 
        SET
            updated_at  = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            status = (
                SELECT v1.stage_id
                FROM drh_stateless_master.migration_status_view v1
                WHERE v1.stage_name = 'ROLLBACK'
                LIMIT 1
            )
        where cgm_raw_file_id  = (select rs.raw_file_id from drh_stateful_raw_data.raw_cgm_extract_data rs where rs.cgm_raw_data_id = p_file_id limit 1);
        RAISE NOTICE 'Updated raw_cgm_extract_data status to ROLLBACK for file_id: %', p_file_id;

        -- Step 5: Finalize success interaction log
        IF inprogress_file_interaction_id IS NOT NULL THEN
            v_file_interaction_params := jsonb_build_object(
                'last_file_interaction_id', inprogress_file_interaction_id,
                'interaction_action_type', 'ROLLBACK CGM FILE DATA',
                'interaction_status', 'SUCCESS',
                'description', 'CGM File Data Rollback Succeeded',
                'study_id', p_study_id,
                'file_name', p_file_name,
                'file_category', 'CGM',
                'created_by', NULL
            );

            file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

            IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
                completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
                result := jsonb_build_object(
                    'status', 'success',
                    'message', 'CGM file data rolled back successfully',
                    'file_id', p_file_id,
                    'interaction_id', completed_file_interaction_id
                );
            ELSE
                RAISE NOTICE 'Failed to finalize file interaction log as SUCCESS.';
            END IF;
        END IF;

    ELSE
        RAISE NOTICE 'CGM metrics revert failed. Aborting rollback process for participant_id: %', p_participant_id;
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Failed to revert CGM metrics. Rollback aborted.',
            'file_id', p_file_id,
            'participant_id', p_participant_id,
            'revert_status', revert_status
        );
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT,
        err_context = PG_EXCEPTION_CONTEXT;

    exception_log_json := jsonb_build_object(
        'function_name', procedure_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    IF inprogress_file_interaction_id IS NOT NULL THEN
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'ROLLBACK CGM FILE DATA',
            'interaction_status', 'FAILED',
            'description', 'CGM File Data Rollback failed due to an error',
            'study_id', p_study_id,
            'file_category', 'CGM',
            'file_id', p_file_id,
            'file_name', p_file_name
        );

        PERFORM drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
    END IF;

    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );

    RAISE NOTICE 'CGM Data Rollback failed: %', error_details_json;

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during CGM file data rollback',
        'error_details', error_details_json
    );
END;
$function$;






-- =====================================================================================
-- Function: drh_stateless_research_study.rollback_mealdata_for_participant_by_fileid
-- Purpose: Roll back meal data entries for a participant, based on a file ID, by:
--          - Deleting mapped nutrition intake records
--          - Updating the file status to "ROLLBACK"
--          - Logging interaction lifecycle steps (IN PROGRESS, SUCCESS, FAILED)
--          - Capturing exceptions and persisting error logs
--
-- Parameters:
--   p_study_id        TEXT   -- Study identifier
--   p_participant_id  TEXT   -- Participant identifier (r_subject_id)
--   p_file_id         TEXT   -- File ID from subject_observation_extract_data
--   p_interaction_id  TEXT   -- Interaction ID for logging
--
-- Returns:
--   JSONB object with status ("success"/"failure"), message, file ID, and interaction ID
--
-- Behavior:
--   - Validates input parameters
--   - Fetches file name using file ID
--   - Creates an IN PROGRESS interaction log
--   - Deletes nutrition intake records linked via mapping table
--   - Updates file upload status to "ROLLBACK" using migration status view
--   - On success, logs SUCCESS interaction and returns confirmation
--   - On failure, logs exception and returns error JSON

-- =====================================================================================

CREATE OR REPLACE FUNCTION drh_stateless_research_study.rollback_mealdata_for_participant_by_fileid(p_study_id text, p_participant_id text, p_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;

    result JSONB;
    p_file_name TEXT;

    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;

    exception_log_json JSONB;
    parameters_lst JSONB;
    error_details_json JSONB;
    current_query TEXT := pg_catalog.current_query();
    procedure_name TEXT := 'drh_stateless_research_study.rollback_mealdata_for_participant_by_fileid';
BEGIN
    -- Default failure response
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Meal data rollback failure',
        'file_id', p_file_id,
        'interaction_id', p_interaction_id
    );

    -- Log input parameters
    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_interaction_id', p_interaction_id,
        'p_file_id', p_file_id,
        'p_participant_id', p_participant_id
    );

    IF p_study_id IS NULL OR p_participant_id IS NULL OR p_file_id IS NULL OR p_interaction_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Missing required input parameters',
            'parameters', parameters_lst
        );
    END IF;

    -- Fetch file name for logging
    SELECT soud.file_name
    INTO p_file_name
    FROM drh_stateful_raw_data.subject_observation_upload_data soud
    JOIN drh_stateful_raw_data.subject_observation_extract_data soed
      ON soud.id = soed.subject_observation_upload_id
    WHERE soed.id = p_file_id
    LIMIT 1;

    IF p_file_name IS NULL THEN
        RAISE EXCEPTION 'No file found for file_id: %', p_file_id;
    END IF;

    -- Start interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'ROLLBACK MEAL FILE DATA',
        'interaction_status', 'IN PROGRESS',
        'description', 'Meal Data Rollback started',
        'study_id', p_study_id,
        'file_name', p_file_name,
        'file_category', 'Meals',
        'file_id', p_file_id,
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    END IF;

    -- Delete meal data mapped to the file_id
    DELETE FROM drh_stateful_research_study.nutrition_intake ni
    USING drh_stateful_research_study.nutritionintake_mapping nim
    WHERE ni.id = nim.nutrition_mapping_id
      AND nim.study_id = p_study_id
      AND nim.r_subject_id = p_participant_id
      AND nim.observation_mapping_id = p_file_id;

    RAISE NOTICE 'Deleted meal data for file_id: %', p_file_id;

    -- Update file status to ROLLBACK
    UPDATE drh_stateful_raw_data.subject_observation_upload_data 
    SET
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        status = (
            SELECT stage_id
            FROM drh_stateless_master.migration_status_view
            WHERE stage_name = 'ROLLBACK'
            LIMIT 1
        )
    WHERE id = (
        SELECT subject_observation_upload_id
        FROM drh_stateful_raw_data.subject_observation_extract_data
        WHERE id = p_file_id
        LIMIT 1
    );

    RAISE NOTICE 'Updated file status to ROLLBACK for file_id: %', p_file_id;

    -- Success log
    IF inprogress_file_interaction_id IS NOT NULL THEN
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'ROLLBACK MEAL FILE DATA',
            'interaction_status', 'SUCCESS',
            'description', 'Meal Data Rollback Succeeded',
            'study_id', p_study_id,
            'file_name', p_file_name,
            'file_category', 'Meals',
            'file_id', p_file_id,
            'created_by', NULL
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
            result := jsonb_build_object(
                'status', 'success',
                'message', 'Meal file data rolled back successfully',
                'file_id', p_file_id,
                'interaction_id', completed_file_interaction_id
            );
        END IF;
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT,
        err_context = PG_EXCEPTION_CONTEXT;

    exception_log_json := jsonb_build_object(
        'function_name', procedure_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    IF inprogress_file_interaction_id IS NOT NULL THEN
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'ROLLBACK MEAL FILE DATA',
            'interaction_status', 'FAILED',
            'description', 'Meal Data Rollback failed due to error',
            'study_id', p_study_id,
            'file_category', 'Meals',
            'file_id', p_file_id,
            'file_name', p_file_name
        );

        PERFORM drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
    END IF;

    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during meal data rollback',
        'error_details', error_details_json
    );
END;
$function$
;


-- ================================================
-- Function: rollback_fitnessdata_for_participant_by_fileid
-- Schema: drh_stateless_research_study
--
-- Description:
-- Rolls back fitness data for a given participant and file ID
-- by:
--   1. Deleting related entries from observation_fitness_component
--      and observation_fitness tables.
--   2. Updating the associated upload file status to 'ROLLBACK'.
--   3. Logging the rollback interaction and errors (if any).
--
-- Parameters:
--   p_study_id        - ID of the study
--   p_participant_id  - Participant ID (r_subject_id)
--   p_file_id         - ID of the extract file (observation_mapping_id)
--   p_interaction_id  - Interaction ID for logging
--
-- Returns:
--   JSONB object indicating 'success' or 'failure' with relevant details.
--
-- Notes:
--   - This function logs actions in the file interaction log.
--   - All actions are audited and exceptions logged.
--   - Uses save_file_interaction_log and log_exception utilities.

-- ================================================


CREATE OR REPLACE FUNCTION drh_stateless_research_study.rollback_fitnessdata_for_participant_by_fileid(p_study_id text, p_participant_id text, p_file_id text, p_interaction_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
    v_file_interaction_params JSONB;

    result JSONB;
    p_file_name TEXT;
    f_map_id TEXT;

    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;

    exception_log_json JSONB;
    parameters_lst JSONB;
    error_details_json JSONB;
    current_query TEXT := pg_catalog.current_query();
    procedure_name TEXT := 'drh_stateless_research_study.rollback_fitnessdata_for_participant_by_fileid';
BEGIN
    -- Default failure response
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Fitness data rollback failure',
        'file_id', p_file_id,
        'interaction_id', p_interaction_id
    );

    -- Log input parameters
    parameters_lst := jsonb_build_object(
        'p_study_id', p_study_id,
        'p_interaction_id', p_interaction_id,
        'p_file_id', p_file_id,
        'p_participant_id', p_participant_id
    );

    IF p_study_id IS NULL OR p_participant_id IS NULL OR p_file_id IS NULL OR p_interaction_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Missing required input parameters',
            'parameters', parameters_lst
        );
    END IF;

    -- Fetch file name for logging
    SELECT soud.file_name
    INTO p_file_name
    FROM drh_stateful_raw_data.subject_observation_upload_data soud
    JOIN drh_stateful_raw_data.subject_observation_extract_data soed
      ON soud.id = soed.subject_observation_upload_id
    WHERE soed.id = p_file_id
    LIMIT 1;

    IF p_file_name IS NULL THEN
        RAISE EXCEPTION 'No file found for file_id: %', p_file_id;
    END IF;

    -- Start interaction log
    v_file_interaction_params := jsonb_build_object(
        'last_file_interaction_id', p_interaction_id,
        'interaction_action_type', 'ROLLBACK FITNESS FILE DATA',
        'interaction_status', 'IN PROGRESS',
        'description', 'Fitness Data Rollback started',
        'study_id', p_study_id,
        'file_name', p_file_name,
        'file_category', 'Fitness',
        'file_id', p_file_id,
        'created_by', NULL
    );

    file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

    IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
        inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
    END IF;

    -- Get fitness_mapping_id to delete dependent component entries
    SELECT fm.fitness_mapping_id 
    INTO f_map_id
    FROM drh_stateful_research_study.fitness_mapping fm
    WHERE fm.study_id = p_study_id 
      AND fm.r_subject_id = p_participant_id 
      AND fm.observation_mapping_id = p_file_id
    LIMIT 1;

    IF f_map_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'No fitness mapping found for given file_id and participant_id',
            'file_id', p_file_id,
            'participant_id', p_participant_id
        );
    END IF;

    -- Delete from observation_fitness_component
    DELETE FROM drh_stateful_research_study.observation_fitness_component  
    WHERE observation_id = f_map_id;

    -- Delete from observation_fitness
    DELETE FROM drh_stateful_research_study.observation_fitness
    WHERE observation_id = f_map_id;

    RAISE NOTICE 'Deleted Fitness data for file_id: %', p_file_id;

    -- Update file status to ROLLBACK
    UPDATE drh_stateful_raw_data.subject_observation_upload_data 
    SET
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        status = (
            SELECT stage_id
            FROM drh_stateless_master.migration_status_view
            WHERE stage_name = 'ROLLBACK'
            LIMIT 1
        )
    WHERE id = (
        SELECT subject_observation_upload_id
        FROM drh_stateful_raw_data.subject_observation_extract_data
        WHERE id = p_file_id
        LIMIT 1
    );

    RAISE NOTICE 'Updated file status to ROLLBACK for file_id: %', p_file_id;

    -- Success log
    IF inprogress_file_interaction_id IS NOT NULL THEN
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'ROLLBACK FITNESS FILE DATA',
            'interaction_status', 'SUCCESS',
            'description', 'Fitness Data Rollback Succeeded',
            'study_id', p_study_id,
            'file_name', p_file_name,
            'file_category', 'Fitness',
            'file_id', p_file_id,
            'created_by', NULL
        );

        file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);

        IF file_interaction_result IS NOT NULL AND file_interaction_result ->> 'status' = 'success' THEN
            completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
            result := jsonb_build_object(
                'status', 'success',
                'message', 'Fitness file data rolled back successfully',
                'file_id', p_file_id,
                'interaction_id', completed_file_interaction_id
            );
        END IF;
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT,
        err_context = PG_EXCEPTION_CONTEXT;

    exception_log_json := jsonb_build_object(
        'function_name', procedure_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    IF inprogress_file_interaction_id IS NOT NULL THEN
        v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'ROLLBACK FITNESS FILE DATA',
            'interaction_status', 'FAILED',
            'description', 'Fitness Data Rollback failed due to error',
            'study_id', p_study_id,
            'file_category', 'Fitness',
            'file_id', p_file_id,
            'file_name', COALESCE(p_file_name, 'unknown')
        );

        PERFORM drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params);
    END IF;

    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during Fitness data rollback',
        'error_details', error_details_json
    );
END;
$function$
;




