#!/usr/bin/env -S deno run  --allow-env  --allow-all

/**
 * This TypeScript file implements a SQL migration feature for PostgreSQL databases using Deno.
 * It provides methods for defining and executing migrations.
 *
 * @module Information_Schema_Lifecycle_Management_Migration
 */

import * as typ from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/typical/mod.ts";
import * as ws from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/lib/universal/whitespace.ts";
import * as SQLa from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/render/mod.ts";
import { pgSQLa } from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.13.34/pattern/pgdcp/deps.ts";
//import * as udm from "https://raw.githubusercontent.com/netspective-labs/sql-aide/v0.14.9/pattern/udm/mod.ts";

type EmitContext = typ.typical.SQLa.SqlEmitContext;

const gts = typ.governedTemplateState<
    typ.TypicalDomainQS,
    typ.TypicalDomainsQS,
    EmitContext
>();
const gm = typ.governedModel<
    typ.TypicalDomainQS,
    typ.TypicalDomainsQS,
    EmitContext
>(gts.ddlOptions);

export const {
    text,
    textNullable,
    integer,
    date,
    varChar,
    varCharNullable,
    integerNullable,
    //boolean,
    dateTimeNullable,
    dateNullable,
    dateTime,
    floatNullable,
    ulidNullable,
    ulid,
    jsonbNullable,
    jsonB,
    //booleanNullable,
} = gm.domains;
const { autoIncPrimaryKey: autoIncPK, ulidPrimaryKey: primaryKey } = gm.keys;

interface MigrationVersion {
    readonly description: string;
    readonly dateTime: Date;
}

enum StateStatus {
    STATEFUL = "_stateful_",
    IDEMPOTENT = "_idempotent_",
}

const ctx = SQLa.typicalSqlEmitContext({
    sqlDialect: SQLa.postgreSqlDialect(),
});

const prependMigrateSPText = "migrate_";

const masterSchema = SQLa.sqlSchemaDefn("drh_udi_ingress", {
    isIdempotent: true,
});
export const udmts = typ.governedTemplateState<EmitContext>({
    defaultNS: masterSchema,
});

// Function to read SQL from a list of .psql files
async function readSQLFiles(filePaths: readonly string[]): Promise<string[]> {
    const sqlContents = [];
    for (const filePath of filePaths) {
        try {
            const absolutePath = new URL(filePath, import.meta.url).pathname;
            const data = await Deno.readTextFile(absolutePath);
            sqlContents.push(data);
        } catch (err) {
            console.error(`Error reading file ${filePath}:`, err);
            throw err;
        }
    }
    return sqlContents;
}

// List of dependencies and test dependencies

// Read SQL queries from files
const testMigrateDependenciesWithPgtap = [
    "../../../../test/postgres/ingestion-center/004-idempotent-migrate-unit-test.psql",
    "../../../../test/postgres/ingestion-center/suite.pgtap.psql",
] as const;

const infoSchemaLifecycle = SQLa.sqlSchemaDefn("info_schema_lifecycle", {
    isIdempotent: true,
});

export const migrationInput: MigrationVersion = {
    description: "ai-permission",
    dateTime: new Date(2025, 3, 5, 18, 16),
};
function formatDateToCustomString(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth()).padStart(2, "0"); // Month is zero-based
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${year}_${month}_${day}_${hours}_${minutes}`;
}

export const migrateVersion = formatDateToCustomString(migrationInput.dateTime);

const migrateSP = pgSQLa.storedProcedure(
    prependMigrateSPText +
        "v" +
        migrateVersion +
        StateStatus.IDEMPOTENT +
        migrationInput.description,
    {},
    (name, args, _) =>
        pgSQLa.typedPlPgSqlBody(name, args, ctx, {
            autoBeginEnd: false,
        }),
    {
        embeddedStsOptions: SQLa.typicalSqlTextSupplierOptions(),
        autoBeginEnd: false,
        isIdempotent: true,
        sqlNS: infoSchemaLifecycle,
        headerBodySeparator: "$migrateVersionSP$",
    },
)`

BEGIN

CREATE OR REPLACE FUNCTION drh_stateless_vanna.log_vanna_ai_response(p_question text, p_sql_query text, p_created_by text, p_query_type text DEFAULT 'training'::text, p_json_result text DEFAULT NULL::text, p_results text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  err_context TEXT;
  err_state TEXT;
  err_message TEXT;
  err_detail TEXT;
  err_hint TEXT;
  error_details_json JSONB;
  result JSONB;
  vanna_log_id TEXT;
BEGIN
  INSERT INTO drh_stateful_ai_insights.vanna_ai_request_response (
    id,
    question,
    sql_query,
    results,
    json_result,
    ai_query_type,
    created_at,
    updated_at,
    created_by
  )
  VALUES (
    drh_stateless_util.get_unique_id(),
    p_question,
    p_sql_query,
    p_results,
    p_json_result,
    COALESCE(p_query_type, 'training'),
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
    p_created_by
  )
  RETURNING id INTO vanna_log_id;

  RETURN jsonb_build_object(
    'status', 'success',
    'message', 'Vanna AI log inserted successfully',
    'vanna_log_id', vanna_log_id
  );

EXCEPTION WHEN OTHERS THEN
  GET STACKED DIAGNOSTICS
    err_context = PG_EXCEPTION_CONTEXT,
    err_state = RETURNED_SQLSTATE,
    err_message = MESSAGE_TEXT,
    err_detail = PG_EXCEPTION_DETAIL,
    err_hint = PG_EXCEPTION_HINT;

  INSERT INTO drh_stateful_activity_audit.exception_log (
    function_name,
    error_code,
    error_message,
    error_detail,
    error_hint,
    error_context,
    occurred_at,
    resolved,
    resolved_at,
    resolver_comments
  )
  VALUES (
    'drh_stateless_vanna.log_vanna_ai_response',
    err_state,
    err_message,
    err_detail,
    err_hint,
    err_context,
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
    'No',
    NULL,
    NULL
  );

  error_details_json := jsonb_build_object(
    'error', err_message,
    'detail', err_detail,
    'hint', err_hint,
    'context', err_context,
    'state', err_state
  );

  result := jsonb_build_object(
    'status', 'failure',
    'message', 'Error occurred during vanna_log insertion',
    'error_details', error_details_json
  );
  RETURN result;
END;
$function$
;


-- Step: Revoke all privileges across non-stateless schemas
DO $$
DECLARE
    target_role TEXT := 'vana_interaction_usr';
    sch TEXT;
    obj RECORD;
BEGIN
    FOR sch IN
        SELECT DISTINCT nspname
        FROM pg_namespace
        WHERE nspname NOT LIKE 'pg_%'
          AND nspname <> 'information_schema'
    LOOP
        RAISE NOTICE 'Revoking from schema: %', sch;
        EXECUTE format('REVOKE ALL ON SCHEMA %I FROM %I;', sch, target_role);

        -- Revoke all on tables and views
        FOR obj IN
			            SELECT table_name
			FROM information_schema.tables
			WHERE table_schema= sch
        LOOP
            EXECUTE format('REVOKE ALL ON TABLE %I.%I FROM %I;', sch, obj.table_name, target_role);
        END LOOP;

        -- Revoke all on sequences
        FOR obj IN
            SELECT sequencename FROM pg_sequences WHERE schemaname = sch
        LOOP
            EXECUTE format('REVOKE ALL ON SEQUENCE %I.%I FROM %I;', sch, obj.sequencename, target_role);
        END LOOP;

        -- Revoke all on functions (ignoring argument list)
        FOR obj IN
            SELECT routine_name FROM information_schema.routines WHERE routine_schema = sch
        LOOP
            BEGIN
                EXECUTE format('REVOKE ALL ON FUNCTION %I.%I() FROM %I;', sch, obj.routine_name, target_role);
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Skipping function: %.%', sch, obj.routine_name;
            END;
        END LOOP;
    END LOOP;
END $$;

-- Step: Grant specific access
-- Usage on selected schemas
GRANT USAGE ON SCHEMA drh_stateful_master TO vana_interaction_usr;
GRANT USAGE ON SCHEMA drh_stateful_party TO vana_interaction_usr;
GRANT USAGE ON SCHEMA drh_stateful_raw_observation TO vana_interaction_usr;
GRANT USAGE ON SCHEMA drh_stateful_research_study TO vana_interaction_usr;
GRANT USAGE ON SCHEMA drh_stateless_util TO vana_interaction_usr;
GRANT USAGE ON SCHEMA drh_stateful_ai_insights TO vana_interaction_usr;
GRANT USAGE ON SCHEMA drh_stateless_vanna TO vana_interaction_usr;

-- Select on tables in selected schemas
GRANT SELECT ON ALL TABLES IN SCHEMA drh_stateful_master TO vana_interaction_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA drh_stateful_raw_observation TO vana_interaction_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA drh_stateful_research_study TO vana_interaction_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA drh_stateful_ai_insights TO vana_interaction_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA drh_stateless_util TO vana_interaction_usr;
GRANT SELECT ON ALL TABLES IN SCHEMA drh_stateless_vanna TO vana_interaction_usr;
GRANT SELECT ON TABLE drh_stateful_party.gender_type TO vana_interaction_usr;

-- Select and insert on specific table
GRANT SELECT, INSERT ON TABLE drh_stateful_ai_insights.vanna_ai_request_response TO vana_interaction_usr;

-- Default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA drh_stateful_master GRANT SELECT ON TABLES TO vana_interaction_usr;
ALTER DEFAULT PRIVILEGES IN SCHEMA drh_stateful_raw_observation GRANT SELECT ON TABLES TO vana_interaction_usr;
ALTER DEFAULT PRIVILEGES IN SCHEMA drh_stateful_research_study GRANT SELECT ON TABLES TO vana_interaction_usr;
ALTER DEFAULT PRIVILEGES IN SCHEMA drh_stateless_util GRANT SELECT ON TABLES TO vana_interaction_usr;
ALTER DEFAULT PRIVILEGES IN SCHEMA drh_stateful_ai_insights GRANT SELECT ON TABLES TO vana_interaction_usr;
ALTER DEFAULT PRIVILEGES IN SCHEMA drh_stateless_vanna GRANT SELECT ON TABLES TO vana_interaction_usr;

--revoke permisssion from role table
REVOKE ALL PRIVILEGES ON TABLE drh_stateful_master."role" FROM vana_interaction_usr;


---------------------------------------------------------------------------------------------------------------------------------------


-- Step : Revoke all privileges across non-stateless schemas
DO $$
DECLARE
    target_role TEXT := 'vana_admin';
    sch TEXT;
    obj RECORD;
BEGIN
    FOR sch IN
        SELECT DISTINCT nspname
        FROM pg_namespace
        WHERE nspname NOT LIKE 'pg_%'
          AND nspname <> 'information_schema'
    LOOP
        RAISE NOTICE 'Revoking from schema: %', sch;
        EXECUTE format('REVOKE ALL ON SCHEMA %I FROM %I;', sch, target_role);

        -- Revoke all on tables and views
        FOR obj IN
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = sch
        LOOP
            EXECUTE format('REVOKE ALL ON TABLE %I.%I FROM %I;', sch, obj.table_name, target_role);
        END LOOP;

        -- Revoke all on sequences
        FOR obj IN
            SELECT sequencename FROM pg_sequences WHERE schemaname = sch
        LOOP
            EXECUTE format('REVOKE ALL ON SEQUENCE %I.%I FROM %I;', sch, obj.sequencename, target_role);
        END LOOP;

        -- Revoke all on functions
        FOR obj IN
            SELECT routine_name, specific_name
            FROM information_schema.routines
            WHERE routine_schema = sch
        LOOP
            BEGIN
                EXECUTE format('REVOKE ALL ON FUNCTION %I.%I() FROM %I;', sch, obj.routine_name, target_role);
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Skipping function: %.%', sch, obj.routine_name;
            END;
        END LOOP;
    END LOOP;
END $$;

-- Step : Grant specific access (already idempotent)
-- Grant USAGE on schemas
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    FOREACH schema_name IN ARRAY ARRAY[
        'drh_stateful_master',
        'drh_stateful_party',
        'drh_stateful_raw_observation',
        'drh_stateful_research_study',
        'drh_stateless_util',
        'drh_stateful_ai_insights',
        'drh_stateless_vanna',
        'drh_stateful_authentication'
    ]
    LOOP
        EXECUTE format('GRANT USAGE ON SCHEMA %I TO vana_admin;', schema_name);
    END LOOP;
END $$;

-- Grant SELECT on all tables in selected schemas
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    FOREACH schema_name IN ARRAY ARRAY[
        'drh_stateful_master',
        'drh_stateful_raw_observation',
        'drh_stateful_research_study',
        'drh_stateful_ai_insights',
        'drh_stateful_authentication',
        'drh_stateless_util',
        'drh_stateless_ai_insights',
        'drh_stateless_vanna'
    ]
    LOOP
        EXECUTE format('GRANT SELECT ON ALL TABLES IN SCHEMA %I TO vana_admin;', schema_name);
    END LOOP;
END $$;

-- Grant SELECT and INSERT on specific table
GRANT SELECT, INSERT ON TABLE drh_stateful_ai_insights.vanna_ai_request_response TO vana_admin;

-- Grant EXECUTE on function
GRANT EXECUTE ON FUNCTION drh_stateless_vanna.log_vanna_ai_response(text,text,text,text,text,text) TO vana_admin;

-- Step 5: Set default privileges for future tables
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    FOREACH schema_name IN ARRAY ARRAY[
        'drh_stateful_master',
        'drh_stateful_raw_observation',
        'drh_stateful_research_study',
        'drh_stateless_util',
        'drh_stateful_ai_insights',
        'drh_stateless_vanna',
        'drh_stateful_authentication'
    ]
    LOOP
        EXECUTE format('ALTER DEFAULT PRIVILEGES IN SCHEMA %I GRANT SELECT ON TABLES TO vana_admin;', schema_name);
    END LOOP;
END $$;

    END
  `;

/**
 * Generates SQL Data Definition Language (DDL) for the migrations.
 *
 * @returns {string} The SQL DDL for migrations.
 */

function sqlDDLGenerateMigration() {
    return SQLa.SQL<EmitContext>(gts.ddlOptions)`

    ${migrateSP}

    `;
}

export function generated() {
    const ctx = SQLa.typicalSqlEmitContext({
        sqlDialect: SQLa.postgreSqlDialect(),
    });
    const testDependencies: string[] = [];
    for (const filePath of testMigrateDependenciesWithPgtap) {
        try {
            const absolutePath = import.meta.resolve(filePath);
            testDependencies.push(absolutePath);
        } catch (err) {
            console.error(`Error reading filepath ${filePath}:`, err);
            throw err;
        }
    }

    // after this execution `ctx` will contain list of all tables which will be
    // passed into `udmts.pumlERD` below (ctx should only be used once)
    const driverGenerateMigrationSQL = ws.unindentWhitespace(
        sqlDDLGenerateMigration().SQL(ctx),
    );
    return {
        driverGenerateMigrationSQL,
        pumlERD: udmts.pumlERD(ctx).content,
        destroySQL: ws.unindentWhitespace(`
      DROP PROCEDURE IF EXISTS "${migrateSP.sqlNS?.sqlNamespace}"."${migrateSP.routineName}" CASCADE;

      `),
        testDependencies,
    };
}
