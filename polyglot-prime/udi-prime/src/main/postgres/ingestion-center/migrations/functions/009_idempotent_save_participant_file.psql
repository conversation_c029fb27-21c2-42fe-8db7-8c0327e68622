DROP FUNCTION IF EXISTS drh_stateless_research_study.insert_participants_through_file_upload(p_study_id text, p_file_url text, p_org_party_id text, p_created_by text, p_file_content_json jsonb);
DROP FUNCTION IF EXISTS drh_stateless_research_study.insert_participants_through_file_upload(p_study_id text, p_file_url text, p_org_party_id text, p_created_by text, p_file_content_json jsonb,last_file_interaction_id text);
DROP FUNCTION IF EXISTS drh_stateless_research_study.insert_participants_through_file_upload(text, text, text, text, jsonb, text, jsonb);



CREATE OR REPLACE FUNCTION drh_stateless_research_study.insert_participants_through_file_upload(p_study_id text, p_file_url text, p_org_party_id text, p_created_by text, p_file_content_json jsonb, last_file_interaction_id text, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;    
    org_id TEXT;      
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.insert_participants_through_file_upload';
    current_query TEXT := pg_catalog.current_query();       
  
    participant RECORD;
    save_status JSONB;
    is_success BOOLEAN;
    fn_error_message TEXT;    
    participant_id TEXT;
    participant_display_id TEXT;

    -- Arrays to store success and failure details
    success_participants JSONB := '[]'::JSONB;
    failure_participants JSONB := '[]'::JSONB;
    
    -- Declare variables for participant data to pass to save function
    p_gender TEXT;
    p_age INT;
    p_diagnosis_icd TEXT;
    p_med_rxnorm TEXT;
    p_treatment_modality TEXT;
    p_race TEXT;
    p_ethnicity TEXT;
    p_bmi REAL;
    p_baseline_hba1c REAL;
    p_diabetes_type TEXT;
    p_study_arm TEXT;
    p_study_display_id TEXT;
    p_participant_id TEXT;
    p_participant_display_id text;
   
    study_created_by TEXT;
    current_visibility TEXT;
    is_archived BOOLEAN;
   
   	file_interaction_result JSONB;
    inprogress_file_interaction_id TEXT;
    completed_file_interaction_id TEXT;
	v_file_interaction_params JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;   
    f_map_id TEXT ;
 

    --Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_file_url', p_file_url,
    'p_org_party_id', p_org_party_id,
    'p_created_by', p_created_by,
    'p_file_content_json', p_file_content_json,
    'last_file_interaction_id', last_file_interaction_id
    );

	-- Log failure in file interaction log
    IF last_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', last_file_interaction_id,
            'interaction_action_type', 'SAVE PARTICIPANT DATA',
            'interaction_status', 'IN PROGRESS',
            'description', 'Save participant data started',
            'db_file_id', NULL,
            'file_name', NULL,
            'file_category', 'Participant',
            'created_by', p_created_by
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   		-- Extract file_interaction_id from result
	    IF file_interaction_result ->> 'status' = 'success' THEN
	       inprogress_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
	    ELSE
	        RETURN jsonb_build_object(
	            'status', 'failure',
	            'message', 'Failed to insert file interaction',
	            'error_details', file_interaction_result -> 'error_details'
	        );
	    END IF;	
    END IF;
    -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = p_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != p_created_by THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;

    -- Fetch organization ID
    SELECT organization_id INTO org_id
    FROM drh_stateless_research_study.organization_party_view
    WHERE organization_party_id = p_org_party_id;
   
   
    -- Insert data into the study_participant_file_mapping table
    INSERT INTO drh_stateful_research_study.study_participant_file_mapping (
        rs_file_map_id, 
        study_id, 
        file_url, 
        file_content_json, 
        tenant_id, 
        rec_status_id, 
        created_at, 
        created_by, 
        updated_at, 
        updated_by, 
        deleted_at, 
        deleted_by
    ) 
    VALUES (
        drh_stateless_util.get_unique_id(),
        p_study_id, 
        p_file_url, 
        p_file_content_json,  -- Insert the decoded JSON
        org_id, 
        (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1), 
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- Automatically set to current timestamp on creation
        p_created_by, 
        NULL, 
        NULL, 
        NULL, 
        NULL
        
    )returning rs_file_map_id into f_map_id;
   
      
    -- Iterate over the decoded JSON and insert participants
    FOR participant IN 
        SELECT * FROM jsonb_array_elements(p_file_content_json) AS part
    LOOP
        -- Extract participant_id if present
        p_participant_display_id := participant.value->>'participant_id';

        -- Check if the participant_id exists
        IF p_participant_display_id IS NOT NULL THEN
            
            IF NOT EXISTS (
                SELECT 1
                FROM drh_stateless_research_study.participant_data_view pdv
                WHERE pdv.study_id = p_study_id
                AND pdv.participant_display_id = p_participant_display_id
            ) THEN 
                
                -- Participant ID provided, map directly
                save_status := drh_stateless_research_study.save_individual_participant_from_file(
                    p_study_id, 
                    p_org_party_id, 
                    p_participant_display_id,
                    participant.value->>'gender', 
                    (participant.value->>'age')::INT, 
                    p_created_by, 
                    participant.value->>'diagnosis_icd', 
                    participant.value->>'med_rxnorm', 
                    participant.value->>'treatment_modality', 
                    participant.value->>'race', 
                    participant.value->>'ethnicity', 
                    ROUND((participant.value->>'bmi')::NUMERIC, 1), 
                    ROUND((participant.value->>'baseline_hba1c')::NUMERIC, 1), 
                    participant.value->>'diabetes_type', 
                    participant.value->>'study_arm'
                );   
               
               -- Check for success or failure
                is_success := (save_status->>'status') = 'success';
                IF is_success THEN
                    -- Extract participant details from success response
                    participant_display_id := save_status->>'participant_display_id';
                    participant_id := save_status->>'participant_id';

                    -- Add to success array
                    success_participants := success_participants || jsonb_build_object(
                        'participant_display_id', participant_display_id,
                        'participant_id', participant_id
                    );

                    INSERT INTO drh_stateful_research_study.participant_file_mapping (
                    id,
				    file_mapping_id, 
				    r_subject_id, 
				    study_id, 
				    tenant_id, 
				    rec_status_id, 
				    created_at, 
				    created_by, 
				    updated_at, 
				    updated_by, 
				    deleted_at, 
				    deleted_by
					) 
					VALUES (
                        drh_stateless_util.get_unique_id(),
					    f_map_id,  -- file_mapping_id (ULID or UUID)
					    participant_id,  -- r_subject_id
					    p_study_id,                    -- study_id or NULL
					    org_id,                      -- tenant_id
					    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1), 
				        CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- Automatically set to current timestamp on creation
				        p_created_by, 
				        NULL, 
				        NULL, 
				        NULL, 
				        NULL
					);
                ELSE
                    -- Capture error message
                    fn_error_message := save_status->>'message';

                    -- Add to failure array
                    failure_participants := failure_participants || jsonb_build_object(
                        'participant_id', p_participant_display_id,
                        'error_message', fn_error_message
                    );
                END IF;
            ELSE
                -- Add to failure array if participant display ID already exists
                failure_participants := failure_participants || jsonb_build_object(
                    'participant_display_id', p_participant_display_id,
                    'error_message', 'Participant display ID already exists'
                );
            END IF;
        END IF;
    END LOOP;
   
   -- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'SAVE PARTICIPANT DATA',
            'interaction_status', 'SUCCESS',
            'description', 'Save participant data completed',
            'db_file_id', NULL,
            'file_name', NULL,
            'file_category', 'Participant',
            'created_by', p_created_by
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   		-- Extract file_interaction_id from result
	    IF file_interaction_result ->> 'status' = 'success' THEN
	       completed_file_interaction_id := file_interaction_result ->> 'file_interaction_id';
	      
	    ELSE
	        RETURN jsonb_build_object(
	            'status', 'failure',
	            'message', 'Failed to insert file interaction',
	            'error_details', file_interaction_result -> 'error_details'
	        );
	    END IF;	
    END IF;

    -- Only perform activity logging if p_activity_json contains session_id
    IF p_activity_json ? 'session_id' THEN
        --Fetch level and type of activity
        SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
        SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='UPLOAD_PARTICIPANT_DATA' AND deleted_at IS NULL;

        -- Create new activity log JSON with the required fields
        v_activity_log_json := p_activity_json || jsonb_build_object(
            'activity_type_id', v_activity_type_id,
            'activity_level_id', v_activity_level_id,
            'activity_name', 'Upload Participant Data',
            'activity_description', 'Participant data uploaded successfully'
        );

        --Add activity log
        PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_created_by,v_activity_log_json); 
    END IF;
    -- Return success response
    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'Participants successfully saved.',
        'success_participants', success_participants,
        'failure_participants', failure_participants,
        'file_interaction_id',completed_file_interaction_id
    );

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

	-- Log failure in file interaction log
    IF inprogress_file_interaction_id IS NOT NULL THEN 
       v_file_interaction_params := jsonb_build_object(
            'last_file_interaction_id', inprogress_file_interaction_id,
            'interaction_action_type', 'SAVE PARTICIPANT DATA',
            'interaction_status', 'FAILED',
            'description', 'Save participant data failed',
            'db_file_id', NULL,
            'file_name', NULL,
            'file_category', 'Participant',
            'created_by', p_created_by
        );
       	-- Call save_file_interaction_log to get the file interaction ID   
   		file_interaction_result := drh_stateless_raw_observation.save_file_interaction_log(v_file_interaction_params );
   		
    END IF;
    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during participant data upload', 'error_details', error_details_json);
    RETURN result;
END;
$function$
;



---------------------------------------------------------------------------------------------------------------------------------------


CREATE OR REPLACE FUNCTION drh_stateless_research_study.save_individual_participant_from_file(p_study_id text, p_org_party_id text, p_participant_display_id text, p_gender text, p_age integer, p_created_by text, p_diagnosis_icd text DEFAULT NULL::text, p_med_rxnorm text DEFAULT NULL::text, p_treatment_modality text DEFAULT NULL::text, p_race text DEFAULT NULL::text, p_ethnicity text DEFAULT NULL::text, p_bmi double precision DEFAULT NULL::double precision, p_baseline_hba1c double precision DEFAULT NULL::double precision, p_diabetes_type text DEFAULT NULL::text, p_study_arm text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
  SECURITY DEFINER
AS $function$
DECLARE
    v_gender_id text;
    v_race_id text;
    v_ethnicity_id text;
    
    v_research_subject_id text;
    v_patient_id text;
    v_tenant_id text;
    v_study_display_id text;
    v_rec_status_id int;
    result jsonb;   
   
    org_id TEXT;      
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.save_individual_participant_from_file';
    current_query TEXT := pg_catalog.current_query();  
   
   bmi_loinc_code TEXT :='39156-5';--reference from drh_stateful_master.loinc_codes
   hba1c_loinc_code TEXT :='4548-4';--reference from drh_stateful_master.loinc_codes  
  
   study_created_by TEXT;
   current_visibility TEXT;
   is_archived BOOLEAN;
   exception_log_json JSONB;
   parameters_lst JSONB;
BEGIN
    -- Initialize result to failure by default
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during participant creation through file');   

    parameters_lst := jsonb_build_object(
    'p_study_id', p_study_id,
    'p_org_party_id', p_org_party_id,
    'p_participant_display_id', p_participant_display_id,
    'p_gender', p_gender,
    'p_age', p_age,
    'p_created_by', p_created_by,
    'p_diagnosis_icd', p_diagnosis_icd,
    'p_med_rxnorm', p_med_rxnorm,
    'p_treatment_modality', p_treatment_modality,
    'p_race', p_race,
    'p_ethnicity', p_ethnicity,
    'p_bmi', p_bmi,
    'p_baseline_hba1c', p_baseline_hba1c,
    'p_diabetes_type', p_diabetes_type,
    'p_study_arm', p_study_arm
    );

   
   -- Get the study's created_by and current visibility
    SELECT rs.created_by, sv.visibility_name, rs.archive_status 
    INTO study_created_by, current_visibility, is_archived
    FROM drh_stateful_research_study.research_study rs
    JOIN drh_stateful_master.study_visibility sv ON visibility = sv.visibility_id
    WHERE rs.study_id = p_study_id;

    -- Check visibility restriction policy
    IF current_visibility = 'Private' OR  is_archived IS TRUE THEN -- private
        IF study_created_by != p_created_by THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Permission denied. Only the study creator can update the details.'
            );
            RETURN result;
        END IF;
    END IF;

    -- Check if participant_display_id is provided
    IF p_participant_display_id IS NULL OR TRIM(p_participant_display_id) = '' THEN
        result := jsonb_build_object('status', 'failure', 'message', 'Participant display ID cannot be NULL or empty');
        RETURN result;
    END IF;

    IF p_age IS NULL OR p_age < 0 THEN
        result := jsonb_build_object('status', 'failure', 'message', 'Age must be a valid integer and cannot be NULL or negative');
        RETURN result;
    END IF;

    IF p_gender IS NULL OR TRIM(p_gender) = '' THEN
        result := jsonb_build_object('status', 'failure', 'message', 'Gender cannot be NULL or empty');
        RETURN result;
    END IF;

    -- Check if gender exists in the master table
    SELECT gtv.gender_type_id INTO v_gender_id
    FROM drh_stateless_master.gender_type_view gtv 
    WHERE gtv.code = UPPER(p_gender) 
    LIMIT 1;

    IF v_gender_id IS NULL THEN
        result := jsonb_build_object('status', 'failure', 'message', 'Invalid gender. Gender does not exist in the master table');
        RETURN result;
    END IF;
       
      -- Check if participant_display_id is provided
    IF p_participant_display_id IS NOT NULL then
    
        IF p_created_by IS NULL or p_org_party_id IS null or p_study_id IS null THEN         
           result := jsonb_build_object('status', 'failure', 'message', 'One of the mandatory fields are NULL ');
           RETURN result;
        END IF;

        BEGIN
            -- Retrieve tenant_id and study_display_id
            SELECT rs.organization_id, rsv.study_display_id
            INTO v_tenant_id, v_study_display_id
            FROM drh_stateless_research_study.organization_party_view rs
            JOIN drh_stateless_research_study.research_study_view rsv ON rsv.study_id = p_study_id
            WHERE rs.organization_party_id = p_org_party_id
            LIMIT 1;

		    if v_tenant_id is null then
			      result := jsonb_build_object('status', 'failure', 'message', 'Tenant Id cannot be NULL');
			      return result;
		    end if;
	       
		    if v_study_display_id is null then
		      result := jsonb_build_object('status', 'failure', 'message', 'study_display_id cannot be NULL');
		      return result;
		    end if;

            -- Get ACTIVE record status ID
            SELECT rs.value INTO v_rec_status_id FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1;

            -- Ensure gender exists in the master table or insert it
            IF p_gender IS NOT NULL THEN
                SELECT gtv.gender_type_id INTO v_gender_id
			    FROM drh_stateless_master.gender_type_view gtv 
			    WHERE gtv.value = p_gender limit 1;

                IF v_gender_id IS NULL THEN
                    INSERT INTO drh_stateful_party.gender_type (gender_type_id, code, value, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by, activity_log)
			        VALUES(drh_stateless_util.get_unique_id(), UPPER(p_gender), gender, CURRENT_TIMESTAMP, p_created_by, NULL, NULL, NULL, NULL, NULL)      
			        RETURNING gender_type_id INTO v_gender_id;   
                END IF;
            ELSE
                SELECT gtv.gender_type_id INTO v_gender_id
                FROM drh_stateless_master.gender_type_view gtv  
                WHERE gtv.code = 'UNKNOWN'
                LIMIT 1;
            END IF;

            -- Ensure race exists in the master table or insert it
            IF p_race IS NOT NULL THEN
                SELECT rtv.race_type_id INTO v_race_id
			    FROM drh_stateless_master.race_type_view rtv 
			    WHERE rtv.race_text = p_race limit 1;
			   
                IF v_race_id IS NULL then                
                SELECT rtv.race_type_id INTO v_race_id   -- ensure to input all possible race /ethincity in master tables
                FROM drh_stateless_master.race_type_view rtv  
                WHERE rtv.code = 'UNKN'
                LIMIT 1;
               
                END IF;
            ELSE
                SELECT rtv.race_type_id INTO v_race_id
                FROM drh_stateless_master.race_type_view rtv  
                WHERE rtv.code = 'UNK'
                LIMIT 1;
            END IF;

            -- Ensure ethnicity exists in the master table or insert it
            IF p_ethnicity IS NOT NULL THEN
                SELECT etv.ethnicity_type_id INTO v_ethnicity_id
                FROM drh_stateless_master.ethnicity_type_view etv 
                WHERE etv.display = p_ethnicity limit 1;

                IF v_ethnicity_id IS NULL THEN
                    SELECT etv.ethnicity_type_id INTO v_ethnicity_id    -- ensure to input all possible race /ethincity in master tables
	                FROM drh_stateless_master.ethnicity_type_view etv  
	                WHERE etv.code = 'UNK'
	                LIMIT 1;
                END IF;
            ELSE
                SELECT etv.ethnicity_type_id INTO v_ethnicity_id
                FROM drh_stateless_master.ethnicity_type_view etv  
                WHERE etv.code = 'UNK'
                LIMIT 1;
            END IF;
           
            -- Check if the participant already exists in the study
            IF EXISTS (
                SELECT 1 
                FROM drh_stateful_research_study.research_subject 
                WHERE participant_identifier = p_participant_display_id
                AND study_reference = p_study_id
            ) THEN
                result := jsonb_build_object('status', 'failure', 'message', 'Participant already exists in the study');
                RETURN result;
            ELSE   
                -- Insert into patient table
                INSERT INTO drh_stateful_research_study.patient(
                id, identifier_system, identifier_value, name_use, name_family, 
                name_given, gender_type_id, birth_date, age, address_use, address_line1, 
                address_city, address_state, address_postal_code, address_country, 
                contact_relationship, contact_name_family, contact_name_given, contact_telecom_system, 
                contact_telecom_value, contact_telecom_use, tenant_id, org_party_id, rec_status_id, 
                created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
                )
                VALUES( 
                    drh_stateless_util.get_unique_id(),  -- Generated patient ID
                    NULL, -- Empty identifier_system
                    NULL, -- Empty identifier_value
                    NULL, -- Empty name_use
                    NULL, -- Empty name_family
                    NULL, -- Empty name_given
                    v_gender_id,
                    NULL, -- Empty birth_date
                    COALESCE(p_age, 0),  -- Default age if empty
                    NULL, -- Empty address_use
                    NULL, -- Empty address_line1
                    NULL, -- Empty address_city
                    NULL, -- Empty address_state
                    NULL, -- Empty address_postal_code
                    NULL, -- Empty address_country
                    NULL, -- Empty contact_relationship
                    NULL, -- Empty contact_name_family
                    NULL, -- Empty contact_name_given
                    NULL, -- Empty contact_telecom_system
                    NULL, -- Empty contact_telecom_value
                    NULL, -- Empty contact_telecom_use
                    v_tenant_id, -- Tenant ID
                    p_org_party_id,  -- org_party_id
                    v_rec_status_id,  -- rec_status_id
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
                    p_created_by, -- created_by
                    NULL, NULL, NULL, NULL  -- Null fields for updated and deleted
                ) RETURNING id INTO v_patient_id;
        
                -- Insert into research_subject table
                INSERT INTO drh_stateful_research_study.research_subject (
                    rsubject_id, participant_identifier, study_reference, individual_reference, 
                    status_id, "group", diabetes_type, diagnosis_icd, med_rxnorm, treatment_modality, 
                    race_type_id, ethnicity_type_id, tenant_id, rec_status_id, created_at, created_by, 
                    updated_at, updated_by, deleted_at, deleted_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),  -- rsubject_id
                    p_participant_display_id,            -- participant_identifier
                    p_study_id,                          -- study_reference
                    v_patient_id,                        -- individual_reference
                    (SELECT code FROM drh_stateful_master.research_subject_status_master WHERE display_name = 'on-study' LIMIT 1),
                    p_study_arm,                         -- study group (e.g. arm)
                    p_diabetes_type,                     -- diabetes type
                    p_diagnosis_icd,                     -- diagnosis ICD
                    p_med_rxnorm,                        -- medication code
                    p_treatment_modality,                -- treatment modality
                    v_race_id,  -- race type
                    v_ethnicity_id, -- ethnicity type
                    v_tenant_id,                         -- Tenant ID
                    v_rec_status_id,                     -- rec_status_id
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', -- created_at
                    p_created_by,                        -- created_by
                    NULL, NULL, NULL, NULL               -- Updated and deleted info
                ) RETURNING rsubject_id INTO v_research_subject_id;
	        END IF;  
	        
	        -- Insert BMI observation if p_bmi is provided
	        IF p_bmi IS NOT NULL THEN
	            INSERT INTO drh_stateful_research_study.subject_observation (
	                id, research_subject_id, code, category, value, unit, effective_datetime, 
	                tenant_id, rec_status_id, created_at, created_by
	            ) 
	            VALUES (
	                drh_stateless_util.generate_unique_id(),
	                v_research_subject_id,  -- research_subject_id
	                bmi_loinc_code,         -- LOINC code for BMI
	                'Clinical',             -- Category
	                p_bmi,                  -- BMI value
	                'kg/m^2',               -- Unit
	                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
	                v_tenant_id,            -- Tenant ID
	                v_rec_status_id,        -- rec_status_id
	                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
	                p_created_by            -- created_by
	            );
	        END IF;
	
	        -- Insert Baseline HbA1c observation if p_baseline_hba1c is provided
	        IF p_baseline_hba1c IS NOT NULL THEN
	            INSERT INTO drh_stateful_research_study.subject_observation (
	                id, research_subject_id, code, category, value, unit, effective_datetime, 
	                tenant_id, rec_status_id, created_at, created_by
	            )
	            VALUES (
	                drh_stateless_util.generate_unique_id(),
	                v_research_subject_id,  -- research_subject_id
	                hba1c_loinc_code,       -- LOINC code for HbA1c
	                'Clinical',             -- Category
	                p_baseline_hba1c,       -- HbA1c value
	                '%',                    -- Unit for HbA1c
	                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Effective datetime
	                v_tenant_id,            -- Tenant ID
	                v_rec_status_id,        -- rec_status_id
	                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- created_at
	                p_created_by            -- created_by
	            );
	        END IF;
	       
           -- Insert new records into participant_base
           PERFORM drh_stateless_raw_observation.save_participant_base(v_research_subject_id);
	       
            -- Update success result
            result := jsonb_build_object(
                'status', 'success',
                'message', 'Participant created successfully',
                'participant_display_id', p_participant_display_id,
                'participant_id', v_research_subject_id
            );
        END;
        
    END IF;
   
   -- Return result even if no participant_display_id was provided
    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
    'function_name', function_name,
    'error_code', err_state,
    'error_message', err_message,
    'error_detail', err_detail,
    'error_hint', err_hint,
    'error_context', err_context,
    'query', current_query,
    'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during individual participant data insertion through file', 'error_details', error_details_json);
    RETURN result;
END;
$function$
;


------------------------------------------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION drh_stateless_research_study.insert_missing_participant_file_mappings()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    spfm_row RECORD;
    content_entry JSONB;
    participant_id TEXT;
    subject RECORD;
BEGIN
    RAISE NOTICE 'Starting participant-file mapping insertion...';

    -- Loop through all entries in study_participant_file_mapping
    FOR spfm_row IN
        SELECT * FROM drh_stateful_research_study.study_participant_file_mapping
        WHERE rec_status_id = 1 -- optional filter
    LOOP
        RAISE NOTICE 'Processing file mapping ID: %, study ID: %, tenant ID: %',
            spfm_row.rs_file_map_id, spfm_row.study_id, spfm_row.tenant_id;

        -- Loop through each JSONB object in file_content_json
        FOR content_entry IN
            SELECT * FROM jsonb_array_elements(spfm_row.file_content_json)
        LOOP
            participant_id := content_entry->>'participant_id';
            RAISE NOTICE '  Found participant_id in JSON: %', participant_id;

            IF participant_id IS NULL THEN
                RAISE NOTICE '  Skipping entry: participant_id is NULL in file_content_json.';
                CONTINUE;
            END IF;

            -- Try to find the matching research_subject
            SELECT * INTO subject
            FROM drh_stateful_research_study.research_subject rs
            WHERE rs.participant_identifier = participant_id
              AND rs.study_reference = spfm_row.study_id
              AND rs.tenant_id = spfm_row.tenant_id;

            IF FOUND THEN
                RAISE NOTICE '  Matched research_subject ID: % for participant_id: %',
                    subject.rsubject_id, participant_id;

                -- Check if already mapped
                IF NOT EXISTS (
                    SELECT 1
                    FROM drh_stateful_research_study.participant_file_mapping pfm
                    WHERE pfm.r_subject_id = subject.rsubject_id
                      AND pfm.file_mapping_id = spfm_row.rs_file_map_id
                ) THEN
                    -- Insert mapping
                    INSERT INTO drh_stateful_research_study.participant_file_mapping (
                        id,
                        file_mapping_id,
                        r_subject_id,
                        study_id,
                        tenant_id,
                        rec_status_id,
                        created_at,
                        created_by
                    )
                    VALUES (
                        drh_stateless_util.generate_unique_id(),
                        spfm_row.rs_file_map_id,
                        subject.rsubject_id,
                        spfm_row.study_id,
                        spfm_row.tenant_id,
                        (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
                        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                        NULL
                    );
                    RAISE NOTICE '  Inserted mapping: subject ID %, file_mapping ID %',
                        subject.rsubject_id, spfm_row.rs_file_map_id;
                ELSE
                    RAISE NOTICE '  Mapping already exists: subject ID %, file_mapping ID %',
                        subject.rsubject_id, spfm_row.rs_file_map_id;
                END IF;
            ELSE
                RAISE NOTICE '  No matching research_subject found for participant_id: %', participant_id;
            END IF;

        END LOOP;
    END LOOP;

    RAISE NOTICE 'Completed participant-file mapping process.';
END;
$function$
;
