import json
import os
import csv
from datetime import datetime

def validate_file(file_path, schema):
    # Check if the file exists
    if not os.path.isfile(file_path):
        print(f"File '{file_path}' not found.")
        return False

    # Check if the file is empty
    if os.stat(file_path).st_size == 0:
        print(f"File '{file_path}' is empty.")
        return False

    missing_fields = []
    empty_columns = []
    data_errors = False

    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            rows = list(reader)

            if len(rows) == 0:
                print(f"File '{file_path}' contains no data.")
                return False

            # Check for missing required fields and empty columns
            for field in schema:
                field_name = field['name']
                required = field.get('required', False)

                # Check if field is missing
                if required and field_name not in reader.fieldnames:
                    missing_fields.append(field_name)

                # Check for empty values in the field across rows
                empty_values = [row[field_name] == '' or row[field_name] is None for row in rows]
                if any(empty_values):
                    empty_columns.append(field_name)

                # Check field values based on expected type
                for row in rows:
                    value = row.get(field_name, None)
                    if value:
                        expected_type = field['type']
                        if expected_type == 'date':
                            try:
                                datetime.strptime(value, field['format'])
                            except ValueError:
                                print(f"Invalid date format in {file_path}: {field_name} - {value}")
                                data_errors = True
                        elif expected_type == 'integer':
                            if not value.isdigit():
                                print(f"Invalid integer value in {file_path}: {field_name} - {value}")
                                data_errors = True
                        elif expected_type == 'number':
                            try:
                                float(value)
                            except ValueError:
                                print(f"Invalid number value in {file_path}: {field_name} - {value}")
                                data_errors = True
            # Report missing fields and empty columns
            if missing_fields:
                print(f"Missing required fields in {file_path}: {', '.join(missing_fields)}")
            if empty_columns:
                print(f"Columns with empty values in {file_path}: {', '.join(empty_columns)}")

            if data_errors:
                return False

        return True
    except Exception as e:
        print(f"Error reading file '{file_path}': {e}")
        return False

def validate_study_data(json_path, data_folder):
    # Load JSON schema
    with open(json_path, 'r') as f:
        schema = json.load(f)

    # Iterate over each resource in the schema
    validation_errors = []
    for resource in schema['resources']:
        #file_path = os.path.join(data_folder, resource['path'])
        file_path = resource['path']
        print(f"Validating file: {file_path}")

        if not validate_file(file_path, resource['schema']['fields']):
            validation_errors.append(f"Validation failed for {file_path}")

    if validation_errors:
        print("\nValidation Errors:")
        for error in validation_errors:
            print(error)
    else:
        print("\nAll files validated successfully.")

if __name__ == "__main__":
    # Adjust the paths accordingly
    json_file_path = 'study-validation-datapackage.json'
    data_folder = 'research-study-sample'  # Folder where your files are located
    validate_study_data(json_file_path, data_folder)
