DROP TABLE IF EXISTS interaction;

-- activity_log definition
DROP TABLE IF EXISTS activity_log;

CREATE TABLE activity_log (
    activity_id INTEGER PRIMARY KEY AUTOINCREMENT,
    -- SQLite uses AUTOINCREMENT for serial-like behavior
    activity_name TEXT,
    -- SQLite uses TEXT instead of varchar
    activity_type TEXT,
    activity_description TEXT NULL,
    root_id INTEGER NULL,
    -- SQLite uses INTEGER for int4
    parent_id INTEGER NULL,
    activity_hierarchy TEXT NULL,
    hierarchy_path TEXT NULL,
    request_url TEXT NULL,
    tenant_id INTEGER NULL,
    platform TEXT NULL,
    environment TEXT NULL,
    created_by TEXT NULL,
    user_name TEXT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    -- SQLite uses TEXT to store dates, with the current timestamp as default
    app_version TEXT NULL,
    test_case TEXT NULL,
    session_id TEXT NULL,
    linkage_id TEXT NULL,
    ip_address TEXT NULL,
    location_latitude TEXT NULL,
    location_longitude TEXT NULL,
    activity_data TEXT NULL,
    activity_log_level TEXT NULL,
    session_unique_id TEXT NOT NULL
);