DROP TABLE IF EXISTS filter_interaction;

CREATE TABLE filter_interaction (
    filter_interaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
    -- SQLite uses AUTOINCREMENT for serial-like behavior
    filter_name TEXT,
    -- SQLite uses TEXT instead of varchar
    filter_type TEXT,
    filter_description TEXT,
    view_mode TEXT NULL,
    created_by TEXT NULL,
    updated_by TEXT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    -- SQLite uses TEXT to store dates, with the current timestamp as default
    updated_at TEXT DEFAULT (datetime('now')),
    filter TEXT NOT NULL
);

DROP TABLE IF EXISTS vanna_ai_request_respose;

CREATE TABLE vanna_ai_request_respose (
    id TEXT PRIMARY KEY,
    question TEXT NOT NULL,
    sql_query TEXT NULL,
    results TEXT NULL,
    json_result TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT
);