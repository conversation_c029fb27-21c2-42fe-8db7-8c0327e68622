-- Attach the databases
ATTACH DATABASE 'resource-surveillance-dclp1.sqlite.db' AS "resource_surveillance_dclp1";
ATTACH DATABASE 'resource-surveillance-dclp3.sqlite.db' AS "resource_surveillance_dclp3";
ATTACH DATABASE 'resource-surveillance-dss1.sqlite.db' AS "resource_surveillance_dss1";
ATTACH DATABASE 'resource-surveillance-ntlt.sqlite.db' AS "resource_surveillance_ntlt";

-- Function to insert or ignore from all tables
CREATE TEMP TABLE temp_tables (name TEXT);
CREATE TEMP TABLE temp_queries (query TEXT);

-- Get list of tables from one of the databases (assuming all have the same structure)
INSERT INTO temp_tables(name)
SELECT name FROM resource_surveillance_dclp1.sqlite_master WHERE type='table';

-- Generate insert queries for each table
INSERT INTO temp_queries(query)
SELECT 'INSERT OR IGNORE INTO ' || name || ' SELECT * FROM resource_surveillance_dclp1.' || name FROM temp_tables
UNION
SELECT 'INSERT OR IGNORE INTO ' || name || ' SELECT * FROM resource_surveillance_dclp3.' || name FROM temp_tables
UNION
SELECT 'INSERT OR IGNORE INTO ' || name || ' SELECT * FROM resource_surveillance_dss1.' || name FROM temp_tables
UNION
SELECT 'INSERT OR IGNORE INTO ' || name || ' SELECT * FROM resource_surveillance_ntlt.' || name FROM temp_tables;

-- Execute the queries
BEGIN;
SELECT query FROM temp_queries;
COMMIT;

-- Detach the databases
DETACH DATABASE "resource_surveillance_dclp1";
DETACH DATABASE "resource_surveillance_dclp3";
DETACH DATABASE "resource_surveillance_dss1";
DETACH DATABASE "resource_surveillance_ntlt";

-- Clean up
DROP TABLE temp_tables;
DROP TABLE temp_queries;
