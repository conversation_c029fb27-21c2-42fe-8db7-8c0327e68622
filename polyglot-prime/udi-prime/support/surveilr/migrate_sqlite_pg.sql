--ATTACH 'user=postgres password=***** host=************ port=5432 dbname=pgdcp_prime_razak' AS DRH (TYPE POSTGRES);

ATTACH DATABASE 'target.sqlite.db' AS "RS";

CREATE SCHEMA DRH.drh_surveilr;

CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.assurance_schema AS SELECT * FROM RS.assurance_schema;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.behavior AS SELECT * FROM RS.behavior;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.code_notebook_cell AS SELECT * FROM RS.code_notebook_cell;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.code_notebook_kernel AS SELECT * FROM RS.code_notebook_kernel;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.code_notebook_state AS SELECT * FROM RS.code_notebook_state;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.device AS SELECT * FROM RS.device;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.device_party_relationship AS SELECT * FROM RS.device_party_relationship;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.gender_type AS SELECT * FROM RS.gender_type;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.organization AS SELECT * FROM RS.organization;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.organization_role AS SELECT * FROM RS.organization_role;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.organization_role_type AS SELECT * FROM RS.organization_role_type;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.party AS SELECT * FROM RS.party;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.party_relation AS SELECT * FROM RS.party_relation;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.party_relation_type AS SELECT * FROM RS.party_relation_type;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.party_type AS SELECT * FROM RS.party_type;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.person AS SELECT * FROM RS.person;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.sqlpage_files AS SELECT * FROM RS.sqlpage_files;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource AS SELECT * FROM RS.uniform_resource;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_author AS SELECT * FROM RS.uniform_resource_author;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_cgm_file_metadata AS SELECT * FROM RS.uniform_resource_cgm_file_metadata;

--CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_cgm_tracing AS SELECT * FROM RS.uniform_resource_cgm_tracing;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_cgm_tracing_dclp1 AS SELECT * FROM RS.uniform_resource_cgm_tracing_dclp1;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_cgm_tracing_dclp3 AS SELECT * FROM RS.uniform_resource_cgm_tracing_dclp3;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_cgm_tracing_dss1 AS SELECT * FROM RS.uniform_resource_cgm_tracing_dss1;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_cgm_tracing_ntlt AS SELECT * FROM RS.uniform_resource_cgm_tracing_ntlt;

CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_institution AS SELECT * FROM RS.uniform_resource_institution;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_investigator AS SELECT * FROM RS.uniform_resource_investigator;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_lab AS SELECT * FROM RS.uniform_resource_lab;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_participant AS SELECT * FROM RS.uniform_resource_participant;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_publication AS SELECT * FROM RS.uniform_resource_publication;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_site AS SELECT * FROM RS.uniform_resource_site;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_study AS SELECT * FROM RS.uniform_resource_study;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.uniform_resource_transform AS SELECT * FROM RS.uniform_resource_transform;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_resource_path_match_rule AS SELECT * FROM RS.ur_ingest_resource_path_match_rule;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_resource_path_rewrite_rule AS SELECT * FROM RS.ur_ingest_resource_path_rewrite_rule;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session AS SELECT * FROM RS.ur_ingest_session;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_attachment AS SELECT * FROM RS.ur_ingest_session_attachment;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_fs_path AS SELECT * FROM RS.ur_ingest_session_fs_path;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_fs_path_entry AS SELECT * FROM RS.ur_ingest_session_fs_path_entry;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_imap_account AS SELECT * FROM RS.ur_ingest_session_imap_account;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_imap_acct_folder AS SELECT * FROM RS.ur_ingest_session_imap_acct_folder;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_imap_acct_folder_message AS SELECT * FROM RS.ur_ingest_session_imap_acct_folder_message;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_account AS SELECT * FROM RS.ur_ingest_session_plm_account;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_acct_label AS SELECT * FROM RS.ur_ingest_session_plm_acct_label;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_acct_project AS SELECT * FROM RS.ur_ingest_session_plm_acct_project;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_acct_project_issue AS SELECT * FROM RS.ur_ingest_session_plm_acct_project_issue;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_acct_relationship AS SELECT * FROM RS.ur_ingest_session_plm_acct_relationship;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_comment AS SELECT * FROM RS.ur_ingest_session_plm_comment;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_issue_reaction AS SELECT * FROM RS.ur_ingest_session_plm_issue_reaction;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_issue_type AS SELECT * FROM RS.ur_ingest_session_plm_issue_type;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_milestone AS SELECT * FROM RS.ur_ingest_session_plm_milestone;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_reaction AS SELECT * FROM RS.ur_ingest_session_plm_reaction;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_plm_user AS SELECT * FROM RS.ur_ingest_session_plm_user;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_task AS SELECT * FROM RS.ur_ingest_session_task;
CREATE TABLE IF NOT EXISTS DRH.drh_surveilr.ur_ingest_session_udi_pgp_sql AS SELECT * FROM RS.ur_ingest_session_udi_pgp_sql;

-- assurance_schema
-- behavior
-- code_notebook_cell
-- code_notebook_kernel
-- code_notebook_state
-- device
-- device_party_relationship
-- gender_type
-- organization
-- organization_role
-- organization_role_type
-- party
-- party_relation
-- party_relation_type
-- party_type
-- person
-- sqlpage_files
-- uniform_resource
-- uniform_resource_author
-- uniform_resource_cgm_file_metadata
-- uniform_resource_cgm_tracing
-- uniform_resource_institution
-- uniform_resource_investigator
-- uniform_resource_lab
-- uniform_resource_participant
-- uniform_resource_publication
-- uniform_resource_site
-- uniform_resource_study
-- uniform_resource_transform
-- ur_ingest_resource_path_match_rule
-- ur_ingest_resource_path_rewrite_rule
-- ur_ingest_session
-- ur_ingest_session_attachment
-- ur_ingest_session_fs_path
-- ur_ingest_session_fs_path_entry
-- ur_ingest_session_imap_account
-- ur_ingest_session_imap_acct_folder
-- ur_ingest_session_imap_acct_folder_message
-- ur_ingest_session_plm_account
-- ur_ingest_session_plm_acct_label
-- ur_ingest_session_plm_acct_project
-- ur_ingest_session_plm_acct_project_issue
-- ur_ingest_session_plm_acct_relationship
-- ur_ingest_session_plm_comment
-- ur_ingest_session_plm_issue_reaction
-- ur_ingest_session_plm_issue_type
-- ur_ingest_session_plm_milestone
-- ur_ingest_session_plm_reaction
-- ur_ingest_session_plm_user
-- ur_ingest_session_task
-- ur_ingest_session_udi_pgp_sql