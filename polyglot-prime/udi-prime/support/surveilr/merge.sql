ATTACH DATABASE 'resource-surveillance-dclp1.sqlite.db' AS "resource_surveillance_dclp1";
ATTACH DATABASE 'resource-surveillance-dclp3.sqlite.db' AS "resource_surveillance_dclp3";
ATTACH DATABASE 'resource-surveillance-dss1.sqlite.db' AS "resource_surveillance_dss1";
ATTACH DATABASE 'resource-surveillance-ntlt.sqlite.db' AS "resource_surveillance_ntlt";

CREATE TABLE uniform_resource_author(
  author_id TEXT PRIMARY KEY NOT NULL,
  name TEXT,
  email TEXT,
  investigator_id TEXT,
  study_id TEXT
);

CREATE TABLE uniform_resource_cgm_file_metadata(
  metadata_id TEXT PRIMARY KEY NOT NULL,
  devicename TEXT,
  device_id TEXT,
  source_platform TEXT,
  patient_id TEXT,
  file_name TEXT,
  file_format TEXT,
  file_upload_date TEXT,
  data_start_date TEXT,
  data_end_date TEXT,
  study_id TEXT
);

CREATE TABLE uniform_resource_cgm_tracing(
  SID TEXT PRIMARY KEY NOT NULL,
  Period TEXT,
  Date_Time TEXT,
  CGM_Value TEXT
);

CREATE TABLE uniform_resource_institution(
  institution_id TEXT PRIMARY KEY NOT NULL,
  institution_name TEXT,
  city TEXT,
  state TEXT,
  country TEXT
);

CREATE TABLE uniform_resource_investigator(
  investigator_id TEXT PRIMARY KEY NOT NULL,
  investigator_name TEXT,
  email TEXT,
  study_id TEXT
);

CREATE TABLE uniform_resource_lab(
  lab_id TEXT PRIMARY KEY NOT NULL,
  lab_name TEXT,
  lab_pi TEXT,
  institution_id TEXT,
  study_id TEXT
);

CREATE TABLE uniform_resource_participant(
  sid TEXT PRIMARY KEY NOT NULL,
  study TEXT,
  age TEXT,
  sex TEXT,
  race TEXT,
  bmi TEXT,
  baseline_hba1c TEXT,
  tx_modality TEXT,
  diabetes_type TEXT,
  study_arm TEXT,
  diagnosis_icd TEXT,
  med_rxnorm TEXT
);

CREATE TABLE uniform_resource_publication(
  publication_id TEXT PRIMARY KEY NOT NULL,
  publication_title TEXT,
  digital_object_identifier TEXT,
  publication_site TEXT,
  study_id TEXT
);

CREATE TABLE uniform_resource_site(
  study_id TEXT PRIMARY KEY NOT NULL,
  site_id TEXT,
  site_name TEXT,
  site_type TEXT
);

CREATE TABLE uniform_resource_study(
  study_id TEXT PRIMARY KEY NOT NULL,
  study_name TEXT,
  start_date TEXT,
  end_date TEXT,
  treatment_modalities TEXT,
  funding_source TEXT,
  nct_number TEXT,
  study_description TEXT
);

INSERT OR IGNORE INTO assurance_schema SELECT * FROM resource_surveillance_dclp1.assurance_schema;
INSERT OR IGNORE INTO assurance_schema SELECT * FROM resource_surveillance_dclp3.assurance_schema;
INSERT OR IGNORE INTO assurance_schema SELECT * FROM resource_surveillance_dss1.assurance_schema;
INSERT OR IGNORE INTO assurance_schema SELECT * FROM resource_surveillance_ntlt.assurance_schema;
INSERT OR IGNORE INTO behavior SELECT * FROM resource_surveillance_dclp1.behavior;
INSERT OR IGNORE INTO behavior SELECT * FROM resource_surveillance_dclp3.behavior;
INSERT OR IGNORE INTO behavior SELECT * FROM resource_surveillance_dss1.behavior;
INSERT OR IGNORE INTO behavior SELECT * FROM resource_surveillance_ntlt.behavior;
INSERT OR IGNORE INTO code_notebook_cell SELECT * FROM resource_surveillance_dclp1.code_notebook_cell;
INSERT OR IGNORE INTO code_notebook_cell SELECT * FROM resource_surveillance_dclp3.code_notebook_cell;
INSERT OR IGNORE INTO code_notebook_cell SELECT * FROM resource_surveillance_dss1.code_notebook_cell;
INSERT OR IGNORE INTO code_notebook_cell SELECT * FROM resource_surveillance_ntlt.code_notebook_cell;
INSERT OR IGNORE INTO code_notebook_kernel SELECT * FROM resource_surveillance_dclp1.code_notebook_kernel;
INSERT OR IGNORE INTO code_notebook_kernel SELECT * FROM resource_surveillance_dclp3.code_notebook_kernel;
INSERT OR IGNORE INTO code_notebook_kernel SELECT * FROM resource_surveillance_dss1.code_notebook_kernel;
INSERT OR IGNORE INTO code_notebook_kernel SELECT * FROM resource_surveillance_ntlt.code_notebook_kernel;
INSERT OR IGNORE INTO code_notebook_state SELECT * FROM resource_surveillance_dclp1.code_notebook_state;
INSERT OR IGNORE INTO code_notebook_state SELECT * FROM resource_surveillance_dclp3.code_notebook_state;
INSERT OR IGNORE INTO code_notebook_state SELECT * FROM resource_surveillance_dss1.code_notebook_state;
INSERT OR IGNORE INTO code_notebook_state SELECT * FROM resource_surveillance_ntlt.code_notebook_state;
INSERT OR IGNORE INTO device SELECT * FROM resource_surveillance_dclp1.device;
INSERT OR IGNORE INTO device SELECT * FROM resource_surveillance_dclp3.device;
INSERT OR IGNORE INTO device SELECT * FROM resource_surveillance_dss1.device;
INSERT OR IGNORE INTO device SELECT * FROM resource_surveillance_ntlt.device;
INSERT OR IGNORE INTO device_party_relationship SELECT * FROM resource_surveillance_dclp1.device_party_relationship;
INSERT OR IGNORE INTO device_party_relationship SELECT * FROM resource_surveillance_dclp3.device_party_relationship;
INSERT OR IGNORE INTO device_party_relationship SELECT * FROM resource_surveillance_dss1.device_party_relationship;
INSERT OR IGNORE INTO device_party_relationship SELECT * FROM resource_surveillance_ntlt.device_party_relationship;
INSERT OR IGNORE INTO gender_type SELECT * FROM resource_surveillance_dclp1.gender_type;
INSERT OR IGNORE INTO gender_type SELECT * FROM resource_surveillance_dclp3.gender_type;
INSERT OR IGNORE INTO gender_type SELECT * FROM resource_surveillance_dss1.gender_type;
INSERT OR IGNORE INTO gender_type SELECT * FROM resource_surveillance_ntlt.gender_type;
INSERT OR IGNORE INTO organization SELECT * FROM resource_surveillance_dclp1.organization;
INSERT OR IGNORE INTO organization SELECT * FROM resource_surveillance_dclp3.organization;
INSERT OR IGNORE INTO organization SELECT * FROM resource_surveillance_dss1.organization;
INSERT OR IGNORE INTO organization SELECT * FROM resource_surveillance_ntlt.organization;
INSERT OR IGNORE INTO organization_role SELECT * FROM resource_surveillance_dclp1.organization_role;
INSERT OR IGNORE INTO organization_role SELECT * FROM resource_surveillance_dclp3.organization_role;
INSERT OR IGNORE INTO organization_role SELECT * FROM resource_surveillance_dss1.organization_role;
INSERT OR IGNORE INTO organization_role SELECT * FROM resource_surveillance_ntlt.organization_role;
INSERT OR IGNORE INTO organization_role_type SELECT * FROM resource_surveillance_dclp1.organization_role_type;
INSERT OR IGNORE INTO organization_role_type SELECT * FROM resource_surveillance_dclp3.organization_role_type;
INSERT OR IGNORE INTO organization_role_type SELECT * FROM resource_surveillance_dss1.organization_role_type;
INSERT OR IGNORE INTO organization_role_type SELECT * FROM resource_surveillance_ntlt.organization_role_type;
INSERT OR IGNORE INTO party SELECT * FROM resource_surveillance_dclp1.party;
INSERT OR IGNORE INTO party SELECT * FROM resource_surveillance_dclp3.party;
INSERT OR IGNORE INTO party SELECT * FROM resource_surveillance_dss1.party;
INSERT OR IGNORE INTO party SELECT * FROM resource_surveillance_ntlt.party;
INSERT OR IGNORE INTO party_relation SELECT * FROM resource_surveillance_dclp1.party_relation;
INSERT OR IGNORE INTO party_relation SELECT * FROM resource_surveillance_dclp3.party_relation;
INSERT OR IGNORE INTO party_relation SELECT * FROM resource_surveillance_dss1.party_relation;
INSERT OR IGNORE INTO party_relation SELECT * FROM resource_surveillance_ntlt.party_relation;
INSERT OR IGNORE INTO party_relation_type SELECT * FROM resource_surveillance_dclp1.party_relation_type;
INSERT OR IGNORE INTO party_relation_type SELECT * FROM resource_surveillance_dclp3.party_relation_type;
INSERT OR IGNORE INTO party_relation_type SELECT * FROM resource_surveillance_dss1.party_relation_type;
INSERT OR IGNORE INTO party_relation_type SELECT * FROM resource_surveillance_ntlt.party_relation_type;
INSERT OR IGNORE INTO party_type SELECT * FROM resource_surveillance_dclp1.party_type;
INSERT OR IGNORE INTO party_type SELECT * FROM resource_surveillance_dclp3.party_type;
INSERT OR IGNORE INTO party_type SELECT * FROM resource_surveillance_dss1.party_type;
INSERT OR IGNORE INTO party_type SELECT * FROM resource_surveillance_ntlt.party_type;
INSERT OR IGNORE INTO person SELECT * FROM resource_surveillance_dclp1.person;
INSERT OR IGNORE INTO person SELECT * FROM resource_surveillance_dclp3.person;
INSERT OR IGNORE INTO person SELECT * FROM resource_surveillance_dss1.person;
INSERT OR IGNORE INTO person SELECT * FROM resource_surveillance_ntlt.person;
INSERT OR IGNORE INTO sqlpage_files SELECT * FROM resource_surveillance_dclp1.sqlpage_files;
INSERT OR IGNORE INTO sqlpage_files SELECT * FROM resource_surveillance_dclp3.sqlpage_files;
INSERT OR IGNORE INTO sqlpage_files SELECT * FROM resource_surveillance_dss1.sqlpage_files;
INSERT OR IGNORE INTO sqlpage_files SELECT * FROM resource_surveillance_ntlt.sqlpage_files;
INSERT OR IGNORE INTO uniform_resource SELECT * FROM resource_surveillance_dclp1.uniform_resource;
INSERT OR IGNORE INTO uniform_resource SELECT * FROM resource_surveillance_dclp3.uniform_resource;
INSERT OR IGNORE INTO uniform_resource SELECT * FROM resource_surveillance_dss1.uniform_resource;
INSERT OR IGNORE INTO uniform_resource SELECT * FROM resource_surveillance_ntlt.uniform_resource;
INSERT OR IGNORE INTO uniform_resource_author SELECT * FROM resource_surveillance_dclp1.uniform_resource_author;
INSERT OR IGNORE INTO uniform_resource_author SELECT * FROM resource_surveillance_dclp3.uniform_resource_author;
INSERT OR IGNORE INTO uniform_resource_author SELECT * FROM resource_surveillance_dss1.uniform_resource_author;
INSERT OR IGNORE INTO uniform_resource_author SELECT * FROM resource_surveillance_ntlt.uniform_resource_author;
INSERT OR IGNORE INTO uniform_resource_cgm_file_metadata SELECT * FROM resource_surveillance_dclp1.uniform_resource_cgm_file_metadata;
INSERT OR IGNORE INTO uniform_resource_cgm_file_metadata SELECT * FROM resource_surveillance_dclp3.uniform_resource_cgm_file_metadata;
INSERT OR IGNORE INTO uniform_resource_cgm_file_metadata SELECT * FROM resource_surveillance_dss1.uniform_resource_cgm_file_metadata;
INSERT OR IGNORE INTO uniform_resource_cgm_file_metadata SELECT * FROM resource_surveillance_ntlt.uniform_resource_cgm_file_metadata;
INSERT OR IGNORE INTO uniform_resource_cgm_tracing SELECT * FROM resource_surveillance_dclp1.uniform_resource_cgm_tracing;
INSERT OR IGNORE INTO uniform_resource_cgm_tracing SELECT * FROM resource_surveillance_dclp3.uniform_resource_cgm_tracing;
INSERT OR IGNORE INTO uniform_resource_cgm_tracing SELECT * FROM resource_surveillance_dss1.uniform_resource_cgm_tracing;
INSERT OR IGNORE INTO uniform_resource_cgm_tracing SELECT * FROM resource_surveillance_ntlt.uniform_resource_cgm_tracing;
INSERT OR IGNORE INTO uniform_resource_institution SELECT * FROM resource_surveillance_dclp1.uniform_resource_institution;
INSERT OR IGNORE INTO uniform_resource_institution SELECT * FROM resource_surveillance_dclp3.uniform_resource_institution;
INSERT OR IGNORE INTO uniform_resource_institution SELECT * FROM resource_surveillance_dss1.uniform_resource_institution;
INSERT OR IGNORE INTO uniform_resource_institution SELECT * FROM resource_surveillance_ntlt.uniform_resource_institution;
INSERT OR IGNORE INTO uniform_resource_investigator SELECT * FROM resource_surveillance_dclp1.uniform_resource_investigator;
INSERT OR IGNORE INTO uniform_resource_investigator SELECT * FROM resource_surveillance_dclp3.uniform_resource_investigator;
INSERT OR IGNORE INTO uniform_resource_investigator SELECT * FROM resource_surveillance_dss1.uniform_resource_investigator;
INSERT OR IGNORE INTO uniform_resource_investigator SELECT * FROM resource_surveillance_ntlt.uniform_resource_investigator;
INSERT OR IGNORE INTO uniform_resource_lab SELECT * FROM resource_surveillance_dclp1.uniform_resource_lab;
INSERT OR IGNORE INTO uniform_resource_lab SELECT * FROM resource_surveillance_dclp3.uniform_resource_lab;
INSERT OR IGNORE INTO uniform_resource_lab SELECT * FROM resource_surveillance_dss1.uniform_resource_lab;
INSERT OR IGNORE INTO uniform_resource_lab SELECT * FROM resource_surveillance_ntlt.uniform_resource_lab;
INSERT OR IGNORE INTO uniform_resource_participant SELECT * FROM resource_surveillance_dclp1.uniform_resource_participant;
INSERT OR IGNORE INTO uniform_resource_participant SELECT * FROM resource_surveillance_dclp3.uniform_resource_participant;
INSERT OR IGNORE INTO uniform_resource_participant SELECT * FROM resource_surveillance_dss1.uniform_resource_participant;
INSERT OR IGNORE INTO uniform_resource_participant SELECT * FROM resource_surveillance_ntlt.uniform_resource_participant;
INSERT OR IGNORE INTO uniform_resource_publication SELECT * FROM resource_surveillance_dclp1.uniform_resource_publication;
INSERT OR IGNORE INTO uniform_resource_publication SELECT * FROM resource_surveillance_dclp3.uniform_resource_publication;
INSERT OR IGNORE INTO uniform_resource_publication SELECT * FROM resource_surveillance_dss1.uniform_resource_publication;
INSERT OR IGNORE INTO uniform_resource_publication SELECT * FROM resource_surveillance_ntlt.uniform_resource_publication;
INSERT OR IGNORE INTO uniform_resource_site SELECT * FROM resource_surveillance_dclp1.uniform_resource_site;
INSERT OR IGNORE INTO uniform_resource_site SELECT * FROM resource_surveillance_dclp3.uniform_resource_site;
INSERT OR IGNORE INTO uniform_resource_site SELECT * FROM resource_surveillance_dss1.uniform_resource_site;
INSERT OR IGNORE INTO uniform_resource_site SELECT * FROM resource_surveillance_ntlt.uniform_resource_site;
INSERT OR IGNORE INTO uniform_resource_study SELECT * FROM resource_surveillance_dclp1.uniform_resource_study;
INSERT OR IGNORE INTO uniform_resource_study SELECT * FROM resource_surveillance_dclp3.uniform_resource_study;
INSERT OR IGNORE INTO uniform_resource_study SELECT * FROM resource_surveillance_dss1.uniform_resource_study;
INSERT OR IGNORE INTO uniform_resource_study SELECT * FROM resource_surveillance_ntlt.uniform_resource_study;
INSERT OR IGNORE INTO uniform_resource_transform SELECT * FROM resource_surveillance_dclp1.uniform_resource_transform;
INSERT OR IGNORE INTO uniform_resource_transform SELECT * FROM resource_surveillance_dclp3.uniform_resource_transform;
INSERT OR IGNORE INTO uniform_resource_transform SELECT * FROM resource_surveillance_dss1.uniform_resource_transform;
INSERT OR IGNORE INTO uniform_resource_transform SELECT * FROM resource_surveillance_ntlt.uniform_resource_transform;
INSERT OR IGNORE INTO ur_ingest_resource_path_match_rule SELECT * FROM resource_surveillance_dclp1.ur_ingest_resource_path_match_rule;
INSERT OR IGNORE INTO ur_ingest_resource_path_match_rule SELECT * FROM resource_surveillance_dclp3.ur_ingest_resource_path_match_rule;
INSERT OR IGNORE INTO ur_ingest_resource_path_match_rule SELECT * FROM resource_surveillance_dss1.ur_ingest_resource_path_match_rule;
INSERT OR IGNORE INTO ur_ingest_resource_path_match_rule SELECT * FROM resource_surveillance_ntlt.ur_ingest_resource_path_match_rule;
INSERT OR IGNORE INTO ur_ingest_resource_path_rewrite_rule SELECT * FROM resource_surveillance_dclp1.ur_ingest_resource_path_rewrite_rule;
INSERT OR IGNORE INTO ur_ingest_resource_path_rewrite_rule SELECT * FROM resource_surveillance_dclp3.ur_ingest_resource_path_rewrite_rule;
INSERT OR IGNORE INTO ur_ingest_resource_path_rewrite_rule SELECT * FROM resource_surveillance_dss1.ur_ingest_resource_path_rewrite_rule;
INSERT OR IGNORE INTO ur_ingest_resource_path_rewrite_rule SELECT * FROM resource_surveillance_ntlt.ur_ingest_resource_path_rewrite_rule;
INSERT OR IGNORE INTO ur_ingest_session SELECT * FROM resource_surveillance_dclp1.ur_ingest_session;
INSERT OR IGNORE INTO ur_ingest_session SELECT * FROM resource_surveillance_dclp3.ur_ingest_session;
INSERT OR IGNORE INTO ur_ingest_session SELECT * FROM resource_surveillance_dss1.ur_ingest_session;
INSERT OR IGNORE INTO ur_ingest_session SELECT * FROM resource_surveillance_ntlt.ur_ingest_session;
INSERT OR IGNORE INTO ur_ingest_session_attachment SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_attachment;
INSERT OR IGNORE INTO ur_ingest_session_attachment SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_attachment;
INSERT OR IGNORE INTO ur_ingest_session_attachment SELECT * FROM resource_surveillance_dss1.ur_ingest_session_attachment;
INSERT OR IGNORE INTO ur_ingest_session_attachment SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_attachment;
INSERT OR IGNORE INTO ur_ingest_session_fs_path SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_fs_path;
INSERT OR IGNORE INTO ur_ingest_session_fs_path SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_fs_path;
INSERT OR IGNORE INTO ur_ingest_session_fs_path SELECT * FROM resource_surveillance_dss1.ur_ingest_session_fs_path;
INSERT OR IGNORE INTO ur_ingest_session_fs_path SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_fs_path;
INSERT OR IGNORE INTO ur_ingest_session_fs_path_entry SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_fs_path_entry;
INSERT OR IGNORE INTO ur_ingest_session_fs_path_entry SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_fs_path_entry;
INSERT OR IGNORE INTO ur_ingest_session_fs_path_entry SELECT * FROM resource_surveillance_dss1.ur_ingest_session_fs_path_entry;
INSERT OR IGNORE INTO ur_ingest_session_fs_path_entry SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_fs_path_entry;
INSERT OR IGNORE INTO ur_ingest_session_imap_account SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_imap_account;
INSERT OR IGNORE INTO ur_ingest_session_imap_account SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_imap_account;
INSERT OR IGNORE INTO ur_ingest_session_imap_account SELECT * FROM resource_surveillance_dss1.ur_ingest_session_imap_account;
INSERT OR IGNORE INTO ur_ingest_session_imap_account SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_imap_account;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_imap_acct_folder;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_imap_acct_folder;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder SELECT * FROM resource_surveillance_dss1.ur_ingest_session_imap_acct_folder;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_imap_acct_folder;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder_message SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_imap_acct_folder_message;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder_message SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_imap_acct_folder_message;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder_message SELECT * FROM resource_surveillance_dss1.ur_ingest_session_imap_acct_folder_message;
INSERT OR IGNORE INTO ur_ingest_session_imap_acct_folder_message SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_imap_acct_folder_message;
INSERT OR IGNORE INTO ur_ingest_session_plm_account SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_account;
INSERT OR IGNORE INTO ur_ingest_session_plm_account SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_account;
INSERT OR IGNORE INTO ur_ingest_session_plm_account SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_account;
INSERT OR IGNORE INTO ur_ingest_session_plm_account SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_account;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_label SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_acct_label;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_label SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_acct_label;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_label SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_acct_label;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_label SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_acct_label;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_acct_project;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_acct_project;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_acct_project;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_acct_project;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project_issue SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_acct_project_issue;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project_issue SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_acct_project_issue;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project_issue SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_acct_project_issue;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_project_issue SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_acct_project_issue;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_relationship SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_acct_relationship;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_relationship SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_acct_relationship;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_relationship SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_acct_relationship;
INSERT OR IGNORE INTO ur_ingest_session_plm_acct_relationship SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_acct_relationship;
INSERT OR IGNORE INTO ur_ingest_session_plm_comment SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_comment;
INSERT OR IGNORE INTO ur_ingest_session_plm_comment SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_comment;
INSERT OR IGNORE INTO ur_ingest_session_plm_comment SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_comment;
INSERT OR IGNORE INTO ur_ingest_session_plm_comment SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_comment;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_reaction SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_issue_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_reaction SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_issue_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_reaction SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_issue_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_reaction SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_issue_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_type SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_issue_type;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_type SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_issue_type;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_type SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_issue_type;
INSERT OR IGNORE INTO ur_ingest_session_plm_issue_type SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_issue_type;
INSERT OR IGNORE INTO ur_ingest_session_plm_milestone SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_milestone;
INSERT OR IGNORE INTO ur_ingest_session_plm_milestone SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_milestone;
INSERT OR IGNORE INTO ur_ingest_session_plm_milestone SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_milestone;
INSERT OR IGNORE INTO ur_ingest_session_plm_milestone SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_milestone;
INSERT OR IGNORE INTO ur_ingest_session_plm_reaction SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_reaction SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_reaction SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_reaction SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_reaction;
INSERT OR IGNORE INTO ur_ingest_session_plm_user SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_plm_user;
INSERT OR IGNORE INTO ur_ingest_session_plm_user SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_plm_user;
INSERT OR IGNORE INTO ur_ingest_session_plm_user SELECT * FROM resource_surveillance_dss1.ur_ingest_session_plm_user;
INSERT OR IGNORE INTO ur_ingest_session_plm_user SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_plm_user;
INSERT OR IGNORE INTO ur_ingest_session_task SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_task;
INSERT OR IGNORE INTO ur_ingest_session_task SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_task;
INSERT OR IGNORE INTO ur_ingest_session_task SELECT * FROM resource_surveillance_dss1.ur_ingest_session_task;
INSERT OR IGNORE INTO ur_ingest_session_task SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_task;
INSERT OR IGNORE INTO ur_ingest_session_udi_pgp_sql SELECT * FROM resource_surveillance_dclp1.ur_ingest_session_udi_pgp_sql;
INSERT OR IGNORE INTO ur_ingest_session_udi_pgp_sql SELECT * FROM resource_surveillance_dclp3.ur_ingest_session_udi_pgp_sql;
INSERT OR IGNORE INTO ur_ingest_session_udi_pgp_sql SELECT * FROM resource_surveillance_dss1.ur_ingest_session_udi_pgp_sql;
INSERT OR IGNORE INTO ur_ingest_session_udi_pgp_sql SELECT * FROM resource_surveillance_ntlt.ur_ingest_session_udi_pgp_sql;

DETACH DATABASE resource_surveillance_dclp1;
DETACH DATABASE resource_surveillance_dclp3;
DETACH DATABASE resource_surveillance_dss1;
DETACH DATABASE resource_surveillance_ntlt;