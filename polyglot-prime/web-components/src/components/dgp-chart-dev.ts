import { html, css, LitElement, PropertyValueMap } from 'lit';
import * as d3 from 'd3';
import { customElement, property } from 'lit/decorators.js';

interface DataEntry {
  datetime: string;
  glucose: number | null;
}

interface WeekData {
  start: Date;
  end: Date;
  data: DataEntry[];
}

interface Margin {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

@customElement('dgp-chart')
export class DGP<PERSON>hart extends LitElement {
  @property({ type: Object })
  result = null;

  static properties = {
    result: { type: Object },
  };

  private data: DataEntry[] = [];
  private selector: string | null = null;
  private svg!: d3.Selection<SVGGElement, unknown, null, undefined>;
  private index: number | undefined;
  private margin: Margin;
  private width: number;
  private height: number;
  private parseDate(dateString: string): Date | null {
    return dateString ? new Date(dateString) : null;
  }
  private formatDayOfWeek: ((date: Date) => string) | undefined;
  private x: d3.ScaleTime<number, number> | null = null;
  private y: d3.ScaleLinear<number, number> | null = null;
  private referenceGlucoseLevels: [number, number];
  private first12AM: any;


  constructor() {
    super();

    // Default configuration options (can be overridden)
    this.margin = {
      top: 30,
      right: 30,
      bottom: 35,
      left: 80,
    };
    this.width = 800;
    this.height = 150;

    // Set up default scales and parsers
    this.referenceGlucoseLevels = [70, 180];
  }

  private loadGlucoseData(): void {
    const originalData: any = this.result;
    const transformedData = originalData.map((entry: any) => ({
      datetime: `${entry.date}T${entry.hour.padStart(2, '0')}:00:00`,
      glucose: entry.glucose,
    }));

    const dgpEndDate = new Date(
      transformedData[transformedData.length - 1].datetime
    );
    const dgpStartDate = new Date(dgpEndDate);
    dgpStartDate.setDate(dgpEndDate.getDate() - 13);

    const timeSeries = this.generateTimeSeries(dgpStartDate, dgpEndDate);
    const dataMap = new Map(
      transformedData.map((d: any) => [
        this.formatDateToMatch(new Date(d.datetime)),
        d.glucose,
      ])
    );

    const completeData: DataEntry[] = timeSeries.map((d) => {
      const formattedDate = this.formatDateToMatch(d);
      const glucoseValue = dataMap.get(formattedDate);
      return {
        datetime: formattedDate,
        glucose: glucoseValue !== undefined ? glucoseValue as number : null,
      };
    });


    const weeks = this.divideDatesIntoWeeks(
      completeData[0].datetime,
      dgpEndDate,
      completeData
    );

    this.drawChart(weeks[0].data, '#dgp-wk1');
    this.drawChart(weeks[1].data, '#dgp-wk2');
  }

  private divideDatesIntoWeeks(
    startDate: string,
    endDate: Date,
    data: DataEntry[]
  ): WeekData[] {
    const weeks: WeekData[] = [];
    let currentWeekStart = new Date(startDate);

    while (currentWeekStart <= endDate) {
      const currentWeekEnd = new Date(currentWeekStart);
      currentWeekEnd.setDate(currentWeekStart.getDate() + 6);
      const weekData = data.filter((entry) => {
        const entryDate = new Date(entry.datetime);
        return entryDate >= currentWeekStart && entryDate <= currentWeekEnd;
      });

      weeks.push({
        start: new Date(currentWeekStart),
        end: new Date(currentWeekEnd),
        data: weekData,
      });

      currentWeekStart.setDate(currentWeekStart.getDate() + 7);
    }
    return weeks;
  }

  private formatDateToMatch(date: Date): string {
    return date.toISOString().slice(0, 19);
  }

  private generateTimeSeries(start: Date, end: Date): Date[] {
    const times: Date[] = [];
    let current = new Date(start);
    while (current <= end) {
      times.push(new Date(current));
      current.setHours(current.getHours() + 1);
    }
    return times;
  }

  private drawChart(data: DataEntry[], selector: string): void {
    this.data = data;
    this.selector = selector;
    this.parseDate = (dateString: string): Date => {
      return d3.utcParse("%Y-%m-%dT%H:%M:%S")(dateString) ?? new Date(0);
    };

    this.formatDayOfWeek = d3.timeFormat("%A");
    this.preprocessData();

    const firstDay = new Date(
      d3.min(
        this.data.filter((d) => d.datetime !== null),
        (d) => new Date(d.datetime)
      )
        ?.toISOString()
        .split("T")[0] ?? ""
    );

    this.first12AM = new Date(firstDay);
    this.first12AM.setUTCHours(0, 0, 0, 0);

    this.initializeScales();
    this.drawSVG();
    this.drawAxes();
    this.drawLines();
    this.drawReferenceLines();
    this.drawHighlightAreas();
    this.drawClipPaths();
    this.drawGlucoseLines();
  }

  drawGlucoseLines() {
    const line: any = d3
      .line()
      .x((d: any) => this.x!(new Date(d.datetime)))
      .y((d: any) => this.y!(d.glucose))
      .defined((d: any) => d.glucose !== 0)
      .curve(d3.curveBasis);

    // Draw the glucose lines
    this.svg.append("path").datum(this.data).attr("class", "line").attr("d", line);

    this.svg
      .append("path")
      .datum(this.data)
      .attr("class", "highlight-glucose-l-line")
      .attr("d", line)
      // .style("fill", "rgb(210,131, 107)")
      .style("opacity", 1)
      .attr("clip-path", "url(#clip-below-70)");

    this.svg
      .append("path")
      .datum(this.data)
      .attr("class", "highlight-line")
      .attr("d", line)
      .attr("clip-path", "url(#clip)");

    this.svg
      .append("path")
      .datum(this.data)
      .attr("class", "highlight-glucose-h-line")
      .attr("d", line)
      // .style("fill", "rgb(237, 204, 134)")
      .style("opacity", 1)
      .attr("clip-path", "url(#clip-above-180)");

    // Draw horizontal lines
    this.svg
      .append("line")
      .attr("class", "horizontal-line")
      .attr("x1", 0)
      .attr("x2", this.width)
      .attr("y1", 0)
      .attr("y2", 0);

    this.svg
      .append("line")
      .attr("class", "horizontal-line")
      .attr("x1", 0)
      .attr("x2", this.width)
      .attr("y1", this.height)
      .attr("y2", this.height);
  }

  drawClipPaths() {
    this.svg
      .append("clipPath")
      .attr("id", "clip")
      .append("rect")
      .attr("x", this.x!(this.first12AM as Date))
      .attr("y", this.y!(this.referenceGlucoseLevels[1]))
      .attr(
        "width",
        this.x!(d3.max(this.data, (d: any) => d.datetime)) - this.x!(this.first12AM as Date)
      )
      .attr(
        "height",
        this.y!(this.referenceGlucoseLevels[0]) -
        this.y!(this.referenceGlucoseLevels[1])
      );

    this.svg
      .append("clipPath")
      .attr("id", "clip-above-180")
      .append("rect")
      .attr("x", this.x!(this.first12AM as Date))
      .attr("y", 0)
      .attr(
        "width",
        this.x!(d3.max(this.data, (d: any) => d.datetime)) - this.x!(this.first12AM as Date)
      )
      .attr("height", this.y!(this.referenceGlucoseLevels[1]) - 0);

    this.svg
      .append("clipPath")
      .attr("id", "clip-below-70")
      .append("rect")
      .attr("x", this.x!(this.first12AM as Date))
      .attr("y", this.y!(this.referenceGlucoseLevels[0]))
      .attr(
        "width",
        this.x!(d3.max(this.data, (d: any) => d.datetime)) - this.x!(this.first12AM as Date)
      )
      .attr("height", this.height - this.y!(this.referenceGlucoseLevels[0]));
  }

  drawHighlightAreas() {

    // Draw highlighted areas
    this.svg
      .append("rect")
      .attr("class", "highlight-area-below")
      .attr("x", this.x!(this.first12AM as Date))
      .attr("y", this.y!(this.referenceGlucoseLevels[1]))
      .attr(
        "width",
        this.x!(d3.max(this.data, (d: any) => d.datetime)) - this.x!(this.first12AM!)
      )
      .attr(
        "height",
        this.y!(this.referenceGlucoseLevels[0]) -
        this.y!(this.referenceGlucoseLevels[1])
      )
      .style("fill", "rgba(223, 223, 223, 1)")
      .style("opacity", 1);
  }

  drawReferenceLines() {
    // Draw reference lines
    this.svg
      .selectAll(".reference-line")
      .data(this.referenceGlucoseLevels)
      .enter()
      .append("line")
      .attr("class", "reference-line")
      .attr("x1", 0)
      .attr("x2", this.width)
      .attr("y1", (d) => this.y!(d))
      .attr("y2", (d) => this.y!(d));
  }

  drawLines() {
    const twelveAMs = Array.from(
      new Set(
        this.data
          .map((d) =>
            d.datetime ? new Date(d.datetime).toISOString().split("T")[0] : null
          )
          .filter((dateStr) => dateStr !== null)
      )
    ).map((dateStr) => this.parseDate(dateStr + "T00:00:00"));

    const twelvePMs = Array.from(
      new Set(
        this.data.map((d) =>
          d.datetime ? new Date(d.datetime).toISOString().split("T")[0] : null
        )
      )
    ).map((dateStr) => this.parseDate(dateStr + "T12:00:00"));

    this.svg
      .selectAll(".vertical-line-am")
      .data(twelveAMs)
      .enter()
      .append("line")
      .attr("class", "vertical-line")
      .attr("x1", (d: any) => this.x!(d))
      .attr("x2", (d: any) => this.x!(d))
      .attr("y1", 0)
      .attr("y2", this.height);

    this.svg
      .selectAll(".vertical-line-pm")
      .data(twelvePMs)
      .enter()
      .append("line")
      .attr("class", "vertical-line")
      .attr("x1", (d: any) => this.x!(d))
      .attr("x2", (d: any) => this.x!(d))
      .attr("y1", 0)
      .attr("y2", this.height);

    this.svg
      .selectAll(".label-date")
      .data(twelveAMs)
      .enter()
      .append("text")
      .attr("class", "label-date")
      .attr("x", (d: any) => this.x!(d) + 5)
      .attr("y", 10)
      .text((d: any) => new Date(d).getDate())
      .style("font-size", "10px")
      .style("text-anchor", "start");

    if (this.index == 0) {
      this.svg
        .selectAll(".label-12pm")
        .data(twelvePMs)
        .enter()
        .append("text")
        .attr("class", "day-label")
        .attr("x", (d: any) => {
          let xPos = this.x!(d);
          const usableWidth = this.width - this.margin.left - this.margin.right;
          const scaledXPos =
            this.margin.left + (xPos / this.width) * usableWidth;
          return scaledXPos;
        })
        .attr("y", this.height + 30)
        .attr("text-anchor", "middle")
        .text("12 pm");

      // Add day labels at the top
      const days = Array.from(
        new Set(
          this.data.map((d) =>
            d.datetime ? new Date(d.datetime).toISOString().split("T")[0] : null
          )
        )
      );
      const dayLabels = days.map((dateStr) => ({
        date: this.parseDate(dateStr + "T12:00:00"),
        label: this.formatDayOfWeek!(this.parseDate(dateStr + "T12:00:00") as Date),
      }));

      this.svg
        .selectAll(".day-label-top")
        .data(dayLabels)
        .enter()
        .append("text")
        .attr("class", "day-label-top")
        .attr("x", (d: any) => {
          let xPos = this.x!(d.date);

          // Calculate the usable width (total width minus left and right margins)
          const usableWidth = this.width - this.margin.left - this.margin.right;

          // Scale the x position relative to the usable width
          const scaledXPos =
            this.margin.left + (xPos / this.width) * usableWidth;

          return scaledXPos;
        })
        .attr("y", -10)
        .text((d: any) => d.label);
    }
  }

  drawAxes() {
    const yAxis = d3
      .axisLeft(this.y!)
      .tickValues(this.referenceGlucoseLevels)
      .tickFormat((d) =>
        this.referenceGlucoseLevels.includes(Number(d)) ? `${d}` : ""
      );

    const yAxisGroup = this.svg.append("g").attr("class", "axis y-axis").call(yAxis);

    yAxisGroup.select(".domain").style("display", "none");

    this.svg
      .append("text")
      .attr("class", "mg-dl-label")
      .attr("x", this.height / 2 - 65)
      .attr("y", this.margin.left - 10)
      .text("mg/dL");
  }

  drawSVG() {
    if (this.selector) {
      const container = this.renderRoot.querySelector(this.selector) as HTMLElement;

      d3.select(container).selectAll("svg").remove();

      // Create the SVG container
      const svg = d3
        .select(container)
        .attr(
          "viewBox",
          `0 0 ${this.width + this.margin.left + this.margin.right} ${this.height + this.margin.top + this.margin.bottom
          }`
        )
        .attr("preserveAspectRatio", "xMidYMid meet")
        .classed("svg-content-responsive", true)
        .append("g")
        .attr("transform", `translate(${this.margin.left},${this.margin.top})`);

      this.svg = svg;
    }

  }

  initializeScales() {
    const validMaxDate = d3.max(this.data, (d) => {
      const dateValue = d.datetime;
      return dateValue && !isNaN(new Date(dateValue).getTime()) ? new Date(dateValue) : null;
    });

    const domainStart = this.first12AM || new Date(0);
    const domainEnd = validMaxDate || new Date();

    this.x = d3
      .scaleUtc()
      .domain([domainStart, domainEnd])
      .range([0, this.width]);

    this.y = d3.scaleLinear().domain([0, 400]).nice().range([this.height, 0]);
  }


  preprocessData() {
    this.data.forEach((d) => {
      d.datetime = this.parseDate(d.datetime)?.toISOString() ?? "";
      d.glucose = d.glucose !== null ? +d.glucose : 0;
    });
  }

  updated(_changedProperties: PropertyValueMap<any> | Map<PropertyKey, unknown>): void {
    super.updated(_changedProperties);
    this.loadGlucoseData();
  }

  render() {
    return html`
      <div id="daily-gp1">
        <svg id="dgp-wk1"></svg>
      </div>
      <div id="daily-gp2" class="mt-4">
        <svg id="dgp-wk2"></svg>
      </div>
    `;
  }

  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }
    .chart-container {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
    svg {
      width: 100%;
      height: auto;
    }
    .line {
        fill: none;
        stroke: lightgrey;
        stroke-width: 1px;
    }

    .highlight-area {
        fill: lightgreen;
        opacity: 1;
    }

    .highlight-line {
        fill: none;
        stroke: green;
        stroke-width: 1px;
    }

    .highlight-glucose-h-line {
        fill: none;
        stroke: orange;
        stroke-width: 1px;
    }

    .highlight-glucose-l-line {
        fill: none;
        stroke: red;
        stroke-width: 1px;
    }

    .reference-line {
        stroke: black;
        stroke-width: 1px;
    }

    .vertical-line {
        stroke: rgb(223, 223, 223);
        stroke-width: 1px;
    }

    .day-label {
        font-size: 10px;
        fill: #000;
    }

    .day-label-top {
        font-size: 12px;
        text-anchor: middle;
        fill: #000;
    }

    .axis path,
    .axis line {
        fill: none;
        shape-rendering: crispEdges;
    }

    .mg-dl-label {
        font-size: 14px;
        font-weight: bold;
        text-anchor: middle;
        fill: #000;
        transform: rotate(-90deg);
        transform-origin: left center;
    }

    .horizontal-line {
        stroke: rgb(223, 223, 223);
        stroke-width: 1px;
    }
  `;
}
