import { html, css, LitElement, unsafeCSS } from 'lit';
import * as d3 from 'd3';
import tailwindStyle from "../assets/styles.css?inline";
import customStyle from "../assets/custom.css?inline";
import { bgDgpNoData } from '../lib/bgDgpNoData';

const tailwindElement = unsafeCSS(tailwindStyle);
const custom = unsafeCSS(customStyle);
const current = css`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }
    .chart-container {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
    svg {
      width: 100%;
      height: auto;
    }
    .line {
        fill: none;
        stroke: lightgrey;
        stroke-width: 1px;
    }

    .highlight-area {
        fill: lightgreen;
        opacity: 1;
    }

    .highlight-line {
        fill: none;
        stroke: green;
        stroke-width: 1px;
    }

    .highlight-glucose-h-line {
        fill: none;
        stroke: orange;
        stroke-width: 1px;
    }

    .highlight-glucose-l-line {
        fill: none;
        stroke: red;
        stroke-width: 1px;
    }

    .reference-line {
        stroke: black;
        stroke-width: 1px;
    }

    .vertical-line {
        stroke: rgb(223, 223, 223);
        stroke-width: 1px;
    }

    .day-label {
        font-size: 10px;
        fill: #000;
    }

    .day-label-top {
        font-size: 12px;
        text-anchor: middle;
        fill: #000;
    }

    .axis path,
    .axis line {
        fill: none;
        shape-rendering: crispEdges;
    }

    .mg-dl-label {
        font-size: 14px;
        font-weight: bold;
        text-anchor: middle;
        fill: #000;
        transform: rotate(-90deg);
        transform-origin: left center;
    }

    .horizontal-line {
        stroke: rgb(223, 223, 223);
        stroke-width: 1px;
    }
  `;

export class DGPChart extends LitElement {

    static properties = {
        result: { type: Object },
        noDataFound: { type: Boolean },
    };

    constructor() {
        super();
        this.data;
        this.selector;
        this.index;
        this.result = {};

        // Default configuration options (can be overridden)
        this.margin = {
            top: 30,
            right: 30,
            bottom: 35,
            left: 80,
        };
        this.width = 800;
        this.height = 150;

        // Set up scales and parsers
        this.parseDate;
        this.formatDayOfWeek;
        this.x = null;
        this.y = null;

        this.referenceGlucoseLevels = [70, 180];

        // Preprocess the data
        this.first12AM;
        this.visibleNoData = this.noDataFound;
    }

    loadGlucoseData() {

        let originalData = this.result;
        const transformedData = originalData.map((entry) => ({
            datetime: `${entry.date}T${entry.hour.padStart(2, "0")}:00:00`,
            glucose: entry.glucose,
        }));

        const dgpEndDate = new Date(
            transformedData[transformedData.length - 1].datetime
        );
        let dgpStartDate = new Date(dgpEndDate);

        // Subtract 14 days (13 + 1 day) from dgpEndDate to get the start date for the last 14 days
        dgpStartDate.setDate(dgpEndDate.getDate() - 13);

        const timeSeries = this.generateTimeSeries(dgpStartDate, dgpEndDate);
        const dataMap = new Map(
            transformedData.map((d) => [
                this.formatDateToMatch(new Date(d.datetime)),
                d.glucose,
            ])
        );

        const completeData = timeSeries.map((d) => {
            const formattedDate = this.formatDateToMatch(d);
            const glucoseValue = dataMap.get(formattedDate);
            return {
                datetime: formattedDate,
                glucose: glucoseValue === undefined ? null : glucoseValue,
            };
        });

        const weeks = this.divideDatesIntoWeeks(
            completeData[0].datetime,
            dgpEndDate,
            completeData
        );

        const container1 = this.renderRoot.querySelector('#dgp-wk1');
        const container2 = this.renderRoot.querySelector('#dgp-wk2');
        this.drawChart(weeks[0].data, container1);
        this.drawChart(weeks[1].data, container2);

    }

    divideDatesIntoWeeks(startDate, endDate, data) {
        const weeks = [];
        let currentWeekStart = new Date(startDate);

        while (currentWeekStart <= endDate) {
            const currentWeekEnd = new Date(currentWeekStart);
            currentWeekEnd.setDate(currentWeekStart.getDate() + 6);
            const weekData = data.filter((entry) => {
                const entryDate = new Date(entry.datetime);
                return entryDate >= currentWeekStart && entryDate <= currentWeekEnd;
            });

            weeks.push({
                start: new Date(currentWeekStart),
                end: new Date(currentWeekEnd),
                data: weekData,
            });

            currentWeekStart.setDate(currentWeekStart.getDate() + 7);
        }
        return weeks;
    }

    preprocessData() {
        this.data.forEach((d) => {
            d.datetime = this.parseDate(d.datetime);
            d.glucose = +d.glucose;
        });
    }

    formatDateToMatch(date) {
        return date.toISOString().slice(0, 19);
    }

    generateTimeSeries(start, end) {
        const times = [];
        let current = start;
        while (current <= end) {
            times.push(new Date(current));
            current.setHours(current.getHours() + 1);
        }
        return times;
    }

    initializeScales() {
        this.x = d3
            .scaleUtc()
            .domain([this.first12AM, d3.max(this.data, (d) => d.datetime)])
            .range([0, this.width]);

        this.y = d3.scaleLinear().domain([0, 400]).nice().range([this.height, 0]);
    }

    drawChart(data, selector) {
        this.data = data;
        this.selector = selector;
        this.parseDate = d3.utcParse("%Y-%m-%dT%H:%M:%S");
        this.formatDayOfWeek = d3.timeFormat("%A");
        this.preprocessData();
        const firstDay = new Date(
            d3
                .min(
                    this.data.filter((d) => d.datetime),
                    (d) => d.datetime
                )
                ?.toISOString()
                .split("T")[0]
        );
        this.first12AM = new Date(firstDay);
        this.first12AM.setUTCHours(0, 0, 0, 0);
        this.initializeScales();
        const svg = this.drawSVG();
        this.drawAxes(svg);
        this.drawLines(svg);
        this.drawReferenceLines(svg);
        this.drawHighlightAreas(svg);
        this.drawClipPaths(svg);
        this.drawGlucoseLines(svg);
    }

    drawGlucoseLines(svg) {
        const line = d3
            .line()
            .x((d) => this.x(new Date(d.datetime)))
            .y((d) => this.y(d.glucose))
            .defined((d) => d.glucose !== 0)
            .curve(d3.curveBasis);

        // Draw the glucose lines
        svg.append("path").datum(this.data).attr("class", "line").attr("d", line);

        svg
            .append("path")
            .datum(this.data)
            .attr("class", "highlight-glucose-l-line")
            .attr("d", line)
            // .style("fill", "rgb(210,131, 107)")
            .style("opacity", 1)
            .attr("clip-path", "url(#clip-below-70)");

        svg
            .append("path")
            .datum(this.data)
            .attr("class", "highlight-line")
            .attr("d", line)
            .attr("clip-path", "url(#clip)");

        svg
            .append("path")
            .datum(this.data)
            .attr("class", "highlight-glucose-h-line")
            .attr("d", line)
            // .style("fill", "rgb(237, 204, 134)")
            .style("opacity", 1)
            .attr("clip-path", "url(#clip-above-180)");

        // Draw horizontal lines
        svg
            .append("line")
            .attr("class", "horizontal-line")
            .attr("x1", 0)
            .attr("x2", this.width)
            .attr("y1", 0)
            .attr("y2", 0);

        svg
            .append("line")
            .attr("class", "horizontal-line")
            .attr("x1", 0)
            .attr("x2", this.width)
            .attr("y1", this.height)
            .attr("y2", this.height);
    }

    drawClipPaths(svg) {
        svg
            .append("clipPath")
            .attr("id", "clip")
            .append("rect")
            .attr("x", this.x(this.first12AM))
            .attr("y", this.y(this.referenceGlucoseLevels[1]))
            .attr(
                "width",
                this.x(d3.max(this.data, (d) => d.datetime)) - this.x(this.first12AM)
            )
            .attr(
                "height",
                this.y(this.referenceGlucoseLevels[0]) -
                this.y(this.referenceGlucoseLevels[1])
            );

        svg
            .append("clipPath")
            .attr("id", "clip-above-180")
            .append("rect")
            .attr("x", this.x(this.first12AM))
            .attr("y", 0)
            .attr(
                "width",
                this.x(d3.max(this.data, (d) => d.datetime)) - this.x(this.first12AM)
            )
            .attr("height", this.y(this.referenceGlucoseLevels[1]) - 0);

        svg
            .append("clipPath")
            .attr("id", "clip-below-70")
            .append("rect")
            .attr("x", this.x(this.first12AM))
            .attr("y", this.y(this.referenceGlucoseLevels[0]))
            .attr(
                "width",
                this.x(d3.max(this.data, (d) => d.datetime)) - this.x(this.first12AM)
            )
            .attr("height", this.height - this.y(this.referenceGlucoseLevels[0]));
    }

    drawHighlightAreas(svg) {
        const firstDay = new Date(
            d3
                .min(
                    this.data.filter((d) => d.datetime),
                    (d) => d.datetime
                )
                ?.toISOString()
                .split("T")[0]
        );
        // const first12AM = new Date(firstDay);
        // first12AM.setUTCHours(0, 0, 0, 0);

        // const referenceGlucoseLevels = [70, 180];

        // Draw highlighted areas
        svg
            .append("rect")
            .attr("class", "highlight-area-below")
            .attr("x", this.x(this.first12AM))
            .attr("y", this.y(this.referenceGlucoseLevels[1]))
            .attr(
                "width",
                this.x(d3.max(this.data, (d) => d.datetime)) - this.x(this.first12AM)
            )
            .attr(
                "height",
                this.y(this.referenceGlucoseLevels[0]) -
                this.y(this.referenceGlucoseLevels[1])
            )
            .style("fill", "rgba(223, 223, 223, 1)")
            .style("opacity", 1);
    }

    drawReferenceLines(svg) {
        // Draw reference lines
        svg
            .selectAll(".reference-line")
            .data(this.referenceGlucoseLevels)
            .enter()
            .append("line")
            .attr("class", "reference-line")
            .attr("x1", 0)
            .attr("x2", this.width)
            .attr("y1", (d) => this.y(d))
            .attr("y2", (d) => this.y(d));
    }

    drawLines(svg) {
        const twelveAMs = Array.from(
            new Set(
                this.data
                    .map((d) =>
                        d.datetime ? new Date(d.datetime).toISOString().split("T")[0] : null
                    )
                    .filter((dateStr) => dateStr !== null)
            )
        ).map((dateStr) => this.parseDate(dateStr + "T00:00:00"));

        const twelvePMs = Array.from(
            new Set(
                this.data.map((d) =>
                    d.datetime ? new Date(d.datetime).toISOString().split("T")[0] : null
                )
            )
        ).map((dateStr) => this.parseDate(dateStr + "T12:00:00"));

        svg
            .selectAll(".vertical-line-am")
            .data(twelveAMs)
            .enter()
            .append("line")
            .attr("class", "vertical-line")
            .attr("x1", (d) => this.x(d))
            .attr("x2", (d) => this.x(d))
            .attr("y1", 0)
            .attr("y2", this.height);

        svg
            .selectAll(".vertical-line-pm")
            .data(twelvePMs)
            .enter()
            .append("line")
            .attr("class", "vertical-line")
            .attr("x1", (d) => this.x(d))
            .attr("x2", (d) => this.x(d))
            .attr("y1", 0)
            .attr("y2", this.height);

        svg
            .selectAll(".label-date")
            .data(twelveAMs)
            .enter()
            .append("text")
            .attr("class", "label-date")
            .attr("x", (d) => this.x(d) + 5)
            .attr("y", 10)
            .text((d) => new Date(d).getDate())
            .style("font-size", "10px")
            .style("text-anchor", "start");

        if (this.index == 0) {
            svg
                .selectAll(".label-12pm")
                .data(twelvePMs)
                .enter()
                .append("text")
                .attr("class", "day-label")
                .attr("x", (d) => {
                    let xPos = this.x(d);
                    const usableWidth = this.width - this.margin.left - this.margin.right;
                    const scaledXPos =
                        this.margin.left + (xPos / this.width) * usableWidth;
                    return scaledXPos;
                })
                .attr("y", this.height + 30)
                .attr("text-anchor", "middle")
                .text("12 pm");

            // Add day labels at the top
            const days = Array.from(
                new Set(
                    this.data.map((d) =>
                        d.datetime ? new Date(d.datetime).toISOString().split("T")[0] : null
                    )
                )
            );
            const dayLabels = days.map((dateStr) => ({
                date: this.parseDate(dateStr + "T12:00:00"),
                label: this.formatDayOfWeek(this.parseDate(dateStr + "T12:00:00")),
            }));

            svg
                .selectAll(".day-label-top")
                .data(dayLabels)
                .enter()
                .append("text")
                .attr("class", "day-label-top")
                .attr("x", (d) => {
                    let xPos = this.x(d.date);

                    // Calculate the usable width (total width minus left and right margins)
                    const usableWidth = this.width - this.margin.left - this.margin.right;

                    // Scale the x position relative to the usable width
                    const scaledXPos =
                        this.margin.left + (xPos / this.width) * usableWidth;

                    return scaledXPos;
                })
                .attr("y", -10)
                .text((d) => d.label);
        }
    }

    drawAxes(svg) {
        // Define Y axis with reference glucose levels
        const yAxis = d3
            .axisLeft(this.y)
            .tickValues(this.referenceGlucoseLevels)
            .tickFormat((d) => (this.referenceGlucoseLevels.includes(d) ? d : ""));

        const yAxisGroup = svg.append("g").attr("class", "axis y-axis").call(yAxis);

        yAxisGroup.select(".domain").style("display", "none");

        svg
            .append("text")
            .attr("class", "mg-dl-label")
            .attr("x", this.height / 2 - 65)
            .attr("y", this.margin.left - 10)
            .text("mg/dL");
    }

    drawSVG() {
        // Remove any existing SVG elements
        d3.select(this.selector).selectAll("svg").remove();

        // Create the SVG container
        const svg = d3
            .select(this.selector)
            .attr(
                "viewBox",
                `0 0 ${this.width + this.margin.left + this.margin.right} ${this.height + this.margin.top + this.margin.bottom
                }`
            )
            .attr("preserveAspectRatio", "xMidYMid meet")
            .classed("svg-content-responsive", true)
            .append("g")
            .attr("transform", `translate(${this.margin.left},${this.margin.top})`);

        return svg;
    }

    updated(_changedProperties) {
        super.updated(_changedProperties);
        if (_changedProperties.has('noDataFound')) {
            this.visibleNoData = this.noDataFound;
            this.requestUpdate();
        }
        if (this.result.length) {
            this.loadGlucoseData();
        }
    }


    render() {
        return html`
        ${this.visibleNoData
                ? html`
        <div class="dgp-chart-element-no-data-error">
        <div>
        <div
                class="flex justify-center z-[10] w-full h-full relative text-md font-bold text-center bg-center bg-no-repeat bg-contain bg-[url('${bgDgpNoData}')] h-[35rem]">
                <div class="content-center">
                <div class="rounded-[5px] w-full p-6 bg-[#a3b8d3ab] text-white" style="border: 1px solid #557dae">
                    <p>No data Found, Try again with another date range.</p>
                </div>
                </div>
            </div>
            </div>
        </div>
        `
                : html`
                <div id="daily-gp1">
                        <svg id="dgp-wk1"></svg>
                    </div>
                    <div id="daily-gp2" class="mt-4">
                        <svg id="dgp-wk2"></svg>
                </div>
                `}

        `;
    }

    static styles = [tailwindElement, custom, current];

}
customElements.define('dgp-chart', DGPChart);