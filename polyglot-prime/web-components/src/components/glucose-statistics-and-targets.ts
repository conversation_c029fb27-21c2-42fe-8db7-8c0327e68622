import { html, LitElement, unsafeCSS } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import tailwindStyle from "../assets/styles.css?inline";
import customStyle from "../assets/custom.css?inline";
import './formula-component';

const tailwindElement = unsafeCSS(tailwindStyle);
const custom = unsafeCSS(customStyle);

@customElement('glucose-statistics-and-targets')
export class GlucoseStatsTargetTracker extends LitElement {

    static styles = [tailwindElement, custom];

    @property({ type: String }) startDate: String = '';
    @property({ type: String }) endDate: String = '';
    @property({ type: Number }) dateDiff: Number = 0;

    render() {
        return html`
        
        <div class="w-full border border-gray-400 rounded">
            <div class="flex p-2" style="background-color: #E3E3E2; border-bottom: 1px solid #E3E3E2;">
            <p class="text-black font-sans text-base font-bold">
                GLUCOSE STATISTICS AND TARGETS
            </p>
            </div>
            <div class="p-5">
            <div class="w-[100%] flex row justify-between">
                <p>
                <span class="startDate">${this.startDate ? this.startDate : 'Loading...'}</span> -
                <span class="endDate">${this.endDate ? this.endDate : 'Loading...'}</span>
                </p>
                <p class="dateDiff">${this.dateDiff ? this.dateDiff + ' days' : 'Loading...'}</p>
            </div>

            <div class="w-[100%] flex row justify-between pt-2">
                <p class="text-gray-800 font-bold text-sm @apply leading-[26px]">Time CGM Active</p>
                <div class="text-gray-800 font-bold text-sm">

                <div  role="status" id="percentageTimeCgmActiveLoader">
                    <svg aria-hidden="true" class="w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"></path>
                        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"></path>
                    </svg>
                    <span class="sr-only">Loading...</span>
                </div>
                
                <div class="flex flex-row p-1">
                    <div>
                    <span class="percentageTimeCgmActive">87.61</span>
                    <span class="text-gray-800 font-normal text-xs percentageTimeCgmActiveUnit">%</span>
                    </div>
                    <div>                    
                    <formula-component content="The Daily Glucose Profile Chart visualizes a participant's glucose levels over a specified timeframe, typically the last 14 days. Each point on the graph represents a glucose reading taken at a specific hour, indicating the participant's response to food intake, exercise, medication, and other lifestyle factors. Monitoring these thresholds helps in identifying periods of risk: hypoglycemia, for glucose levels below 70 mg/dL, and hyperglycemia, for levels above 180 mg/dL. This analysis can guide interventions and adjustments in treatment. A consistently high or low profile may lead to further investigation and modifications in treatment plans."></formula-component>
                    </div>
                </div>

                </div>
            </div>

            <div class="w-[100%] flex row justify-between pt-2">
                <p class="text-gray-800 font-bold text-sm @apply leading-[26px]">Number of Days CGM Worn</p>
                <div class="text-gray-800 font-bold text-sm">
                <div class="flex flex-row p-1">
                    <div>
                    <span class="percentageTimeCgmActive">87.61</span>
                    <span class="text-gray-800 font-normal text-xs percentageTimeCgmActiveUnit">%</span>
                    </div>
                    <div>
                    <formula-component content="Hypoglycemia Component = VLow + (0.8 × Low)
                                            Hyperglycemia Component = VHigh + (0.5 × High)
                                            GRI = (3.0 × Hypoglycemia Component) + (1.6 × Hyperglycemia Component)
                                            Equivalently,
                                            GRI = (3.0 × VLow) + (2.4 × Low) + (1.6 × VHigh) + (0.8 × High)"></formula-component>
                    </div>
                </div>
                </div>
            </div>

            <div class="w-[100%] flex row justify-between pt-2">
                <table
                class="table-auto text-gray-800 font-normal text-xs mt-2 mb-2 bg-zinc-200 w-full border-collapse border border-slate-500">
                <thead>
                    <tr>
                    <th colspan="2" class="p-2 bg-gray-300">
                        <p>Ranges And Targets For Type 1 or Type 2 Diabetes</p>
                    </th>
                    </tr>
                    <tr>
                    <th class="text-left p-2 bg-gray-200">
                        <p>Glucose Ranges</p>
                    </th>
                    <th class="text-left p-2 bg-gray-200 text-right">
                        <p>Targets % of Readings (Time/Day)</p>
                    </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                    <td class="p-2">Target Range 70-180 mg/dL</td>
                    <td class="p-2 text-right">Greater than 70% (16h 48min)</td>
                    </tr>
                    <tr>
                    <td class="p-2">Below 70 mg/dL</td>
                    <td class="p-2 text-right">Less than 4% (58min)</td>
                    </tr>
                    <tr>
                    <td class="p-2">Below 54 mg/dL</td>
                    <td class="p-2 text-right">Less than 1% (14min)</td>
                    </tr>
                    <tr>
                    <td class="p-2">Above 180 mg/dL</td>
                    <td class="p-2 text-right">Less than 25% (6h)</td>
                    </tr>
                    <tr>
                    <td class="p-2">Above 250 mg/dL</td>
                    <td class="p-2 text-right">Less than 5% (1h 12min)</td>
                    </tr>
                    <tr>
                    <td colspan="2" class="p-2 bg-gray-100">
                        <p class="font-mono text-center">
                        Each 5% increase in time in range (70-180 mg/dL) is clinically beneficial.
                        </p>
                    </td>
                    </tr>
                </tbody>
                </table>
            </div>
            
            <div class="w-[100%] flex row justify-between pt-2">
                <p class="text-gray-800 font-bold text-sm @apply leading-[26px]">Mean Glucose</p>
                <div class="text-gray-800 font-bold text-sm">
                <div class="flex flex-row p-1">
                    <div>
                    <span class="percentageTimeCgmActive">87.61</span>
                    <span class="text-gray-800 font-normal text-xs percentageTimeCgmActiveUnit">%</span>
                    </div>
                    <div>
                    <div class="cursor-pointer" data-name="time_cgm_active">
                        <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 5H7V7H17V5Z" fill="currentColor"></path>
                        <path d="M7 9H9V11H7V9Z" fill="currentColor"></path>
                        <path d="M9 13H7V15H9V13Z" fill="currentColor"></path>
                        <path d="M7 17H9V19H7V17Z" fill="currentColor"></path>
                        <path d="M13 9H11V11H13V9Z" fill="currentColor"></path>
                        <path d="M11 13H13V15H11V13Z" fill="currentColor"></path>
                        <path d="M13 17H11V19H13V17Z" fill="currentColor"></path>
                        <path d="M15 9H17V11H15V9Z" fill="currentColor"></path>
                        <path d="M17 13H15V19H17V13Z" fill="currentColor"></path>
                        <path clip-rule="evenodd"
                            d="M3 3C3 1.89543 3.89543 1 5 1H19C20.1046 1 21 1.89543 21 3V21C21 22.1046 20.1046 23 19 23H5C3.89543 23 3 22.1046 3 21V3ZM5 3H19V21H5V3Z"
                            fill="currentColor" fill-rule="evenodd"></path>
                        </svg>
                    </div>
                    </div>
                </div>
                </div>
            </div>

            <div class="w-[100%] flex row justify-between pt-2">
                <p class="text-gray-800 font-bold text-sm @apply leading-[26px]">Glucose Management Indicator (GMI)</p>
                <div class="text-gray-800 font-bold text-sm">
                <div class="flex flex-row p-1">
                    <div>
                    <span class="percentageTimeCgmActive">87.61</span>
                    <span class="text-gray-800 font-normal text-xs percentageTimeCgmActiveUnit">%</span>
                    </div>
                    <div>
                    <div class="cursor-pointer" data-name="time_cgm_active">
                        <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 5H7V7H17V5Z" fill="currentColor"></path>
                        <path d="M7 9H9V11H7V9Z" fill="currentColor"></path>
                        <path d="M9 13H7V15H9V13Z" fill="currentColor"></path>
                        <path d="M7 17H9V19H7V17Z" fill="currentColor"></path>
                        <path d="M13 9H11V11H13V9Z" fill="currentColor"></path>
                        <path d="M11 13H13V15H11V13Z" fill="currentColor"></path>
                        <path d="M13 17H11V19H13V17Z" fill="currentColor"></path>
                        <path d="M15 9H17V11H15V9Z" fill="currentColor"></path>
                        <path d="M17 13H15V19H17V13Z" fill="currentColor"></path>
                        <path clip-rule="evenodd"
                            d="M3 3C3 1.89543 3.89543 1 5 1H19C20.1046 1 21 1.89543 21 3V21C21 22.1046 20.1046 23 19 23H5C3.89543 23 3 22.1046 3 21V3ZM5 3H19V21H5V3Z"
                            fill="currentColor" fill-rule="evenodd"></path>
                        </svg>
                    </div>
                    </div>
                </div>
                </div>
            </div>

            <div class="w-[100%] flex row justify-between pt-2">
                <p class="text-gray-800 font-bold text-sm @apply leading-[26px]">Glucose Variability</p>
                <div class="text-gray-800 font-bold text-sm">
                <div class="flex flex-row p-1">
                    <div>
                    <span class="percentageTimeCgmActive">87.61</span>
                    <span class="text-gray-800 font-normal text-xs percentageTimeCgmActiveUnit">%</span>
                    </div>
                    <div>
                    <div class="cursor-pointer" data-name="time_cgm_active">
                        <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 5H7V7H17V5Z" fill="currentColor"></path>
                        <path d="M7 9H9V11H7V9Z" fill="currentColor"></path>
                        <path d="M9 13H7V15H9V13Z" fill="currentColor"></path>
                        <path d="M7 17H9V19H7V17Z" fill="currentColor"></path>
                        <path d="M13 9H11V11H13V9Z" fill="currentColor"></path>
                        <path d="M11 13H13V15H11V13Z" fill="currentColor"></path>
                        <path d="M13 17H11V19H13V17Z" fill="currentColor"></path>
                        <path d="M15 9H17V11H15V9Z" fill="currentColor"></path>
                        <path d="M17 13H15V19H17V13Z" fill="currentColor"></path>
                        <path clip-rule="evenodd"
                            d="M3 3C3 1.89543 3.89543 1 5 1H19C20.1046 1 21 1.89543 21 3V21C21 22.1046 20.1046 23 19 23H5C3.89543 23 3 22.1046 3 21V3ZM5 3H19V21H5V3Z"
                            fill="currentColor" fill-rule="evenodd"></path>
                        </svg>
                    </div>
                    </div>
                </div>
                </div>
            </div>
            
            <div class="w-[100%] flex row justify-between pt-2 pb-4">
                <p class="text-gray-700 font-bold text-xs">
                Defined as percent coefficient of variation (%CV); target ≤36%
                </p>
            </div>

            </div>
        </div>
    `;

    }
}