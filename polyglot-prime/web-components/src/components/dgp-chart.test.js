import { html, fixture, expect } from '@open-wc/testing';
import sinon from 'sinon';
import './dgp-chart';

describe('DGPChart Component', () => {
    let element;

    beforeEach(async () => {
        element = await fixture(html`<dgp-chart></dgp-chart>`);
    });

    it('should render the component', async () => {
        expect(element).to.be.instanceOf(HTMLElement);
    })

    it('should have default properties', () => {
        expect(element.result).to.deep.equal({});
        expect(element.margin).to.deep.equal({
            top: 30,
            right: 30,
            bottom: 35,
            left: 80,
        });
        expect(element.width).to.equal(800);
        expect(element.height).to.equal(150);
    });


    it('should update and call loadGlucoseData when result is set', async () => {
        const sampleData = [
            { date: '2024-11-14', hour: '08', glucose: 120 },
            { date: '2024-11-14', hour: '09', glucose: 130 },
        ];

        const spy = sinon.spy(element, 'loadGlucoseData');

        element.result = sampleData;
        await element.updateComplete;

        expect(spy).to.have.been.calledOnce;
        expect(element.result).to.deep.equal(sampleData);
    });

    it('should divide data into weeks correctly', () => {
        const sampleData = [
            { datetime: '2024-11-01T00:00:00', glucose: 100 },
            { datetime: '2024-11-07T23:00:00', glucose: 110 },
            { datetime: '2024-11-08T00:00:00', glucose: 120 },
            { datetime: '2024-11-14T23:00:00', glucose: 130 },
        ];

        const weeks = element.divideDatesIntoWeeks(
            '2024-11-01T00:00:00',
            new Date('2024-11-14T23:00:00'),
            sampleData
        );
        expect(weeks).to.have.lengthOf(2);
    });

    it('should call drawChart with the correct arguments', () => {
        const sampleWeeks = [
            { data: [{ datetime: '2024-11-01T00:00:00', glucose: 100 }] },
            { data: [{ datetime: '2024-11-08T00:00:00', glucose: 120 }] },
        ];

        const spy = sinon.spy(element, 'drawChart');

        element.drawChart(sampleWeeks[0].data, '#dgp-wk1');
        element.drawChart(sampleWeeks[1].data, '#dgp-wk2');

        expect(spy).to.have.been.calledTwice;
    });
})