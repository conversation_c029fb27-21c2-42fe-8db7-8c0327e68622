import { LitElement, html, css, PropertyValues, unsafeCSS } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import * as d3 from 'd3';
import tailwindStyle from "../assets/styles.css?inline";
import customStyle from "../assets/custom.css?inline";
import { bgStackedBarNoData } from '../lib/bgStackedBarNoData';

type DataPoint = {
  category: string;
  value: number;
};

type EndPoint = {
  x: number;
  y: number;
};

const tailwindElement = unsafeCSS(tailwindStyle);
const custom = unsafeCSS(customStyle);
const current = css`
:host {
  display: block;
  width: 100%;
  height: 100%;
}
.error-text {
  fill: red;
  font-size: 16px;
  text-anchor: middle;
}
`;

@customElement('stacked-bar-chart')
export class StackedBarChartD3Aide extends LitElement {
  static styles = [tailwindElement, custom, current];

  @property({ type: Array })
  data: Array<{ category: string; value: number; color: string }> = [];

  @property({ type: Number })
  chartWidth: number = 600;

  @property({ type: Number })
  chartHeight: number = 400;

  @property({ type: Boolean })
  noDataFound: boolean = false;

  @state()
  private visibleNoData = false;

  private margin = { top: 80, right: 0, bottom: 60, left: 0 };
  private svg!: d3.Selection<SVGGElement, unknown, null, undefined>;
  private x!: d3.ScaleBand<string>;
  private y!: d3.ScaleLinear<number, number>;

  constructor() {
    super();
    this.visibleNoData = this.noDataFound;
  }

  render() {
    return html`
    ${this.visibleNoData
        ? html`
            <div class="tir-chart-element-no-data-error">
            <div>
            <div class="flex justify-center z-[10] w-full h-full relative text-md font-bold text-center bg-center bg-no-repeat bg-contain bg-[url('${bgStackedBarNoData}')] h-[25rem]">
              <div class="content-center">
                <div class="rounded-[5px] w-full p-6 bg-[#a3b8d3ab] text-white" style="border: 1px solid #557dae">
                  <p>No data Found, Try again with another date range.</p>
                </div>
              </div>
            </div>
          </div>
          </div>
          `
        : html`
        <div class="chartContainer">
          <svg id="tir-chart" class="m-0"></svg>
        </div>
        `}

    `;
  }

  protected updated(_changedProperties: PropertyValues) {
    if (_changedProperties.has('noDataFound')) {
      this.visibleNoData = this.noDataFound;
      this.requestUpdate();
    }
    super.updated(_changedProperties);
    this.initializeChart();
  }

  private initializeChart() {
    this.createSVG();
    this.createScales();
    this.drawStackedBars();
    this.drawTextAndLines();
  }

  private createSVG() {
    const svgContainer = this.renderRoot.querySelector('.chartContainer') as HTMLElement;

    const svg = d3.select(svgContainer);
    svg.selectAll('*').remove();

    this.svg = d3
      .select(svgContainer)
      .append('svg')
      .attr('class', 'w-full h-auto m-0')
      .attr('preserveAspectRatio', 'xMidYMid meet')
      .attr('viewBox', `0 0 ${this.chartWidth} ${this.chartHeight}`)
      .append('g')
      .attr('transform', `translate(-${this.margin.left + 200},${this.margin.top})`);
  }

  private createScales() {
    this.x = d3
      .scaleBand()
      .domain(['Goals for Type 1 and Type 2 Diabetes'])
      .range([0, this.chartWidth])
      .padding(0.8);

    this.y = d3
      .scaleLinear()
      .domain([0, 100])
      .range([this.chartHeight - this.margin.top - this.margin.bottom, 0]);
  }

  private drawStackedBars() {
    let y0 = 0;
    this.data.forEach((d) => {
      const barHeight = d.value > 0 ? d.value + 2 : 2;
      const color = d.value > 0 ? d.color : '#d3d3d3';

      this.svg
        .append('rect')
        .attr('x', this.x('Goals for Type 1 and Type 2 Diabetes')!)
        .attr('y', this.y(y0 + barHeight))
        .attr('width', this.x.bandwidth())
        .attr('height', this.y(y0) - this.y(y0 + barHeight))
        .attr('fill', color)
        .attr('stroke', 'white');

      y0 += barHeight;
    });
  }



  drawTextAndLines(): void {
    let y1 = 0;
    // @ts-ignore
    let highEndPoint: EndPoint | undefined;
    // @ts-ignore
    let veryHighEndPoint: EndPoint | undefined;
    // @ts-ignore
    let lowEndPoint: EndPoint | undefined;
    // @ts-ignore
    let veryLowEndPoint: EndPoint | undefined;
    // @ts-ignore
    let targetValue = 0;

    this.data.forEach((d: DataPoint) => {
      const barValue = d.value > 0 ? d.value : 2;
      let lineY = y1 + barValue / 2;
      let textY = y1 + barValue / 2 + 5;
      let lineLength = 130;

      targetValue =
        this.data.find((item: DataPoint) => item.category === "Target")?.value ?? 0;

      const goalX =
        (this.x?.("Goals for Type 1 and Type 2 Diabetes") ?? 0) +
        (this.x?.bandwidth?.() ?? 0);


      if (d.category === "Very High") {
        lineY += 7;
        textY = lineY + 4;
        lineLength = 25;
        veryHighEndPoint = {
          x: goalX + 120 + lineLength,
          y: this.y(lineY),
        };
      } else if (d.category === "High") {
        lineY += 5;
        textY = lineY - 4;
        lineLength = 25;
        highEndPoint = {
          x: goalX + 120 + lineLength,
          y: this.y(lineY),
        };
      } else if (d.category === "Low") {
        lineY += 1;
        textY = lineY + 4;
        lineLength = 25;
        lowEndPoint = {
          x: goalX + 120 + lineLength,
          y: this.y(lineY),
        };
      } else if (d.category === "Very Low") {
        textY = lineY - 4;
        lineLength = 25;
        veryLowEndPoint = {
          x: goalX + 120 + lineLength,
          y: this.y(lineY),
        };
      }

      this.svg
        .append("text")
        .attr("x", goalX + 10)
        .attr("y", this.y(textY))
        .attr("text-anchor", "start")
        .attr("dy", ".50em")
        .style("font-weight", "bold")
        .style("font-size", "12px")
        .append("tspan")
        .attr("x", goalX + 10)
        .text(d.category)
        .append("tspan")
        .attr("x", goalX + 10 + 130)
        .attr("text-anchor", "end")
        .text(d.category !== "Target" ? `${d.value}%` : "");

      if (d.category === "Very High") {
        this.svg
          .append("text")
          .attr("x", goalX + 10)
          .attr("y", this.y(textY + 4))
          .text("Goal: <5%")
          .style("font-size", "12px")
          .style("fill", "#7A7A7B");
      }

      if (d.category === "Very Low") {
        this.svg
          .append("text")
          .attr("x", goalX + 10)
          .attr("y", this.y(textY - 10))
          .text("Goal: <1%")
          .style("font-size", "12px")
          .style("fill", "#7A7A7B");

        this.svg
          .append("text")
          .attr("x", goalX + 90)
          .attr("y", this.y(textY - 10))
          .attr("text-anchor", "start")
          .text("Each 1% time in range = about 15 minutes")
          .style("font-size", "12px")
          .style("fill", "#7A7A7B");
      }

      const highValue =
        this.data.find((item: DataPoint) => item.category === "High")?.value ?? 0;

      if (d.category === "Target") {
        this.svg
          .append("text")
          .attr("x", goalX + 154)
          .attr("y", this.y(textY))
          .attr("text-anchor", "start")
          .attr("dy", ".50em")
          .style("font-size", "15px")
          .style("font-weight", "bold")
          .text(`${d.value}%`);

        this.svg
          .append("text")
          .attr("x", goalX + 202)
          .attr("y", this.y(textY - 3))
          .text("Goal: ≥70%")
          .style("font-size", "12px")
          .style("fill", "#7A7A7B");

        this.svg
          .append("text")
          .attr("x", goalX + 275)
          .attr("y", this.y(textY - 12))
          .attr("text-anchor", "start")
          .text("Each 5% increase is clinically beneficial")
          .style("font-size", "12px")
          .style("fill", "#7A7A7B");

        if (highValue > 0) {
          this.svg
            .append("text")
            .attr("x", goalX - 100)
            .attr("y", this.y(y1 + d.value))
            .attr("text-anchor", "start")
            .text("180")
            .style("font-weight", "bold")
            .style("font-size", "12px");
        }
      }

      if (d.category === "High" && highValue > 0) {
        this.svg
          .append("text")
          .attr("x", goalX - 100)
          .attr("y", this.y(y1 + d.value + 5))
          .attr("text-anchor", "start")
          .style("font-weight", "bold")
          .text("250")
          .style("font-size", "12px");
      }

      this.svg
        .append("line")
        .attr("x1", goalX + 10)
        .attr("y1", this.y(lineY))
        .attr("x2", goalX + 120 + lineLength)
        .attr("y2", this.y(lineY))
        .attr("width", 200)
        .attr("stroke", "black")
        .attr("stroke-width", 1)
        .attr("stroke-linecap", "round");

      y1 += barValue;
    });

    // Logic for connecting lines remains the same.
  }



}
