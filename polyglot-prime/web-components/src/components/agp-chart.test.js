import { html, fixture, expect } from '@open-wc/testing';
import sinon from 'sinon';
import './agp-chart';

describe('AGPChart Component', () => {
  const data = [
    {
      "hour": "00",
      "p5": 0,
      "p25": 137.75531914893617,
      "p50": 143.64893617021278,
      "p75": 149.38297872340425,
      "p95": 152.74468085106383
    },
    {
      "hour": "01",
      "p5": 0,
      "p25": 133.4468085106383,
      "p50": 139.38297872340425,
      "p75": 144.62765957446808,
      "p95": 148.70212765957447
    },
    {
      "hour": "02",
      "p5": 0,
      "p25": 134.13829787234042,
      "p50": 140.63829787234042,
      "p75": 147.51063829787233,
      "p95": 152.70212765957447
    },
    {
      "hour": "03",
      "p5": 0,
      "p25": 135.21052631578948,
      "p50": 140.1263157894737,
      "p75": 144.93684210526317,
      "p95": 148.55789473684212
    },
    {
      "hour": "04",
      "p5": 0,
      "p25": 128.6,
      "p50": 135.1157894736842,
      "p75": 140.58947368421053,
      "p95": 144.25263157894736
    },
    {
      "hour": "05",
      "p5": 0,
      "p25": 120.57894736842105,
      "p50": 125.56842105263158,
      "p75": 131.1578947368421,
      "p95": 135.07368421052632
    },
    {
      "hour": "06",
      "p5": 0,
      "p25": 113.27083333333333,
      "p50": 118.73958333333333,
      "p75": 124.80208333333333,
      "p95": 130.02083333333334
    },
    {
      "hour": "07",
      "p5": 0,
      "p25": 108.92708333333333,
      "p50": 118.9375,
      "p75": 126.51041666666667,
      "p95": 131.80208333333334
    },
    {
      "hour": "08",
      "p5": 0,
      "p25": 110.76041666666667,
      "p50": 122.44791666666667,
      "p75": 129.33333333333334,
      "p95": 135.33333333333334
    },
    {
      "hour": "09",
      "p5": 0,
      "p25": 121.77659574468085,
      "p50": 130,
      "p75": 139.12765957446808,
      "p95": 144.27659574468086
    },
    {
      "hour": "10",
      "p5": 0,
      "p25": 124.03157894736842,
      "p50": 131.76842105263157,
      "p75": 139.0842105263158,
      "p95": 144.2421052631579
    },
    {
      "hour": "11",
      "p5": 0,
      "p25": 119.57894736842105,
      "p50": 126.47368421052632,
      "p75": 133.45263157894738,
      "p95": 138.14736842105262
    },
    {
      "hour": "12",
      "p5": 0,
      "p25": 115.59574468085107,
      "p50": 124.12765957446808,
      "p75": 133.27659574468086,
      "p95": 138.5212765957447
    },
    {
      "hour": "13",
      "p5": 0,
      "p25": 114.61290322580645,
      "p50": 125.81720430107526,
      "p75": 135.9784946236559,
      "p95": 143.2688172043011
    },
    {
      "hour": "14",
      "p5": 0,
      "p25": 134.4943820224719,
      "p50": 143.37078651685394,
      "p75": 151.3370786516854,
      "p95": 156.98876404494382
    },
    {
      "hour": "15",
      "p5": 0,
      "p25": 129.5888888888889,
      "p50": 141.7111111111111,
      "p75": 152.26666666666668,
      "p95": 157.85555555555555
    },
    {
      "hour": "16",
      "p5": 0,
      "p25": 126.35164835164835,
      "p50": 134.56043956043956,
      "p75": 142.82417582417582,
      "p95": 148.45054945054946
    },
    {
      "hour": "17",
      "p5": 0,
      "p25": 117.4065934065934,
      "p50": 126.45054945054945,
      "p75": 136.6153846153846,
      "p95": 143
    },
    {
      "hour": "18",
      "p5": 0,
      "p25": 112.25,
      "p50": 122,
      "p75": 131.31521739130434,
      "p95": 137.44565217391303
    },
    {
      "hour": "19",
      "p5": 0,
      "p25": 120.72043010752688,
      "p50": 129.19354838709677,
      "p75": 139.13978494623655,
      "p95": 144.4731182795699
    },
    {
      "hour": "20",
      "p5": 0,
      "p25": 119.15053763440861,
      "p50": 129.94623655913978,
      "p75": 140.08602150537635,
      "p95": 146.1505376344086
    },
    {
      "hour": "21",
      "p5": 0,
      "p25": 117.7032967032967,
      "p50": 132.16483516483515,
      "p75": 141.57142857142858,
      "p95": 147.86813186813185
    },
    {
      "hour": "22",
      "p5": 0,
      "p25": 122.95652173913044,
      "p50": 132.1086956521739,
      "p75": 140.66304347826087,
      "p95": 146.57608695652175
    },
    {
      "hour": "23",
      "p5": 0,
      "p25": 131.60638297872342,
      "p50": 138.64893617021278,
      "p75": 145.91489361702128,
      "p95": 150.74468085106383
    }
  ];

  it('should render the component', async () => {
    const el = await fixture(
      html`<agp-chart></agp-chart>`
    );
    expect(el).to.be.instanceOf(HTMLElement);
  });

  it('should accept data, width, and height as properties', async () => {

    const el = await fixture(
      html`<agp-chart
         .data=${data}
         .width=${710}
         .height=${440}
       ></agp-chart>`
    );

    expect(el.data).to.deep.equal(data);
    expect(el.width).to.equal(710);
    expect(el.height).to.equal(440);
  });
  
});
