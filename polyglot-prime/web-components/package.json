{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build --emptyOutDir", "preview": "vite preview --outDir html", "test": "vitest", "test:ui": "vitest --ui", "test:report": "vitest --reporter=html"}, "dependencies": {"@types/d3": "^7.4.3", "d3": "^7.9.0", "lit": "^3.2.1", "sass": "^1.81.0"}, "devDependencies": {"@open-wc/testing": "^4.0.0", "@types/jest": "^29.5.14", "@vitest/browser": "latest", "@vitest/ui": "^2.1.5", "autoprefixer": "^10.4.20", "happy-dom": "^15.11.6", "jest": "^29.7.0", "postcss": "^8.4.49", "sinon": "^19.0.2", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "vite": "^5.4.10", "vitest": "^2.1.5"}}